version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: quant_postgres
    environment:
      POSTGRES_DB: quant_trading
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - quant_network

  influxdb:
    image: influxdb:2.7
    container_name: quant_influxdb
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: admin
      DOCKER_INFLUXDB_INIT_PASSWORD: password123
      DOCKER_INFLUXDB_INIT_ORG: quant-trading
      DOCKER_INFLUXDB_INIT_BUCKET: market_data
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
    networks:
      - quant_network

  redis:
    image: redis:7-alpine
    container_name: quant_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - quant_network

volumes:
  postgres_data:
  influxdb_data:
  redis_data:

networks:
  quant_network:
    driver: bridge
