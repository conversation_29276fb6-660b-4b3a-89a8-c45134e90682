"""
最小化WebSocket测试
"""
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_websocket():
    """测试基础WebSocket功能"""
    print("=== 测试基础WebSocket功能 ===")
    
    try:
        # 测试FastAPI WebSocket导入
        from fastapi import WebSocket, WebSocketDisconnect
        print("✅ FastAPI WebSocket导入成功")
        
        # 测试连接管理器
        from app.websocket.connection_manager import ConnectionManager
        print("✅ 连接管理器导入成功")
        
        # 创建连接管理器
        manager = ConnectionManager()
        print("✅ 连接管理器创建成功")
        
        # 获取连接统计
        stats = manager.get_connection_stats()
        print(f"✅ 连接统计: {stats}")
        
        # 测试基本功能
        print(f"  活跃连接数: {stats.get('active_connections', 0)}")
        print(f"  总连接数: {stats.get('total_connections', 0)}")
        print(f"  消息发送数: {stats.get('messages_sent', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础WebSocket测试失败: {e}")
        return False

def test_websocket_dependencies():
    """测试WebSocket相关依赖"""
    print("\n=== 测试WebSocket依赖 ===")
    
    dependencies = [
        'fastapi',
        'websockets', 
        'uvicorn',
        'starlette'
    ]
    
    success_count = 0
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} 可用")
            success_count += 1
        except ImportError:
            print(f"❌ {dep} 不可用")
    
    print(f"\n依赖检查结果: {success_count}/{len(dependencies)} 可用")
    return success_count == len(dependencies)

def main():
    """主函数"""
    print("🚀 WebSocket最小化测试")
    print("=" * 30)
    
    # 测试依赖
    deps_ok = test_websocket_dependencies()
    
    # 测试基础功能
    basic_ok = test_basic_websocket()
    
    print("\n" + "=" * 30)
    print("📊 测试结果:")
    print(f"依赖检查: {'✅ 通过' if deps_ok else '❌ 失败'}")
    print(f"基础功能: {'✅ 通过' if basic_ok else '❌ 失败'}")
    
    if deps_ok and basic_ok:
        print("\n🎉 WebSocket功能正常！")
        print("✅ 所有依赖已安装")
        print("✅ 连接管理器工作正常")
        print("✅ 可以进行WebSocket通信")
        return True
    else:
        print("\n⚠️  WebSocket功能有问题")
        if not deps_ok:
            print("❌ 缺少必要依赖")
        if not basic_ok:
            print("❌ 基础功能异常")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
