"""
风险管理模块
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """风险等级"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class AlertType(Enum):
    """告警类型"""
    POSITION_LIMIT = "POSITION_LIMIT"
    DRAWDOWN = "DRAWDOWN"
    VOLATILITY = "VOLATILITY"
    CONCENTRATION = "CONCENTRATION"
    LIQUIDITY = "LIQUIDITY"
    STRATEGY_FAILURE = "STRATEGY_FAILURE"

@dataclass
class RiskAlert:
    """风险告警"""
    alert_type: AlertType
    risk_level: RiskLevel
    ts_code: str
    message: str
    current_value: float
    threshold: float
    timestamp: datetime

@dataclass
class PositionInfo:
    """持仓信息"""
    ts_code: str
    shares: int
    avg_cost: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    weight: float  # 占总资产比例

class RiskManager:
    """风险管理器"""
    
    def __init__(self):
        self.alerts = []
        self.risk_limits = {
            'max_single_position': 0.1,  # 单只股票最大持仓比例
            'max_sector_exposure': 0.3,  # 单个行业最大敞口
            'max_daily_drawdown': 0.05,  # 单日最大回撤
            'max_total_drawdown': 0.15,  # 总最大回撤
            'min_liquidity_ratio': 0.1,  # 最小流动性比例
            'max_volatility': 0.3,       # 最大波动率
            'max_leverage': 1.0           # 最大杠杆率
        }
    
    def check_position_limits(self, positions: List[PositionInfo], total_assets: float) -> List[RiskAlert]:
        """检查持仓限制"""
        alerts = []
        
        for position in positions:
            # 检查单只股票持仓比例
            position_ratio = position.market_value / total_assets
            
            if position_ratio > self.risk_limits['max_single_position']:
                alert = RiskAlert(
                    alert_type=AlertType.POSITION_LIMIT,
                    risk_level=RiskLevel.HIGH if position_ratio > 0.15 else RiskLevel.MEDIUM,
                    ts_code=position.ts_code,
                    message=f"单只股票持仓比例过高: {position_ratio:.2%}",
                    current_value=position_ratio,
                    threshold=self.risk_limits['max_single_position'],
                    timestamp=datetime.now()
                )
                alerts.append(alert)
        
        return alerts
    
    def check_sector_concentration(self, positions: List[PositionInfo], sector_mapping: Dict[str, str], total_assets: float) -> List[RiskAlert]:
        """检查行业集中度"""
        alerts = []
        sector_exposure = {}
        
        # 计算各行业敞口
        for position in positions:
            sector = sector_mapping.get(position.ts_code, "未知")
            if sector not in sector_exposure:
                sector_exposure[sector] = 0
            sector_exposure[sector] += position.market_value
        
        # 检查行业集中度
        for sector, exposure in sector_exposure.items():
            exposure_ratio = exposure / total_assets
            
            if exposure_ratio > self.risk_limits['max_sector_exposure']:
                alert = RiskAlert(
                    alert_type=AlertType.CONCENTRATION,
                    risk_level=RiskLevel.HIGH if exposure_ratio > 0.4 else RiskLevel.MEDIUM,
                    ts_code="",
                    message=f"{sector}行业敞口过高: {exposure_ratio:.2%}",
                    current_value=exposure_ratio,
                    threshold=self.risk_limits['max_sector_exposure'],
                    timestamp=datetime.now()
                )
                alerts.append(alert)
        
        return alerts
    
    def calculate_portfolio_drawdown(self, portfolio_values: pd.Series) -> Tuple[float, float]:
        """计算组合回撤"""
        if len(portfolio_values) < 2:
            return 0.0, 0.0
        
        # 计算累计最高点
        cumulative_max = portfolio_values.expanding().max()
        
        # 计算回撤
        drawdown = (portfolio_values - cumulative_max) / cumulative_max
        
        # 当前回撤
        current_drawdown = drawdown.iloc[-1]
        
        # 最大回撤
        max_drawdown = drawdown.min()
        
        return current_drawdown, max_drawdown
    
    def check_drawdown_limits(self, portfolio_values: pd.Series) -> List[RiskAlert]:
        """检查回撤限制"""
        alerts = []
        
        current_drawdown, max_drawdown = self.calculate_portfolio_drawdown(portfolio_values)
        
        # 检查当前回撤
        if abs(current_drawdown) > self.risk_limits['max_daily_drawdown']:
            alert = RiskAlert(
                alert_type=AlertType.DRAWDOWN,
                risk_level=RiskLevel.HIGH if abs(current_drawdown) > 0.08 else RiskLevel.MEDIUM,
                ts_code="",
                message=f"当前回撤过大: {current_drawdown:.2%}",
                current_value=abs(current_drawdown),
                threshold=self.risk_limits['max_daily_drawdown'],
                timestamp=datetime.now()
            )
            alerts.append(alert)
        
        # 检查最大回撤
        if abs(max_drawdown) > self.risk_limits['max_total_drawdown']:
            alert = RiskAlert(
                alert_type=AlertType.DRAWDOWN,
                risk_level=RiskLevel.CRITICAL,
                ts_code="",
                message=f"最大回撤超限: {max_drawdown:.2%}",
                current_value=abs(max_drawdown),
                threshold=self.risk_limits['max_total_drawdown'],
                timestamp=datetime.now()
            )
            alerts.append(alert)
        
        return alerts
    
    def calculate_portfolio_volatility(self, returns: pd.Series, window: int = 20) -> float:
        """计算组合波动率"""
        if len(returns) < window:
            return 0.0
        
        # 计算滚动标准差并年化
        volatility = returns.rolling(window=window).std() * np.sqrt(252)
        return volatility.iloc[-1] if not volatility.empty else 0.0
    
    def check_volatility_limits(self, returns: pd.Series) -> List[RiskAlert]:
        """检查波动率限制"""
        alerts = []
        
        volatility = self.calculate_portfolio_volatility(returns)
        
        if volatility > self.risk_limits['max_volatility']:
            alert = RiskAlert(
                alert_type=AlertType.VOLATILITY,
                risk_level=RiskLevel.HIGH if volatility > 0.4 else RiskLevel.MEDIUM,
                ts_code="",
                message=f"组合波动率过高: {volatility:.2%}",
                current_value=volatility,
                threshold=self.risk_limits['max_volatility'],
                timestamp=datetime.now()
            )
            alerts.append(alert)
        
        return alerts
    
    def check_liquidity_risk(self, positions: List[PositionInfo], volume_data: Dict[str, float]) -> List[RiskAlert]:
        """检查流动性风险"""
        alerts = []
        
        for position in positions:
            daily_volume = volume_data.get(position.ts_code, 0)
            
            if daily_volume > 0:
                # 计算持仓占日均成交量比例
                liquidity_ratio = position.shares / daily_volume
                
                if liquidity_ratio > 0.1:  # 持仓超过日均成交量10%
                    alert = RiskAlert(
                        alert_type=AlertType.LIQUIDITY,
                        risk_level=RiskLevel.HIGH if liquidity_ratio > 0.2 else RiskLevel.MEDIUM,
                        ts_code=position.ts_code,
                        message=f"流动性风险: 持仓占日均成交量 {liquidity_ratio:.2%}",
                        current_value=liquidity_ratio,
                        threshold=0.1,
                        timestamp=datetime.now()
                    )
                    alerts.append(alert)
        
        return alerts
    
    def check_strategy_performance(self, strategy_returns: pd.Series, benchmark_returns: pd.Series, window: int = 30) -> List[RiskAlert]:
        """检查策略表现"""
        alerts = []
        
        if len(strategy_returns) < window or len(benchmark_returns) < window:
            return alerts
        
        # 计算超额收益
        excess_returns = strategy_returns - benchmark_returns
        recent_excess = excess_returns.tail(window).mean()
        
        # 如果最近表现持续跑输基准
        if recent_excess < -0.02:  # 跑输2%
            alert = RiskAlert(
                alert_type=AlertType.STRATEGY_FAILURE,
                risk_level=RiskLevel.HIGH if recent_excess < -0.05 else RiskLevel.MEDIUM,
                ts_code="",
                message=f"策略表现不佳: 近{window}日跑输基准 {recent_excess:.2%}",
                current_value=recent_excess,
                threshold=-0.02,
                timestamp=datetime.now()
            )
            alerts.append(alert)
        
        return alerts
    
    def comprehensive_risk_check(self, 
                                positions: List[PositionInfo],
                                portfolio_values: pd.Series,
                                returns: pd.Series,
                                total_assets: float,
                                sector_mapping: Dict[str, str] = None,
                                volume_data: Dict[str, float] = None,
                                benchmark_returns: pd.Series = None) -> Dict[str, Any]:
        """综合风险检查"""
        
        all_alerts = []
        
        # 持仓限制检查
        all_alerts.extend(self.check_position_limits(positions, total_assets))
        
        # 行业集中度检查
        if sector_mapping:
            all_alerts.extend(self.check_sector_concentration(positions, sector_mapping, total_assets))
        
        # 回撤检查
        all_alerts.extend(self.check_drawdown_limits(portfolio_values))
        
        # 波动率检查
        all_alerts.extend(self.check_volatility_limits(returns))
        
        # 流动性检查
        if volume_data:
            all_alerts.extend(self.check_liquidity_risk(positions, volume_data))
        
        # 策略表现检查
        if benchmark_returns is not None:
            all_alerts.extend(self.check_strategy_performance(returns, benchmark_returns))
        
        # 按风险等级分类
        risk_summary = {
            'CRITICAL': [a for a in all_alerts if a.risk_level == RiskLevel.CRITICAL],
            'HIGH': [a for a in all_alerts if a.risk_level == RiskLevel.HIGH],
            'MEDIUM': [a for a in all_alerts if a.risk_level == RiskLevel.MEDIUM],
            'LOW': [a for a in all_alerts if a.risk_level == RiskLevel.LOW]
        }
        
        # 计算风险指标
        current_drawdown, max_drawdown = self.calculate_portfolio_drawdown(portfolio_values)
        volatility = self.calculate_portfolio_volatility(returns)
        
        return {
            'alerts': all_alerts,
            'risk_summary': risk_summary,
            'risk_metrics': {
                'current_drawdown': current_drawdown,
                'max_drawdown': max_drawdown,
                'volatility': volatility,
                'total_positions': len(positions),
                'total_alerts': len(all_alerts)
            },
            'risk_score': self._calculate_risk_score(all_alerts),
            'check_time': datetime.now().isoformat()
        }
    
    def _calculate_risk_score(self, alerts: List[RiskAlert]) -> int:
        """计算风险评分 (0-100)"""
        if not alerts:
            return 0
        
        score = 0
        for alert in alerts:
            if alert.risk_level == RiskLevel.CRITICAL:
                score += 25
            elif alert.risk_level == RiskLevel.HIGH:
                score += 15
            elif alert.risk_level == RiskLevel.MEDIUM:
                score += 8
            else:
                score += 3
        
        return min(score, 100)
    
    def update_risk_limits(self, new_limits: Dict[str, float]):
        """更新风险限制"""
        self.risk_limits.update(new_limits)
        logger.info(f"风险限制已更新: {new_limits}")

# 全局风险管理器实例
risk_manager = RiskManager()

def get_risk_manager() -> RiskManager:
    """获取风险管理器实例"""
    return risk_manager
