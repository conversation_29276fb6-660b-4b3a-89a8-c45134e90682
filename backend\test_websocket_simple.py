"""
简单的WebSocket测试
"""
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_websocket_imports():
    """测试WebSocket相关导入"""
    print("=== 测试WebSocket导入 ===")
    
    try:
        # 测试FastAPI导入
        from fastapi import WebSocket, WebSocketDisconnect
        print("✅ FastAPI WebSocket导入成功")
        
        # 测试WebSocket连接管理器
        from app.websocket.connection_manager import ConnectionManager
        print("✅ WebSocket连接管理器导入成功")
        
        # 创建连接管理器实例
        manager = ConnectionManager()
        print("✅ 连接管理器实例创建成功")
        
        # 获取连接统计
        stats = manager.get_connection_stats()
        print(f"连接统计: {stats}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_websocket_handler():
    """测试WebSocket处理器"""
    print("\n=== 测试WebSocket处理器 ===")
    
    try:
        from app.websocket.websocket_handler import WebSocketHandler, get_websocket_handler
        print("✅ WebSocket处理器导入成功")
        
        # 获取处理器实例
        handler = get_websocket_handler()
        print("✅ WebSocket处理器实例获取成功")
        
        # 检查管理器
        if hasattr(handler, 'manager'):
            stats = handler.manager.get_connection_stats()
            print(f"✅ 连接统计获取成功: {stats}")
        else:
            print("⚠️  处理器没有manager属性")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n=== 测试依赖包 ===")
    
    dependencies = [
        ('fastapi', 'FastAPI'),
        ('websockets', 'WebSockets'),
        ('uvicorn', 'Uvicorn'),
        ('starlette', 'Starlette'),
        ('pydantic', 'Pydantic')
    ]
    
    results = []
    for package, name in dependencies:
        try:
            __import__(package)
            print(f"✅ {name} 已安装")
            results.append(True)
        except ImportError:
            print(f"❌ {name} 未安装")
            results.append(False)
    
    return all(results)

def main():
    """主测试函数"""
    print("🚀 开始WebSocket简单测试")
    print("=" * 40)
    
    tests = [
        ("依赖包检查", test_dependencies),
        ("WebSocket导入", test_websocket_imports),
        ("WebSocket处理器", test_websocket_handler)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status} {test_name} 测试完成\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
            results.append((test_name, False))
    
    # 汇总结果
    print("=" * 40)
    print("📊 WebSocket测试结果:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("\n🎉 WebSocket功能完全正常！")
    elif success_count >= 2:
        print("\n✅ WebSocket基本功能正常")
    else:
        print("\n⚠️  WebSocket功能需要检查")

if __name__ == "__main__":
    main()
