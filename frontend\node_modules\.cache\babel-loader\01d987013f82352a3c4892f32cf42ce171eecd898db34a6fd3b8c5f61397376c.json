{"ast": null, "code": "export function create(x, y) {\n  if (x == null) {\n    x = 0;\n  }\n  if (y == null) {\n    y = 0;\n  }\n  return [x, y];\n}\nexport function copy(out, v) {\n  out[0] = v[0];\n  out[1] = v[1];\n  return out;\n}\nexport function clone(v) {\n  return [v[0], v[1]];\n}\nexport function set(out, a, b) {\n  out[0] = a;\n  out[1] = b;\n  return out;\n}\nexport function add(out, v1, v2) {\n  out[0] = v1[0] + v2[0];\n  out[1] = v1[1] + v2[1];\n  return out;\n}\nexport function scaleAndAdd(out, v1, v2, a) {\n  out[0] = v1[0] + v2[0] * a;\n  out[1] = v1[1] + v2[1] * a;\n  return out;\n}\nexport function sub(out, v1, v2) {\n  out[0] = v1[0] - v2[0];\n  out[1] = v1[1] - v2[1];\n  return out;\n}\nexport function len(v) {\n  return Math.sqrt(lenSquare(v));\n}\nexport var length = len;\nexport function lenSquare(v) {\n  return v[0] * v[0] + v[1] * v[1];\n}\nexport var lengthSquare = lenSquare;\nexport function mul(out, v1, v2) {\n  out[0] = v1[0] * v2[0];\n  out[1] = v1[1] * v2[1];\n  return out;\n}\nexport function div(out, v1, v2) {\n  out[0] = v1[0] / v2[0];\n  out[1] = v1[1] / v2[1];\n  return out;\n}\nexport function dot(v1, v2) {\n  return v1[0] * v2[0] + v1[1] * v2[1];\n}\nexport function scale(out, v, s) {\n  out[0] = v[0] * s;\n  out[1] = v[1] * s;\n  return out;\n}\nexport function normalize(out, v) {\n  var d = len(v);\n  if (d === 0) {\n    out[0] = 0;\n    out[1] = 0;\n  } else {\n    out[0] = v[0] / d;\n    out[1] = v[1] / d;\n  }\n  return out;\n}\nexport function distance(v1, v2) {\n  return Math.sqrt((v1[0] - v2[0]) * (v1[0] - v2[0]) + (v1[1] - v2[1]) * (v1[1] - v2[1]));\n}\nexport var dist = distance;\nexport function distanceSquare(v1, v2) {\n  return (v1[0] - v2[0]) * (v1[0] - v2[0]) + (v1[1] - v2[1]) * (v1[1] - v2[1]);\n}\nexport var distSquare = distanceSquare;\nexport function negate(out, v) {\n  out[0] = -v[0];\n  out[1] = -v[1];\n  return out;\n}\nexport function lerp(out, v1, v2, t) {\n  out[0] = v1[0] + t * (v2[0] - v1[0]);\n  out[1] = v1[1] + t * (v2[1] - v1[1]);\n  return out;\n}\nexport function applyTransform(out, v, m) {\n  var x = v[0];\n  var y = v[1];\n  out[0] = m[0] * x + m[2] * y + m[4];\n  out[1] = m[1] * x + m[3] * y + m[5];\n  return out;\n}\nexport function min(out, v1, v2) {\n  out[0] = Math.min(v1[0], v2[0]);\n  out[1] = Math.min(v1[1], v2[1]);\n  return out;\n}\nexport function max(out, v1, v2) {\n  out[0] = Math.max(v1[0], v2[0]);\n  out[1] = Math.max(v1[1], v2[1]);\n  return out;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}