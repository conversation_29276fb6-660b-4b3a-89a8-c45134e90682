"""
DeepSeek API客户端
"""
import requests
import json
import time
from typing import Dict, List, Optional, Any
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

class DeepSeekClient:
    """DeepSeek API客户端"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or settings.DEEPSEEK_API_KEY
        self.base_url = settings.DEEPSEEK_BASE_URL
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        })
        
        # API调用统计
        self.call_count = 0
        self.total_tokens = 0
        self.last_call_time = 0
    
    def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """发送API请求"""
        url = f"{self.base_url}/{endpoint}"
        
        try:
            # 记录调用时间（用于频率控制）
            current_time = time.time()
            if self.last_call_time > 0:
                time_diff = current_time - self.last_call_time
                if time_diff < 1:  # 控制调用频率，至少间隔1秒
                    time.sleep(1 - time_diff)
            
            self.last_call_time = time.time()
            
            response = self.session.post(url, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            # 更新统计信息
            self.call_count += 1
            if 'usage' in result:
                self.total_tokens += result['usage'].get('total_tokens', 0)
            
            logger.debug(f"DeepSeek API调用成功，累计调用: {self.call_count} 次")
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"DeepSeek API请求失败: {e}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"DeepSeek API响应解析失败: {e}")
            raise
    
    def chat_completion(self, messages: List[Dict[str, str]], model: str = "deepseek-chat") -> Dict[str, Any]:
        """聊天完成接口"""
        data = {
            "model": model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        return self._make_request("chat/completions", data)
    
    def analyze_market_sentiment(self, news_text: str) -> Dict[str, Any]:
        """分析市场情绪"""
        messages = [
            {
                "role": "system",
                "content": "你是一个专业的金融分析师，专门分析新闻对股市的情绪影响。请分析给定的新闻文本，返回情绪分数（-1到1之间，-1表示极度负面，1表示极度正面）、情绪标签（POSITIVE/NEGATIVE/NEUTRAL）和置信度（0到1之间）。"
            },
            {
                "role": "user",
                "content": f"请分析以下新闻的市场情绪：\n\n{news_text}\n\n请以JSON格式返回结果，包含sentiment_score、sentiment_label、confidence和analysis字段。"
            }
        ]
        
        try:
            response = self.chat_completion(messages)
            content = response['choices'][0]['message']['content']
            
            # 尝试解析JSON响应
            try:
                result = json.loads(content)
                return {
                    'sentiment_score': result.get('sentiment_score', 0.0),
                    'sentiment_label': result.get('sentiment_label', 'NEUTRAL'),
                    'confidence': result.get('confidence', 0.5),
                    'analysis': result.get('analysis', content),
                    'raw_response': content
                }
            except json.JSONDecodeError:
                # 如果不是JSON格式，返回原始内容
                return {
                    'sentiment_score': 0.0,
                    'sentiment_label': 'NEUTRAL',
                    'confidence': 0.5,
                    'analysis': content,
                    'raw_response': content
                }
                
        except Exception as e:
            logger.error(f"市场情绪分析失败: {e}")
            return {
                'sentiment_score': 0.0,
                'sentiment_label': 'NEUTRAL',
                'confidence': 0.0,
                'analysis': f"分析失败: {str(e)}",
                'error': str(e)
            }
    
    def identify_risk_events(self, news_text: str) -> Dict[str, Any]:
        """识别风险事件"""
        messages = [
            {
                "role": "system",
                "content": "你是一个专业的风险管理专家，专门识别新闻中的金融风险事件。请分析给定的新闻文本，识别可能的风险因素，评估风险等级（LOW/MEDIUM/HIGH），并提供风险说明。"
            },
            {
                "role": "user",
                "content": f"请分析以下新闻中的风险事件：\n\n{news_text}\n\n请以JSON格式返回结果，包含risk_level、risk_factors、impact_assessment和recommendations字段。"
            }
        ]
        
        try:
            response = self.chat_completion(messages)
            content = response['choices'][0]['message']['content']
            
            try:
                result = json.loads(content)
                return {
                    'risk_level': result.get('risk_level', 'LOW'),
                    'risk_factors': result.get('risk_factors', []),
                    'impact_assessment': result.get('impact_assessment', ''),
                    'recommendations': result.get('recommendations', []),
                    'raw_response': content
                }
            except json.JSONDecodeError:
                return {
                    'risk_level': 'LOW',
                    'risk_factors': [],
                    'impact_assessment': content,
                    'recommendations': [],
                    'raw_response': content
                }
                
        except Exception as e:
            logger.error(f"风险事件识别失败: {e}")
            return {
                'risk_level': 'LOW',
                'risk_factors': [],
                'impact_assessment': f"分析失败: {str(e)}",
                'recommendations': [],
                'error': str(e)
            }
    
    def generate_daily_report(self, market_data: Dict[str, Any]) -> str:
        """生成每日复盘报告"""
        messages = [
            {
                "role": "system",
                "content": "你是一个专业的股市分析师，负责撰写每日市场复盘报告。请根据提供的市场数据，生成一份简洁明了的复盘报告。"
            },
            {
                "role": "user",
                "content": f"请根据以下市场数据生成每日复盘报告：\n\n{json.dumps(market_data, ensure_ascii=False, indent=2)}\n\n报告应包含市场概况、重点关注、风险提示和明日展望。"
            }
        ]
        
        try:
            response = self.chat_completion(messages)
            return response['choices'][0]['message']['content']
            
        except Exception as e:
            logger.error(f"生成每日报告失败: {e}")
            return f"报告生成失败: {str(e)}"
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """获取API使用统计"""
        return {
            'call_count': self.call_count,
            'total_tokens': self.total_tokens,
            'average_tokens_per_call': self.total_tokens / max(self.call_count, 1)
        }

# 全局DeepSeek客户端实例
deepseek_client = None

def get_deepseek_client() -> Optional[DeepSeekClient]:
    """获取DeepSeek客户端实例"""
    global deepseek_client
    
    if deepseek_client is None:
        if settings.DEEPSEEK_API_KEY:
            deepseek_client = DeepSeekClient()
        else:
            logger.warning("DeepSeek API Key未配置，AI功能将不可用")
            return None
    
    return deepseek_client
