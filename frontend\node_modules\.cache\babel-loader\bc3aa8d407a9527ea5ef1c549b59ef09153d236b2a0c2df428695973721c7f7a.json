{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/* global Int32Array */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Model from '../model/Model.js';\nimport DataDiffer from './DataDiffer.js';\nimport { DefaultDataProvider } from './helper/dataProvider.js';\nimport { summarizeDimensions } from './helper/dimensionHelper.js';\nimport SeriesDimensionDefine from './SeriesDimensionDefine.js';\nimport { SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ORIGINAL } from '../util/types.js';\nimport { convertOptionIdName, isDataItemOption } from '../util/model.js';\nimport { setCommonECData } from '../util/innerStore.js';\nimport { isSourceInstance } from './Source.js';\nimport DataStore from './DataStore.js';\nimport { isSeriesDataSchema } from './helper/SeriesDataSchema.js';\nvar isObject = zrUtil.isObject;\nvar map = zrUtil.map;\nvar CtorInt32Array = typeof Int32Array === 'undefined' ? Array : Int32Array;\n// Use prefix to avoid index to be the same as otherIdList[idx],\n// which will cause weird update animation.\nvar ID_PREFIX = 'e\\0\\0';\nvar INDEX_NOT_FOUND = -1;\n// type SeriesDimensionIndex = DimensionIndex;\nvar TRANSFERABLE_PROPERTIES = ['hasItemOption', '_nameList', '_idList', '_invertedIndicesMap', '_dimSummary', 'userOutput', '_rawData', '_dimValueGetter', '_nameDimIdx', '_idDimIdx', '_nameRepeatCount'];\nvar CLONE_PROPERTIES = ['_approximateExtent'];\n// -----------------------------\n// Internal method declarations:\n// -----------------------------\nvar prepareInvertedIndex;\nvar getId;\nvar getIdNameFromStore;\nvar normalizeDimensions;\nvar transferProperties;\nvar cloneListForMapAndSample;\nvar makeIdFromName;\nvar SeriesData = /** @class */function () {\n  /**\r\n   * @param dimensionsInput.dimensions\r\n   *        For example, ['someDimName', {name: 'someDimName', type: 'someDimType'}, ...].\r\n   *        Dimensions should be concrete names like x, y, z, lng, lat, angle, radius\r\n   */\n  function SeriesData(dimensionsInput, hostModel) {\n    this.type = 'list';\n    this._dimOmitted = false;\n    this._nameList = [];\n    this._idList = [];\n    // Models of data option is stored sparse for optimizing memory cost\n    // Never used yet (not used yet).\n    // private _optionModels: Model[] = [];\n    // Global visual properties after visual coding\n    this._visual = {};\n    // Global layout properties.\n    this._layout = {};\n    // Item visual properties after visual coding\n    this._itemVisuals = [];\n    // Item layout properties after layout\n    this._itemLayouts = [];\n    // Graphic elements\n    this._graphicEls = [];\n    // key: dim, value: extent\n    this._approximateExtent = {};\n    this._calculationInfo = {};\n    // Having detected that there is data item is non primitive type\n    // (in type `OptionDataItemObject`).\n    // Like `data: [ { value: xx, itemStyle: {...} }, ...]`\n    // At present it only happen in `SOURCE_FORMAT_ORIGINAL`.\n    this.hasItemOption = false;\n    // Methods that create a new list based on this list should be listed here.\n    // Notice that those method should `RETURN` the new list.\n    this.TRANSFERABLE_METHODS = ['cloneShallow', 'downSample', 'minmaxDownSample', 'lttbDownSample', 'map'];\n    // Methods that change indices of this list should be listed here.\n    this.CHANGABLE_METHODS = ['filterSelf', 'selectRange'];\n    this.DOWNSAMPLE_METHODS = ['downSample', 'minmaxDownSample', 'lttbDownSample'];\n    var dimensions;\n    var assignStoreDimIdx = false;\n    if (isSeriesDataSchema(dimensionsInput)) {\n      dimensions = dimensionsInput.dimensions;\n      this._dimOmitted = dimensionsInput.isDimensionOmitted();\n      this._schema = dimensionsInput;\n    } else {\n      assignStoreDimIdx = true;\n      dimensions = dimensionsInput;\n    }\n    dimensions = dimensions || ['x', 'y'];\n    var dimensionInfos = {};\n    var dimensionNames = [];\n    var invertedIndicesMap = {};\n    var needsHasOwn = false;\n    var emptyObj = {};\n    for (var i = 0; i < dimensions.length; i++) {\n      // Use the original dimensions[i], where other flag props may exists.\n      var dimInfoInput = dimensions[i];\n      var dimensionInfo = zrUtil.isString(dimInfoInput) ? new SeriesDimensionDefine({\n        name: dimInfoInput\n      }) : !(dimInfoInput instanceof SeriesDimensionDefine) ? new SeriesDimensionDefine(dimInfoInput) : dimInfoInput;\n      var dimensionName = dimensionInfo.name;\n      dimensionInfo.type = dimensionInfo.type || 'float';\n      if (!dimensionInfo.coordDim) {\n        dimensionInfo.coordDim = dimensionName;\n        dimensionInfo.coordDimIndex = 0;\n      }\n      var otherDims = dimensionInfo.otherDims = dimensionInfo.otherDims || {};\n      dimensionNames.push(dimensionName);\n      dimensionInfos[dimensionName] = dimensionInfo;\n      if (emptyObj[dimensionName] != null) {\n        needsHasOwn = true;\n      }\n      if (dimensionInfo.createInvertedIndices) {\n        invertedIndicesMap[dimensionName] = [];\n      }\n      if (otherDims.itemName === 0) {\n        this._nameDimIdx = i;\n      }\n      if (otherDims.itemId === 0) {\n        this._idDimIdx = i;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        zrUtil.assert(assignStoreDimIdx || dimensionInfo.storeDimIndex >= 0);\n      }\n      if (assignStoreDimIdx) {\n        dimensionInfo.storeDimIndex = i;\n      }\n    }\n    this.dimensions = dimensionNames;\n    this._dimInfos = dimensionInfos;\n    this._initGetDimensionInfo(needsHasOwn);\n    this.hostModel = hostModel;\n    this._invertedIndicesMap = invertedIndicesMap;\n    if (this._dimOmitted) {\n      var dimIdxToName_1 = this._dimIdxToName = zrUtil.createHashMap();\n      zrUtil.each(dimensionNames, function (dimName) {\n        dimIdxToName_1.set(dimensionInfos[dimName].storeDimIndex, dimName);\n      });\n    }\n  }\n  /**\r\n   *\r\n   * Get concrete dimension name by dimension name or dimension index.\r\n   * If input a dimension name, do not validate whether the dimension name exits.\r\n   *\r\n   * @caution\r\n   * @param dim Must make sure the dimension is `SeriesDimensionLoose`.\r\n   * Because only those dimensions will have auto-generated dimension names if not\r\n   * have a user-specified name, and other dimensions will get a return of null/undefined.\r\n   *\r\n   * @notice Because of this reason, should better use `getDimensionIndex` instead, for examples:\r\n   * ```js\r\n   * const val = data.getStore().get(data.getDimensionIndex(dim), dataIdx);\r\n   * ```\r\n   *\r\n   * @return Concrete dim name.\r\n   */\n  SeriesData.prototype.getDimension = function (dim) {\n    var dimIdx = this._recognizeDimIndex(dim);\n    if (dimIdx == null) {\n      return dim;\n    }\n    dimIdx = dim;\n    if (!this._dimOmitted) {\n      return this.dimensions[dimIdx];\n    }\n    // Retrieve from series dimension definition because it probably contains\n    // generated dimension name (like 'x', 'y').\n    var dimName = this._dimIdxToName.get(dimIdx);\n    if (dimName != null) {\n      return dimName;\n    }\n    var sourceDimDef = this._schema.getSourceDimension(dimIdx);\n    if (sourceDimDef) {\n      return sourceDimDef.name;\n    }\n  };\n  /**\r\n   * Get dimension index in data store. Return -1 if not found.\r\n   * Can be used to index value from getRawValue.\r\n   */\n  SeriesData.prototype.getDimensionIndex = function (dim) {\n    var dimIdx = this._recognizeDimIndex(dim);\n    if (dimIdx != null) {\n      return dimIdx;\n    }\n    if (dim == null) {\n      return -1;\n    }\n    var dimInfo = this._getDimInfo(dim);\n    return dimInfo ? dimInfo.storeDimIndex : this._dimOmitted ? this._schema.getSourceDimensionIndex(dim) : -1;\n  };\n  /**\r\n   * The meanings of the input parameter `dim`:\r\n   *\r\n   * + If dim is a number (e.g., `1`), it means the index of the dimension.\r\n   *   For example, `getDimension(0)` will return 'x' or 'lng' or 'radius'.\r\n   * + If dim is a number-like string (e.g., `\"1\"`):\r\n   *     + If there is the same concrete dim name defined in `series.dimensions` or `dataset.dimensions`,\r\n   *        it means that concrete name.\r\n   *     + If not, it will be converted to a number, which means the index of the dimension.\r\n   *        (why? because of the backward compatibility. We have been tolerating number-like string in\r\n   *        dimension setting, although now it seems that it is not a good idea.)\r\n   *     For example, `visualMap[i].dimension: \"1\"` is the same meaning as `visualMap[i].dimension: 1`,\r\n   *     if no dimension name is defined as `\"1\"`.\r\n   * + If dim is a not-number-like string, it means the concrete dim name.\r\n   *   For example, it can be be default name `\"x\"`, `\"y\"`, `\"z\"`, `\"lng\"`, `\"lat\"`, `\"angle\"`, `\"radius\"`,\r\n   *   or customized in `dimensions` property of option like `\"age\"`.\r\n   *\r\n   * @return recognized `DimensionIndex`. Otherwise return null/undefined (means that dim is `DimensionName`).\r\n   */\n  SeriesData.prototype._recognizeDimIndex = function (dim) {\n    if (zrUtil.isNumber(dim)\n    // If being a number-like string but not being defined as a dimension name.\n    || dim != null && !isNaN(dim) && !this._getDimInfo(dim) && (!this._dimOmitted || this._schema.getSourceDimensionIndex(dim) < 0)) {\n      return +dim;\n    }\n  };\n  SeriesData.prototype._getStoreDimIndex = function (dim) {\n    var dimIdx = this.getDimensionIndex(dim);\n    if (process.env.NODE_ENV !== 'production') {\n      if (dimIdx == null) {\n        throw new Error('Unknown dimension ' + dim);\n      }\n    }\n    return dimIdx;\n  };\n  /**\r\n   * Get type and calculation info of particular dimension\r\n   * @param dim\r\n   *        Dimension can be concrete names like x, y, z, lng, lat, angle, radius\r\n   *        Or a ordinal number. For example getDimensionInfo(0) will return 'x' or 'lng' or 'radius'\r\n   */\n  SeriesData.prototype.getDimensionInfo = function (dim) {\n    // Do not clone, because there may be categories in dimInfo.\n    return this._getDimInfo(this.getDimension(dim));\n  };\n  SeriesData.prototype._initGetDimensionInfo = function (needsHasOwn) {\n    var dimensionInfos = this._dimInfos;\n    this._getDimInfo = needsHasOwn ? function (dimName) {\n      return dimensionInfos.hasOwnProperty(dimName) ? dimensionInfos[dimName] : undefined;\n    } : function (dimName) {\n      return dimensionInfos[dimName];\n    };\n  };\n  /**\r\n   * concrete dimension name list on coord.\r\n   */\n  SeriesData.prototype.getDimensionsOnCoord = function () {\n    return this._dimSummary.dataDimsOnCoord.slice();\n  };\n  SeriesData.prototype.mapDimension = function (coordDim, idx) {\n    var dimensionsSummary = this._dimSummary;\n    if (idx == null) {\n      return dimensionsSummary.encodeFirstDimNotExtra[coordDim];\n    }\n    var dims = dimensionsSummary.encode[coordDim];\n    return dims ? dims[idx] : null;\n  };\n  SeriesData.prototype.mapDimensionsAll = function (coordDim) {\n    var dimensionsSummary = this._dimSummary;\n    var dims = dimensionsSummary.encode[coordDim];\n    return (dims || []).slice();\n  };\n  SeriesData.prototype.getStore = function () {\n    return this._store;\n  };\n  /**\r\n   * Initialize from data\r\n   * @param data source or data or data store.\r\n   * @param nameList The name of a datum is used on data diff and\r\n   *        default label/tooltip.\r\n   *        A name can be specified in encode.itemName,\r\n   *        or dataItem.name (only for series option data),\r\n   *        or provided in nameList from outside.\r\n   */\n  SeriesData.prototype.initData = function (data, nameList, dimValueGetter) {\n    var _this = this;\n    var store;\n    if (data instanceof DataStore) {\n      store = data;\n    }\n    if (!store) {\n      var dimensions = this.dimensions;\n      var provider = isSourceInstance(data) || zrUtil.isArrayLike(data) ? new DefaultDataProvider(data, dimensions.length) : data;\n      store = new DataStore();\n      var dimensionInfos = map(dimensions, function (dimName) {\n        return {\n          type: _this._dimInfos[dimName].type,\n          property: dimName\n        };\n      });\n      store.initData(provider, dimensionInfos, dimValueGetter);\n    }\n    this._store = store;\n    // Reset\n    this._nameList = (nameList || []).slice();\n    this._idList = [];\n    this._nameRepeatCount = {};\n    this._doInit(0, store.count());\n    // Cache summary info for fast visit. See \"dimensionHelper\".\n    // Needs to be initialized after store is prepared.\n    this._dimSummary = summarizeDimensions(this, this._schema);\n    this.userOutput = this._dimSummary.userOutput;\n  };\n  /**\r\n   * Caution: Can be only called on raw data (before `this._indices` created).\r\n   */\n  SeriesData.prototype.appendData = function (data) {\n    var range = this._store.appendData(data);\n    this._doInit(range[0], range[1]);\n  };\n  /**\r\n   * Caution: Can be only called on raw data (before `this._indices` created).\r\n   * This method does not modify `rawData` (`dataProvider`), but only\r\n   * add values to store.\r\n   *\r\n   * The final count will be increased by `Math.max(values.length, names.length)`.\r\n   *\r\n   * @param values That is the SourceType: 'arrayRows', like\r\n   *        [\r\n   *            [12, 33, 44],\r\n   *            [NaN, 43, 1],\r\n   *            ['-', 'asdf', 0]\r\n   *        ]\r\n   *        Each item is exactly corresponding to a dimension.\r\n   */\n  SeriesData.prototype.appendValues = function (values, names) {\n    var _a = this._store.appendValues(values, names && names.length),\n      start = _a.start,\n      end = _a.end;\n    var shouldMakeIdFromName = this._shouldMakeIdFromName();\n    this._updateOrdinalMeta();\n    if (names) {\n      for (var idx = start; idx < end; idx++) {\n        var sourceIdx = idx - start;\n        this._nameList[idx] = names[sourceIdx];\n        if (shouldMakeIdFromName) {\n          makeIdFromName(this, idx);\n        }\n      }\n    }\n  };\n  SeriesData.prototype._updateOrdinalMeta = function () {\n    var store = this._store;\n    var dimensions = this.dimensions;\n    for (var i = 0; i < dimensions.length; i++) {\n      var dimInfo = this._dimInfos[dimensions[i]];\n      if (dimInfo.ordinalMeta) {\n        store.collectOrdinalMeta(dimInfo.storeDimIndex, dimInfo.ordinalMeta);\n      }\n    }\n  };\n  SeriesData.prototype._shouldMakeIdFromName = function () {\n    var provider = this._store.getProvider();\n    return this._idDimIdx == null && provider.getSource().sourceFormat !== SOURCE_FORMAT_TYPED_ARRAY && !provider.fillStorage;\n  };\n  SeriesData.prototype._doInit = function (start, end) {\n    if (start >= end) {\n      return;\n    }\n    var store = this._store;\n    var provider = store.getProvider();\n    this._updateOrdinalMeta();\n    var nameList = this._nameList;\n    var idList = this._idList;\n    var sourceFormat = provider.getSource().sourceFormat;\n    var isFormatOriginal = sourceFormat === SOURCE_FORMAT_ORIGINAL;\n    // Each data item is value\n    // [1, 2]\n    // 2\n    // Bar chart, line chart which uses category axis\n    // only gives the 'y' value. 'x' value is the indices of category\n    // Use a tempValue to normalize the value to be a (x, y) value\n    // If dataItem is {name: ...} or {id: ...}, it has highest priority.\n    // This kind of ids and names are always stored `_nameList` and `_idList`.\n    if (isFormatOriginal && !provider.pure) {\n      var sharedDataItem = [];\n      for (var idx = start; idx < end; idx++) {\n        // NOTICE: Try not to write things into dataItem\n        var dataItem = provider.getItem(idx, sharedDataItem);\n        if (!this.hasItemOption && isDataItemOption(dataItem)) {\n          this.hasItemOption = true;\n        }\n        if (dataItem) {\n          var itemName = dataItem.name;\n          if (nameList[idx] == null && itemName != null) {\n            nameList[idx] = convertOptionIdName(itemName, null);\n          }\n          var itemId = dataItem.id;\n          if (idList[idx] == null && itemId != null) {\n            idList[idx] = convertOptionIdName(itemId, null);\n          }\n        }\n      }\n    }\n    if (this._shouldMakeIdFromName()) {\n      for (var idx = start; idx < end; idx++) {\n        makeIdFromName(this, idx);\n      }\n    }\n    prepareInvertedIndex(this);\n  };\n  /**\r\n   * PENDING: In fact currently this function is only used to short-circuit\r\n   * the calling of `scale.unionExtentFromData` when data have been filtered by modules\r\n   * like \"dataZoom\". `scale.unionExtentFromData` is used to calculate data extent for series on\r\n   * an axis, but if a \"axis related data filter module\" is used, the extent of the axis have\r\n   * been fixed and no need to calling `scale.unionExtentFromData` actually.\r\n   * But if we add \"custom data filter\" in future, which is not \"axis related\", this method may\r\n   * be still needed.\r\n   *\r\n   * Optimize for the scenario that data is filtered by a given extent.\r\n   * Consider that if data amount is more than hundreds of thousand,\r\n   * extent calculation will cost more than 10ms and the cache will\r\n   * be erased because of the filtering.\r\n   */\n  SeriesData.prototype.getApproximateExtent = function (dim) {\n    return this._approximateExtent[dim] || this._store.getDataExtent(this._getStoreDimIndex(dim));\n  };\n  /**\r\n   * Calculate extent on a filtered data might be time consuming.\r\n   * Approximate extent is only used for: calculate extent of filtered data outside.\r\n   */\n  SeriesData.prototype.setApproximateExtent = function (extent, dim) {\n    dim = this.getDimension(dim);\n    this._approximateExtent[dim] = extent.slice();\n  };\n  SeriesData.prototype.getCalculationInfo = function (key) {\n    return this._calculationInfo[key];\n  };\n  SeriesData.prototype.setCalculationInfo = function (key, value) {\n    isObject(key) ? zrUtil.extend(this._calculationInfo, key) : this._calculationInfo[key] = value;\n  };\n  /**\r\n   * @return Never be null/undefined. `number` will be converted to string. Because:\r\n   * In most cases, name is used in display, where returning a string is more convenient.\r\n   * In other cases, name is used in query (see `indexOfName`), where we can keep the\r\n   * rule that name `2` equals to name `'2'`.\r\n   */\n  SeriesData.prototype.getName = function (idx) {\n    var rawIndex = this.getRawIndex(idx);\n    var name = this._nameList[rawIndex];\n    if (name == null && this._nameDimIdx != null) {\n      name = getIdNameFromStore(this, this._nameDimIdx, rawIndex);\n    }\n    if (name == null) {\n      name = '';\n    }\n    return name;\n  };\n  SeriesData.prototype._getCategory = function (dimIdx, idx) {\n    var ordinal = this._store.get(dimIdx, idx);\n    var ordinalMeta = this._store.getOrdinalMeta(dimIdx);\n    if (ordinalMeta) {\n      return ordinalMeta.categories[ordinal];\n    }\n    return ordinal;\n  };\n  /**\r\n   * @return Never null/undefined. `number` will be converted to string. Because:\r\n   * In all cases having encountered at present, id is used in making diff comparison, which\r\n   * are usually based on hash map. We can keep the rule that the internal id are always string\r\n   * (treat `2` is the same as `'2'`) to make the related logic simple.\r\n   */\n  SeriesData.prototype.getId = function (idx) {\n    return getId(this, this.getRawIndex(idx));\n  };\n  SeriesData.prototype.count = function () {\n    return this._store.count();\n  };\n  /**\r\n   * Get value. Return NaN if idx is out of range.\r\n   *\r\n   * @notice Should better to use `data.getStore().get(dimIndex, dataIdx)` instead.\r\n   */\n  SeriesData.prototype.get = function (dim, idx) {\n    var store = this._store;\n    var dimInfo = this._dimInfos[dim];\n    if (dimInfo) {\n      return store.get(dimInfo.storeDimIndex, idx);\n    }\n  };\n  /**\r\n   * @notice Should better to use `data.getStore().getByRawIndex(dimIndex, dataIdx)` instead.\r\n   */\n  SeriesData.prototype.getByRawIndex = function (dim, rawIdx) {\n    var store = this._store;\n    var dimInfo = this._dimInfos[dim];\n    if (dimInfo) {\n      return store.getByRawIndex(dimInfo.storeDimIndex, rawIdx);\n    }\n  };\n  SeriesData.prototype.getIndices = function () {\n    return this._store.getIndices();\n  };\n  SeriesData.prototype.getDataExtent = function (dim) {\n    return this._store.getDataExtent(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getSum = function (dim) {\n    return this._store.getSum(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getMedian = function (dim) {\n    return this._store.getMedian(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getValues = function (dimensions, idx) {\n    var _this = this;\n    var store = this._store;\n    return zrUtil.isArray(dimensions) ? store.getValues(map(dimensions, function (dim) {\n      return _this._getStoreDimIndex(dim);\n    }), idx) : store.getValues(dimensions);\n  };\n  /**\r\n   * If value is NaN. Including '-'\r\n   * Only check the coord dimensions.\r\n   */\n  SeriesData.prototype.hasValue = function (idx) {\n    var dataDimIndicesOnCoord = this._dimSummary.dataDimIndicesOnCoord;\n    for (var i = 0, len = dataDimIndicesOnCoord.length; i < len; i++) {\n      // Ordinal type originally can be string or number.\n      // But when an ordinal type is used on coord, it can\n      // not be string but only number. So we can also use isNaN.\n      if (isNaN(this._store.get(dataDimIndicesOnCoord[i], idx))) {\n        return false;\n      }\n    }\n    return true;\n  };\n  /**\r\n   * Retrieve the index with given name\r\n   */\n  SeriesData.prototype.indexOfName = function (name) {\n    for (var i = 0, len = this._store.count(); i < len; i++) {\n      if (this.getName(i) === name) {\n        return i;\n      }\n    }\n    return -1;\n  };\n  SeriesData.prototype.getRawIndex = function (idx) {\n    return this._store.getRawIndex(idx);\n  };\n  SeriesData.prototype.indexOfRawIndex = function (rawIndex) {\n    return this._store.indexOfRawIndex(rawIndex);\n  };\n  /**\r\n   * Only support the dimension which inverted index created.\r\n   * Do not support other cases until required.\r\n   * @param dim concrete dim\r\n   * @param value ordinal index\r\n   * @return rawIndex\r\n   */\n  SeriesData.prototype.rawIndexOf = function (dim, value) {\n    var invertedIndices = dim && this._invertedIndicesMap[dim];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!invertedIndices) {\n        throw new Error('Do not supported yet');\n      }\n    }\n    var rawIndex = invertedIndices && invertedIndices[value];\n    if (rawIndex == null || isNaN(rawIndex)) {\n      return INDEX_NOT_FOUND;\n    }\n    return rawIndex;\n  };\n  /**\r\n   * Retrieve the index of nearest value\r\n   * @param dim\r\n   * @param value\r\n   * @param [maxDistance=Infinity]\r\n   * @return If and only if multiple indices has\r\n   *         the same value, they are put to the result.\r\n   */\n  SeriesData.prototype.indicesOfNearest = function (dim, value, maxDistance) {\n    return this._store.indicesOfNearest(this._getStoreDimIndex(dim), value, maxDistance);\n  };\n  SeriesData.prototype.each = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    this._store.each(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n  };\n  SeriesData.prototype.filterSelf = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    this._store = this._store.filter(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n    return this;\n  };\n  /**\r\n   * Select data in range. (For optimization of filter)\r\n   * (Manually inline code, support 5 million data filtering in data zoom.)\r\n   */\n  SeriesData.prototype.selectRange = function (range) {\n    'use strict';\n\n    var _this = this;\n    var innerRange = {};\n    var dims = zrUtil.keys(range);\n    var dimIndices = [];\n    zrUtil.each(dims, function (dim) {\n      var dimIdx = _this._getStoreDimIndex(dim);\n      innerRange[dimIdx] = range[dim];\n      dimIndices.push(dimIdx);\n    });\n    this._store = this._store.selectRange(innerRange);\n    return this;\n  };\n  /* eslint-enable max-len */\n  SeriesData.prototype.mapArray = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    ctx = ctx || this;\n    var result = [];\n    this.each(dims, function () {\n      result.push(cb && cb.apply(this, arguments));\n    }, ctx);\n    return result;\n  };\n  SeriesData.prototype.map = function (dims, cb, ctx, ctxCompat) {\n    'use strict';\n\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || ctxCompat || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.map(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n    return list;\n  };\n  SeriesData.prototype.modify = function (dims, cb, ctx, ctxCompat) {\n    var _this = this;\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || ctxCompat || this;\n    if (process.env.NODE_ENV !== 'production') {\n      zrUtil.each(normalizeDimensions(dims), function (dim) {\n        var dimInfo = _this.getDimensionInfo(dim);\n        if (!dimInfo.isCalculationCoord) {\n          console.error('Danger: only stack dimension can be modified');\n        }\n      });\n    }\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    // If do shallow clone here, if there are too many stacked series,\n    // it still cost lots of memory, because `_store.dimensions` are not shared.\n    // We should consider there probably be shallow clone happen in each series\n    // in consequent filter/map.\n    this._store.modify(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n  };\n  /**\r\n   * Large data down sampling on given dimension\r\n   * @param sampleIndex Sample index for name and id\r\n   */\n  SeriesData.prototype.downSample = function (dimension, rate, sampleValue, sampleIndex) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.downSample(this._getStoreDimIndex(dimension), rate, sampleValue, sampleIndex);\n    return list;\n  };\n  /**\r\n   * Large data down sampling using min-max\r\n   * @param {string} valueDimension\r\n   * @param {number} rate\r\n   */\n  SeriesData.prototype.minmaxDownSample = function (valueDimension, rate) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.minmaxDownSample(this._getStoreDimIndex(valueDimension), rate);\n    return list;\n  };\n  /**\r\n   * Large data down sampling using largest-triangle-three-buckets\r\n   * @param {string} valueDimension\r\n   * @param {number} targetCount\r\n   */\n  SeriesData.prototype.lttbDownSample = function (valueDimension, rate) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.lttbDownSample(this._getStoreDimIndex(valueDimension), rate);\n    return list;\n  };\n  SeriesData.prototype.getRawDataItem = function (idx) {\n    return this._store.getRawDataItem(idx);\n  };\n  /**\r\n   * Get model of one data item.\r\n   */\n  // TODO: Type of data item\n  SeriesData.prototype.getItemModel = function (idx) {\n    var hostModel = this.hostModel;\n    var dataItem = this.getRawDataItem(idx);\n    return new Model(dataItem, hostModel, hostModel && hostModel.ecModel);\n  };\n  /**\r\n   * Create a data differ\r\n   */\n  SeriesData.prototype.diff = function (otherList) {\n    var thisList = this;\n    return new DataDiffer(otherList ? otherList.getStore().getIndices() : [], this.getStore().getIndices(), function (idx) {\n      return getId(otherList, idx);\n    }, function (idx) {\n      return getId(thisList, idx);\n    });\n  };\n  /**\r\n   * Get visual property.\r\n   */\n  SeriesData.prototype.getVisual = function (key) {\n    var visual = this._visual;\n    return visual && visual[key];\n  };\n  SeriesData.prototype.setVisual = function (kvObj, val) {\n    this._visual = this._visual || {};\n    if (isObject(kvObj)) {\n      zrUtil.extend(this._visual, kvObj);\n    } else {\n      this._visual[kvObj] = val;\n    }\n  };\n  /**\r\n   * Get visual property of single data item\r\n   */\n  // eslint-disable-next-line\n  SeriesData.prototype.getItemVisual = function (idx, key) {\n    var itemVisual = this._itemVisuals[idx];\n    var val = itemVisual && itemVisual[key];\n    if (val == null) {\n      // Use global visual property\n      return this.getVisual(key);\n    }\n    return val;\n  };\n  /**\r\n   * If exists visual property of single data item\r\n   */\n  SeriesData.prototype.hasItemVisual = function () {\n    return this._itemVisuals.length > 0;\n  };\n  /**\r\n   * Make sure itemVisual property is unique\r\n   */\n  // TODO: use key to save visual to reduce memory.\n  SeriesData.prototype.ensureUniqueItemVisual = function (idx, key) {\n    var itemVisuals = this._itemVisuals;\n    var itemVisual = itemVisuals[idx];\n    if (!itemVisual) {\n      itemVisual = itemVisuals[idx] = {};\n    }\n    var val = itemVisual[key];\n    if (val == null) {\n      val = this.getVisual(key);\n      // TODO Performance?\n      if (zrUtil.isArray(val)) {\n        val = val.slice();\n      } else if (isObject(val)) {\n        val = zrUtil.extend({}, val);\n      }\n      itemVisual[key] = val;\n    }\n    return val;\n  };\n  // eslint-disable-next-line\n  SeriesData.prototype.setItemVisual = function (idx, key, value) {\n    var itemVisual = this._itemVisuals[idx] || {};\n    this._itemVisuals[idx] = itemVisual;\n    if (isObject(key)) {\n      zrUtil.extend(itemVisual, key);\n    } else {\n      itemVisual[key] = value;\n    }\n  };\n  /**\r\n   * Clear itemVisuals and list visual.\r\n   */\n  SeriesData.prototype.clearAllVisual = function () {\n    this._visual = {};\n    this._itemVisuals = [];\n  };\n  SeriesData.prototype.setLayout = function (key, val) {\n    isObject(key) ? zrUtil.extend(this._layout, key) : this._layout[key] = val;\n  };\n  /**\r\n   * Get layout property.\r\n   */\n  SeriesData.prototype.getLayout = function (key) {\n    return this._layout[key];\n  };\n  /**\r\n   * Get layout of single data item\r\n   */\n  SeriesData.prototype.getItemLayout = function (idx) {\n    return this._itemLayouts[idx];\n  };\n  /**\r\n   * Set layout of single data item\r\n   */\n  SeriesData.prototype.setItemLayout = function (idx, layout, merge) {\n    this._itemLayouts[idx] = merge ? zrUtil.extend(this._itemLayouts[idx] || {}, layout) : layout;\n  };\n  /**\r\n   * Clear all layout of single data item\r\n   */\n  SeriesData.prototype.clearItemLayouts = function () {\n    this._itemLayouts.length = 0;\n  };\n  /**\r\n   * Set graphic element relative to data. It can be set as null\r\n   */\n  SeriesData.prototype.setItemGraphicEl = function (idx, el) {\n    var seriesIndex = this.hostModel && this.hostModel.seriesIndex;\n    setCommonECData(seriesIndex, this.dataType, idx, el);\n    this._graphicEls[idx] = el;\n  };\n  SeriesData.prototype.getItemGraphicEl = function (idx) {\n    return this._graphicEls[idx];\n  };\n  SeriesData.prototype.eachItemGraphicEl = function (cb, context) {\n    zrUtil.each(this._graphicEls, function (el, idx) {\n      if (el) {\n        cb && cb.call(context, el, idx);\n      }\n    });\n  };\n  /**\r\n   * Shallow clone a new list except visual and layout properties, and graph elements.\r\n   * New list only change the indices.\r\n   */\n  SeriesData.prototype.cloneShallow = function (list) {\n    if (!list) {\n      list = new SeriesData(this._schema ? this._schema : map(this.dimensions, this._getDimInfo, this), this.hostModel);\n    }\n    transferProperties(list, this);\n    list._store = this._store;\n    return list;\n  };\n  /**\r\n   * Wrap some method to add more feature\r\n   */\n  SeriesData.prototype.wrapMethod = function (methodName, injectFunction) {\n    var originalMethod = this[methodName];\n    if (!zrUtil.isFunction(originalMethod)) {\n      return;\n    }\n    this.__wrappedMethods = this.__wrappedMethods || [];\n    this.__wrappedMethods.push(methodName);\n    this[methodName] = function () {\n      var res = originalMethod.apply(this, arguments);\n      return injectFunction.apply(this, [res].concat(zrUtil.slice(arguments)));\n    };\n  };\n  // ----------------------------------------------------------\n  // A work around for internal method visiting private member.\n  // ----------------------------------------------------------\n  SeriesData.internalField = function () {\n    prepareInvertedIndex = function (data) {\n      var invertedIndicesMap = data._invertedIndicesMap;\n      zrUtil.each(invertedIndicesMap, function (invertedIndices, dim) {\n        var dimInfo = data._dimInfos[dim];\n        // Currently, only dimensions that has ordinalMeta can create inverted indices.\n        var ordinalMeta = dimInfo.ordinalMeta;\n        var store = data._store;\n        if (ordinalMeta) {\n          invertedIndices = invertedIndicesMap[dim] = new CtorInt32Array(ordinalMeta.categories.length);\n          // The default value of TypedArray is 0. To avoid miss\n          // mapping to 0, we should set it as INDEX_NOT_FOUND.\n          for (var i = 0; i < invertedIndices.length; i++) {\n            invertedIndices[i] = INDEX_NOT_FOUND;\n          }\n          for (var i = 0; i < store.count(); i++) {\n            // Only support the case that all values are distinct.\n            invertedIndices[store.get(dimInfo.storeDimIndex, i)] = i;\n          }\n        }\n      });\n    };\n    getIdNameFromStore = function (data, dimIdx, idx) {\n      return convertOptionIdName(data._getCategory(dimIdx, idx), null);\n    };\n    /**\r\n     * @see the comment of `List['getId']`.\r\n     */\n    getId = function (data, rawIndex) {\n      var id = data._idList[rawIndex];\n      if (id == null && data._idDimIdx != null) {\n        id = getIdNameFromStore(data, data._idDimIdx, rawIndex);\n      }\n      if (id == null) {\n        id = ID_PREFIX + rawIndex;\n      }\n      return id;\n    };\n    normalizeDimensions = function (dimensions) {\n      if (!zrUtil.isArray(dimensions)) {\n        dimensions = dimensions != null ? [dimensions] : [];\n      }\n      return dimensions;\n    };\n    /**\r\n     * Data in excludeDimensions is copied, otherwise transferred.\r\n     */\n    cloneListForMapAndSample = function (original) {\n      var list = new SeriesData(original._schema ? original._schema : map(original.dimensions, original._getDimInfo, original), original.hostModel);\n      // FIXME If needs stackedOn, value may already been stacked\n      transferProperties(list, original);\n      return list;\n    };\n    transferProperties = function (target, source) {\n      zrUtil.each(TRANSFERABLE_PROPERTIES.concat(source.__wrappedMethods || []), function (propName) {\n        if (source.hasOwnProperty(propName)) {\n          target[propName] = source[propName];\n        }\n      });\n      target.__wrappedMethods = source.__wrappedMethods;\n      zrUtil.each(CLONE_PROPERTIES, function (propName) {\n        target[propName] = zrUtil.clone(source[propName]);\n      });\n      target._calculationInfo = zrUtil.extend({}, source._calculationInfo);\n    };\n    makeIdFromName = function (data, idx) {\n      var nameList = data._nameList;\n      var idList = data._idList;\n      var nameDimIdx = data._nameDimIdx;\n      var idDimIdx = data._idDimIdx;\n      var name = nameList[idx];\n      var id = idList[idx];\n      if (name == null && nameDimIdx != null) {\n        nameList[idx] = name = getIdNameFromStore(data, nameDimIdx, idx);\n      }\n      if (id == null && idDimIdx != null) {\n        idList[idx] = id = getIdNameFromStore(data, idDimIdx, idx);\n      }\n      if (id == null && name != null) {\n        var nameRepeatCount = data._nameRepeatCount;\n        var nmCnt = nameRepeatCount[name] = (nameRepeatCount[name] || 0) + 1;\n        id = name;\n        if (nmCnt > 1) {\n          id += '__ec__' + nmCnt;\n        }\n        idList[idx] = id;\n      }\n    };\n  }();\n  return SeriesData;\n}();\nexport default SeriesData;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}