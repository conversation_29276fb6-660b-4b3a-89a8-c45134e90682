"""
数据服务测试脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.tushare_client import get_tushare_client
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_tushare_connection():
    """测试Tushare连接"""
    print("=== 测试Tushare连接 ===")
    try:
        client = get_tushare_client()
        
        # 测试获取交易日历
        from datetime import datetime, timedelta
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        cal = client.get_trade_calendar(
            start_date.strftime('%Y%m%d'),
            end_date.strftime('%Y%m%d')
        )
        
        if not cal.empty:
            print(f"✅ Tushare连接成功，获取到 {len(cal)} 条交易日历数据")
            return True
        else:
            print("❌ 获取交易日历数据为空")
            return False
            
    except Exception as e:
        print(f"❌ Tushare连接失败: {e}")
        return False

def test_stock_basic():
    """测试股票基本信息获取"""
    print("\n=== 测试股票基本信息获取 ===")
    try:
        client = get_tushare_client()
        
        # 获取A股基本信息（限制数量）
        df = client.pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,list_date')
        
        if not df.empty:
            print(f"✅ 获取A股基本信息成功，共 {len(df)} 只股票")
            print("前5只股票信息：")
            print(df.head().to_string())
            return True
        else:
            print("❌ 未获取到A股基本信息")
            return False
            
    except Exception as e:
        print(f"❌ 获取股票基本信息失败: {e}")
        return False

def test_daily_data():
    """测试日线数据获取"""
    print("\n=== 测试日线数据获取 ===")
    try:
        client = get_tushare_client()
        
        # 测试获取平安银行的日线数据
        test_stock = '000001.SZ'
        from datetime import datetime, timedelta
        end_date = datetime.now()
        start_date = end_date - timedelta(days=10)
        
        df = client.get_daily_data(
            test_stock,
            start_date.strftime('%Y%m%d'),
            end_date.strftime('%Y%m%d')
        )
        
        if not df.empty:
            print(f"✅ 获取 {test_stock} 日线数据成功，共 {len(df)} 条记录")
            print("最新5条数据：")
            print(df.head().to_string())
            return True
        else:
            print(f"❌ 未获取到 {test_stock} 日线数据")
            return False
            
    except Exception as e:
        print(f"❌ 获取日线数据失败: {e}")
        return False

def test_basic_daily():
    """测试基本面数据获取"""
    print("\n=== 测试基本面数据获取 ===")
    try:
        client = get_tushare_client()
        
        # 测试获取平安银行的基本面数据
        test_stock = '000001.SZ'
        from datetime import datetime, timedelta
        end_date = datetime.now()
        start_date = end_date - timedelta(days=10)
        
        df = client.get_basic_daily(
            test_stock,
            start_date.strftime('%Y%m%d'),
            end_date.strftime('%Y%m%d')
        )
        
        if not df.empty:
            print(f"✅ 获取 {test_stock} 基本面数据成功，共 {len(df)} 条记录")
            print("最新5条数据：")
            print(df.head().to_string())
            return True
        else:
            print(f"❌ 未获取到 {test_stock} 基本面数据")
            return False
            
    except Exception as e:
        print(f"❌ 获取基本面数据失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始数据服务测试")
    
    tests = [
        ("Tushare连接", test_tushare_connection),
        ("股票基本信息", test_stock_basic),
        ("日线数据", test_daily_data),
        ("基本面数据", test_basic_daily),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count >= 2:
        print("🎉 数据服务基本可用！")
    else:
        print("⚠️  数据服务可能有问题，请检查网络和API权限")

if __name__ == "__main__":
    main()
