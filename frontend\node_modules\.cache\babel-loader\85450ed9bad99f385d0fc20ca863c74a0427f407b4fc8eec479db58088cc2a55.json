{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { makeInner } from '../util/model.js';\nimport { getDecalFromPalette } from '../model/mixin/palette.js';\nvar DEFAULT_OPTION = {\n  label: {\n    enabled: true\n  },\n  decal: {\n    show: false\n  }\n};\nvar inner = makeInner();\nvar decalPaletteScope = {};\nexport default function ariaVisual(ecModel, api) {\n  var ariaModel = ecModel.getModel('aria');\n  // See \"area enabled\" detection code in `GlobalModel.ts`.\n  if (!ariaModel.get('enabled')) {\n    return;\n  }\n  var defaultOption = zrUtil.clone(DEFAULT_OPTION);\n  zrUtil.merge(defaultOption.label, ecModel.getLocaleModel().get('aria'), false);\n  zrUtil.merge(ariaModel.option, defaultOption, false);\n  setDecal();\n  setLabel();\n  function setDecal() {\n    var decalModel = ariaModel.getModel('decal');\n    var useDecal = decalModel.get('show');\n    if (useDecal) {\n      // Each type of series use one scope.\n      // Pie and funnel are using different scopes.\n      var paletteScopeGroupByType_1 = zrUtil.createHashMap();\n      ecModel.eachSeries(function (seriesModel) {\n        if (seriesModel.isColorBySeries()) {\n          return;\n        }\n        var decalScope = paletteScopeGroupByType_1.get(seriesModel.type);\n        if (!decalScope) {\n          decalScope = {};\n          paletteScopeGroupByType_1.set(seriesModel.type, decalScope);\n        }\n        inner(seriesModel).scope = decalScope;\n      });\n      ecModel.eachRawSeries(function (seriesModel) {\n        if (ecModel.isSeriesFiltered(seriesModel)) {\n          return;\n        }\n        if (zrUtil.isFunction(seriesModel.enableAriaDecal)) {\n          // Let series define how to use decal palette on data\n          seriesModel.enableAriaDecal();\n          return;\n        }\n        var data = seriesModel.getData();\n        if (!seriesModel.isColorBySeries()) {\n          var dataAll_1 = seriesModel.getRawData();\n          var idxMap_1 = {};\n          var decalScope_1 = inner(seriesModel).scope;\n          data.each(function (idx) {\n            var rawIdx = data.getRawIndex(idx);\n            idxMap_1[rawIdx] = idx;\n          });\n          var dataCount_1 = dataAll_1.count();\n          dataAll_1.each(function (rawIdx) {\n            var idx = idxMap_1[rawIdx];\n            var name = dataAll_1.getName(rawIdx) || rawIdx + '';\n            var paletteDecal = getDecalFromPalette(seriesModel.ecModel, name, decalScope_1, dataCount_1);\n            var specifiedDecal = data.getItemVisual(idx, 'decal');\n            data.setItemVisual(idx, 'decal', mergeDecal(specifiedDecal, paletteDecal));\n          });\n        } else {\n          var paletteDecal = getDecalFromPalette(seriesModel.ecModel, seriesModel.name, decalPaletteScope, ecModel.getSeriesCount());\n          var specifiedDecal = data.getVisual('decal');\n          data.setVisual('decal', mergeDecal(specifiedDecal, paletteDecal));\n        }\n        function mergeDecal(specifiedDecal, paletteDecal) {\n          // Merge decal from palette to decal from itemStyle.\n          // User do not need to specify all of the decal props.\n          var resultDecal = specifiedDecal ? zrUtil.extend(zrUtil.extend({}, paletteDecal), specifiedDecal) : paletteDecal;\n          resultDecal.dirty = true;\n          return resultDecal;\n        }\n      });\n    }\n  }\n  function setLabel() {\n    var dom = api.getZr().dom;\n    // TODO: support for SSR\n    if (!dom) {\n      return;\n    }\n    var labelLocale = ecModel.getLocaleModel().get('aria');\n    var labelModel = ariaModel.getModel('label');\n    labelModel.option = zrUtil.defaults(labelModel.option, labelLocale);\n    if (!labelModel.get('enabled')) {\n      return;\n    }\n    dom.setAttribute('role', 'img');\n    if (labelModel.get('description')) {\n      dom.setAttribute('aria-label', labelModel.get('description'));\n      return;\n    }\n    var seriesCnt = ecModel.getSeriesCount();\n    var maxDataCnt = labelModel.get(['data', 'maxCount']) || 10;\n    var maxSeriesCnt = labelModel.get(['series', 'maxCount']) || 10;\n    var displaySeriesCnt = Math.min(seriesCnt, maxSeriesCnt);\n    var ariaLabel;\n    if (seriesCnt < 1) {\n      // No series, no aria label\n      return;\n    } else {\n      var title = getTitle();\n      if (title) {\n        var withTitle = labelModel.get(['general', 'withTitle']);\n        ariaLabel = replace(withTitle, {\n          title: title\n        });\n      } else {\n        ariaLabel = labelModel.get(['general', 'withoutTitle']);\n      }\n      var seriesLabels_1 = [];\n      var prefix = seriesCnt > 1 ? labelModel.get(['series', 'multiple', 'prefix']) : labelModel.get(['series', 'single', 'prefix']);\n      ariaLabel += replace(prefix, {\n        seriesCount: seriesCnt\n      });\n      ecModel.eachSeries(function (seriesModel, idx) {\n        if (idx < displaySeriesCnt) {\n          var seriesLabel = void 0;\n          var seriesName = seriesModel.get('name');\n          var withName = seriesName ? 'withName' : 'withoutName';\n          seriesLabel = seriesCnt > 1 ? labelModel.get(['series', 'multiple', withName]) : labelModel.get(['series', 'single', withName]);\n          seriesLabel = replace(seriesLabel, {\n            seriesId: seriesModel.seriesIndex,\n            seriesName: seriesModel.get('name'),\n            seriesType: getSeriesTypeName(seriesModel.subType)\n          });\n          var data = seriesModel.getData();\n          if (data.count() > maxDataCnt) {\n            // Show part of data\n            var partialLabel = labelModel.get(['data', 'partialData']);\n            seriesLabel += replace(partialLabel, {\n              displayCnt: maxDataCnt\n            });\n          } else {\n            seriesLabel += labelModel.get(['data', 'allData']);\n          }\n          var middleSeparator_1 = labelModel.get(['data', 'separator', 'middle']);\n          var endSeparator_1 = labelModel.get(['data', 'separator', 'end']);\n          var excludeDimensionId_1 = labelModel.get(['data', 'excludeDimensionId']);\n          var dataLabels = [];\n          for (var i = 0; i < data.count(); i++) {\n            if (i < maxDataCnt) {\n              var name_1 = data.getName(i);\n              var value = !excludeDimensionId_1 ? data.getValues(i) : zrUtil.filter(data.getValues(i), function (v, j) {\n                return zrUtil.indexOf(excludeDimensionId_1, j) === -1;\n              });\n              var dataLabel = labelModel.get(['data', name_1 ? 'withName' : 'withoutName']);\n              dataLabels.push(replace(dataLabel, {\n                name: name_1,\n                value: value.join(middleSeparator_1)\n              }));\n            }\n          }\n          seriesLabel += dataLabels.join(middleSeparator_1) + endSeparator_1;\n          seriesLabels_1.push(seriesLabel);\n        }\n      });\n      var separatorModel = labelModel.getModel(['series', 'multiple', 'separator']);\n      var middleSeparator = separatorModel.get('middle');\n      var endSeparator = separatorModel.get('end');\n      ariaLabel += seriesLabels_1.join(middleSeparator) + endSeparator;\n      dom.setAttribute('aria-label', ariaLabel);\n    }\n  }\n  function replace(str, keyValues) {\n    if (!zrUtil.isString(str)) {\n      return str;\n    }\n    var result = str;\n    zrUtil.each(keyValues, function (value, key) {\n      result = result.replace(new RegExp('\\\\{\\\\s*' + key + '\\\\s*\\\\}', 'g'), value);\n    });\n    return result;\n  }\n  function getTitle() {\n    var title = ecModel.get('title');\n    if (title && title.length) {\n      title = title[0];\n    }\n    return title && title.text;\n  }\n  function getSeriesTypeName(type) {\n    var typeNames = ecModel.getLocaleModel().get(['series', 'typeNames']);\n    return typeNames[type] || typeNames.chart;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}