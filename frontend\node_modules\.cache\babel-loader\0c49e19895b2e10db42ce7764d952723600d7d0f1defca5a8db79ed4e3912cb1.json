{"ast": null, "code": "import { __extends } from \"tslib\";\nimport * as echarts from 'echarts';\nimport EChartsReactCore from './core';\n// export the Component the echarts Object.\nvar EChartsReact = /** @class */function (_super) {\n  __extends(EChartsReact, _super);\n  function EChartsReact(props) {\n    var _this = _super.call(this, props) || this;\n    // 初始化为 echarts 整个包\n    _this.echarts = echarts;\n    return _this;\n  }\n  return EChartsReact;\n}(EChartsReactCore);\nexport default EChartsReact;\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}