{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { use } from '../../extension.js';\nimport ComponentView from '../../view/Component.js';\nimport SingleAxisView from '../axis/SingleAxisView.js';\nimport axisModelCreator from '../../coord/axisModelCreator.js';\nimport SingleAxisModel from '../../coord/single/AxisModel.js';\nimport singleCreator from '../../coord/single/singleCreator.js';\nimport { install as installAxisPointer } from '../axisPointer/install.js';\nimport AxisView from '../axis/AxisView.js';\nimport SingleAxisPointer from '../axisPointer/SingleAxisPointer.js';\nvar SingleView = /** @class */function (_super) {\n  __extends(SingleView, _super);\n  function SingleView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SingleView.type;\n    return _this;\n  }\n  SingleView.type = 'single';\n  return SingleView;\n}(ComponentView);\nexport function install(registers) {\n  use(installAxisPointer);\n  AxisView.registerAxisPointerClass('SingleAxisPointer', SingleAxisPointer);\n  registers.registerComponentView(SingleView);\n  // Axis\n  registers.registerComponentView(SingleAxisView);\n  registers.registerComponentModel(SingleAxisModel);\n  axisModelCreator(registers, 'single', SingleAxisModel, SingleAxisModel.defaultOption);\n  registers.registerCoordinateSystem('single', singleCreator);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}