{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * @file Visual mapping.\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar visualDefault = {\n  /**\r\n   * @public\r\n   */\n  get: function (visualType, key, isCategory) {\n    var value = zrUtil.clone((defaultOption[visualType] || {})[key]);\n    return isCategory ? zrUtil.isArray(value) ? value[value.length - 1] : value : value;\n  }\n};\nvar defaultOption = {\n  color: {\n    active: ['#006edd', '#e0ffff'],\n    inactive: ['rgba(0,0,0,0)']\n  },\n  colorHue: {\n    active: [0, 360],\n    inactive: [0, 0]\n  },\n  colorSaturation: {\n    active: [0.3, 1],\n    inactive: [0, 0]\n  },\n  colorLightness: {\n    active: [0.9, 0.5],\n    inactive: [0, 0]\n  },\n  colorAlpha: {\n    active: [0.3, 1],\n    inactive: [0, 0]\n  },\n  opacity: {\n    active: [0.3, 1],\n    inactive: [0, 0]\n  },\n  symbol: {\n    active: ['circle', 'roundRect', 'diamond'],\n    inactive: ['none']\n  },\n  symbolSize: {\n    active: [10, 50],\n    inactive: [0, 0]\n  }\n};\nexport default visualDefault;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}