const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const { app } = require('electron');
const findFreePort = require('find-free-port');
const psTree = require('ps-tree');

class ServiceManager {
  constructor() {
    this.backendProcess = null;
    this.frontendProcess = null;
    this.services = {
      backend: {
        name: 'Backend API',
        process: null,
        port: 8000,
        status: 'stopped', // stopped, starting, running, error
        pid: null,
        logs: []
      },
      frontend: {
        name: 'Frontend UI',
        process: null,
        port: 3001,
        status: 'stopped',
        pid: null,
        logs: []
      }
    };
    
    this.isDev = process.argv.includes('--dev');
    this.resourcesPath = this.isDev 
      ? path.join(__dirname, '../../../') 
      : path.join(process.resourcesPath, 'app.asar.unpacked');
  }

  async startAllServices() {
    try {
      console.log('开始启动所有服务...');
      
      // 检查端口可用性
      await this.checkPorts();
      
      // 启动后端服务
      const backendResult = await this.startBackend();
      if (!backendResult.success) {
        throw new Error(`后端启动失败: ${backendResult.error}`);
      }
      
      // 等待后端启动完成
      await this.waitForService('backend', 30000);
      
      // 启动前端服务
      const frontendResult = await this.startFrontend();
      if (!frontendResult.success) {
        throw new Error(`前端启动失败: ${frontendResult.error}`);
      }
      
      // 等待前端启动完成
      await this.waitForService('frontend', 20000);
      
      console.log('所有服务启动成功');
      return {
        success: true,
        services: this.getServiceStatus()
      };
      
    } catch (error) {
      console.error('启动服务失败:', error);
      await this.stopAllServices();
      return {
        success: false,
        error: error.message
      };
    }
  }

  async startBackend() {
    try {
      console.log('启动后端服务...');
      
      this.services.backend.status = 'starting';
      
      // 确定后端可执行文件路径
      const backendPath = this.isDev 
        ? path.join(__dirname, '../../../backend')
        : path.join(this.resourcesPath, 'backend');
      
      let backendExe;
      if (this.isDev) {
        // 开发模式：使用Python直接运行
        backendExe = 'python';
        var args = ['-m', 'uvicorn', 'app.main:app', '--host', '0.0.0.0', '--port', this.services.backend.port.toString()];
        var options = { cwd: backendPath };
      } else {
        // 生产模式：使用打包的可执行文件
        backendExe = path.join(backendPath, 'main.exe');
        var args = ['--port', this.services.backend.port.toString()];
        var options = { cwd: backendPath };
      }
      
      // 检查可执行文件是否存在
      if (!this.isDev && !fs.existsSync(backendExe)) {
        throw new Error(`后端可执行文件不存在: ${backendExe}`);
      }
      
      // 启动进程
      this.services.backend.process = spawn(backendExe, args, {
        ...options,
        stdio: ['pipe', 'pipe', 'pipe'],
        detached: false
      });
      
      this.services.backend.pid = this.services.backend.process.pid;
      
      // 设置进程事件监听
      this.setupProcessListeners('backend');
      
      console.log(`后端服务进程已启动，PID: ${this.services.backend.pid}`);
      return { success: true };
      
    } catch (error) {
      console.error('启动后端服务失败:', error);
      this.services.backend.status = 'error';
      return { success: false, error: error.message };
    }
  }

  async startFrontend() {
    try {
      console.log('启动前端服务...');
      
      this.services.frontend.status = 'starting';
      
      if (this.isDev) {
        // 开发模式：使用npm start
        const frontendPath = path.join(__dirname, '../../../frontend');
        
        this.services.frontend.process = spawn('npm', ['start'], {
          cwd: frontendPath,
          stdio: ['pipe', 'pipe', 'pipe'],
          detached: false,
          shell: true
        });
      } else {
        // 生产模式：使用静态文件服务器
        const express = require('express');
        const frontendPath = path.join(this.resourcesPath, 'frontend');
        
        if (!fs.existsSync(frontendPath)) {
          throw new Error(`前端文件不存在: ${frontendPath}`);
        }
        
        // 创建Express服务器
        const app = express();
        app.use(express.static(frontendPath));
        
        // 处理SPA路由
        app.get('*', (req, res) => {
          res.sendFile(path.join(frontendPath, 'index.html'));
        });
        
        // 启动服务器
        this.frontendServer = app.listen(this.services.frontend.port, () => {
          console.log(`前端服务器启动在端口 ${this.services.frontend.port}`);
          this.services.frontend.status = 'running';
        });
        
        // 模拟进程对象
        this.services.frontend.process = {
          pid: process.pid,
          kill: () => {
            if (this.frontendServer) {
              this.frontendServer.close();
            }
          }
        };
      }
      
      this.services.frontend.pid = this.services.frontend.process.pid;
      
      // 设置进程事件监听
      this.setupProcessListeners('frontend');
      
      console.log(`前端服务进程已启动，PID: ${this.services.frontend.pid}`);
      return { success: true };
      
    } catch (error) {
      console.error('启动前端服务失败:', error);
      this.services.frontend.status = 'error';
      return { success: false, error: error.message };
    }
  }

  setupProcessListeners(serviceName) {
    const service = this.services[serviceName];
    const process = service.process;
    
    if (!process) return;
    
    // 标准输出
    if (process.stdout) {
      process.stdout.on('data', (data) => {
        const log = data.toString().trim();
        if (log) {
          service.logs.push({
            timestamp: new Date().toISOString(),
            level: 'INFO',
            message: log
          });
          
          // 保持日志数量限制
          if (service.logs.length > 1000) {
            service.logs = service.logs.slice(-500);
          }
          
          console.log(`[${serviceName.toUpperCase()}] ${log}`);
          
          // 检查服务是否启动成功
          if (serviceName === 'backend' && log.includes('Uvicorn running on')) {
            service.status = 'running';
          } else if (serviceName === 'frontend' && log.includes('webpack compiled')) {
            service.status = 'running';
          }
        }
      });
    }
    
    // 错误输出
    if (process.stderr) {
      process.stderr.on('data', (data) => {
        const log = data.toString().trim();
        if (log) {
          service.logs.push({
            timestamp: new Date().toISOString(),
            level: 'ERROR',
            message: log
          });
          
          console.error(`[${serviceName.toUpperCase()} ERROR] ${log}`);
        }
      });
    }
    
    // 进程退出
    process.on('exit', (code, signal) => {
      console.log(`${serviceName}服务进程退出，代码: ${code}, 信号: ${signal}`);
      service.status = code === 0 ? 'stopped' : 'error';
      service.process = null;
      service.pid = null;
    });
    
    // 进程错误
    process.on('error', (error) => {
      console.error(`${serviceName}服务进程错误:`, error);
      service.status = 'error';
      service.logs.push({
        timestamp: new Date().toISOString(),
        level: 'ERROR',
        message: `进程错误: ${error.message}`
      });
    });
  }

  async stopAllServices() {
    try {
      console.log('停止所有服务...');
      
      const promises = [];
      
      // 停止后端服务
      if (this.services.backend.process) {
        promises.push(this.stopService('backend'));
      }
      
      // 停止前端服务
      if (this.services.frontend.process) {
        promises.push(this.stopService('frontend'));
      }
      
      await Promise.all(promises);
      
      console.log('所有服务已停止');
      return { success: true };
      
    } catch (error) {
      console.error('停止服务失败:', error);
      return { success: false, error: error.message };
    }
  }

  async stopService(serviceName) {
    return new Promise((resolve) => {
      const service = this.services[serviceName];
      
      if (!service.process) {
        resolve();
        return;
      }
      
      console.log(`停止${serviceName}服务...`);
      
      // 设置超时
      const timeout = setTimeout(() => {
        console.log(`强制终止${serviceName}服务`);
        this.killProcessTree(service.pid);
        service.status = 'stopped';
        service.process = null;
        service.pid = null;
        resolve();
      }, 5000);
      
      // 监听进程退出
      service.process.once('exit', () => {
        clearTimeout(timeout);
        service.status = 'stopped';
        service.process = null;
        service.pid = null;
        console.log(`${serviceName}服务已停止`);
        resolve();
      });
      
      // 发送终止信号
      try {
        if (process.platform === 'win32') {
          this.killProcessTree(service.pid);
        } else {
          service.process.kill('SIGTERM');
        }
      } catch (error) {
        console.error(`终止${serviceName}服务失败:`, error);
        clearTimeout(timeout);
        resolve();
      }
    });
  }

  killProcessTree(pid) {
    if (process.platform === 'win32') {
      try {
        require('child_process').execSync(`taskkill /pid ${pid} /T /F`, { stdio: 'ignore' });
      } catch (error) {
        console.error('Windows进程终止失败:', error);
      }
    } else {
      psTree(pid, (err, children) => {
        if (err) {
          console.error('获取子进程失败:', err);
          return;
        }
        
        children.forEach(child => {
          try {
            process.kill(child.PID, 'SIGKILL');
          } catch (error) {
            console.error(`终止子进程${child.PID}失败:`, error);
          }
        });
        
        try {
          process.kill(pid, 'SIGKILL');
        } catch (error) {
          console.error(`终止主进程${pid}失败:`, error);
        }
      });
    }
  }

  async waitForService(serviceName, timeout = 30000) {
    return new Promise((resolve, reject) => {
      const service = this.services[serviceName];
      const startTime = Date.now();
      
      const checkStatus = () => {
        if (service.status === 'running') {
          resolve();
        } else if (service.status === 'error') {
          reject(new Error(`${serviceName}服务启动失败`));
        } else if (Date.now() - startTime > timeout) {
          reject(new Error(`${serviceName}服务启动超时`));
        } else {
          setTimeout(checkStatus, 1000);
        }
      };
      
      checkStatus();
    });
  }

  async checkPorts() {
    try {
      // 检查后端端口
      const backendPort = await findFreePort(this.services.backend.port);
      if (backendPort[0] !== this.services.backend.port) {
        throw new Error(`后端端口${this.services.backend.port}被占用`);
      }
      
      // 检查前端端口
      const frontendPort = await findFreePort(this.services.frontend.port);
      if (frontendPort[0] !== this.services.frontend.port) {
        throw new Error(`前端端口${this.services.frontend.port}被占用`);
      }
      
      return true;
    } catch (error) {
      console.error('端口检查失败:', error);
      throw error;
    }
  }

  getServiceStatus() {
    return {
      backend: {
        name: this.services.backend.name,
        status: this.services.backend.status,
        port: this.services.backend.port,
        pid: this.services.backend.pid,
        url: `http://localhost:${this.services.backend.port}`
      },
      frontend: {
        name: this.services.frontend.name,
        status: this.services.frontend.status,
        port: this.services.frontend.port,
        pid: this.services.frontend.pid,
        url: `http://localhost:${this.services.frontend.port}`
      }
    };
  }

  getLogs() {
    return {
      backend: this.services.backend.logs.slice(-100), // 最近100条
      frontend: this.services.frontend.logs.slice(-100)
    };
  }
}

module.exports = ServiceManager;
