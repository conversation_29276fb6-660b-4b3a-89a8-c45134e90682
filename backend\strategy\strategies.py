"""
交易策略定义
"""
import pandas as pd
import numpy as np
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

class TradingStrategy:
    """交易策略基类"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.parameters = {}
    
    def generate_signals(self, data: Dict[str, pd.Series], positions: Dict[str, Any]) -> Dict[str, str]:
        """生成交易信号"""
        raise NotImplementedError("子类必须实现此方法")
    
    def set_parameters(self, **kwargs):
        """设置策略参数"""
        self.parameters.update(kwargs)

class MACDStrategy(TradingStrategy):
    """MACD策略"""
    
    def __init__(self):
        super().__init__(
            name="MACD策略",
            description="基于MACD指标的金叉死叉策略"
        )
        self.parameters = {
            'fast_period': 12,
            'slow_period': 26,
            'signal_period': 9,
            'min_macd_value': 0.01  # 最小MACD值，避免在震荡区间频繁交易
        }
    
    def generate_signals(self, data: Dict[str, pd.Series], positions: Dict[str, Any]) -> Dict[str, str]:
        """生成MACD交易信号"""
        signals = {}
        
        for ts_code, day_data in data.items():
            try:
                # 检查必要的指标是否存在
                if not all(col in day_data.index for col in ['macd', 'macd_signal', 'macd_histogram']):
                    continue
                
                macd = day_data['macd']
                macd_signal = day_data['macd_signal']
                macd_histogram = day_data['macd_histogram']
                
                # 跳过无效数据
                if pd.isna(macd) or pd.isna(macd_signal) or pd.isna(macd_histogram):
                    continue
                
                # 金叉买入信号：MACD线上穿信号线，且MACD值为正
                if (macd > macd_signal and 
                    macd > self.parameters['min_macd_value'] and 
                    ts_code not in positions):
                    signals[ts_code] = 'BUY'
                
                # 死叉卖出信号：MACD线下穿信号线，或MACD值转负
                elif (macd < macd_signal or macd < 0) and ts_code in positions:
                    signals[ts_code] = 'SELL'
                
                else:
                    signals[ts_code] = 'HOLD'
                    
            except Exception as e:
                logger.error(f"MACD策略处理 {ts_code} 失败: {e}")
                signals[ts_code] = 'HOLD'
        
        return signals

class RSIStrategy(TradingStrategy):
    """RSI策略"""
    
    def __init__(self):
        super().__init__(
            name="RSI策略",
            description="基于RSI指标的超买超卖策略"
        )
        self.parameters = {
            'rsi_period': 14,
            'oversold_threshold': 30,
            'overbought_threshold': 70,
            'exit_oversold': 50,
            'exit_overbought': 50
        }
    
    def generate_signals(self, data: Dict[str, pd.Series], positions: Dict[str, Any]) -> Dict[str, str]:
        """生成RSI交易信号"""
        signals = {}
        
        for ts_code, day_data in data.items():
            try:
                if 'rsi' not in day_data.index:
                    continue
                
                rsi = day_data['rsi']
                
                if pd.isna(rsi):
                    continue
                
                # 超卖买入信号
                if rsi <= self.parameters['oversold_threshold'] and ts_code not in positions:
                    signals[ts_code] = 'BUY'
                
                # 超买卖出信号
                elif rsi >= self.parameters['overbought_threshold'] and ts_code in positions:
                    signals[ts_code] = 'SELL'
                
                # 持仓时的退出信号
                elif ts_code in positions:
                    if rsi >= self.parameters['exit_oversold']:  # 从超卖区域回升
                        signals[ts_code] = 'SELL'
                    else:
                        signals[ts_code] = 'HOLD'
                
                else:
                    signals[ts_code] = 'HOLD'
                    
            except Exception as e:
                logger.error(f"RSI策略处理 {ts_code} 失败: {e}")
                signals[ts_code] = 'HOLD'
        
        return signals

class MovingAverageStrategy(TradingStrategy):
    """双均线策略"""
    
    def __init__(self):
        super().__init__(
            name="双均线策略",
            description="基于快慢均线交叉的趋势跟踪策略"
        )
        self.parameters = {
            'fast_ma': 5,
            'slow_ma': 20,
            'trend_filter': True  # 是否使用趋势过滤
        }
    
    def generate_signals(self, data: Dict[str, pd.Series], positions: Dict[str, Any]) -> Dict[str, str]:
        """生成双均线交易信号"""
        signals = {}
        
        for ts_code, day_data in data.items():
            try:
                fast_ma_col = f'ma_{self.parameters["fast_ma"]}'
                slow_ma_col = f'ma_{self.parameters["slow_ma"]}'
                
                if not all(col in day_data.index for col in [fast_ma_col, slow_ma_col]):
                    continue
                
                fast_ma = day_data[fast_ma_col]
                slow_ma = day_data[slow_ma_col]
                current_price = day_data['close']
                
                if pd.isna(fast_ma) or pd.isna(slow_ma):
                    continue
                
                # 金叉买入信号：快线上穿慢线
                if fast_ma > slow_ma and current_price > fast_ma and ts_code not in positions:
                    signals[ts_code] = 'BUY'
                
                # 死叉卖出信号：快线下穿慢线
                elif fast_ma < slow_ma and ts_code in positions:
                    signals[ts_code] = 'SELL'
                
                # 价格跌破快线也卖出
                elif current_price < fast_ma and ts_code in positions:
                    signals[ts_code] = 'SELL'
                
                else:
                    signals[ts_code] = 'HOLD'
                    
            except Exception as e:
                logger.error(f"双均线策略处理 {ts_code} 失败: {e}")
                signals[ts_code] = 'HOLD'
        
        return signals

class BollingerBandsStrategy(TradingStrategy):
    """布林带策略"""
    
    def __init__(self):
        super().__init__(
            name="布林带策略",
            description="基于布林带的均值回归策略"
        )
        self.parameters = {
            'period': 20,
            'std_dev': 2,
            'entry_threshold': 0.95,  # 触及下轨的比例
            'exit_threshold': 0.5     # 回到中轨的比例
        }
    
    def generate_signals(self, data: Dict[str, pd.Series], positions: Dict[str, Any]) -> Dict[str, str]:
        """生成布林带交易信号"""
        signals = {}
        
        for ts_code, day_data in data.items():
            try:
                if not all(col in day_data.index for col in ['bb_upper', 'bb_middle', 'bb_lower']):
                    continue
                
                bb_upper = day_data['bb_upper']
                bb_middle = day_data['bb_middle']
                bb_lower = day_data['bb_lower']
                current_price = day_data['close']
                
                if pd.isna(bb_upper) or pd.isna(bb_middle) or pd.isna(bb_lower):
                    continue
                
                # 计算价格在布林带中的位置
                bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
                
                # 触及下轨买入
                if bb_position <= (1 - self.parameters['entry_threshold']) and ts_code not in positions:
                    signals[ts_code] = 'BUY'
                
                # 触及上轨或回到中轨卖出
                elif (bb_position >= self.parameters['entry_threshold'] or 
                      bb_position >= self.parameters['exit_threshold']) and ts_code in positions:
                    signals[ts_code] = 'SELL'
                
                else:
                    signals[ts_code] = 'HOLD'
                    
            except Exception as e:
                logger.error(f"布林带策略处理 {ts_code} 失败: {e}")
                signals[ts_code] = 'HOLD'
        
        return signals

class CompositeStrategy(TradingStrategy):
    """综合策略"""
    
    def __init__(self):
        super().__init__(
            name="综合策略",
            description="结合多个技术指标的综合策略"
        )
        self.parameters = {
            'min_signals': 2,  # 最少需要的买入信号数量
            'use_volume_filter': True,  # 是否使用成交量过滤
            'volume_threshold': 1.5     # 成交量倍数阈值
        }
        
        # 子策略
        self.sub_strategies = [
            MACDStrategy(),
            RSIStrategy(),
            MovingAverageStrategy()
        ]
    
    def generate_signals(self, data: Dict[str, pd.Series], positions: Dict[str, Any]) -> Dict[str, str]:
        """生成综合交易信号"""
        signals = {}
        
        # 获取各子策略的信号
        sub_signals = {}
        for strategy in self.sub_strategies:
            sub_signals[strategy.name] = strategy.generate_signals(data, positions)
        
        for ts_code in data.keys():
            try:
                buy_signals = 0
                sell_signals = 0
                
                # 统计各策略的信号
                for strategy_name, strategy_signals in sub_signals.items():
                    if ts_code in strategy_signals:
                        if strategy_signals[ts_code] == 'BUY':
                            buy_signals += 1
                        elif strategy_signals[ts_code] == 'SELL':
                            sell_signals += 1
                
                # 成交量过滤
                volume_ok = True
                if self.parameters['use_volume_filter'] and 'volume_ratio' in data[ts_code].index:
                    volume_ratio = data[ts_code]['volume_ratio']
                    if not pd.isna(volume_ratio):
                        volume_ok = volume_ratio >= self.parameters['volume_threshold']
                
                # 综合判断
                if buy_signals >= self.parameters['min_signals'] and volume_ok and ts_code not in positions:
                    signals[ts_code] = 'BUY'
                elif sell_signals >= 1 and ts_code in positions:  # 任何一个策略发出卖出信号就卖出
                    signals[ts_code] = 'SELL'
                else:
                    signals[ts_code] = 'HOLD'
                    
            except Exception as e:
                logger.error(f"综合策略处理 {ts_code} 失败: {e}")
                signals[ts_code] = 'HOLD'
        
        return signals

# 预定义策略实例
AVAILABLE_STRATEGIES = {
    'macd': MACDStrategy(),
    'rsi': RSIStrategy(),
    'ma': MovingAverageStrategy(),
    'bb': BollingerBandsStrategy(),
    'composite': CompositeStrategy()
}

def get_strategy(strategy_name: str) -> TradingStrategy:
    """获取策略实例"""
    if strategy_name in AVAILABLE_STRATEGIES:
        return AVAILABLE_STRATEGIES[strategy_name]
    else:
        raise ValueError(f"未知策略: {strategy_name}")

def list_strategies() -> List[Dict[str, str]]:
    """列出所有可用策略"""
    return [
        {
            'name': name,
            'display_name': strategy.name,
            'description': strategy.description
        }
        for name, strategy in AVAILABLE_STRATEGIES.items()
    ]
