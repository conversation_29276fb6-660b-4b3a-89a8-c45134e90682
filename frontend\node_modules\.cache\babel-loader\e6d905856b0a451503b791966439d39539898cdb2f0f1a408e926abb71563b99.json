{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport Axis from '../Axis.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nvar AngleAxis = /** @class */function (_super) {\n  __extends(AngleAxis, _super);\n  function AngleAxis(scale, angleExtent) {\n    return _super.call(this, 'angle', scale, angleExtent || [0, 360]) || this;\n  }\n  AngleAxis.prototype.pointToData = function (point, clamp) {\n    return this.polar.pointToData(point, clamp)[this.dim === 'radius' ? 0 : 1];\n  };\n  /**\r\n   * Only be called in category axis.\r\n   * Angle axis uses text height to decide interval\r\n   *\r\n   * @override\r\n   * @return {number} Auto interval for cateogry axis tick and label\r\n   */\n  AngleAxis.prototype.calculateCategoryInterval = function () {\n    var axis = this;\n    var labelModel = axis.getLabelModel();\n    var ordinalScale = axis.scale;\n    var ordinalExtent = ordinalScale.getExtent();\n    // Providing this method is for optimization:\n    // avoid generating a long array by `getTicks`\n    // in large category data case.\n    var tickCount = ordinalScale.count();\n    if (ordinalExtent[1] - ordinalExtent[0] < 1) {\n      return 0;\n    }\n    var tickValue = ordinalExtent[0];\n    var unitSpan = axis.dataToCoord(tickValue + 1) - axis.dataToCoord(tickValue);\n    var unitH = Math.abs(unitSpan);\n    // Not precise, just use height as text width\n    // and each distance from axis line yet.\n    var rect = textContain.getBoundingRect(tickValue == null ? '' : tickValue + '', labelModel.getFont(), 'center', 'top');\n    var maxH = Math.max(rect.height, 7);\n    var dh = maxH / unitH;\n    // 0/0 is NaN, 1/0 is Infinity.\n    isNaN(dh) && (dh = Infinity);\n    var interval = Math.max(0, Math.floor(dh));\n    var cache = inner(axis.model);\n    var lastAutoInterval = cache.lastAutoInterval;\n    var lastTickCount = cache.lastTickCount;\n    // Use cache to keep interval stable while moving zoom window,\n    // otherwise the calculated interval might jitter when the zoom\n    // window size is close to the interval-changing size.\n    if (lastAutoInterval != null && lastTickCount != null && Math.abs(lastAutoInterval - interval) <= 1 && Math.abs(lastTickCount - tickCount) <= 1\n    // Always choose the bigger one, otherwise the critical\n    // point is not the same when zooming in or zooming out.\n    && lastAutoInterval > interval) {\n      interval = lastAutoInterval;\n    }\n    // Only update cache if cache not used, otherwise the\n    // changing of interval is too insensitive.\n    else {\n      cache.lastTickCount = tickCount;\n      cache.lastAutoInterval = interval;\n    }\n    return interval;\n  };\n  return AngleAxis;\n}(Axis);\nAngleAxis.prototype.dataToAngle = Axis.prototype.dataToCoord;\nAngleAxis.prototype.angleToData = Axis.prototype.coordToData;\nexport default AngleAxis;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}