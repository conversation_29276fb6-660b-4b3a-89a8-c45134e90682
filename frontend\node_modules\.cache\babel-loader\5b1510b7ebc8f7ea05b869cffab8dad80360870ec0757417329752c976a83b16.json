{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport ChartView from '../../view/Chart.js';\nimport { setLabelLineStyle, getLabelLineStatesModels } from '../../label/labelGuideHelper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar opacityAccessPath = ['itemStyle', 'opacity'];\n/**\r\n * Piece of pie including Sector, Label, LabelLine\r\n */\nvar FunnelPiece = /** @class */function (_super) {\n  __extends(FunnelPiece, _super);\n  function FunnelPiece(data, idx) {\n    var _this = _super.call(this) || this;\n    var polygon = _this;\n    var labelLine = new graphic.Polyline();\n    var text = new graphic.Text();\n    polygon.setTextContent(text);\n    _this.setTextGuideLine(labelLine);\n    _this.updateData(data, idx, true);\n    return _this;\n  }\n  FunnelPiece.prototype.updateData = function (data, idx, firstCreate) {\n    var polygon = this;\n    var seriesModel = data.hostModel;\n    var itemModel = data.getItemModel(idx);\n    var layout = data.getItemLayout(idx);\n    var emphasisModel = itemModel.getModel('emphasis');\n    var opacity = itemModel.get(opacityAccessPath);\n    opacity = opacity == null ? 1 : opacity;\n    if (!firstCreate) {\n      saveOldStyle(polygon);\n    }\n    // Update common style\n    polygon.useStyle(data.getItemVisual(idx, 'style'));\n    polygon.style.lineJoin = 'round';\n    if (firstCreate) {\n      polygon.setShape({\n        points: layout.points\n      });\n      polygon.style.opacity = 0;\n      graphic.initProps(polygon, {\n        style: {\n          opacity: opacity\n        }\n      }, seriesModel, idx);\n    } else {\n      graphic.updateProps(polygon, {\n        style: {\n          opacity: opacity\n        },\n        shape: {\n          points: layout.points\n        }\n      }, seriesModel, idx);\n    }\n    setStatesStylesFromModel(polygon, itemModel);\n    this._updateLabel(data, idx);\n    toggleHoverEmphasis(this, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  };\n  FunnelPiece.prototype._updateLabel = function (data, idx) {\n    var polygon = this;\n    var labelLine = this.getTextGuideLine();\n    var labelText = polygon.getTextContent();\n    var seriesModel = data.hostModel;\n    var itemModel = data.getItemModel(idx);\n    var layout = data.getItemLayout(idx);\n    var labelLayout = layout.label;\n    var style = data.getItemVisual(idx, 'style');\n    var visualColor = style.fill;\n    setLabelStyle(\n    // position will not be used in setLabelStyle\n    labelText, getLabelStatesModels(itemModel), {\n      labelFetcher: data.hostModel,\n      labelDataIndex: idx,\n      defaultOpacity: style.opacity,\n      defaultText: data.getName(idx)\n    }, {\n      normal: {\n        align: labelLayout.textAlign,\n        verticalAlign: labelLayout.verticalAlign\n      }\n    });\n    polygon.setTextConfig({\n      local: true,\n      inside: !!labelLayout.inside,\n      insideStroke: visualColor,\n      // insideFill: 'auto',\n      outsideFill: visualColor\n    });\n    var linePoints = labelLayout.linePoints;\n    labelLine.setShape({\n      points: linePoints\n    });\n    polygon.textGuideLineConfig = {\n      anchor: linePoints ? new graphic.Point(linePoints[0][0], linePoints[0][1]) : null\n    };\n    // Make sure update style on labelText after setLabelStyle.\n    // Because setLabelStyle will replace a new style on it.\n    graphic.updateProps(labelText, {\n      style: {\n        x: labelLayout.x,\n        y: labelLayout.y\n      }\n    }, seriesModel, idx);\n    labelText.attr({\n      rotation: labelLayout.rotation,\n      originX: labelLayout.x,\n      originY: labelLayout.y,\n      z2: 10\n    });\n    setLabelLineStyle(polygon, getLabelLineStatesModels(itemModel), {\n      // Default use item visual color\n      stroke: visualColor\n    });\n  };\n  return FunnelPiece;\n}(graphic.Polygon);\nvar FunnelView = /** @class */function (_super) {\n  __extends(FunnelView, _super);\n  function FunnelView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = FunnelView.type;\n    _this.ignoreLabelLineUpdate = true;\n    return _this;\n  }\n  FunnelView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var group = this.group;\n    data.diff(oldData).add(function (idx) {\n      var funnelPiece = new FunnelPiece(data, idx);\n      data.setItemGraphicEl(idx, funnelPiece);\n      group.add(funnelPiece);\n    }).update(function (newIdx, oldIdx) {\n      var piece = oldData.getItemGraphicEl(oldIdx);\n      piece.updateData(data, newIdx);\n      group.add(piece);\n      data.setItemGraphicEl(newIdx, piece);\n    }).remove(function (idx) {\n      var piece = oldData.getItemGraphicEl(idx);\n      graphic.removeElementWithFadeOut(piece, seriesModel, idx);\n    }).execute();\n    this._data = data;\n  };\n  FunnelView.prototype.remove = function () {\n    this.group.removeAll();\n    this._data = null;\n  };\n  FunnelView.prototype.dispose = function () {};\n  FunnelView.type = 'funnel';\n  return FunnelView;\n}(ChartView);\nexport default FunnelView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}