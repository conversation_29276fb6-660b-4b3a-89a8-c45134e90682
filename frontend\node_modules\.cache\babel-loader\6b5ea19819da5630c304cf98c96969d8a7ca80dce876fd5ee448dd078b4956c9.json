{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport * as modelUtil from '../util/model.js';\nimport ComponentModel from './Component.js';\nimport { PaletteMixin } from './mixin/palette.js';\nimport { DataFormatMixin } from '../model/mixin/dataFormat.js';\nimport { getLayoutParams, mergeLayoutParam, fetchLayoutMode } from '../util/layout.js';\nimport { createTask } from '../core/task.js';\nimport { mountExtend } from '../util/clazz.js';\nimport { SourceManager } from '../data/helper/sourceManager.js';\nimport { defaultSeriesFormatTooltip } from '../component/tooltip/seriesFormatTooltip.js';\nvar inner = modelUtil.makeInner();\nfunction getSelectionKey(data, dataIndex) {\n  return data.getName(dataIndex) || data.getId(dataIndex);\n}\nexport var SERIES_UNIVERSAL_TRANSITION_PROP = '__universalTransitionEnabled';\nvar SeriesModel = /** @class */function (_super) {\n  __extends(SeriesModel, _super);\n  function SeriesModel() {\n    // [Caution]: Because this class or desecendants can be used as `XXX.extend(subProto)`,\n    // the class members must not be initialized in constructor or declaration place.\n    // Otherwise there is bad case:\n    //   class A {xxx = 1;}\n    //   enableClassExtend(A);\n    //   class B extends A {}\n    //   var C = B.extend({xxx: 5});\n    //   var c = new C();\n    //   console.log(c.xxx); // expect 5 but always 1.\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    // ---------------------------------------\n    // Props about data selection\n    // ---------------------------------------\n    _this._selectedDataIndicesMap = {};\n    return _this;\n  }\n  SeriesModel.prototype.init = function (option, parentModel, ecModel) {\n    this.seriesIndex = this.componentIndex;\n    this.dataTask = createTask({\n      count: dataTaskCount,\n      reset: dataTaskReset\n    });\n    this.dataTask.context = {\n      model: this\n    };\n    this.mergeDefaultAndTheme(option, ecModel);\n    var sourceManager = inner(this).sourceManager = new SourceManager(this);\n    sourceManager.prepareSource();\n    var data = this.getInitialData(option, ecModel);\n    wrapData(data, this);\n    this.dataTask.context.data = data;\n    if (process.env.NODE_ENV !== 'production') {\n      zrUtil.assert(data, 'getInitialData returned invalid data.');\n    }\n    inner(this).dataBeforeProcessed = data;\n    // If we reverse the order (make data firstly, and then make\n    // dataBeforeProcessed by cloneShallow), cloneShallow will\n    // cause data.graph.data !== data when using\n    // module:echarts/data/Graph or module:echarts/data/Tree.\n    // See module:echarts/data/helper/linkSeriesData\n    // Theoretically, it is unreasonable to call `seriesModel.getData()` in the model\n    // init or merge stage, because the data can be restored. So we do not `restoreData`\n    // and `setData` here, which forbids calling `seriesModel.getData()` in this stage.\n    // Call `seriesModel.getRawData()` instead.\n    // this.restoreData();\n    autoSeriesName(this);\n    this._initSelectedMapFromData(data);\n  };\n  /**\r\n   * Util for merge default and theme to option\r\n   */\n  SeriesModel.prototype.mergeDefaultAndTheme = function (option, ecModel) {\n    var layoutMode = fetchLayoutMode(this);\n    var inputPositionParams = layoutMode ? getLayoutParams(option) : {};\n    // Backward compat: using subType on theme.\n    // But if name duplicate between series subType\n    // (for example: parallel) add component mainType,\n    // add suffix 'Series'.\n    var themeSubType = this.subType;\n    if (ComponentModel.hasClass(themeSubType)) {\n      themeSubType += 'Series';\n    }\n    zrUtil.merge(option, ecModel.getTheme().get(this.subType));\n    zrUtil.merge(option, this.getDefaultOption());\n    // Default label emphasis `show`\n    modelUtil.defaultEmphasis(option, 'label', ['show']);\n    this.fillDataTextStyle(option.data);\n    if (layoutMode) {\n      mergeLayoutParam(option, inputPositionParams, layoutMode);\n    }\n  };\n  SeriesModel.prototype.mergeOption = function (newSeriesOption, ecModel) {\n    // this.settingTask.dirty();\n    newSeriesOption = zrUtil.merge(this.option, newSeriesOption, true);\n    this.fillDataTextStyle(newSeriesOption.data);\n    var layoutMode = fetchLayoutMode(this);\n    if (layoutMode) {\n      mergeLayoutParam(this.option, newSeriesOption, layoutMode);\n    }\n    var sourceManager = inner(this).sourceManager;\n    sourceManager.dirty();\n    sourceManager.prepareSource();\n    var data = this.getInitialData(newSeriesOption, ecModel);\n    wrapData(data, this);\n    this.dataTask.dirty();\n    this.dataTask.context.data = data;\n    inner(this).dataBeforeProcessed = data;\n    autoSeriesName(this);\n    this._initSelectedMapFromData(data);\n  };\n  SeriesModel.prototype.fillDataTextStyle = function (data) {\n    // Default data label emphasis `show`\n    // FIXME Tree structure data ?\n    // FIXME Performance ?\n    if (data && !zrUtil.isTypedArray(data)) {\n      var props = ['show'];\n      for (var i = 0; i < data.length; i++) {\n        if (data[i] && data[i].label) {\n          modelUtil.defaultEmphasis(data[i], 'label', props);\n        }\n      }\n    }\n  };\n  /**\r\n   * Init a data structure from data related option in series\r\n   * Must be overridden.\r\n   */\n  SeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return;\n  };\n  /**\r\n   * Append data to list\r\n   */\n  SeriesModel.prototype.appendData = function (params) {\n    // FIXME ???\n    // (1) If data from dataset, forbidden append.\n    // (2) support append data of dataset.\n    var data = this.getRawData();\n    data.appendData(params.data);\n  };\n  /**\r\n   * Consider some method like `filter`, `map` need make new data,\r\n   * We should make sure that `seriesModel.getData()` get correct\r\n   * data in the stream procedure. So we fetch data from upstream\r\n   * each time `task.perform` called.\r\n   */\n  SeriesModel.prototype.getData = function (dataType) {\n    var task = getCurrentTask(this);\n    if (task) {\n      var data = task.context.data;\n      return dataType == null || !data.getLinkedData ? data : data.getLinkedData(dataType);\n    } else {\n      // When series is not alive (that may happen when click toolbox\n      // restore or setOption with not merge mode), series data may\n      // be still need to judge animation or something when graphic\n      // elements want to know whether fade out.\n      return inner(this).data;\n    }\n  };\n  SeriesModel.prototype.getAllData = function () {\n    var mainData = this.getData();\n    return mainData && mainData.getLinkedDataAll ? mainData.getLinkedDataAll() : [{\n      data: mainData\n    }];\n  };\n  SeriesModel.prototype.setData = function (data) {\n    var task = getCurrentTask(this);\n    if (task) {\n      var context = task.context;\n      // Consider case: filter, data sample.\n      // FIXME:TS never used, so comment it\n      // if (context.data !== data && task.modifyOutputEnd) {\n      //     task.setOutputEnd(data.count());\n      // }\n      context.outputData = data;\n      // Caution: setData should update context.data,\n      // Because getData may be called multiply in a\n      // single stage and expect to get the data just\n      // set. (For example, AxisProxy, x y both call\n      // getData and setDate sequentially).\n      // So the context.data should be fetched from\n      // upstream each time when a stage starts to be\n      // performed.\n      if (task !== this.dataTask) {\n        context.data = data;\n      }\n    }\n    inner(this).data = data;\n  };\n  SeriesModel.prototype.getEncode = function () {\n    var encode = this.get('encode', true);\n    if (encode) {\n      return zrUtil.createHashMap(encode);\n    }\n  };\n  SeriesModel.prototype.getSourceManager = function () {\n    return inner(this).sourceManager;\n  };\n  SeriesModel.prototype.getSource = function () {\n    return this.getSourceManager().getSource();\n  };\n  /**\r\n   * Get data before processed\r\n   */\n  SeriesModel.prototype.getRawData = function () {\n    return inner(this).dataBeforeProcessed;\n  };\n  SeriesModel.prototype.getColorBy = function () {\n    var colorBy = this.get('colorBy');\n    return colorBy || 'series';\n  };\n  SeriesModel.prototype.isColorBySeries = function () {\n    return this.getColorBy() === 'series';\n  };\n  /**\r\n   * Get base axis if has coordinate system and has axis.\r\n   * By default use coordSys.getBaseAxis();\r\n   * Can be overridden for some chart.\r\n   * @return {type} description\r\n   */\n  SeriesModel.prototype.getBaseAxis = function () {\n    var coordSys = this.coordinateSystem;\n    // @ts-ignore\n    return coordSys && coordSys.getBaseAxis && coordSys.getBaseAxis();\n  };\n  /**\r\n   * Default tooltip formatter\r\n   *\r\n   * @param dataIndex\r\n   * @param multipleSeries\r\n   * @param dataType\r\n   * @param renderMode valid values: 'html'(by default) and 'richText'.\r\n   *        'html' is used for rendering tooltip in extra DOM form, and the result\r\n   *        string is used as DOM HTML content.\r\n   *        'richText' is used for rendering tooltip in rich text form, for those where\r\n   *        DOM operation is not supported.\r\n   * @return formatted tooltip with `html` and `markers`\r\n   *        Notice: The override method can also return string\r\n   */\n  SeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    return defaultSeriesFormatTooltip({\n      series: this,\n      dataIndex: dataIndex,\n      multipleSeries: multipleSeries\n    });\n  };\n  SeriesModel.prototype.isAnimationEnabled = function () {\n    var ecModel = this.ecModel;\n    // Disable animation if using echarts in node but not give ssr flag.\n    // In ssr mode, renderToString will generate svg with css animation.\n    if (env.node && !(ecModel && ecModel.ssr)) {\n      return false;\n    }\n    var animationEnabled = this.getShallow('animation');\n    if (animationEnabled) {\n      if (this.getData().count() > this.getShallow('animationThreshold')) {\n        animationEnabled = false;\n      }\n    }\n    return !!animationEnabled;\n  };\n  SeriesModel.prototype.restoreData = function () {\n    this.dataTask.dirty();\n  };\n  SeriesModel.prototype.getColorFromPalette = function (name, scope, requestColorNum) {\n    var ecModel = this.ecModel;\n    // PENDING\n    var color = PaletteMixin.prototype.getColorFromPalette.call(this, name, scope, requestColorNum);\n    if (!color) {\n      color = ecModel.getColorFromPalette(name, scope, requestColorNum);\n    }\n    return color;\n  };\n  /**\r\n   * Use `data.mapDimensionsAll(coordDim)` instead.\r\n   * @deprecated\r\n   */\n  SeriesModel.prototype.coordDimToDataDim = function (coordDim) {\n    return this.getRawData().mapDimensionsAll(coordDim);\n  };\n  /**\r\n   * Get progressive rendering count each step\r\n   */\n  SeriesModel.prototype.getProgressive = function () {\n    return this.get('progressive');\n  };\n  /**\r\n   * Get progressive rendering count each step\r\n   */\n  SeriesModel.prototype.getProgressiveThreshold = function () {\n    return this.get('progressiveThreshold');\n  };\n  // PENGING If selectedMode is null ?\n  SeriesModel.prototype.select = function (innerDataIndices, dataType) {\n    this._innerSelect(this.getData(dataType), innerDataIndices);\n  };\n  SeriesModel.prototype.unselect = function (innerDataIndices, dataType) {\n    var selectedMap = this.option.selectedMap;\n    if (!selectedMap) {\n      return;\n    }\n    var selectedMode = this.option.selectedMode;\n    var data = this.getData(dataType);\n    if (selectedMode === 'series' || selectedMap === 'all') {\n      this.option.selectedMap = {};\n      this._selectedDataIndicesMap = {};\n      return;\n    }\n    for (var i = 0; i < innerDataIndices.length; i++) {\n      var dataIndex = innerDataIndices[i];\n      var nameOrId = getSelectionKey(data, dataIndex);\n      selectedMap[nameOrId] = false;\n      this._selectedDataIndicesMap[nameOrId] = -1;\n    }\n  };\n  SeriesModel.prototype.toggleSelect = function (innerDataIndices, dataType) {\n    var tmpArr = [];\n    for (var i = 0; i < innerDataIndices.length; i++) {\n      tmpArr[0] = innerDataIndices[i];\n      this.isSelected(innerDataIndices[i], dataType) ? this.unselect(tmpArr, dataType) : this.select(tmpArr, dataType);\n    }\n  };\n  SeriesModel.prototype.getSelectedDataIndices = function () {\n    if (this.option.selectedMap === 'all') {\n      return [].slice.call(this.getData().getIndices());\n    }\n    var selectedDataIndicesMap = this._selectedDataIndicesMap;\n    var nameOrIds = zrUtil.keys(selectedDataIndicesMap);\n    var dataIndices = [];\n    for (var i = 0; i < nameOrIds.length; i++) {\n      var dataIndex = selectedDataIndicesMap[nameOrIds[i]];\n      if (dataIndex >= 0) {\n        dataIndices.push(dataIndex);\n      }\n    }\n    return dataIndices;\n  };\n  SeriesModel.prototype.isSelected = function (dataIndex, dataType) {\n    var selectedMap = this.option.selectedMap;\n    if (!selectedMap) {\n      return false;\n    }\n    var data = this.getData(dataType);\n    return (selectedMap === 'all' || selectedMap[getSelectionKey(data, dataIndex)]) && !data.getItemModel(dataIndex).get(['select', 'disabled']);\n  };\n  SeriesModel.prototype.isUniversalTransitionEnabled = function () {\n    if (this[SERIES_UNIVERSAL_TRANSITION_PROP]) {\n      return true;\n    }\n    var universalTransitionOpt = this.option.universalTransition;\n    // Quick reject\n    if (!universalTransitionOpt) {\n      return false;\n    }\n    if (universalTransitionOpt === true) {\n      return true;\n    }\n    // Can be simply 'universalTransition: true'\n    return universalTransitionOpt && universalTransitionOpt.enabled;\n  };\n  SeriesModel.prototype._innerSelect = function (data, innerDataIndices) {\n    var _a, _b;\n    var option = this.option;\n    var selectedMode = option.selectedMode;\n    var len = innerDataIndices.length;\n    if (!selectedMode || !len) {\n      return;\n    }\n    if (selectedMode === 'series') {\n      option.selectedMap = 'all';\n    } else if (selectedMode === 'multiple') {\n      if (!zrUtil.isObject(option.selectedMap)) {\n        option.selectedMap = {};\n      }\n      var selectedMap = option.selectedMap;\n      for (var i = 0; i < len; i++) {\n        var dataIndex = innerDataIndices[i];\n        // TODO different types of data share same object.\n        var nameOrId = getSelectionKey(data, dataIndex);\n        selectedMap[nameOrId] = true;\n        this._selectedDataIndicesMap[nameOrId] = data.getRawIndex(dataIndex);\n      }\n    } else if (selectedMode === 'single' || selectedMode === true) {\n      var lastDataIndex = innerDataIndices[len - 1];\n      var nameOrId = getSelectionKey(data, lastDataIndex);\n      option.selectedMap = (_a = {}, _a[nameOrId] = true, _a);\n      this._selectedDataIndicesMap = (_b = {}, _b[nameOrId] = data.getRawIndex(lastDataIndex), _b);\n    }\n  };\n  SeriesModel.prototype._initSelectedMapFromData = function (data) {\n    // Ignore select info in data if selectedMap exists.\n    // NOTE It's only for legacy usage. edge data is not supported.\n    if (this.option.selectedMap) {\n      return;\n    }\n    var dataIndices = [];\n    if (data.hasItemOption) {\n      data.each(function (idx) {\n        var rawItem = data.getRawDataItem(idx);\n        if (rawItem && rawItem.selected) {\n          dataIndices.push(idx);\n        }\n      });\n    }\n    if (dataIndices.length > 0) {\n      this._innerSelect(data, dataIndices);\n    }\n  };\n  // /**\n  //  * @see {module:echarts/stream/Scheduler}\n  //  */\n  // abstract pipeTask: null\n  SeriesModel.registerClass = function (clz) {\n    return ComponentModel.registerClass(clz);\n  };\n  SeriesModel.protoInitialize = function () {\n    var proto = SeriesModel.prototype;\n    proto.type = 'series.__base__';\n    proto.seriesIndex = 0;\n    proto.ignoreStyleOnData = false;\n    proto.hasSymbolVisual = false;\n    proto.defaultSymbol = 'circle';\n    // Make sure the values can be accessed!\n    proto.visualStyleAccessPath = 'itemStyle';\n    proto.visualDrawType = 'fill';\n  }();\n  return SeriesModel;\n}(ComponentModel);\nzrUtil.mixin(SeriesModel, DataFormatMixin);\nzrUtil.mixin(SeriesModel, PaletteMixin);\nmountExtend(SeriesModel, ComponentModel);\n/**\r\n * MUST be called after `prepareSource` called\r\n * Here we need to make auto series, especially for auto legend. But we\r\n * do not modify series.name in option to avoid side effects.\r\n */\nfunction autoSeriesName(seriesModel) {\n  // User specified name has higher priority, otherwise it may cause\n  // series can not be queried unexpectedly.\n  var name = seriesModel.name;\n  if (!modelUtil.isNameSpecified(seriesModel)) {\n    seriesModel.name = getSeriesAutoName(seriesModel) || name;\n  }\n}\nfunction getSeriesAutoName(seriesModel) {\n  var data = seriesModel.getRawData();\n  var dataDims = data.mapDimensionsAll('seriesName');\n  var nameArr = [];\n  zrUtil.each(dataDims, function (dataDim) {\n    var dimInfo = data.getDimensionInfo(dataDim);\n    dimInfo.displayName && nameArr.push(dimInfo.displayName);\n  });\n  return nameArr.join(' ');\n}\nfunction dataTaskCount(context) {\n  return context.model.getRawData().count();\n}\nfunction dataTaskReset(context) {\n  var seriesModel = context.model;\n  seriesModel.setData(seriesModel.getRawData().cloneShallow());\n  return dataTaskProgress;\n}\nfunction dataTaskProgress(param, context) {\n  // Avoid repeat cloneShallow when data just created in reset.\n  if (context.outputData && param.end > context.outputData.count()) {\n    context.model.getRawData().cloneShallow(context.outputData);\n  }\n}\n// TODO refactor\nfunction wrapData(data, seriesModel) {\n  zrUtil.each(zrUtil.concatArray(data.CHANGABLE_METHODS, data.DOWNSAMPLE_METHODS), function (methodName) {\n    data.wrapMethod(methodName, zrUtil.curry(onDataChange, seriesModel));\n  });\n}\nfunction onDataChange(seriesModel, newList) {\n  var task = getCurrentTask(seriesModel);\n  if (task) {\n    // Consider case: filter, selectRange\n    task.setOutputEnd((newList || this).count());\n  }\n  return newList;\n}\nfunction getCurrentTask(seriesModel) {\n  var scheduler = (seriesModel.ecModel || {}).scheduler;\n  var pipeline = scheduler && scheduler.getPipeline(seriesModel.uid);\n  if (pipeline) {\n    // When pipline finished, the currrentTask keep the last\n    // task (renderTask).\n    var task = pipeline.currentTask;\n    if (task) {\n      var agentStubMap = task.agentStubMap;\n      if (agentStubMap) {\n        task = agentStubMap.get(seriesModel.uid);\n      }\n    }\n    return task;\n  }\n}\nexport default SeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}