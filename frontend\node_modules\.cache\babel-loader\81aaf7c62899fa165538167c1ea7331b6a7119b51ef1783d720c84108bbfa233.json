{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { curry, each, hasOwn } from 'zrender/lib/core/util.js';\nfunction legendSelectActionHandler(methodName, payload, ecModel) {\n  var isAllSelect = methodName === 'allSelect' || methodName === 'inverseSelect';\n  var selectedMap = {};\n  var actionLegendIndices = [];\n  ecModel.eachComponent({\n    mainType: 'legend',\n    query: payload\n  }, function (legendModel) {\n    if (isAllSelect) {\n      legendModel[methodName]();\n    } else {\n      legendModel[methodName](payload.name);\n    }\n    makeSelectedMap(legendModel, selectedMap);\n    actionLegendIndices.push(legendModel.componentIndex);\n  });\n  var allSelectedMap = {};\n  // make selectedMap from all legend components\n  ecModel.eachComponent('legend', function (legendModel) {\n    each(selectedMap, function (isSelected, name) {\n      // Force other legend has same selected status\n      // Or the first is toggled to true and other are toggled to false\n      // In the case one legend has some item unSelected in option. And if other legend\n      // doesn't has the item, they will assume it is selected.\n      legendModel[isSelected ? 'select' : 'unSelect'](name);\n    });\n    makeSelectedMap(legendModel, allSelectedMap);\n  });\n  // Return the event explicitly\n  return isAllSelect ? {\n    selected: allSelectedMap,\n    // return legendIndex array to tell the developers which legends are allSelect / inverseSelect\n    legendIndex: actionLegendIndices\n  } : {\n    name: payload.name,\n    selected: allSelectedMap\n  };\n}\nfunction makeSelectedMap(legendModel, out) {\n  var selectedMap = out || {};\n  each(legendModel.getData(), function (model) {\n    var name = model.get('name');\n    // Wrap element\n    if (name === '\\n' || name === '') {\n      return;\n    }\n    var isItemSelected = legendModel.isSelected(name);\n    if (hasOwn(selectedMap, name)) {\n      // Unselected if any legend is unselected\n      selectedMap[name] = selectedMap[name] && isItemSelected;\n    } else {\n      selectedMap[name] = isItemSelected;\n    }\n  });\n  return selectedMap;\n}\nexport function installLegendAction(registers) {\n  /**\r\n   * @event legendToggleSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendToggleSelect'\r\n   * @property {string} [from]\r\n   * @property {string} name Series name or data item name\r\n   */\n  registers.registerAction('legendToggleSelect', 'legendselectchanged', curry(legendSelectActionHandler, 'toggleSelected'));\n  registers.registerAction('legendAllSelect', 'legendselectall', curry(legendSelectActionHandler, 'allSelect'));\n  registers.registerAction('legendInverseSelect', 'legendinverseselect', curry(legendSelectActionHandler, 'inverseSelect'));\n  /**\r\n   * @event legendSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendSelect'\r\n   * @property {string} name Series name or data item name\r\n   */\n  registers.registerAction('legendSelect', 'legendselected', curry(legendSelectActionHandler, 'select'));\n  /**\r\n   * @event legendUnSelect\r\n   * @type {Object}\r\n   * @property {string} type 'legendUnSelect'\r\n   * @property {string} name Series name or data item name\r\n   */\n  registers.registerAction('legendUnSelect', 'legendunselected', curry(legendSelectActionHandler, 'unSelect'));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}