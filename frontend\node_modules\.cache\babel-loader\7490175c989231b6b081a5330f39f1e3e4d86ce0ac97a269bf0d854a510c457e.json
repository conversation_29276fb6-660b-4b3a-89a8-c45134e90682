{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nimport * as roundSectorHelper from '../helper/roundSector.js';\nvar SectorShape = function () {\n  function SectorShape() {\n    this.cx = 0;\n    this.cy = 0;\n    this.r0 = 0;\n    this.r = 0;\n    this.startAngle = 0;\n    this.endAngle = Math.PI * 2;\n    this.clockwise = true;\n    this.cornerRadius = 0;\n  }\n  return SectorShape;\n}();\nexport { SectorShape };\nvar Sector = function (_super) {\n  __extends(Sector, _super);\n  function Sector(opts) {\n    return _super.call(this, opts) || this;\n  }\n  Sector.prototype.getDefaultShape = function () {\n    return new SectorShape();\n  };\n  Sector.prototype.buildPath = function (ctx, shape) {\n    roundSectorHelper.buildPath(ctx, shape);\n  };\n  Sector.prototype.isZeroArea = function () {\n    return this.shape.startAngle === this.shape.endAngle || this.shape.r === this.shape.r0;\n  };\n  return Sector;\n}(Path);\nSector.prototype.type = 'sector';\nexport default Sector;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}