{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { extend, isString } from 'zrender/lib/core/util.js';\nexport default function categoryVisual(ecModel) {\n  var paletteScope = {};\n  ecModel.eachSeriesByType('graph', function (seriesModel) {\n    var categoriesData = seriesModel.getCategoriesData();\n    var data = seriesModel.getData();\n    var categoryNameIdxMap = {};\n    categoriesData.each(function (idx) {\n      var name = categoriesData.getName(idx);\n      // Add prefix to avoid conflict with Object.prototype.\n      categoryNameIdxMap['ec-' + name] = idx;\n      var itemModel = categoriesData.getItemModel(idx);\n      var style = itemModel.getModel('itemStyle').getItemStyle();\n      if (!style.fill) {\n        // Get color from palette.\n        style.fill = seriesModel.getColorFromPalette(name, paletteScope);\n      }\n      categoriesData.setItemVisual(idx, 'style', style);\n      var symbolVisualList = ['symbol', 'symbolSize', 'symbolKeepAspect'];\n      for (var i = 0; i < symbolVisualList.length; i++) {\n        var symbolVisual = itemModel.getShallow(symbolVisualList[i], true);\n        if (symbolVisual != null) {\n          categoriesData.setItemVisual(idx, symbolVisualList[i], symbolVisual);\n        }\n      }\n    });\n    // Assign category color to visual\n    if (categoriesData.count()) {\n      data.each(function (idx) {\n        var model = data.getItemModel(idx);\n        var categoryIdx = model.getShallow('category');\n        if (categoryIdx != null) {\n          if (isString(categoryIdx)) {\n            categoryIdx = categoryNameIdxMap['ec-' + categoryIdx];\n          }\n          var categoryStyle = categoriesData.getItemVisual(categoryIdx, 'style');\n          var style = data.ensureUniqueItemVisual(idx, 'style');\n          extend(style, categoryStyle);\n          var visualList = ['symbol', 'symbolSize', 'symbolKeepAspect'];\n          for (var i = 0; i < visualList.length; i++) {\n            data.setItemVisual(idx, visualList[i], categoriesData.getItemVisual(categoryIdx, visualList[i]));\n          }\n        }\n      });\n    }\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}