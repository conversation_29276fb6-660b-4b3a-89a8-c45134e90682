{"ast": null, "code": "import * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport MeasureCell from \"./MeasureCell\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nexport default function MeasureRow(_ref) {\n  var prefixCls = _ref.prefixCls,\n    columnsKey = _ref.columnsKey,\n    onColumnResize = _ref.onColumnResize;\n  var ref = React.useRef(null);\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    \"aria-hidden\": \"true\",\n    className: \"\".concat(prefixCls, \"-measure-row\"),\n    style: {\n      height: 0,\n      fontSize: 0\n    },\n    ref: ref\n  }, /*#__PURE__*/React.createElement(ResizeObserver.Collection, {\n    onBatchResize: function onBatchResize(infoList) {\n      if (isVisible(ref.current)) {\n        infoList.forEach(function (_ref2) {\n          var columnKey = _ref2.data,\n            size = _ref2.size;\n          onColumnResize(columnKey, size.offsetWidth);\n        });\n      }\n    }\n  }, columnsKey.map(function (columnKey) {\n    return /*#__PURE__*/React.createElement(MeasureCell, {\n      key: columnKey,\n      columnKey: columnKey,\n      onColumnResize: onColumnResize\n    });\n  })));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}