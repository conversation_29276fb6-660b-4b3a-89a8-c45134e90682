"""
实时数据更新系统测试
"""
import asyncio
import json
import logging
from datetime import datetime
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.tasks.scheduler import get_scheduler
from app.tasks.data_tasks import get_data_tasks, register_all_tasks
from app.websocket.connection_manager import get_connection_manager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockWebSocket:
    """模拟WebSocket连接"""
    
    def __init__(self, client_id: str):
        self.client_id = client_id
        self.messages = []
        self.connected = True
    
    async def accept(self):
        """接受连接"""
        pass
    
    async def send_text(self, data: str):
        """发送文本消息"""
        if self.connected:
            message = json.loads(data)
            self.messages.append({
                'timestamp': datetime.now().isoformat(),
                'data': message
            })
            logger.info(f"[{self.client_id}] 收到消息: {message.get('type', 'unknown')}")
    
    def disconnect(self):
        """断开连接"""
        self.connected = False

async def test_scheduler():
    """测试任务调度器"""
    print("=== 测试任务调度器 ===")
    
    scheduler = get_scheduler()
    
    # 添加测试任务
    call_count = 0
    
    def test_task():
        nonlocal call_count
        call_count += 1
        logger.info(f"测试任务执行第 {call_count} 次")
    
    scheduler.add_task("test_task", test_task, interval=2)
    
    # 运行5秒
    start_time = asyncio.get_event_loop().time()
    task = asyncio.create_task(scheduler.start())
    
    await asyncio.sleep(5)
    scheduler.stop()
    
    try:
        await asyncio.wait_for(task, timeout=1.0)
    except asyncio.TimeoutError:
        pass
    
    print(f"✅ 调度器测试完成，任务执行了 {call_count} 次")
    return call_count > 0

async def test_websocket_manager():
    """测试WebSocket连接管理"""
    print("\n=== 测试WebSocket连接管理 ===")
    
    manager = get_connection_manager()
    
    # 创建模拟连接
    ws1 = MockWebSocket("client1")
    ws2 = MockWebSocket("client2")
    
    # 连接客户端
    await manager.connect(ws1, "client1")
    await manager.connect(ws2, "client2")
    
    # 订阅数据
    await manager.subscribe(ws1, ["price_data", "signals"])
    await manager.subscribe(ws2, ["market_data"])
    
    # 广播消息
    await manager.broadcast({
        'type': 'test_message',
        'content': 'Hello WebSocket'
    }, 'price_data')
    
    # 检查消息接收
    client1_messages = len(ws1.messages)
    client2_messages = len(ws2.messages)
    
    # 断开连接
    manager.disconnect(ws1)
    manager.disconnect(ws2)
    
    # 获取连接统计
    stats = manager.get_connection_stats()
    
    print(f"✅ WebSocket管理器测试完成")
    print(f"   Client1 收到消息: {client1_messages}")
    print(f"   Client2 收到消息: {client2_messages}")
    print(f"   最终连接数: {stats['total_connections']}")
    
    return client1_messages > 0

async def test_data_tasks():
    """测试数据任务"""
    print("\n=== 测试数据任务 ===")
    
    data_tasks = get_data_tasks()
    
    # 创建模拟WebSocket连接
    manager = get_connection_manager()
    ws = MockWebSocket("test_client")
    await manager.connect(ws, "test_client")
    await manager.subscribe(ws, ["price_data", "signals", "market_data", "risk_alerts"])
    
    try:
        # 测试实时数据更新
        print("测试实时数据更新...")
        await data_tasks.update_realtime_data()
        
        # 测试技术指标计算
        print("测试技术指标计算...")
        await data_tasks.calculate_indicators()
        
        # 测试风险评估
        print("测试风险评估...")
        await data_tasks.perform_risk_assessment()
        
        # 测试系统健康检查
        print("测试系统健康检查...")
        await data_tasks.system_health_check()
        
        # 检查收到的消息
        message_count = len(ws.messages)
        message_types = [msg['data'].get('type') for msg in ws.messages]
        
        print(f"✅ 数据任务测试完成")
        print(f"   收到消息数量: {message_count}")
        print(f"   消息类型: {set(message_types)}")
        
        return message_count > 0
        
    finally:
        manager.disconnect(ws)

async def test_integrated_system():
    """测试集成系统"""
    print("\n=== 测试集成系统 ===")
    
    # 注册所有任务
    register_all_tasks()
    
    # 创建WebSocket连接
    manager = get_connection_manager()
    ws = MockWebSocket("integrated_test")
    await manager.connect(ws, "integrated_test")
    await manager.subscribe(ws, ["price_data", "signals", "market_data", "risk_alerts"])
    
    # 启动调度器
    scheduler = get_scheduler()
    scheduler_task = asyncio.create_task(scheduler.start())
    
    try:
        # 运行10秒
        print("运行集成系统 10 秒...")
        await asyncio.sleep(10)
        
        # 检查结果
        message_count = len(ws.messages)
        task_status = scheduler.get_task_status()
        
        print(f"✅ 集成系统测试完成")
        print(f"   收到消息数量: {message_count}")
        print(f"   注册任务数量: {len(task_status)}")
        print(f"   启用任务数量: {len([t for t in task_status if t['enabled']])}")
        
        return message_count > 0 and len(task_status) > 0
        
    finally:
        scheduler.stop()
        manager.disconnect(ws)
        
        try:
            await asyncio.wait_for(scheduler_task, timeout=1.0)
        except asyncio.TimeoutError:
            pass

async def main():
    """主测试函数"""
    print("🚀 开始实时数据更新系统测试")
    print("=" * 60)
    
    tests = [
        ("任务调度器", test_scheduler),
        ("WebSocket管理器", test_websocket_manager),
        ("数据任务", test_data_tasks),
        ("集成系统", test_integrated_system)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
            print(f"✅ {test_name} 测试完成")
        except Exception as e:
            print(f"❌ {test_name} 测试失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count >= 3:
        print("\n🎉 实时数据更新系统测试通过！")
        print("💡 功能特点:")
        print("- 定时任务调度")
        print("- WebSocket实时通信")
        print("- 自动数据更新")
        print("- 系统健康监控")
    else:
        print("\n⚠️  部分测试失败，请检查系统配置")

if __name__ == "__main__":
    asyncio.run(main())
