"""
Tushare API权限验证脚本
"""
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
import time

# 配置token
TUSHARE_TOKEN = "772e043e246ef24189447f73f0c276e7fc98f18a0cb7cce72bd0f28d"
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api()

def test_basic_connection():
    """测试基础连接"""
    print("=== 测试基础连接 ===")
    try:
        # 获取交易日历
        cal = pro.trade_cal(exchange='SSE', start_date='20241201', end_date='20241210')
        print(f"✅ 基础连接成功，获取到 {len(cal)} 条交易日历数据")
        return True
    except Exception as e:
        print(f"❌ 基础连接失败: {e}")
        return False

def test_a_stock_data():
    """测试A股数据获取"""
    print("\n=== 测试A股数据获取 ===")
    try:
        # 获取股票基本信息
        stock_basic = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,list_date')
        print(f"✅ A股基本信息获取成功，共 {len(stock_basic)} 只股票")
        
        # 测试获取具体股票的日线数据
        test_stock = '000001.SZ'  # 平安银行
        daily_data = pro.daily(ts_code=test_stock, start_date='20241201', end_date='20241210')
        print(f"✅ A股日线数据获取成功，{test_stock} 获取到 {len(daily_data)} 条数据")
        
        # 测试实时数据
        realtime_data = pro.realtime_quote(ts_code=test_stock)
        print(f"✅ A股实时数据获取成功，{test_stock} 当前价格相关数据")
        
        return True
    except Exception as e:
        print(f"❌ A股数据获取失败: {e}")
        return False

def test_hk_stock_data():
    """测试港股数据获取"""
    print("\n=== 测试港股数据获取 ===")
    try:
        # 获取港股基本信息
        hk_basic = pro.hk_basic(list_status='L')
        print(f"✅ 港股基本信息获取成功，共 {len(hk_basic)} 只股票")
        
        # 测试获取具体港股的日线数据
        test_hk_stock = '00700.HK'  # 腾讯控股
        hk_daily = pro.hk_daily(ts_code=test_hk_stock, start_date='20241201', end_date='20241210')
        print(f"✅ 港股日线数据获取成功，{test_hk_stock} 获取到 {len(hk_daily)} 条数据")
        
        return True
    except Exception as e:
        print(f"❌ 港股数据获取失败: {e}")
        print("注意：港股数据可能需要更高级别的权限或额外的积分")
        return False

def test_fundamental_data():
    """测试基本面数据获取"""
    print("\n=== 测试基本面数据获取 ===")
    try:
        # 测试财务数据
        test_stock = '000001.SZ'
        income = pro.income(ts_code=test_stock, start_date='20230101', end_date='20231231')
        print(f"✅ 利润表数据获取成功，{test_stock} 获取到 {len(income)} 条数据")
        
        # 测试估值数据
        valuation = pro.daily_basic(ts_code=test_stock, start_date='20241201', end_date='20241210')
        print(f"✅ 估值数据获取成功，{test_stock} 获取到 {len(valuation)} 条数据")
        
        return True
    except Exception as e:
        print(f"❌ 基本面数据获取失败: {e}")
        return False

def test_api_limits():
    """测试API调用限制"""
    print("\n=== 测试API调用限制 ===")
    try:
        start_time = time.time()
        call_count = 0
        
        # 连续调用API测试限制
        for i in range(5):
            pro.trade_cal(exchange='SSE', start_date='20241201', end_date='20241202')
            call_count += 1
            time.sleep(0.1)  # 短暂延迟
        
        end_time = time.time()
        print(f"✅ 连续调用 {call_count} 次API成功，耗时 {end_time - start_time:.2f} 秒")
        print("💡 建议：实际使用时应控制调用频率，避免触及限制")
        
        return True
    except Exception as e:
        print(f"❌ API调用限制测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始Tushare API权限验证")
    print(f"Token: {TUSHARE_TOKEN[:20]}...")
    
    results = []
    
    # 执行各项测试
    results.append(("基础连接", test_basic_connection()))
    results.append(("A股数据", test_a_stock_data()))
    results.append(("港股数据", test_hk_stock_data()))
    results.append(("基本面数据", test_fundamental_data()))
    results.append(("API限制", test_api_limits()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count >= 3:
        print("🎉 Token权限验证基本通过，可以开始开发！")
    else:
        print("⚠️  Token权限可能有限制，建议检查权限或联系Tushare客服")

if __name__ == "__main__":
    main()
