{"ast": null, "code": "import { cubicSubdivide } from '../core/curve.js';\nimport Path from '../graphic/Path.js';\nimport { defaults, map } from '../core/util.js';\nimport { lerp } from '../core/vector.js';\nimport { clonePath } from './path.js';\nimport Transformable from '../core/Transformable.js';\nimport { split } from './dividePath.js';\nimport { pathToBezierCurves } from './convertPath.js';\nfunction alignSubpath(subpath1, subpath2) {\n  var len1 = subpath1.length;\n  var len2 = subpath2.length;\n  if (len1 === len2) {\n    return [subpath1, subpath2];\n  }\n  var tmpSegX = [];\n  var tmpSegY = [];\n  var shorterPath = len1 < len2 ? subpath1 : subpath2;\n  var shorterLen = Math.min(len1, len2);\n  var diff = Math.abs(len2 - len1) / 6;\n  var shorterBezierCount = (shorterLen - 2) / 6;\n  var eachCurveSubDivCount = Math.ceil(diff / shorterBezierCount) + 1;\n  var newSubpath = [shorterPath[0], shorterPath[1]];\n  var remained = diff;\n  for (var i = 2; i < shorterLen;) {\n    var x0 = shorterPath[i - 2];\n    var y0 = shorterPath[i - 1];\n    var x1 = shorterPath[i++];\n    var y1 = shorterPath[i++];\n    var x2 = shorterPath[i++];\n    var y2 = shorterPath[i++];\n    var x3 = shorterPath[i++];\n    var y3 = shorterPath[i++];\n    if (remained <= 0) {\n      newSubpath.push(x1, y1, x2, y2, x3, y3);\n      continue;\n    }\n    var actualSubDivCount = Math.min(remained, eachCurveSubDivCount - 1) + 1;\n    for (var k = 1; k <= actualSubDivCount; k++) {\n      var p = k / actualSubDivCount;\n      cubicSubdivide(x0, x1, x2, x3, p, tmpSegX);\n      cubicSubdivide(y0, y1, y2, y3, p, tmpSegY);\n      x0 = tmpSegX[3];\n      y0 = tmpSegY[3];\n      newSubpath.push(tmpSegX[1], tmpSegY[1], tmpSegX[2], tmpSegY[2], x0, y0);\n      x1 = tmpSegX[5];\n      y1 = tmpSegY[5];\n      x2 = tmpSegX[6];\n      y2 = tmpSegY[6];\n    }\n    remained -= actualSubDivCount - 1;\n  }\n  return shorterPath === subpath1 ? [newSubpath, subpath2] : [subpath1, newSubpath];\n}\nfunction createSubpath(lastSubpathSubpath, otherSubpath) {\n  var len = lastSubpathSubpath.length;\n  var lastX = lastSubpathSubpath[len - 2];\n  var lastY = lastSubpathSubpath[len - 1];\n  var newSubpath = [];\n  for (var i = 0; i < otherSubpath.length;) {\n    newSubpath[i++] = lastX;\n    newSubpath[i++] = lastY;\n  }\n  return newSubpath;\n}\nexport function alignBezierCurves(array1, array2) {\n  var _a;\n  var lastSubpath1;\n  var lastSubpath2;\n  var newArray1 = [];\n  var newArray2 = [];\n  for (var i = 0; i < Math.max(array1.length, array2.length); i++) {\n    var subpath1 = array1[i];\n    var subpath2 = array2[i];\n    var newSubpath1 = void 0;\n    var newSubpath2 = void 0;\n    if (!subpath1) {\n      newSubpath1 = createSubpath(lastSubpath1 || subpath2, subpath2);\n      newSubpath2 = subpath2;\n    } else if (!subpath2) {\n      newSubpath2 = createSubpath(lastSubpath2 || subpath1, subpath1);\n      newSubpath1 = subpath1;\n    } else {\n      _a = alignSubpath(subpath1, subpath2), newSubpath1 = _a[0], newSubpath2 = _a[1];\n      lastSubpath1 = newSubpath1;\n      lastSubpath2 = newSubpath2;\n    }\n    newArray1.push(newSubpath1);\n    newArray2.push(newSubpath2);\n  }\n  return [newArray1, newArray2];\n}\nexport function centroid(array) {\n  var signedArea = 0;\n  var cx = 0;\n  var cy = 0;\n  var len = array.length;\n  for (var i = 0, j = len - 2; i < len; j = i, i += 2) {\n    var x0 = array[j];\n    var y0 = array[j + 1];\n    var x1 = array[i];\n    var y1 = array[i + 1];\n    var a = x0 * y1 - x1 * y0;\n    signedArea += a;\n    cx += (x0 + x1) * a;\n    cy += (y0 + y1) * a;\n  }\n  if (signedArea === 0) {\n    return [array[0] || 0, array[1] || 0];\n  }\n  return [cx / signedArea / 3, cy / signedArea / 3, signedArea];\n}\nfunction findBestRingOffset(fromSubBeziers, toSubBeziers, fromCp, toCp) {\n  var bezierCount = (fromSubBeziers.length - 2) / 6;\n  var bestScore = Infinity;\n  var bestOffset = 0;\n  var len = fromSubBeziers.length;\n  var len2 = len - 2;\n  for (var offset = 0; offset < bezierCount; offset++) {\n    var cursorOffset = offset * 6;\n    var score = 0;\n    for (var k = 0; k < len; k += 2) {\n      var idx = k === 0 ? cursorOffset : (cursorOffset + k - 2) % len2 + 2;\n      var x0 = fromSubBeziers[idx] - fromCp[0];\n      var y0 = fromSubBeziers[idx + 1] - fromCp[1];\n      var x1 = toSubBeziers[k] - toCp[0];\n      var y1 = toSubBeziers[k + 1] - toCp[1];\n      var dx = x1 - x0;\n      var dy = y1 - y0;\n      score += dx * dx + dy * dy;\n    }\n    if (score < bestScore) {\n      bestScore = score;\n      bestOffset = offset;\n    }\n  }\n  return bestOffset;\n}\nfunction reverse(array) {\n  var newArr = [];\n  var len = array.length;\n  for (var i = 0; i < len; i += 2) {\n    newArr[i] = array[len - i - 2];\n    newArr[i + 1] = array[len - i - 1];\n  }\n  return newArr;\n}\nfunction findBestMorphingRotation(fromArr, toArr, searchAngleIteration, searchAngleRange) {\n  var result = [];\n  var fromNeedsReverse;\n  for (var i = 0; i < fromArr.length; i++) {\n    var fromSubpathBezier = fromArr[i];\n    var toSubpathBezier = toArr[i];\n    var fromCp = centroid(fromSubpathBezier);\n    var toCp = centroid(toSubpathBezier);\n    if (fromNeedsReverse == null) {\n      fromNeedsReverse = fromCp[2] < 0 !== toCp[2] < 0;\n    }\n    var newFromSubpathBezier = [];\n    var newToSubpathBezier = [];\n    var bestAngle = 0;\n    var bestScore = Infinity;\n    var tmpArr = [];\n    var len = fromSubpathBezier.length;\n    if (fromNeedsReverse) {\n      fromSubpathBezier = reverse(fromSubpathBezier);\n    }\n    var offset = findBestRingOffset(fromSubpathBezier, toSubpathBezier, fromCp, toCp) * 6;\n    var len2 = len - 2;\n    for (var k = 0; k < len2; k += 2) {\n      var idx = (offset + k) % len2 + 2;\n      newFromSubpathBezier[k + 2] = fromSubpathBezier[idx] - fromCp[0];\n      newFromSubpathBezier[k + 3] = fromSubpathBezier[idx + 1] - fromCp[1];\n    }\n    newFromSubpathBezier[0] = fromSubpathBezier[offset] - fromCp[0];\n    newFromSubpathBezier[1] = fromSubpathBezier[offset + 1] - fromCp[1];\n    if (searchAngleIteration > 0) {\n      var step = searchAngleRange / searchAngleIteration;\n      for (var angle = -searchAngleRange / 2; angle <= searchAngleRange / 2; angle += step) {\n        var sa = Math.sin(angle);\n        var ca = Math.cos(angle);\n        var score = 0;\n        for (var k = 0; k < fromSubpathBezier.length; k += 2) {\n          var x0 = newFromSubpathBezier[k];\n          var y0 = newFromSubpathBezier[k + 1];\n          var x1 = toSubpathBezier[k] - toCp[0];\n          var y1 = toSubpathBezier[k + 1] - toCp[1];\n          var newX1 = x1 * ca - y1 * sa;\n          var newY1 = x1 * sa + y1 * ca;\n          tmpArr[k] = newX1;\n          tmpArr[k + 1] = newY1;\n          var dx = newX1 - x0;\n          var dy = newY1 - y0;\n          score += dx * dx + dy * dy;\n        }\n        if (score < bestScore) {\n          bestScore = score;\n          bestAngle = angle;\n          for (var m = 0; m < tmpArr.length; m++) {\n            newToSubpathBezier[m] = tmpArr[m];\n          }\n        }\n      }\n    } else {\n      for (var i_1 = 0; i_1 < len; i_1 += 2) {\n        newToSubpathBezier[i_1] = toSubpathBezier[i_1] - toCp[0];\n        newToSubpathBezier[i_1 + 1] = toSubpathBezier[i_1 + 1] - toCp[1];\n      }\n    }\n    result.push({\n      from: newFromSubpathBezier,\n      to: newToSubpathBezier,\n      fromCp: fromCp,\n      toCp: toCp,\n      rotation: -bestAngle\n    });\n  }\n  return result;\n}\nexport function isCombineMorphing(path) {\n  return path.__isCombineMorphing;\n}\nexport function isMorphing(el) {\n  return el.__morphT >= 0;\n}\nvar SAVED_METHOD_PREFIX = '__mOriginal_';\nfunction saveAndModifyMethod(obj, methodName, modifiers) {\n  var savedMethodName = SAVED_METHOD_PREFIX + methodName;\n  var originalMethod = obj[savedMethodName] || obj[methodName];\n  if (!obj[savedMethodName]) {\n    obj[savedMethodName] = obj[methodName];\n  }\n  var replace = modifiers.replace;\n  var after = modifiers.after;\n  var before = modifiers.before;\n  obj[methodName] = function () {\n    var args = arguments;\n    var res;\n    before && before.apply(this, args);\n    if (replace) {\n      res = replace.apply(this, args);\n    } else {\n      res = originalMethod.apply(this, args);\n    }\n    after && after.apply(this, args);\n    return res;\n  };\n}\nfunction restoreMethod(obj, methodName) {\n  var savedMethodName = SAVED_METHOD_PREFIX + methodName;\n  if (obj[savedMethodName]) {\n    obj[methodName] = obj[savedMethodName];\n    obj[savedMethodName] = null;\n  }\n}\nfunction applyTransformOnBeziers(bezierCurves, mm) {\n  for (var i = 0; i < bezierCurves.length; i++) {\n    var subBeziers = bezierCurves[i];\n    for (var k = 0; k < subBeziers.length;) {\n      var x = subBeziers[k];\n      var y = subBeziers[k + 1];\n      subBeziers[k++] = mm[0] * x + mm[2] * y + mm[4];\n      subBeziers[k++] = mm[1] * x + mm[3] * y + mm[5];\n    }\n  }\n}\nfunction prepareMorphPath(fromPath, toPath) {\n  var fromPathProxy = fromPath.getUpdatedPathProxy();\n  var toPathProxy = toPath.getUpdatedPathProxy();\n  var _a = alignBezierCurves(pathToBezierCurves(fromPathProxy), pathToBezierCurves(toPathProxy)),\n    fromBezierCurves = _a[0],\n    toBezierCurves = _a[1];\n  var fromPathTransform = fromPath.getComputedTransform();\n  var toPathTransform = toPath.getComputedTransform();\n  function updateIdentityTransform() {\n    this.transform = null;\n  }\n  fromPathTransform && applyTransformOnBeziers(fromBezierCurves, fromPathTransform);\n  toPathTransform && applyTransformOnBeziers(toBezierCurves, toPathTransform);\n  saveAndModifyMethod(toPath, 'updateTransform', {\n    replace: updateIdentityTransform\n  });\n  toPath.transform = null;\n  var morphingData = findBestMorphingRotation(fromBezierCurves, toBezierCurves, 10, Math.PI);\n  var tmpArr = [];\n  saveAndModifyMethod(toPath, 'buildPath', {\n    replace: function (path) {\n      var t = toPath.__morphT;\n      var onet = 1 - t;\n      var newCp = [];\n      for (var i = 0; i < morphingData.length; i++) {\n        var item = morphingData[i];\n        var from = item.from;\n        var to = item.to;\n        var angle = item.rotation * t;\n        var fromCp = item.fromCp;\n        var toCp = item.toCp;\n        var sa = Math.sin(angle);\n        var ca = Math.cos(angle);\n        lerp(newCp, fromCp, toCp, t);\n        for (var m = 0; m < from.length; m += 2) {\n          var x0_1 = from[m];\n          var y0_1 = from[m + 1];\n          var x1 = to[m];\n          var y1 = to[m + 1];\n          var x = x0_1 * onet + x1 * t;\n          var y = y0_1 * onet + y1 * t;\n          tmpArr[m] = x * ca - y * sa + newCp[0];\n          tmpArr[m + 1] = x * sa + y * ca + newCp[1];\n        }\n        var x0 = tmpArr[0];\n        var y0 = tmpArr[1];\n        path.moveTo(x0, y0);\n        for (var m = 2; m < from.length;) {\n          var x1 = tmpArr[m++];\n          var y1 = tmpArr[m++];\n          var x2 = tmpArr[m++];\n          var y2 = tmpArr[m++];\n          var x3 = tmpArr[m++];\n          var y3 = tmpArr[m++];\n          if (x0 === x1 && y0 === y1 && x2 === x3 && y2 === y3) {\n            path.lineTo(x3, y3);\n          } else {\n            path.bezierCurveTo(x1, y1, x2, y2, x3, y3);\n          }\n          x0 = x3;\n          y0 = y3;\n        }\n      }\n    }\n  });\n}\nexport function morphPath(fromPath, toPath, animationOpts) {\n  if (!fromPath || !toPath) {\n    return toPath;\n  }\n  var oldDone = animationOpts.done;\n  var oldDuring = animationOpts.during;\n  prepareMorphPath(fromPath, toPath);\n  toPath.__morphT = 0;\n  function restoreToPath() {\n    restoreMethod(toPath, 'buildPath');\n    restoreMethod(toPath, 'updateTransform');\n    toPath.__morphT = -1;\n    toPath.createPathProxy();\n    toPath.dirtyShape();\n  }\n  toPath.animateTo({\n    __morphT: 1\n  }, defaults({\n    during: function (p) {\n      toPath.dirtyShape();\n      oldDuring && oldDuring(p);\n    },\n    done: function () {\n      restoreToPath();\n      oldDone && oldDone();\n    }\n  }, animationOpts));\n  return toPath;\n}\nfunction hilbert(x, y, minX, minY, maxX, maxY) {\n  var bits = 16;\n  x = maxX === minX ? 0 : Math.round(32767 * (x - minX) / (maxX - minX));\n  y = maxY === minY ? 0 : Math.round(32767 * (y - minY) / (maxY - minY));\n  var d = 0;\n  var tmp;\n  for (var s = (1 << bits) / 2; s > 0; s /= 2) {\n    var rx = 0;\n    var ry = 0;\n    if ((x & s) > 0) {\n      rx = 1;\n    }\n    if ((y & s) > 0) {\n      ry = 1;\n    }\n    d += s * s * (3 * rx ^ ry);\n    if (ry === 0) {\n      if (rx === 1) {\n        x = s - 1 - x;\n        y = s - 1 - y;\n      }\n      tmp = x;\n      x = y;\n      y = tmp;\n    }\n  }\n  return d;\n}\nfunction sortPaths(pathList) {\n  var xMin = Infinity;\n  var yMin = Infinity;\n  var xMax = -Infinity;\n  var yMax = -Infinity;\n  var cps = map(pathList, function (path) {\n    var rect = path.getBoundingRect();\n    var m = path.getComputedTransform();\n    var x = rect.x + rect.width / 2 + (m ? m[4] : 0);\n    var y = rect.y + rect.height / 2 + (m ? m[5] : 0);\n    xMin = Math.min(x, xMin);\n    yMin = Math.min(y, yMin);\n    xMax = Math.max(x, xMax);\n    yMax = Math.max(y, yMax);\n    return [x, y];\n  });\n  var items = map(cps, function (cp, idx) {\n    return {\n      cp: cp,\n      z: hilbert(cp[0], cp[1], xMin, yMin, xMax, yMax),\n      path: pathList[idx]\n    };\n  });\n  return items.sort(function (a, b) {\n    return a.z - b.z;\n  }).map(function (item) {\n    return item.path;\n  });\n}\n;\nfunction defaultDividePath(param) {\n  return split(param.path, param.count);\n}\nfunction createEmptyReturn() {\n  return {\n    fromIndividuals: [],\n    toIndividuals: [],\n    count: 0\n  };\n}\nexport function combineMorph(fromList, toPath, animationOpts) {\n  var fromPathList = [];\n  function addFromPath(fromList) {\n    for (var i = 0; i < fromList.length; i++) {\n      var from = fromList[i];\n      if (isCombineMorphing(from)) {\n        addFromPath(from.childrenRef());\n      } else if (from instanceof Path) {\n        fromPathList.push(from);\n      }\n    }\n  }\n  addFromPath(fromList);\n  var separateCount = fromPathList.length;\n  if (!separateCount) {\n    return createEmptyReturn();\n  }\n  var dividePath = animationOpts.dividePath || defaultDividePath;\n  var toSubPathList = dividePath({\n    path: toPath,\n    count: separateCount\n  });\n  if (toSubPathList.length !== separateCount) {\n    console.error('Invalid morphing: unmatched splitted path');\n    return createEmptyReturn();\n  }\n  fromPathList = sortPaths(fromPathList);\n  toSubPathList = sortPaths(toSubPathList);\n  var oldDone = animationOpts.done;\n  var oldDuring = animationOpts.during;\n  var individualDelay = animationOpts.individualDelay;\n  var identityTransform = new Transformable();\n  for (var i = 0; i < separateCount; i++) {\n    var from = fromPathList[i];\n    var to = toSubPathList[i];\n    to.parent = toPath;\n    to.copyTransform(identityTransform);\n    if (!individualDelay) {\n      prepareMorphPath(from, to);\n    }\n  }\n  toPath.__isCombineMorphing = true;\n  toPath.childrenRef = function () {\n    return toSubPathList;\n  };\n  function addToSubPathListToZr(zr) {\n    for (var i = 0; i < toSubPathList.length; i++) {\n      toSubPathList[i].addSelfToZr(zr);\n    }\n  }\n  saveAndModifyMethod(toPath, 'addSelfToZr', {\n    after: function (zr) {\n      addToSubPathListToZr(zr);\n    }\n  });\n  saveAndModifyMethod(toPath, 'removeSelfFromZr', {\n    after: function (zr) {\n      for (var i = 0; i < toSubPathList.length; i++) {\n        toSubPathList[i].removeSelfFromZr(zr);\n      }\n    }\n  });\n  function restoreToPath() {\n    toPath.__isCombineMorphing = false;\n    toPath.__morphT = -1;\n    toPath.childrenRef = null;\n    restoreMethod(toPath, 'addSelfToZr');\n    restoreMethod(toPath, 'removeSelfFromZr');\n  }\n  var toLen = toSubPathList.length;\n  if (individualDelay) {\n    var animating_1 = toLen;\n    var eachDone = function () {\n      animating_1--;\n      if (animating_1 === 0) {\n        restoreToPath();\n        oldDone && oldDone();\n      }\n    };\n    for (var i = 0; i < toLen; i++) {\n      var indivdualAnimationOpts = individualDelay ? defaults({\n        delay: (animationOpts.delay || 0) + individualDelay(i, toLen, fromPathList[i], toSubPathList[i]),\n        done: eachDone\n      }, animationOpts) : animationOpts;\n      morphPath(fromPathList[i], toSubPathList[i], indivdualAnimationOpts);\n    }\n  } else {\n    toPath.__morphT = 0;\n    toPath.animateTo({\n      __morphT: 1\n    }, defaults({\n      during: function (p) {\n        for (var i = 0; i < toLen; i++) {\n          var child = toSubPathList[i];\n          child.__morphT = toPath.__morphT;\n          child.dirtyShape();\n        }\n        oldDuring && oldDuring(p);\n      },\n      done: function () {\n        restoreToPath();\n        for (var i = 0; i < fromList.length; i++) {\n          restoreMethod(fromList[i], 'updateTransform');\n        }\n        oldDone && oldDone();\n      }\n    }, animationOpts));\n  }\n  if (toPath.__zr) {\n    addToSubPathListToZr(toPath.__zr);\n  }\n  return {\n    fromIndividuals: fromPathList,\n    toIndividuals: toSubPathList,\n    count: toLen\n  };\n}\nexport function separateMorph(fromPath, toPathList, animationOpts) {\n  var toLen = toPathList.length;\n  var fromPathList = [];\n  var dividePath = animationOpts.dividePath || defaultDividePath;\n  function addFromPath(fromList) {\n    for (var i = 0; i < fromList.length; i++) {\n      var from = fromList[i];\n      if (isCombineMorphing(from)) {\n        addFromPath(from.childrenRef());\n      } else if (from instanceof Path) {\n        fromPathList.push(from);\n      }\n    }\n  }\n  if (isCombineMorphing(fromPath)) {\n    addFromPath(fromPath.childrenRef());\n    var fromLen = fromPathList.length;\n    if (fromLen < toLen) {\n      var k = 0;\n      for (var i = fromLen; i < toLen; i++) {\n        fromPathList.push(clonePath(fromPathList[k++ % fromLen]));\n      }\n    }\n    fromPathList.length = toLen;\n  } else {\n    fromPathList = dividePath({\n      path: fromPath,\n      count: toLen\n    });\n    var fromPathTransform = fromPath.getComputedTransform();\n    for (var i = 0; i < fromPathList.length; i++) {\n      fromPathList[i].setLocalTransform(fromPathTransform);\n    }\n    if (fromPathList.length !== toLen) {\n      console.error('Invalid morphing: unmatched splitted path');\n      return createEmptyReturn();\n    }\n  }\n  fromPathList = sortPaths(fromPathList);\n  toPathList = sortPaths(toPathList);\n  var individualDelay = animationOpts.individualDelay;\n  for (var i = 0; i < toLen; i++) {\n    var indivdualAnimationOpts = individualDelay ? defaults({\n      delay: (animationOpts.delay || 0) + individualDelay(i, toLen, fromPathList[i], toPathList[i])\n    }, animationOpts) : animationOpts;\n    morphPath(fromPathList[i], toPathList[i], indivdualAnimationOpts);\n  }\n  return {\n    fromIndividuals: fromPathList,\n    toIndividuals: toPathList,\n    count: toPathList.length\n  };\n}\nexport { split as defaultDividePath };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}