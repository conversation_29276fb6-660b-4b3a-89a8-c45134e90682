{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as numberUtil from '../util/number.js';\nimport * as formatUtil from '../util/format.js';\nimport Scale from './Scale.js';\nimport * as helper from './helper.js';\nvar roundNumber = numberUtil.round;\nvar IntervalScale = /** @class */function (_super) {\n  __extends(IntervalScale, _super);\n  function IntervalScale() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'interval';\n    // Step is calculated in adjustExtent.\n    _this._interval = 0;\n    _this._intervalPrecision = 2;\n    return _this;\n  }\n  IntervalScale.prototype.parse = function (val) {\n    return val;\n  };\n  IntervalScale.prototype.contain = function (val) {\n    return helper.contain(val, this._extent);\n  };\n  IntervalScale.prototype.normalize = function (val) {\n    return helper.normalize(val, this._extent);\n  };\n  IntervalScale.prototype.scale = function (val) {\n    return helper.scale(val, this._extent);\n  };\n  IntervalScale.prototype.setExtent = function (start, end) {\n    var thisExtent = this._extent;\n    // start,end may be a Number like '25',so...\n    if (!isNaN(start)) {\n      thisExtent[0] = parseFloat(start);\n    }\n    if (!isNaN(end)) {\n      thisExtent[1] = parseFloat(end);\n    }\n  };\n  IntervalScale.prototype.unionExtent = function (other) {\n    var extent = this._extent;\n    other[0] < extent[0] && (extent[0] = other[0]);\n    other[1] > extent[1] && (extent[1] = other[1]);\n    // unionExtent may called by it's sub classes\n    this.setExtent(extent[0], extent[1]);\n  };\n  IntervalScale.prototype.getInterval = function () {\n    return this._interval;\n  };\n  IntervalScale.prototype.setInterval = function (interval) {\n    this._interval = interval;\n    // Dropped auto calculated niceExtent and use user-set extent.\n    // We assume user wants to set both interval, min, max to get a better result.\n    this._niceExtent = this._extent.slice();\n    this._intervalPrecision = helper.getIntervalPrecision(interval);\n  };\n  /**\r\n   * @param expandToNicedExtent Whether expand the ticks to niced extent.\r\n   */\n  IntervalScale.prototype.getTicks = function (expandToNicedExtent) {\n    var interval = this._interval;\n    var extent = this._extent;\n    var niceTickExtent = this._niceExtent;\n    var intervalPrecision = this._intervalPrecision;\n    var ticks = [];\n    // If interval is 0, return [];\n    if (!interval) {\n      return ticks;\n    }\n    // Consider this case: using dataZoom toolbox, zoom and zoom.\n    var safeLimit = 10000;\n    if (extent[0] < niceTickExtent[0]) {\n      if (expandToNicedExtent) {\n        ticks.push({\n          value: roundNumber(niceTickExtent[0] - interval, intervalPrecision)\n        });\n      } else {\n        ticks.push({\n          value: extent[0]\n        });\n      }\n    }\n    var tick = niceTickExtent[0];\n    while (tick <= niceTickExtent[1]) {\n      ticks.push({\n        value: tick\n      });\n      // Avoid rounding error\n      tick = roundNumber(tick + interval, intervalPrecision);\n      if (tick === ticks[ticks.length - 1].value) {\n        // Consider out of safe float point, e.g.,\n        // -3711126.9907707 + 2e-10 === -3711126.9907707\n        break;\n      }\n      if (ticks.length > safeLimit) {\n        return [];\n      }\n    }\n    // Consider this case: the last item of ticks is smaller\n    // than niceTickExtent[1] and niceTickExtent[1] === extent[1].\n    var lastNiceTick = ticks.length ? ticks[ticks.length - 1].value : niceTickExtent[1];\n    if (extent[1] > lastNiceTick) {\n      if (expandToNicedExtent) {\n        ticks.push({\n          value: roundNumber(lastNiceTick + interval, intervalPrecision)\n        });\n      } else {\n        ticks.push({\n          value: extent[1]\n        });\n      }\n    }\n    return ticks;\n  };\n  IntervalScale.prototype.getMinorTicks = function (splitNumber) {\n    var ticks = this.getTicks(true);\n    var minorTicks = [];\n    var extent = this.getExtent();\n    for (var i = 1; i < ticks.length; i++) {\n      var nextTick = ticks[i];\n      var prevTick = ticks[i - 1];\n      var count = 0;\n      var minorTicksGroup = [];\n      var interval = nextTick.value - prevTick.value;\n      var minorInterval = interval / splitNumber;\n      while (count < splitNumber - 1) {\n        var minorTick = roundNumber(prevTick.value + (count + 1) * minorInterval);\n        // For the first and last interval. The count may be less than splitNumber.\n        if (minorTick > extent[0] && minorTick < extent[1]) {\n          minorTicksGroup.push(minorTick);\n        }\n        count++;\n      }\n      minorTicks.push(minorTicksGroup);\n    }\n    return minorTicks;\n  };\n  /**\r\n   * @param opt.precision If 'auto', use nice presision.\r\n   * @param opt.pad returns 1.50 but not 1.5 if precision is 2.\r\n   */\n  IntervalScale.prototype.getLabel = function (data, opt) {\n    if (data == null) {\n      return '';\n    }\n    var precision = opt && opt.precision;\n    if (precision == null) {\n      precision = numberUtil.getPrecision(data.value) || 0;\n    } else if (precision === 'auto') {\n      // Should be more precise then tick.\n      precision = this._intervalPrecision;\n    }\n    // (1) If `precision` is set, 12.005 should be display as '12.00500'.\n    // (2) Use roundNumber (toFixed) to avoid scientific notation like '3.5e-7'.\n    var dataNum = roundNumber(data.value, precision, true);\n    return formatUtil.addCommas(dataNum);\n  };\n  /**\r\n   * @param splitNumber By default `5`.\r\n   */\n  IntervalScale.prototype.calcNiceTicks = function (splitNumber, minInterval, maxInterval) {\n    splitNumber = splitNumber || 5;\n    var extent = this._extent;\n    var span = extent[1] - extent[0];\n    if (!isFinite(span)) {\n      return;\n    }\n    // User may set axis min 0 and data are all negative\n    // FIXME If it needs to reverse ?\n    if (span < 0) {\n      span = -span;\n      extent.reverse();\n    }\n    var result = helper.intervalScaleNiceTicks(extent, splitNumber, minInterval, maxInterval);\n    this._intervalPrecision = result.intervalPrecision;\n    this._interval = result.interval;\n    this._niceExtent = result.niceTickExtent;\n  };\n  IntervalScale.prototype.calcNiceExtent = function (opt) {\n    var extent = this._extent;\n    // If extent start and end are same, expand them\n    if (extent[0] === extent[1]) {\n      if (extent[0] !== 0) {\n        // Expand extent\n        // Note that extents can be both negative. See #13154\n        var expandSize = Math.abs(extent[0]);\n        // In the fowllowing case\n        //      Axis has been fixed max 100\n        //      Plus data are all 100 and axis extent are [100, 100].\n        // Extend to the both side will cause expanded max is larger than fixed max.\n        // So only expand to the smaller side.\n        if (!opt.fixMax) {\n          extent[1] += expandSize / 2;\n          extent[0] -= expandSize / 2;\n        } else {\n          extent[0] -= expandSize / 2;\n        }\n      } else {\n        extent[1] = 1;\n      }\n    }\n    var span = extent[1] - extent[0];\n    // If there are no data and extent are [Infinity, -Infinity]\n    if (!isFinite(span)) {\n      extent[0] = 0;\n      extent[1] = 1;\n    }\n    this.calcNiceTicks(opt.splitNumber, opt.minInterval, opt.maxInterval);\n    // let extent = this._extent;\n    var interval = this._interval;\n    if (!opt.fixMin) {\n      extent[0] = roundNumber(Math.floor(extent[0] / interval) * interval);\n    }\n    if (!opt.fixMax) {\n      extent[1] = roundNumber(Math.ceil(extent[1] / interval) * interval);\n    }\n  };\n  IntervalScale.prototype.setNiceExtent = function (min, max) {\n    this._niceExtent = [min, max];\n  };\n  IntervalScale.type = 'interval';\n  return IntervalScale;\n}(Scale);\nScale.registerClass(IntervalScale);\nexport default IntervalScale;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}