{"ast": null, "code": "var _jsxFileName = \"C:\\\\Owen_Zhang\\\\\\u91CF\\u5316\\u4EA4\\u6613\\u7CFB\\u7EDF\\\\frontend\\\\src\\\\components\\\\Layout\\\\Header.tsx\";\nimport React from 'react';\nimport { Layout, Typography, Space, Badge, Button } from 'antd';\nimport { BellOutlined, UserOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header: AntHeader\n} = Layout;\nconst {\n  Text\n} = Typography;\nconst Header = () => {\n  const currentTime = new Date().toLocaleString('zh-CN');\n  return /*#__PURE__*/_jsxDEV(AntHeader, {\n    style: {\n      background: '#fff',\n      padding: '0 24px',\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      borderBottom: '1px solid #f0f0f0'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        style: {\n          fontSize: '16px'\n        },\n        children: \"\\u91CF\\u5316\\u4EA4\\u6613\\u76D1\\u63A7\\u7CFB\\u7EDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          marginLeft: '16px'\n        },\n        children: currentTime\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"text\",\n        icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 17\n        }, this),\n        onClick: () => window.location.reload(),\n        children: \"\\u5237\\u65B0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n        count: 3,\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 19\n          }, this),\n          style: {\n            fontSize: '16px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"text\",\n        icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 17\n        }, this),\n        style: {\n          fontSize: '16px'\n        },\n        children: \"\\u7528\\u6237\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "Layout", "Typography", "Space", "Badge", "<PERSON><PERSON>", "BellOutlined", "UserOutlined", "ReloadOutlined", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text", "currentTime", "Date", "toLocaleString", "style", "background", "padding", "display", "justifyContent", "alignItems", "borderBottom", "children", "strong", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "marginLeft", "size", "icon", "onClick", "window", "location", "reload", "count", "_c", "$RefreshReg$"], "sources": ["C:/<PERSON>_<PERSON>/量化交易系统/frontend/src/components/Layout/Header.tsx"], "sourcesContent": ["import React from 'react';\nimport { Layout, Typography, Space, Badge, Button } from 'antd';\nimport { BellOutlined, UserOutlined, ReloadOutlined } from '@ant-design/icons';\n\nconst { Header: AntHeader } = Layout;\nconst { Text } = Typography;\n\nconst Header: React.FC = () => {\n  const currentTime = new Date().toLocaleString('zh-CN');\n\n  return (\n    <AntHeader style={{ \n      background: '#fff', \n      padding: '0 24px',\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      borderBottom: '1px solid #f0f0f0'\n    }}>\n      <div>\n        <Text strong style={{ fontSize: '16px' }}>\n          量化交易监控系统\n        </Text>\n        <Text type=\"secondary\" style={{ marginLeft: '16px' }}>\n          {currentTime}\n        </Text>\n      </div>\n      \n      <Space size=\"middle\">\n        <Button \n          type=\"text\" \n          icon={<ReloadOutlined />}\n          onClick={() => window.location.reload()}\n        >\n          刷新\n        </Button>\n        \n        <Badge count={3} size=\"small\">\n          <Button \n            type=\"text\" \n            icon={<BellOutlined />}\n            style={{ fontSize: '16px' }}\n          />\n        </Badge>\n        \n        <Button \n          type=\"text\" \n          icon={<UserOutlined />}\n          style={{ fontSize: '16px' }}\n        >\n          用户\n        </Button>\n      </Space>\n    </AntHeader>\n  );\n};\n\nexport default Header;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAC/D,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/E,MAAM;EAAEC,MAAM,EAAEC;AAAU,CAAC,GAAGX,MAAM;AACpC,MAAM;EAAEY;AAAK,CAAC,GAAGX,UAAU;AAE3B,MAAMS,MAAgB,GAAGA,CAAA,KAAM;EAC7B,MAAMG,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC;EAEtD,oBACEN,OAAA,CAACE,SAAS;IAACK,KAAK,EAAE;MAChBC,UAAU,EAAE,MAAM;MAClBC,OAAO,EAAE,QAAQ;MACjBC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,eAAe;MAC/BC,UAAU,EAAE,QAAQ;MACpBC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,gBACAd,OAAA;MAAAc,QAAA,gBACEd,OAAA,CAACG,IAAI;QAACY,MAAM;QAACR,KAAK,EAAE;UAAES,QAAQ,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAE1C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPpB,OAAA,CAACG,IAAI;QAACkB,IAAI,EAAC,WAAW;QAACd,KAAK,EAAE;UAAEe,UAAU,EAAE;QAAO,CAAE;QAAAR,QAAA,EAClDV;MAAW;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENpB,OAAA,CAACP,KAAK;MAAC8B,IAAI,EAAC,QAAQ;MAAAT,QAAA,gBAClBd,OAAA,CAACL,MAAM;QACL0B,IAAI,EAAC,MAAM;QACXG,IAAI,eAAExB,OAAA,CAACF,cAAc;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBK,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QAAAd,QAAA,EACzC;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETpB,OAAA,CAACN,KAAK;QAACmC,KAAK,EAAE,CAAE;QAACN,IAAI,EAAC,OAAO;QAAAT,QAAA,eAC3Bd,OAAA,CAACL,MAAM;UACL0B,IAAI,EAAC,MAAM;UACXG,IAAI,eAAExB,OAAA,CAACJ,YAAY;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBb,KAAK,EAAE;YAAES,QAAQ,EAAE;UAAO;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAERpB,OAAA,CAACL,MAAM;QACL0B,IAAI,EAAC,MAAM;QACXG,IAAI,eAAExB,OAAA,CAACH,YAAY;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBb,KAAK,EAAE;UAAES,QAAQ,EAAE;QAAO,CAAE;QAAAF,QAAA,EAC7B;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACU,EAAA,GAhDI7B,MAAgB;AAkDtB,eAAeA,MAAM;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}