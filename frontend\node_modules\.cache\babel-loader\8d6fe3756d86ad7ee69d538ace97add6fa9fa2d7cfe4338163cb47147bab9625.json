{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner, normalizeToArray } from '../../util/model.js';\nvar innerColor = makeInner();\nvar innerDecal = makeInner();\nvar PaletteMixin = /** @class */function () {\n  function PaletteMixin() {}\n  PaletteMixin.prototype.getColorFromPalette = function (name, scope, requestNum) {\n    var defaultPalette = normalizeToArray(this.get('color', true));\n    var layeredPalette = this.get('colorLayer', true);\n    return getFromPalette(this, innerColor, defaultPalette, layeredPalette, name, scope, requestNum);\n  };\n  PaletteMixin.prototype.clearColorPalette = function () {\n    clearPalette(this, innerColor);\n  };\n  return PaletteMixin;\n}();\nexport function getDecalFromPalette(ecModel, name, scope, requestNum) {\n  var defaultDecals = normalizeToArray(ecModel.get(['aria', 'decal', 'decals']));\n  return getFromPalette(ecModel, innerDecal, defaultDecals, null, name, scope, requestNum);\n}\nfunction getNearestPalette(palettes, requestColorNum) {\n  var paletteNum = palettes.length;\n  // TODO palettes must be in order\n  for (var i = 0; i < paletteNum; i++) {\n    if (palettes[i].length > requestColorNum) {\n      return palettes[i];\n    }\n  }\n  return palettes[paletteNum - 1];\n}\n/**\r\n * @param name MUST NOT be null/undefined. Otherwise call this function\r\n *             twise with the same parameters will get different result.\r\n * @param scope default this.\r\n * @return Can be null/undefined\r\n */\nfunction getFromPalette(that, inner, defaultPalette, layeredPalette, name, scope, requestNum) {\n  scope = scope || that;\n  var scopeFields = inner(scope);\n  var paletteIdx = scopeFields.paletteIdx || 0;\n  var paletteNameMap = scopeFields.paletteNameMap = scopeFields.paletteNameMap || {};\n  // Use `hasOwnProperty` to avoid conflict with Object.prototype.\n  if (paletteNameMap.hasOwnProperty(name)) {\n    return paletteNameMap[name];\n  }\n  var palette = requestNum == null || !layeredPalette ? defaultPalette : getNearestPalette(layeredPalette, requestNum);\n  // In case can't find in layered color palette.\n  palette = palette || defaultPalette;\n  if (!palette || !palette.length) {\n    return;\n  }\n  var pickedPaletteItem = palette[paletteIdx];\n  if (name) {\n    paletteNameMap[name] = pickedPaletteItem;\n  }\n  scopeFields.paletteIdx = (paletteIdx + 1) % palette.length;\n  return pickedPaletteItem;\n}\nfunction clearPalette(that, inner) {\n  inner(that).paletteIdx = 0;\n  inner(that).paletteNameMap = {};\n}\nexport { PaletteMixin };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}