{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport createSeriesDataSimply from '../helper/createSeriesDataSimply.js';\nimport { defaultEmphasis } from '../../util/model.js';\nimport { makeSeriesEncodeForNameBased } from '../../data/helper/sourceHelper.js';\nimport LegendVisualProvider from '../../visual/LegendVisualProvider.js';\nimport SeriesModel from '../../model/Series.js';\nvar FunnelSeriesModel = /** @class */function (_super) {\n  __extends(FunnelSeriesModel, _super);\n  function FunnelSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = FunnelSeriesModel.type;\n    return _this;\n  }\n  FunnelSeriesModel.prototype.init = function (option) {\n    _super.prototype.init.apply(this, arguments);\n    // Enable legend selection for each data item\n    // Use a function instead of direct access because data reference may changed\n    this.legendVisualProvider = new LegendVisualProvider(zrUtil.bind(this.getData, this), zrUtil.bind(this.getRawData, this));\n    // Extend labelLine emphasis\n    this._defaultLabelLine(option);\n  };\n  FunnelSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesDataSimply(this, {\n      coordDimensions: ['value'],\n      encodeDefaulter: zrUtil.curry(makeSeriesEncodeForNameBased, this)\n    });\n  };\n  FunnelSeriesModel.prototype._defaultLabelLine = function (option) {\n    // Extend labelLine emphasis\n    defaultEmphasis(option, 'labelLine', ['show']);\n    var labelLineNormalOpt = option.labelLine;\n    var labelLineEmphasisOpt = option.emphasis.labelLine;\n    // Not show label line if `label.normal.show = false`\n    labelLineNormalOpt.show = labelLineNormalOpt.show && option.label.show;\n    labelLineEmphasisOpt.show = labelLineEmphasisOpt.show && option.emphasis.label.show;\n  };\n  // Overwrite\n  FunnelSeriesModel.prototype.getDataParams = function (dataIndex) {\n    var data = this.getData();\n    var params = _super.prototype.getDataParams.call(this, dataIndex);\n    var valueDim = data.mapDimension('value');\n    var sum = data.getSum(valueDim);\n    // Percent is 0 if sum is 0\n    params.percent = !sum ? 0 : +(data.get(valueDim, dataIndex) / sum * 100).toFixed(2);\n    params.$vars.push('percent');\n    return params;\n  };\n  FunnelSeriesModel.type = 'series.funnel';\n  FunnelSeriesModel.defaultOption = {\n    // zlevel: 0,                  // 一级层叠\n    z: 2,\n    legendHoverLink: true,\n    colorBy: 'data',\n    left: 80,\n    top: 60,\n    right: 80,\n    bottom: 60,\n    // width: {totalWidth} - left - right,\n    // height: {totalHeight} - top - bottom,\n    // 默认取数据最小最大值\n    // min: 0,\n    // max: 100,\n    minSize: '0%',\n    maxSize: '100%',\n    sort: 'descending',\n    orient: 'vertical',\n    gap: 0,\n    funnelAlign: 'center',\n    label: {\n      show: true,\n      position: 'outer'\n      // formatter: 标签文本格式器，同Tooltip.formatter，不支持异步回调\n    },\n    labelLine: {\n      show: true,\n      length: 20,\n      lineStyle: {\n        // color: 各异,\n        width: 1\n      }\n    },\n    itemStyle: {\n      // color: 各异,\n      borderColor: '#fff',\n      borderWidth: 1\n    },\n    emphasis: {\n      label: {\n        show: true\n      }\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    }\n  };\n  return FunnelSeriesModel;\n}(SeriesModel);\nexport default FunnelSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}