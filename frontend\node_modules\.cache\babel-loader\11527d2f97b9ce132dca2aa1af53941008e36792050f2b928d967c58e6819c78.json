{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport ZRText from 'zrender/lib/graphic/Text.js';\nimport { isFunction, retrieve2, extend, keys, trim } from 'zrender/lib/core/util.js';\nimport { SPECIAL_STATES, DISPLAY_STATES } from '../util/states.js';\nimport { deprecateReplaceLog } from '../util/log.js';\nimport { makeInner, interpolateRawValues } from '../util/model.js';\nimport { initProps, updateProps } from '../util/graphic.js';\nvar EMPTY_OBJ = {};\nexport function setLabelText(label, labelTexts) {\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    var stateName = SPECIAL_STATES[i];\n    var text = labelTexts[stateName];\n    var state = label.ensureState(stateName);\n    state.style = state.style || {};\n    state.style.text = text;\n  }\n  var oldStates = label.currentStates.slice();\n  label.clearStates(true);\n  label.setStyle({\n    text: labelTexts.normal\n  });\n  label.useStates(oldStates, true);\n}\nfunction getLabelText(opt, stateModels, interpolatedValue) {\n  var labelFetcher = opt.labelFetcher;\n  var labelDataIndex = opt.labelDataIndex;\n  var labelDimIndex = opt.labelDimIndex;\n  var normalModel = stateModels.normal;\n  var baseText;\n  if (labelFetcher) {\n    baseText = labelFetcher.getFormattedLabel(labelDataIndex, 'normal', null, labelDimIndex, normalModel && normalModel.get('formatter'), interpolatedValue != null ? {\n      interpolatedValue: interpolatedValue\n    } : null);\n  }\n  if (baseText == null) {\n    baseText = isFunction(opt.defaultText) ? opt.defaultText(labelDataIndex, opt, interpolatedValue) : opt.defaultText;\n  }\n  var statesText = {\n    normal: baseText\n  };\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    var stateName = SPECIAL_STATES[i];\n    var stateModel = stateModels[stateName];\n    statesText[stateName] = retrieve2(labelFetcher ? labelFetcher.getFormattedLabel(labelDataIndex, stateName, null, labelDimIndex, stateModel && stateModel.get('formatter')) : null, baseText);\n  }\n  return statesText;\n}\nfunction setLabelStyle(targetEl, labelStatesModels, opt, stateSpecified\n// TODO specified position?\n) {\n  opt = opt || EMPTY_OBJ;\n  var isSetOnText = targetEl instanceof ZRText;\n  var needsCreateText = false;\n  for (var i = 0; i < DISPLAY_STATES.length; i++) {\n    var stateModel = labelStatesModels[DISPLAY_STATES[i]];\n    if (stateModel && stateModel.getShallow('show')) {\n      needsCreateText = true;\n      break;\n    }\n  }\n  var textContent = isSetOnText ? targetEl : targetEl.getTextContent();\n  if (needsCreateText) {\n    if (!isSetOnText) {\n      // Reuse the previous\n      if (!textContent) {\n        textContent = new ZRText();\n        targetEl.setTextContent(textContent);\n      }\n      // Use same state proxy\n      if (targetEl.stateProxy) {\n        textContent.stateProxy = targetEl.stateProxy;\n      }\n    }\n    var labelStatesTexts = getLabelText(opt, labelStatesModels);\n    var normalModel = labelStatesModels.normal;\n    var showNormal = !!normalModel.getShallow('show');\n    var normalStyle = createTextStyle(normalModel, stateSpecified && stateSpecified.normal, opt, false, !isSetOnText);\n    normalStyle.text = labelStatesTexts.normal;\n    if (!isSetOnText) {\n      // Always create new\n      targetEl.setTextConfig(createTextConfig(normalModel, opt, false));\n    }\n    for (var i = 0; i < SPECIAL_STATES.length; i++) {\n      var stateName = SPECIAL_STATES[i];\n      var stateModel = labelStatesModels[stateName];\n      if (stateModel) {\n        var stateObj = textContent.ensureState(stateName);\n        var stateShow = !!retrieve2(stateModel.getShallow('show'), showNormal);\n        if (stateShow !== showNormal) {\n          stateObj.ignore = !stateShow;\n        }\n        stateObj.style = createTextStyle(stateModel, stateSpecified && stateSpecified[stateName], opt, true, !isSetOnText);\n        stateObj.style.text = labelStatesTexts[stateName];\n        if (!isSetOnText) {\n          var targetElEmphasisState = targetEl.ensureState(stateName);\n          targetElEmphasisState.textConfig = createTextConfig(stateModel, opt, true);\n        }\n      }\n    }\n    // PENDING: if there is many requirements that emphasis position\n    // need to be different from normal position, we might consider\n    // auto silent is those cases.\n    textContent.silent = !!normalModel.getShallow('silent');\n    // Keep x and y\n    if (textContent.style.x != null) {\n      normalStyle.x = textContent.style.x;\n    }\n    if (textContent.style.y != null) {\n      normalStyle.y = textContent.style.y;\n    }\n    textContent.ignore = !showNormal;\n    // Always create new style.\n    textContent.useStyle(normalStyle);\n    textContent.dirty();\n    if (opt.enableTextSetter) {\n      labelInner(textContent).setLabelText = function (interpolatedValue) {\n        var labelStatesTexts = getLabelText(opt, labelStatesModels, interpolatedValue);\n        setLabelText(textContent, labelStatesTexts);\n      };\n    }\n  } else if (textContent) {\n    // Not display rich text.\n    textContent.ignore = true;\n  }\n  targetEl.dirty();\n}\nexport { setLabelStyle };\nexport function getLabelStatesModels(itemModel, labelName) {\n  labelName = labelName || 'label';\n  var statesModels = {\n    normal: itemModel.getModel(labelName)\n  };\n  for (var i = 0; i < SPECIAL_STATES.length; i++) {\n    var stateName = SPECIAL_STATES[i];\n    statesModels[stateName] = itemModel.getModel([stateName, labelName]);\n  }\n  return statesModels;\n}\n/**\r\n * Set basic textStyle properties.\r\n */\nexport function createTextStyle(textStyleModel, specifiedTextStyle,\n// Fixed style in the code. Can't be set by model.\nopt, isNotNormal, isAttached // If text is attached on an element. If so, auto color will handling in zrender.\n) {\n  var textStyle = {};\n  setTextStyleCommon(textStyle, textStyleModel, opt, isNotNormal, isAttached);\n  specifiedTextStyle && extend(textStyle, specifiedTextStyle);\n  // textStyle.host && textStyle.host.dirty && textStyle.host.dirty(false);\n  return textStyle;\n}\nexport function createTextConfig(textStyleModel, opt, isNotNormal) {\n  opt = opt || {};\n  var textConfig = {};\n  var labelPosition;\n  var labelRotate = textStyleModel.getShallow('rotate');\n  var labelDistance = retrieve2(textStyleModel.getShallow('distance'), isNotNormal ? null : 5);\n  var labelOffset = textStyleModel.getShallow('offset');\n  labelPosition = textStyleModel.getShallow('position') || (isNotNormal ? null : 'inside');\n  // 'outside' is not a valid zr textPostion value, but used\n  // in bar series, and magric type should be considered.\n  labelPosition === 'outside' && (labelPosition = opt.defaultOutsidePosition || 'top');\n  if (labelPosition != null) {\n    textConfig.position = labelPosition;\n  }\n  if (labelOffset != null) {\n    textConfig.offset = labelOffset;\n  }\n  if (labelRotate != null) {\n    labelRotate *= Math.PI / 180;\n    textConfig.rotation = labelRotate;\n  }\n  if (labelDistance != null) {\n    textConfig.distance = labelDistance;\n  }\n  // fill and auto is determined by the color of path fill if it's not specified by developers.\n  textConfig.outsideFill = textStyleModel.get('color') === 'inherit' ? opt.inheritColor || null : 'auto';\n  return textConfig;\n}\n/**\r\n * The uniform entry of set text style, that is, retrieve style definitions\r\n * from `model` and set to `textStyle` object.\r\n *\r\n * Never in merge mode, but in overwrite mode, that is, all of the text style\r\n * properties will be set. (Consider the states of normal and emphasis and\r\n * default value can be adopted, merge would make the logic too complicated\r\n * to manage.)\r\n */\nfunction setTextStyleCommon(textStyle, textStyleModel, opt, isNotNormal, isAttached) {\n  // Consider there will be abnormal when merge hover style to normal style if given default value.\n  opt = opt || EMPTY_OBJ;\n  var ecModel = textStyleModel.ecModel;\n  var globalTextStyle = ecModel && ecModel.option.textStyle;\n  // Consider case:\n  // {\n  //     data: [{\n  //         value: 12,\n  //         label: {\n  //             rich: {\n  //                 // no 'a' here but using parent 'a'.\n  //             }\n  //         }\n  //     }],\n  //     rich: {\n  //         a: { ... }\n  //     }\n  // }\n  var richItemNames = getRichItemNames(textStyleModel);\n  var richResult;\n  if (richItemNames) {\n    richResult = {};\n    for (var name_1 in richItemNames) {\n      if (richItemNames.hasOwnProperty(name_1)) {\n        // Cascade is supported in rich.\n        var richTextStyle = textStyleModel.getModel(['rich', name_1]);\n        // In rich, never `disableBox`.\n        // FIXME: consider `label: {formatter: '{a|xx}', color: 'blue', rich: {a: {}}}`,\n        // the default color `'blue'` will not be adopted if no color declared in `rich`.\n        // That might confuses users. So probably we should put `textStyleModel` as the\n        // root ancestor of the `richTextStyle`. But that would be a break change.\n        setTokenTextStyle(richResult[name_1] = {}, richTextStyle, globalTextStyle, opt, isNotNormal, isAttached, false, true);\n      }\n    }\n  }\n  if (richResult) {\n    textStyle.rich = richResult;\n  }\n  var overflow = textStyleModel.get('overflow');\n  if (overflow) {\n    textStyle.overflow = overflow;\n  }\n  var margin = textStyleModel.get('minMargin');\n  if (margin != null) {\n    textStyle.margin = margin;\n  }\n  setTokenTextStyle(textStyle, textStyleModel, globalTextStyle, opt, isNotNormal, isAttached, true, false);\n}\n// Consider case:\n// {\n//     data: [{\n//         value: 12,\n//         label: {\n//             rich: {\n//                 // no 'a' here but using parent 'a'.\n//             }\n//         }\n//     }],\n//     rich: {\n//         a: { ... }\n//     }\n// }\n// TODO TextStyleModel\nfunction getRichItemNames(textStyleModel) {\n  // Use object to remove duplicated names.\n  var richItemNameMap;\n  while (textStyleModel && textStyleModel !== textStyleModel.ecModel) {\n    var rich = (textStyleModel.option || EMPTY_OBJ).rich;\n    if (rich) {\n      richItemNameMap = richItemNameMap || {};\n      var richKeys = keys(rich);\n      for (var i = 0; i < richKeys.length; i++) {\n        var richKey = richKeys[i];\n        richItemNameMap[richKey] = 1;\n      }\n    }\n    textStyleModel = textStyleModel.parentModel;\n  }\n  return richItemNameMap;\n}\nvar TEXT_PROPS_WITH_GLOBAL = ['fontStyle', 'fontWeight', 'fontSize', 'fontFamily', 'textShadowColor', 'textShadowBlur', 'textShadowOffsetX', 'textShadowOffsetY'];\nvar TEXT_PROPS_SELF = ['align', 'lineHeight', 'width', 'height', 'tag', 'verticalAlign', 'ellipsis'];\nvar TEXT_PROPS_BOX = ['padding', 'borderWidth', 'borderRadius', 'borderDashOffset', 'backgroundColor', 'borderColor', 'shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY'];\nfunction setTokenTextStyle(textStyle, textStyleModel, globalTextStyle, opt, isNotNormal, isAttached, isBlock, inRich) {\n  // In merge mode, default value should not be given.\n  globalTextStyle = !isNotNormal && globalTextStyle || EMPTY_OBJ;\n  var inheritColor = opt && opt.inheritColor;\n  var fillColor = textStyleModel.getShallow('color');\n  var strokeColor = textStyleModel.getShallow('textBorderColor');\n  var opacity = retrieve2(textStyleModel.getShallow('opacity'), globalTextStyle.opacity);\n  if (fillColor === 'inherit' || fillColor === 'auto') {\n    if (process.env.NODE_ENV !== 'production') {\n      if (fillColor === 'auto') {\n        deprecateReplaceLog('color: \\'auto\\'', 'color: \\'inherit\\'');\n      }\n    }\n    if (inheritColor) {\n      fillColor = inheritColor;\n    } else {\n      fillColor = null;\n    }\n  }\n  if (strokeColor === 'inherit' || strokeColor === 'auto') {\n    if (process.env.NODE_ENV !== 'production') {\n      if (strokeColor === 'auto') {\n        deprecateReplaceLog('color: \\'auto\\'', 'color: \\'inherit\\'');\n      }\n    }\n    if (inheritColor) {\n      strokeColor = inheritColor;\n    } else {\n      strokeColor = null;\n    }\n  }\n  if (!isAttached) {\n    // Only use default global textStyle.color if text is individual.\n    // Otherwise it will use the strategy of attached text color because text may be on a path.\n    fillColor = fillColor || globalTextStyle.color;\n    strokeColor = strokeColor || globalTextStyle.textBorderColor;\n  }\n  if (fillColor != null) {\n    textStyle.fill = fillColor;\n  }\n  if (strokeColor != null) {\n    textStyle.stroke = strokeColor;\n  }\n  var textBorderWidth = retrieve2(textStyleModel.getShallow('textBorderWidth'), globalTextStyle.textBorderWidth);\n  if (textBorderWidth != null) {\n    textStyle.lineWidth = textBorderWidth;\n  }\n  var textBorderType = retrieve2(textStyleModel.getShallow('textBorderType'), globalTextStyle.textBorderType);\n  if (textBorderType != null) {\n    textStyle.lineDash = textBorderType;\n  }\n  var textBorderDashOffset = retrieve2(textStyleModel.getShallow('textBorderDashOffset'), globalTextStyle.textBorderDashOffset);\n  if (textBorderDashOffset != null) {\n    textStyle.lineDashOffset = textBorderDashOffset;\n  }\n  if (!isNotNormal && opacity == null && !inRich) {\n    opacity = opt && opt.defaultOpacity;\n  }\n  if (opacity != null) {\n    textStyle.opacity = opacity;\n  }\n  // TODO\n  if (!isNotNormal && !isAttached) {\n    // Set default finally.\n    if (textStyle.fill == null && opt.inheritColor) {\n      textStyle.fill = opt.inheritColor;\n    }\n  }\n  // Do not use `getFont` here, because merge should be supported, where\n  // part of these properties may be changed in emphasis style, and the\n  // others should remain their original value got from normal style.\n  for (var i = 0; i < TEXT_PROPS_WITH_GLOBAL.length; i++) {\n    var key = TEXT_PROPS_WITH_GLOBAL[i];\n    var val = retrieve2(textStyleModel.getShallow(key), globalTextStyle[key]);\n    if (val != null) {\n      textStyle[key] = val;\n    }\n  }\n  for (var i = 0; i < TEXT_PROPS_SELF.length; i++) {\n    var key = TEXT_PROPS_SELF[i];\n    var val = textStyleModel.getShallow(key);\n    if (val != null) {\n      textStyle[key] = val;\n    }\n  }\n  if (textStyle.verticalAlign == null) {\n    var baseline = textStyleModel.getShallow('baseline');\n    if (baseline != null) {\n      textStyle.verticalAlign = baseline;\n    }\n  }\n  if (!isBlock || !opt.disableBox) {\n    for (var i = 0; i < TEXT_PROPS_BOX.length; i++) {\n      var key = TEXT_PROPS_BOX[i];\n      var val = textStyleModel.getShallow(key);\n      if (val != null) {\n        textStyle[key] = val;\n      }\n    }\n    var borderType = textStyleModel.getShallow('borderType');\n    if (borderType != null) {\n      textStyle.borderDash = borderType;\n    }\n    if ((textStyle.backgroundColor === 'auto' || textStyle.backgroundColor === 'inherit') && inheritColor) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (textStyle.backgroundColor === 'auto') {\n          deprecateReplaceLog('backgroundColor: \\'auto\\'', 'backgroundColor: \\'inherit\\'');\n        }\n      }\n      textStyle.backgroundColor = inheritColor;\n    }\n    if ((textStyle.borderColor === 'auto' || textStyle.borderColor === 'inherit') && inheritColor) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (textStyle.borderColor === 'auto') {\n          deprecateReplaceLog('borderColor: \\'auto\\'', 'borderColor: \\'inherit\\'');\n        }\n      }\n      textStyle.borderColor = inheritColor;\n    }\n  }\n}\nexport function getFont(opt, ecModel) {\n  var gTextStyleModel = ecModel && ecModel.getModel('textStyle');\n  return trim([\n  // FIXME in node-canvas fontWeight is before fontStyle\n  opt.fontStyle || gTextStyleModel && gTextStyleModel.getShallow('fontStyle') || '', opt.fontWeight || gTextStyleModel && gTextStyleModel.getShallow('fontWeight') || '', (opt.fontSize || gTextStyleModel && gTextStyleModel.getShallow('fontSize') || 12) + 'px', opt.fontFamily || gTextStyleModel && gTextStyleModel.getShallow('fontFamily') || 'sans-serif'].join(' '));\n}\nexport var labelInner = makeInner();\nexport function setLabelValueAnimation(label, labelStatesModels, value, getDefaultText) {\n  if (!label) {\n    return;\n  }\n  var obj = labelInner(label);\n  obj.prevValue = obj.value;\n  obj.value = value;\n  var normalLabelModel = labelStatesModels.normal;\n  obj.valueAnimation = normalLabelModel.get('valueAnimation');\n  if (obj.valueAnimation) {\n    obj.precision = normalLabelModel.get('precision');\n    obj.defaultInterpolatedText = getDefaultText;\n    obj.statesModels = labelStatesModels;\n  }\n}\nexport function animateLabelValue(textEl, dataIndex, data, animatableModel, labelFetcher) {\n  var labelInnerStore = labelInner(textEl);\n  if (!labelInnerStore.valueAnimation || labelInnerStore.prevValue === labelInnerStore.value) {\n    // Value not changed, no new label animation\n    return;\n  }\n  var defaultInterpolatedText = labelInnerStore.defaultInterpolatedText;\n  // Consider the case that being animating, do not use the `obj.value`,\n  // Otherwise it will jump to the `obj.value` when this new animation started.\n  var currValue = retrieve2(labelInnerStore.interpolatedValue, labelInnerStore.prevValue);\n  var targetValue = labelInnerStore.value;\n  function during(percent) {\n    var interpolated = interpolateRawValues(data, labelInnerStore.precision, currValue, targetValue, percent);\n    labelInnerStore.interpolatedValue = percent === 1 ? null : interpolated;\n    var labelText = getLabelText({\n      labelDataIndex: dataIndex,\n      labelFetcher: labelFetcher,\n      defaultText: defaultInterpolatedText ? defaultInterpolatedText(interpolated) : interpolated + ''\n    }, labelInnerStore.statesModels, interpolated);\n    setLabelText(textEl, labelText);\n  }\n  textEl.percent = 0;\n  (labelInnerStore.prevValue == null ? initProps : updateProps)(textEl, {\n    // percent is used to prevent animation from being aborted #15916\n    percent: 1\n  }, animatableModel, dataIndex, null, during);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}