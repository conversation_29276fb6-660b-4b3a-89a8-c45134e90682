const { contextBridge, ipc<PERSON>enderer } = require('electron');

// 向渲染进程暴露安全的API
contextBridge.exposeInMainWorld('electronAPI', {
  // 应用信息
  getAppInfo: () => ipcRenderer.invoke('get-app-info'),
  
  // 配置管理
  loadConfig: () => ipcRenderer.invoke('load-config'),
  saveConfig: (config) => ipcRenderer.invoke('save-config', config),
  validateToken: (source, token) => ipcRenderer.invoke('validate-token', source, token),
  
  // 服务管理
  startServices: () => ipcRenderer.invoke('start-services'),
  stopServices: () => ipcRenderer.invoke('stop-services'),
  getServiceStatus: () => ipcRenderer.invoke('get-service-status'),
  restartServices: () => ipcRenderer.invoke('restart-services'),
  
  // 健康监控
  startHealthMonitoring: () => ipcRenderer.invoke('start-health-monitoring'),
  stopHealthMonitoring: () => ipcRenderer.invoke('stop-health-monitoring'),
  
  // 工具函数
  openExternal: (url) => ipcRenderer.invoke('open-external', url),
  showMessage: (options) => ipcRenderer.invoke('show-message', options),
  navigateTo: (page) => ipcRenderer.invoke('navigate-to', page),
  getLogs: () => ipcRenderer.invoke('get-logs'),
  
  // 事件监听
  onServiceStatusChange: (callback) => {
    ipcRenderer.on('service-status-change', (event, status) => callback(status));
  },
  
  onHealthCheck: (callback) => {
    ipcRenderer.on('health-check', (event, health) => callback(health));
  },
  
  onFirstRunWelcome: (callback) => {
    ipcRenderer.on('first-run-welcome', () => callback());
  },
  
  onError: (callback) => {
    ipcRenderer.on('error', (event, error) => callback(error));
  },
  
  // 移除事件监听
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
});

// 向渲染进程暴露版本信息
contextBridge.exposeInMainWorld('versions', {
  node: () => process.versions.node,
  chrome: () => process.versions.chrome,
  electron: () => process.versions.electron
});

// 向渲染进程暴露平台信息
contextBridge.exposeInMainWorld('platform', {
  os: process.platform,
  arch: process.arch
});

// 控制台日志（仅开发模式）
if (process.argv.includes('--dev')) {
  contextBridge.exposeInMainWorld('devTools', {
    log: (...args) => console.log('[Renderer]', ...args),
    error: (...args) => console.error('[Renderer]', ...args),
    warn: (...args) => console.warn('[Renderer]', ...args)
  });
}
