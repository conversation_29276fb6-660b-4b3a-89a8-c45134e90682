{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport classNames from 'classnames';\n/**\n * Fill component to provided the scroll content real height.\n */\nvar Filler = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var height = _ref.height,\n    offsetY = _ref.offsetY,\n    offsetX = _ref.offsetX,\n    children = _ref.children,\n    prefixCls = _ref.prefixCls,\n    onInnerResize = _ref.onInnerResize,\n    innerProps = _ref.innerProps,\n    rtl = _ref.rtl,\n    extra = _ref.extra;\n  var outerStyle = {};\n  var innerStyle = {\n    display: 'flex',\n    flexDirection: 'column'\n  };\n  if (offsetY !== undefined) {\n    // Not set `width` since this will break `sticky: right`\n    outerStyle = {\n      height: height,\n      position: 'relative',\n      overflow: 'hidden'\n    };\n    innerStyle = _objectSpread(_objectSpread({}, innerStyle), {}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n      transform: \"translateY(\".concat(offsetY, \"px)\")\n    }, rtl ? 'marginRight' : 'marginLeft', -offsetX), \"position\", 'absolute'), \"left\", 0), \"right\", 0), \"top\", 0));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: outerStyle\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: function onResize(_ref2) {\n      var offsetHeight = _ref2.offsetHeight;\n      if (offsetHeight && onInnerResize) {\n        onInnerResize();\n      }\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", _extends({\n    style: innerStyle,\n    className: classNames(_defineProperty({}, \"\".concat(prefixCls, \"-holder-inner\"), prefixCls)),\n    ref: ref\n  }, innerProps), children, extra)));\n});\nFiller.displayName = 'Filler';\nexport default Filler;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}