{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { extend, indexOf, isArrayLike, isObject, keys, isArray, each } from 'zrender/lib/core/util.js';\nimport { getECData } from './innerStore.js';\nimport { liftColor } from 'zrender/lib/tool/color.js';\nimport { queryDataIndex, makeInner } from './model.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport { error } from './log.js';\n// Reserve 0 as default.\nvar _highlightNextDigit = 1;\nvar _highlightKeyMap = {};\nvar getSavedStates = makeInner();\nvar getComponentStates = makeInner();\nexport var HOVER_STATE_NORMAL = 0;\nexport var HOVER_STATE_BLUR = 1;\nexport var HOVER_STATE_EMPHASIS = 2;\nexport var SPECIAL_STATES = ['emphasis', 'blur', 'select'];\nexport var DISPLAY_STATES = ['normal', 'emphasis', 'blur', 'select'];\nexport var Z2_EMPHASIS_LIFT = 10;\nexport var Z2_SELECT_LIFT = 9;\nexport var HIGHLIGHT_ACTION_TYPE = 'highlight';\nexport var DOWNPLAY_ACTION_TYPE = 'downplay';\nexport var SELECT_ACTION_TYPE = 'select';\nexport var UNSELECT_ACTION_TYPE = 'unselect';\nexport var TOGGLE_SELECT_ACTION_TYPE = 'toggleSelect';\nfunction hasFillOrStroke(fillOrStroke) {\n  return fillOrStroke != null && fillOrStroke !== 'none';\n}\nfunction doChangeHoverState(el, stateName, hoverStateEnum) {\n  if (el.onHoverStateChange && (el.hoverState || 0) !== hoverStateEnum) {\n    el.onHoverStateChange(stateName);\n  }\n  el.hoverState = hoverStateEnum;\n}\nfunction singleEnterEmphasis(el) {\n  // Only mark the flag.\n  // States will be applied in the echarts.ts in next frame.\n  doChangeHoverState(el, 'emphasis', HOVER_STATE_EMPHASIS);\n}\nfunction singleLeaveEmphasis(el) {\n  // Only mark the flag.\n  // States will be applied in the echarts.ts in next frame.\n  if (el.hoverState === HOVER_STATE_EMPHASIS) {\n    doChangeHoverState(el, 'normal', HOVER_STATE_NORMAL);\n  }\n}\nfunction singleEnterBlur(el) {\n  doChangeHoverState(el, 'blur', HOVER_STATE_BLUR);\n}\nfunction singleLeaveBlur(el) {\n  if (el.hoverState === HOVER_STATE_BLUR) {\n    doChangeHoverState(el, 'normal', HOVER_STATE_NORMAL);\n  }\n}\nfunction singleEnterSelect(el) {\n  el.selected = true;\n}\nfunction singleLeaveSelect(el) {\n  el.selected = false;\n}\nfunction updateElementState(el, updater, commonParam) {\n  updater(el, commonParam);\n}\nfunction traverseUpdateState(el, updater, commonParam) {\n  updateElementState(el, updater, commonParam);\n  el.isGroup && el.traverse(function (child) {\n    updateElementState(child, updater, commonParam);\n  });\n}\nexport function setStatesFlag(el, stateName) {\n  switch (stateName) {\n    case 'emphasis':\n      el.hoverState = HOVER_STATE_EMPHASIS;\n      break;\n    case 'normal':\n      el.hoverState = HOVER_STATE_NORMAL;\n      break;\n    case 'blur':\n      el.hoverState = HOVER_STATE_BLUR;\n      break;\n    case 'select':\n      el.selected = true;\n  }\n}\n/**\r\n * If we reuse elements when rerender.\r\n * DON'T forget to clearStates before we update the style and shape.\r\n * Or we may update on the wrong state instead of normal state.\r\n */\nexport function clearStates(el) {\n  if (el.isGroup) {\n    el.traverse(function (child) {\n      child.clearStates();\n    });\n  } else {\n    el.clearStates();\n  }\n}\nfunction getFromStateStyle(el, props, toStateName, defaultValue) {\n  var style = el.style;\n  var fromState = {};\n  for (var i = 0; i < props.length; i++) {\n    var propName = props[i];\n    var val = style[propName];\n    fromState[propName] = val == null ? defaultValue && defaultValue[propName] : val;\n  }\n  for (var i = 0; i < el.animators.length; i++) {\n    var animator = el.animators[i];\n    if (animator.__fromStateTransition\n    // Don't consider the animation to emphasis state.\n    && animator.__fromStateTransition.indexOf(toStateName) < 0 && animator.targetName === 'style') {\n      animator.saveTo(fromState, props);\n    }\n  }\n  return fromState;\n}\nfunction createEmphasisDefaultState(el, stateName, targetStates, state) {\n  var hasSelect = targetStates && indexOf(targetStates, 'select') >= 0;\n  var cloned = false;\n  if (el instanceof Path) {\n    var store = getSavedStates(el);\n    var fromFill = hasSelect ? store.selectFill || store.normalFill : store.normalFill;\n    var fromStroke = hasSelect ? store.selectStroke || store.normalStroke : store.normalStroke;\n    if (hasFillOrStroke(fromFill) || hasFillOrStroke(fromStroke)) {\n      state = state || {};\n      var emphasisStyle = state.style || {};\n      // inherit case\n      if (emphasisStyle.fill === 'inherit') {\n        cloned = true;\n        state = extend({}, state);\n        emphasisStyle = extend({}, emphasisStyle);\n        emphasisStyle.fill = fromFill;\n      }\n      // Apply default color lift\n      else if (!hasFillOrStroke(emphasisStyle.fill) && hasFillOrStroke(fromFill)) {\n        cloned = true;\n        // Not modify the original value.\n        state = extend({}, state);\n        emphasisStyle = extend({}, emphasisStyle);\n        // Already being applied 'emphasis'. DON'T lift color multiple times.\n        emphasisStyle.fill = liftColor(fromFill);\n      }\n      // Not highlight stroke if fill has been highlighted.\n      else if (!hasFillOrStroke(emphasisStyle.stroke) && hasFillOrStroke(fromStroke)) {\n        if (!cloned) {\n          state = extend({}, state);\n          emphasisStyle = extend({}, emphasisStyle);\n        }\n        emphasisStyle.stroke = liftColor(fromStroke);\n      }\n      state.style = emphasisStyle;\n    }\n  }\n  if (state) {\n    // TODO Share with textContent?\n    if (state.z2 == null) {\n      if (!cloned) {\n        state = extend({}, state);\n      }\n      var z2EmphasisLift = el.z2EmphasisLift;\n      state.z2 = el.z2 + (z2EmphasisLift != null ? z2EmphasisLift : Z2_EMPHASIS_LIFT);\n    }\n  }\n  return state;\n}\nfunction createSelectDefaultState(el, stateName, state) {\n  // const hasSelect = indexOf(el.currentStates, stateName) >= 0;\n  if (state) {\n    // TODO Share with textContent?\n    if (state.z2 == null) {\n      state = extend({}, state);\n      var z2SelectLift = el.z2SelectLift;\n      state.z2 = el.z2 + (z2SelectLift != null ? z2SelectLift : Z2_SELECT_LIFT);\n    }\n  }\n  return state;\n}\nfunction createBlurDefaultState(el, stateName, state) {\n  var hasBlur = indexOf(el.currentStates, stateName) >= 0;\n  var currentOpacity = el.style.opacity;\n  var fromState = !hasBlur ? getFromStateStyle(el, ['opacity'], stateName, {\n    opacity: 1\n  }) : null;\n  state = state || {};\n  var blurStyle = state.style || {};\n  if (blurStyle.opacity == null) {\n    // clone state\n    state = extend({}, state);\n    blurStyle = extend({\n      // Already being applied 'emphasis'. DON'T mul opacity multiple times.\n      opacity: hasBlur ? currentOpacity : fromState.opacity * 0.1\n    }, blurStyle);\n    state.style = blurStyle;\n  }\n  return state;\n}\nfunction elementStateProxy(stateName, targetStates) {\n  var state = this.states[stateName];\n  if (this.style) {\n    if (stateName === 'emphasis') {\n      return createEmphasisDefaultState(this, stateName, targetStates, state);\n    } else if (stateName === 'blur') {\n      return createBlurDefaultState(this, stateName, state);\n    } else if (stateName === 'select') {\n      return createSelectDefaultState(this, stateName, state);\n    }\n  }\n  return state;\n}\n/**\r\n * Set hover style (namely \"emphasis style\") of element.\r\n * @param el Should not be `zrender/graphic/Group`.\r\n * @param focus 'self' | 'selfInSeries' | 'series'\r\n */\nexport function setDefaultStateProxy(el) {\n  el.stateProxy = elementStateProxy;\n  var textContent = el.getTextContent();\n  var textGuide = el.getTextGuideLine();\n  if (textContent) {\n    textContent.stateProxy = elementStateProxy;\n  }\n  if (textGuide) {\n    textGuide.stateProxy = elementStateProxy;\n  }\n}\nexport function enterEmphasisWhenMouseOver(el, e) {\n  !shouldSilent(el, e)\n  // \"emphasis\" event highlight has higher priority than mouse highlight.\n  && !el.__highByOuter && traverseUpdateState(el, singleEnterEmphasis);\n}\nexport function leaveEmphasisWhenMouseOut(el, e) {\n  !shouldSilent(el, e)\n  // \"emphasis\" event highlight has higher priority than mouse highlight.\n  && !el.__highByOuter && traverseUpdateState(el, singleLeaveEmphasis);\n}\nexport function enterEmphasis(el, highlightDigit) {\n  el.__highByOuter |= 1 << (highlightDigit || 0);\n  traverseUpdateState(el, singleEnterEmphasis);\n}\nexport function leaveEmphasis(el, highlightDigit) {\n  !(el.__highByOuter &= ~(1 << (highlightDigit || 0))) && traverseUpdateState(el, singleLeaveEmphasis);\n}\nexport function enterBlur(el) {\n  traverseUpdateState(el, singleEnterBlur);\n}\nexport function leaveBlur(el) {\n  traverseUpdateState(el, singleLeaveBlur);\n}\nexport function enterSelect(el) {\n  traverseUpdateState(el, singleEnterSelect);\n}\nexport function leaveSelect(el) {\n  traverseUpdateState(el, singleLeaveSelect);\n}\nfunction shouldSilent(el, e) {\n  return el.__highDownSilentOnTouch && e.zrByTouch;\n}\nexport function allLeaveBlur(api) {\n  var model = api.getModel();\n  var leaveBlurredSeries = [];\n  var allComponentViews = [];\n  model.eachComponent(function (componentType, componentModel) {\n    var componentStates = getComponentStates(componentModel);\n    var isSeries = componentType === 'series';\n    var view = isSeries ? api.getViewOfSeriesModel(componentModel) : api.getViewOfComponentModel(componentModel);\n    !isSeries && allComponentViews.push(view);\n    if (componentStates.isBlured) {\n      // Leave blur anyway\n      view.group.traverse(function (child) {\n        singleLeaveBlur(child);\n      });\n      isSeries && leaveBlurredSeries.push(componentModel);\n    }\n    componentStates.isBlured = false;\n  });\n  each(allComponentViews, function (view) {\n    if (view && view.toggleBlurSeries) {\n      view.toggleBlurSeries(leaveBlurredSeries, false, model);\n    }\n  });\n}\nexport function blurSeries(targetSeriesIndex, focus, blurScope, api) {\n  var ecModel = api.getModel();\n  blurScope = blurScope || 'coordinateSystem';\n  function leaveBlurOfIndices(data, dataIndices) {\n    for (var i = 0; i < dataIndices.length; i++) {\n      var itemEl = data.getItemGraphicEl(dataIndices[i]);\n      itemEl && leaveBlur(itemEl);\n    }\n  }\n  if (targetSeriesIndex == null) {\n    return;\n  }\n  if (!focus || focus === 'none') {\n    return;\n  }\n  var targetSeriesModel = ecModel.getSeriesByIndex(targetSeriesIndex);\n  var targetCoordSys = targetSeriesModel.coordinateSystem;\n  if (targetCoordSys && targetCoordSys.master) {\n    targetCoordSys = targetCoordSys.master;\n  }\n  var blurredSeries = [];\n  ecModel.eachSeries(function (seriesModel) {\n    var sameSeries = targetSeriesModel === seriesModel;\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys && coordSys.master) {\n      coordSys = coordSys.master;\n    }\n    var sameCoordSys = coordSys && targetCoordSys ? coordSys === targetCoordSys : sameSeries; // If there is no coordinate system. use sameSeries instead.\n    if (!(\n    // Not blur other series if blurScope series\n    blurScope === 'series' && !sameSeries\n    // Not blur other coordinate system if blurScope is coordinateSystem\n    || blurScope === 'coordinateSystem' && !sameCoordSys\n    // Not blur self series if focus is series.\n    || focus === 'series' && sameSeries\n    // TODO blurScope: coordinate system\n    )) {\n      var view = api.getViewOfSeriesModel(seriesModel);\n      view.group.traverse(function (child) {\n        // For the elements that have been triggered by other components,\n        // and are still required to be highlighted,\n        // because the current is directly forced to blur the element,\n        // it will cause the focus self to be unable to highlight, so skip the blur of this element.\n        if (child.__highByOuter && sameSeries && focus === 'self') {\n          return;\n        }\n        singleEnterBlur(child);\n      });\n      if (isArrayLike(focus)) {\n        leaveBlurOfIndices(seriesModel.getData(), focus);\n      } else if (isObject(focus)) {\n        var dataTypes = keys(focus);\n        for (var d = 0; d < dataTypes.length; d++) {\n          leaveBlurOfIndices(seriesModel.getData(dataTypes[d]), focus[dataTypes[d]]);\n        }\n      }\n      blurredSeries.push(seriesModel);\n      getComponentStates(seriesModel).isBlured = true;\n    }\n  });\n  ecModel.eachComponent(function (componentType, componentModel) {\n    if (componentType === 'series') {\n      return;\n    }\n    var view = api.getViewOfComponentModel(componentModel);\n    if (view && view.toggleBlurSeries) {\n      view.toggleBlurSeries(blurredSeries, true, ecModel);\n    }\n  });\n}\nexport function blurComponent(componentMainType, componentIndex, api) {\n  if (componentMainType == null || componentIndex == null) {\n    return;\n  }\n  var componentModel = api.getModel().getComponent(componentMainType, componentIndex);\n  if (!componentModel) {\n    return;\n  }\n  getComponentStates(componentModel).isBlured = true;\n  var view = api.getViewOfComponentModel(componentModel);\n  if (!view || !view.focusBlurEnabled) {\n    return;\n  }\n  view.group.traverse(function (child) {\n    singleEnterBlur(child);\n  });\n}\nexport function blurSeriesFromHighlightPayload(seriesModel, payload, api) {\n  var seriesIndex = seriesModel.seriesIndex;\n  var data = seriesModel.getData(payload.dataType);\n  if (!data) {\n    if (process.env.NODE_ENV !== 'production') {\n      error(\"Unknown dataType \" + payload.dataType);\n    }\n    return;\n  }\n  var dataIndex = queryDataIndex(data, payload);\n  // Pick the first one if there is multiple/none exists.\n  dataIndex = (isArray(dataIndex) ? dataIndex[0] : dataIndex) || 0;\n  var el = data.getItemGraphicEl(dataIndex);\n  if (!el) {\n    var count = data.count();\n    var current = 0;\n    // If data on dataIndex is NaN.\n    while (!el && current < count) {\n      el = data.getItemGraphicEl(current++);\n    }\n  }\n  if (el) {\n    var ecData = getECData(el);\n    blurSeries(seriesIndex, ecData.focus, ecData.blurScope, api);\n  } else {\n    // If there is no element put on the data. Try getting it from raw option\n    // TODO Should put it on seriesModel?\n    var focus_1 = seriesModel.get(['emphasis', 'focus']);\n    var blurScope = seriesModel.get(['emphasis', 'blurScope']);\n    if (focus_1 != null) {\n      blurSeries(seriesIndex, focus_1, blurScope, api);\n    }\n  }\n}\nexport function findComponentHighDownDispatchers(componentMainType, componentIndex, name, api) {\n  var ret = {\n    focusSelf: false,\n    dispatchers: null\n  };\n  if (componentMainType == null || componentMainType === 'series' || componentIndex == null || name == null) {\n    return ret;\n  }\n  var componentModel = api.getModel().getComponent(componentMainType, componentIndex);\n  if (!componentModel) {\n    return ret;\n  }\n  var view = api.getViewOfComponentModel(componentModel);\n  if (!view || !view.findHighDownDispatchers) {\n    return ret;\n  }\n  var dispatchers = view.findHighDownDispatchers(name);\n  // At presnet, the component (like Geo) only blur inside itself.\n  // So we do not use `blurScope` in component.\n  var focusSelf;\n  for (var i = 0; i < dispatchers.length; i++) {\n    if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatchers[i])) {\n      error('param should be highDownDispatcher');\n    }\n    if (getECData(dispatchers[i]).focus === 'self') {\n      focusSelf = true;\n      break;\n    }\n  }\n  return {\n    focusSelf: focusSelf,\n    dispatchers: dispatchers\n  };\n}\nexport function handleGlobalMouseOverForHighDown(dispatcher, e, api) {\n  if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatcher)) {\n    error('param should be highDownDispatcher');\n  }\n  var ecData = getECData(dispatcher);\n  var _a = findComponentHighDownDispatchers(ecData.componentMainType, ecData.componentIndex, ecData.componentHighDownName, api),\n    dispatchers = _a.dispatchers,\n    focusSelf = _a.focusSelf;\n  // If `findHighDownDispatchers` is supported on the component,\n  // highlight/downplay elements with the same name.\n  if (dispatchers) {\n    if (focusSelf) {\n      blurComponent(ecData.componentMainType, ecData.componentIndex, api);\n    }\n    each(dispatchers, function (dispatcher) {\n      return enterEmphasisWhenMouseOver(dispatcher, e);\n    });\n  } else {\n    // Try blur all in the related series. Then emphasis the hoverred.\n    // TODO. progressive mode.\n    blurSeries(ecData.seriesIndex, ecData.focus, ecData.blurScope, api);\n    if (ecData.focus === 'self') {\n      blurComponent(ecData.componentMainType, ecData.componentIndex, api);\n    }\n    // Other than series, component that not support `findHighDownDispatcher` will\n    // also use it. But in this case, highlight/downplay are only supported in\n    // mouse hover but not in dispatchAction.\n    enterEmphasisWhenMouseOver(dispatcher, e);\n  }\n}\nexport function handleGlobalMouseOutForHighDown(dispatcher, e, api) {\n  if (process.env.NODE_ENV !== 'production' && !isHighDownDispatcher(dispatcher)) {\n    error('param should be highDownDispatcher');\n  }\n  allLeaveBlur(api);\n  var ecData = getECData(dispatcher);\n  var dispatchers = findComponentHighDownDispatchers(ecData.componentMainType, ecData.componentIndex, ecData.componentHighDownName, api).dispatchers;\n  if (dispatchers) {\n    each(dispatchers, function (dispatcher) {\n      return leaveEmphasisWhenMouseOut(dispatcher, e);\n    });\n  } else {\n    leaveEmphasisWhenMouseOut(dispatcher, e);\n  }\n}\nexport function toggleSelectionFromPayload(seriesModel, payload, api) {\n  if (!isSelectChangePayload(payload)) {\n    return;\n  }\n  var dataType = payload.dataType;\n  var data = seriesModel.getData(dataType);\n  var dataIndex = queryDataIndex(data, payload);\n  if (!isArray(dataIndex)) {\n    dataIndex = [dataIndex];\n  }\n  seriesModel[payload.type === TOGGLE_SELECT_ACTION_TYPE ? 'toggleSelect' : payload.type === SELECT_ACTION_TYPE ? 'select' : 'unselect'](dataIndex, dataType);\n}\nexport function updateSeriesElementSelection(seriesModel) {\n  var allData = seriesModel.getAllData();\n  each(allData, function (_a) {\n    var data = _a.data,\n      type = _a.type;\n    data.eachItemGraphicEl(function (el, idx) {\n      seriesModel.isSelected(idx, type) ? enterSelect(el) : leaveSelect(el);\n    });\n  });\n}\nexport function getAllSelectedIndices(ecModel) {\n  var ret = [];\n  ecModel.eachSeries(function (seriesModel) {\n    var allData = seriesModel.getAllData();\n    each(allData, function (_a) {\n      var data = _a.data,\n        type = _a.type;\n      var dataIndices = seriesModel.getSelectedDataIndices();\n      if (dataIndices.length > 0) {\n        var item = {\n          dataIndex: dataIndices,\n          seriesIndex: seriesModel.seriesIndex\n        };\n        if (type != null) {\n          item.dataType = type;\n        }\n        ret.push(item);\n      }\n    });\n  });\n  return ret;\n}\n/**\r\n * Enable the function that mouseover will trigger the emphasis state.\r\n *\r\n * NOTE:\r\n * This function should be used on the element with dataIndex, seriesIndex.\r\n *\r\n */\nexport function enableHoverEmphasis(el, focus, blurScope) {\n  setAsHighDownDispatcher(el, true);\n  traverseUpdateState(el, setDefaultStateProxy);\n  enableHoverFocus(el, focus, blurScope);\n}\nexport function disableHoverEmphasis(el) {\n  setAsHighDownDispatcher(el, false);\n}\nexport function toggleHoverEmphasis(el, focus, blurScope, isDisabled) {\n  isDisabled ? disableHoverEmphasis(el) : enableHoverEmphasis(el, focus, blurScope);\n}\nexport function enableHoverFocus(el, focus, blurScope) {\n  var ecData = getECData(el);\n  if (focus != null) {\n    // TODO dataIndex may be set after this function. This check is not useful.\n    // if (ecData.dataIndex == null) {\n    //     if (__DEV__) {\n    //         console.warn('focus can only been set on element with dataIndex');\n    //     }\n    // }\n    // else {\n    ecData.focus = focus;\n    ecData.blurScope = blurScope;\n    // }\n  } else if (ecData.focus) {\n    ecData.focus = null;\n  }\n}\nvar OTHER_STATES = ['emphasis', 'blur', 'select'];\nvar defaultStyleGetterMap = {\n  itemStyle: 'getItemStyle',\n  lineStyle: 'getLineStyle',\n  areaStyle: 'getAreaStyle'\n};\n/**\r\n * Set emphasis/blur/selected states of element.\r\n */\nexport function setStatesStylesFromModel(el, itemModel, styleType,\n// default itemStyle\ngetter) {\n  styleType = styleType || 'itemStyle';\n  for (var i = 0; i < OTHER_STATES.length; i++) {\n    var stateName = OTHER_STATES[i];\n    var model = itemModel.getModel([stateName, styleType]);\n    var state = el.ensureState(stateName);\n    // Let it throw error if getterType is not found.\n    state.style = getter ? getter(model) : model[defaultStyleGetterMap[styleType]]();\n  }\n}\n/**\r\n *\r\n * Set element as highlight / downplay dispatcher.\r\n * It will be checked when element received mouseover event or from highlight action.\r\n * It's in change of all highlight/downplay behavior of it's children.\r\n *\r\n * @param el\r\n * @param el.highDownSilentOnTouch\r\n *        In touch device, mouseover event will be trigger on touchstart event\r\n *        (see module:zrender/dom/HandlerProxy). By this mechanism, we can\r\n *        conveniently use hoverStyle when tap on touch screen without additional\r\n *        code for compatibility.\r\n *        But if the chart/component has select feature, which usually also use\r\n *        hoverStyle, there might be conflict between 'select-highlight' and\r\n *        'hover-highlight' especially when roam is enabled (see geo for example).\r\n *        In this case, `highDownSilentOnTouch` should be used to disable\r\n *        hover-highlight on touch device.\r\n * @param asDispatcher If `false`, do not set as \"highDownDispatcher\".\r\n */\nexport function setAsHighDownDispatcher(el, asDispatcher) {\n  var disable = asDispatcher === false;\n  var extendedEl = el;\n  // Make `highDownSilentOnTouch` and `onStateChange` only work after\n  // `setAsHighDownDispatcher` called. Avoid it is modified by user unexpectedly.\n  if (el.highDownSilentOnTouch) {\n    extendedEl.__highDownSilentOnTouch = el.highDownSilentOnTouch;\n  }\n  // Simple optimize, since this method might be\n  // called for each elements of a group in some cases.\n  if (!disable || extendedEl.__highDownDispatcher) {\n    // Emphasis, normal can be triggered manually by API or other components like hover link.\n    // el[method]('emphasis', onElementEmphasisEvent)[method]('normal', onElementNormalEvent);\n    // Also keep previous record.\n    extendedEl.__highByOuter = extendedEl.__highByOuter || 0;\n    extendedEl.__highDownDispatcher = !disable;\n  }\n}\nexport function isHighDownDispatcher(el) {\n  return !!(el && el.__highDownDispatcher);\n}\n/**\r\n * Enable component highlight/downplay features:\r\n * + hover link (within the same name)\r\n * + focus blur in component\r\n */\nexport function enableComponentHighDownFeatures(el, componentModel, componentHighDownName) {\n  var ecData = getECData(el);\n  ecData.componentMainType = componentModel.mainType;\n  ecData.componentIndex = componentModel.componentIndex;\n  ecData.componentHighDownName = componentHighDownName;\n}\n/**\r\n * Support highlight/downplay record on each elements.\r\n * For the case: hover highlight/downplay (legend, visualMap, ...) and\r\n * user triggered highlight/downplay should not conflict.\r\n * Only all of the highlightDigit cleared, return to normal.\r\n * @param {string} highlightKey\r\n * @return {number} highlightDigit\r\n */\nexport function getHighlightDigit(highlightKey) {\n  var highlightDigit = _highlightKeyMap[highlightKey];\n  if (highlightDigit == null && _highlightNextDigit <= 32) {\n    highlightDigit = _highlightKeyMap[highlightKey] = _highlightNextDigit++;\n  }\n  return highlightDigit;\n}\nexport function isSelectChangePayload(payload) {\n  var payloadType = payload.type;\n  return payloadType === SELECT_ACTION_TYPE || payloadType === UNSELECT_ACTION_TYPE || payloadType === TOGGLE_SELECT_ACTION_TYPE;\n}\nexport function isHighDownPayload(payload) {\n  var payloadType = payload.type;\n  return payloadType === HIGHLIGHT_ACTION_TYPE || payloadType === DOWNPLAY_ACTION_TYPE;\n}\nexport function savePathStates(el) {\n  var store = getSavedStates(el);\n  store.normalFill = el.style.fill;\n  store.normalStroke = el.style.stroke;\n  var selectState = el.states.select || {};\n  store.selectFill = selectState.style && selectState.style.fill || null;\n  store.selectStroke = selectState.style && selectState.style.stroke || null;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}