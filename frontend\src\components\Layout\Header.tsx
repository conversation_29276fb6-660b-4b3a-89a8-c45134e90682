import React from 'react';
import { Layout, Typography, Space, Badge, Button } from 'antd';
import { BellOutlined, UserOutlined, ReloadOutlined } from '@ant-design/icons';

const { Header: AntHeader } = Layout;
const { Text } = Typography;

const Header: React.FC = () => {
  const currentTime = new Date().toLocaleString('zh-CN');

  return (
    <AntHeader style={{ 
      background: '#fff', 
      padding: '0 24px',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderBottom: '1px solid #f0f0f0'
    }}>
      <div>
        <Text strong style={{ fontSize: '16px' }}>
          量化交易监控系统
        </Text>
        <Text type="secondary" style={{ marginLeft: '16px' }}>
          {currentTime}
        </Text>
      </div>
      
      <Space size="middle">
        <Button 
          type="text" 
          icon={<ReloadOutlined />}
          onClick={() => window.location.reload()}
        >
          刷新
        </Button>
        
        <Badge count={3} size="small">
          <Button 
            type="text" 
            icon={<BellOutlined />}
            style={{ fontSize: '16px' }}
          />
        </Badge>
        
        <Button 
          type="text" 
          icon={<UserOutlined />}
          style={{ fontSize: '16px' }}
        >
          用户
        </Button>
      </Space>
    </AntHeader>
  );
};

export default Header;
