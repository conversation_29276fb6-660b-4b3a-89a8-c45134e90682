{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Model from './Model.js';\nimport * as componentUtil from '../util/component.js';\nimport { enableClassManagement, parseClassType, isExtendedClass, mountExtend } from '../util/clazz.js';\nimport { makeInner, queryReferringComponents } from '../util/model.js';\nimport * as layout from '../util/layout.js';\nvar inner = makeInner();\nvar ComponentModel = /** @class */function (_super) {\n  __extends(ComponentModel, _super);\n  function ComponentModel(option, parentModel, ecModel) {\n    var _this = _super.call(this, option, parentModel, ecModel) || this;\n    _this.uid = componentUtil.getUID('ec_cpt_model');\n    return _this;\n  }\n  ComponentModel.prototype.init = function (option, parentModel, ecModel) {\n    this.mergeDefaultAndTheme(option, ecModel);\n  };\n  ComponentModel.prototype.mergeDefaultAndTheme = function (option, ecModel) {\n    var layoutMode = layout.fetchLayoutMode(this);\n    var inputPositionParams = layoutMode ? layout.getLayoutParams(option) : {};\n    var themeModel = ecModel.getTheme();\n    zrUtil.merge(option, themeModel.get(this.mainType));\n    zrUtil.merge(option, this.getDefaultOption());\n    if (layoutMode) {\n      layout.mergeLayoutParam(option, inputPositionParams, layoutMode);\n    }\n  };\n  ComponentModel.prototype.mergeOption = function (option, ecModel) {\n    zrUtil.merge(this.option, option, true);\n    var layoutMode = layout.fetchLayoutMode(this);\n    if (layoutMode) {\n      layout.mergeLayoutParam(this.option, option, layoutMode);\n    }\n  };\n  /**\r\n   * Called immediately after `init` or `mergeOption` of this instance called.\r\n   */\n  ComponentModel.prototype.optionUpdated = function (newCptOption, isInit) {};\n  /**\r\n   * [How to declare defaultOption]:\r\n   *\r\n   * (A) If using class declaration in typescript (since echarts 5):\r\n   * ```ts\r\n   * import {ComponentOption} from '../model/option.js';\r\n   * export interface XxxOption extends ComponentOption {\r\n   *     aaa: number\r\n   * }\r\n   * export class XxxModel extends Component {\r\n   *     static type = 'xxx';\r\n   *     static defaultOption: XxxOption = {\r\n   *         aaa: 123\r\n   *     }\r\n   * }\r\n   * Component.registerClass(XxxModel);\r\n   * ```\r\n   * ```ts\r\n   * import {inheritDefaultOption} from '../util/component.js';\r\n   * import {XxxModel, XxxOption} from './XxxModel.js';\r\n   * export interface XxxSubOption extends XxxOption {\r\n   *     bbb: number\r\n   * }\r\n   * class XxxSubModel extends XxxModel {\r\n   *     static defaultOption: XxxSubOption = inheritDefaultOption(XxxModel.defaultOption, {\r\n   *         bbb: 456\r\n   *     })\r\n   *     fn() {\r\n   *         let opt = this.getDefaultOption();\r\n   *         // opt is {aaa: 123, bbb: 456}\r\n   *     }\r\n   * }\r\n   * ```\r\n   *\r\n   * (B) If using class extend (previous approach in echarts 3 & 4):\r\n   * ```js\r\n   * let XxxComponent = Component.extend({\r\n   *     defaultOption: {\r\n   *         xx: 123\r\n   *     }\r\n   * })\r\n   * ```\r\n   * ```js\r\n   * let XxxSubComponent = XxxComponent.extend({\r\n   *     defaultOption: {\r\n   *         yy: 456\r\n   *     },\r\n   *     fn: function () {\r\n   *         let opt = this.getDefaultOption();\r\n   *         // opt is {xx: 123, yy: 456}\r\n   *     }\r\n   * })\r\n   * ```\r\n   */\n  ComponentModel.prototype.getDefaultOption = function () {\n    var ctor = this.constructor;\n    // If using class declaration, it is different to travel super class\n    // in legacy env and auto merge defaultOption. So if using class\n    // declaration, defaultOption should be merged manually.\n    if (!isExtendedClass(ctor)) {\n      // When using ts class, defaultOption must be declared as static.\n      return ctor.defaultOption;\n    }\n    // FIXME: remove this approach?\n    var fields = inner(this);\n    if (!fields.defaultOption) {\n      var optList = [];\n      var clz = ctor;\n      while (clz) {\n        var opt = clz.prototype.defaultOption;\n        opt && optList.push(opt);\n        clz = clz.superClass;\n      }\n      var defaultOption = {};\n      for (var i = optList.length - 1; i >= 0; i--) {\n        defaultOption = zrUtil.merge(defaultOption, optList[i], true);\n      }\n      fields.defaultOption = defaultOption;\n    }\n    return fields.defaultOption;\n  };\n  /**\r\n   * Notice: always force to input param `useDefault` in case that forget to consider it.\r\n   * The same behavior as `modelUtil.parseFinder`.\r\n   *\r\n   * @param useDefault In many cases like series refer axis and axis refer grid,\r\n   *        If axis index / axis id not specified, use the first target as default.\r\n   *        In other cases like dataZoom refer axis, if not specified, measn no refer.\r\n   */\n  ComponentModel.prototype.getReferringComponents = function (mainType, opt) {\n    var indexKey = mainType + 'Index';\n    var idKey = mainType + 'Id';\n    return queryReferringComponents(this.ecModel, mainType, {\n      index: this.get(indexKey, true),\n      id: this.get(idKey, true)\n    }, opt);\n  };\n  ComponentModel.prototype.getBoxLayoutParams = function () {\n    // Consider itself having box layout configs.\n    var boxLayoutModel = this;\n    return {\n      left: boxLayoutModel.get('left'),\n      top: boxLayoutModel.get('top'),\n      right: boxLayoutModel.get('right'),\n      bottom: boxLayoutModel.get('bottom'),\n      width: boxLayoutModel.get('width'),\n      height: boxLayoutModel.get('height')\n    };\n  };\n  /**\r\n   * Get key for zlevel.\r\n   * If developers don't configure zlevel. We will assign zlevel to series based on the key.\r\n   * For example, lines with trail effect and progressive series will in an individual zlevel.\r\n   */\n  ComponentModel.prototype.getZLevelKey = function () {\n    return '';\n  };\n  ComponentModel.prototype.setZLevel = function (zlevel) {\n    this.option.zlevel = zlevel;\n  };\n  ComponentModel.protoInitialize = function () {\n    var proto = ComponentModel.prototype;\n    proto.type = 'component';\n    proto.id = '';\n    proto.name = '';\n    proto.mainType = '';\n    proto.subType = '';\n    proto.componentIndex = 0;\n  }();\n  return ComponentModel;\n}(Model);\nmountExtend(ComponentModel, Model);\nenableClassManagement(ComponentModel);\ncomponentUtil.enableSubTypeDefaulter(ComponentModel);\ncomponentUtil.enableTopologicalTravel(ComponentModel, getDependencies);\nfunction getDependencies(componentType) {\n  var deps = [];\n  zrUtil.each(ComponentModel.getClassesByMainType(componentType), function (clz) {\n    deps = deps.concat(clz.dependencies || clz.prototype.dependencies || []);\n  });\n  // Ensure main type.\n  deps = zrUtil.map(deps, function (type) {\n    return parseClassType(type).main;\n  });\n  // Hack dataset for convenience.\n  if (componentType !== 'dataset' && zrUtil.indexOf(deps, 'dataset') <= 0) {\n    deps.unshift('dataset');\n  }\n  return deps;\n}\nexport default ComponentModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}