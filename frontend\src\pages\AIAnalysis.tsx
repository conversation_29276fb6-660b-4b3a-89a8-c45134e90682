import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Typography, 
  Tag, 
  Progress, 
  Timeline,
  Alert,
  Button,
  Input,
  Space,
  Spin
} from 'antd';
import { 
  RobotOutlined,
  BulbOutlined,
  WarningOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  SendOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { TextArea } = Input;

interface SentimentData {
  ts_code: string;
  name: string;
  sentiment_score: number;
  sentiment_label: string;
  confidence: number;
  news_count: number;
}

interface RiskEvent {
  id: string;
  ts_code: string;
  name: string;
  event_type: string;
  risk_level: string;
  description: string;
  impact: string;
  time: string;
}

interface DailyReport {
  date: string;
  market_summary: string;
  key_events: string[];
  performance_analysis: string;
  recommendations: string[];
}

const AIAnalysis: React.FC = () => {
  const [sentimentData, setSentimentData] = useState<SentimentData[]>([]);
  const [riskEvents, setRiskEvents] = useState<RiskEvent[]>([]);
  const [dailyReport, setDailyReport] = useState<DailyReport | null>(null);
  const [newsInput, setNewsInput] = useState('');
  const [analyzing, setAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);

  // 模拟数据
  useEffect(() => {
    const mockSentiment: SentimentData[] = [
      {
        ts_code: '000001.SZ',
        name: '平安银行',
        sentiment_score: 0.65,
        sentiment_label: 'POSITIVE',
        confidence: 0.85,
        news_count: 12
      },
      {
        ts_code: '000002.SZ',
        name: '万科A',
        sentiment_score: -0.32,
        sentiment_label: 'NEGATIVE',
        confidence: 0.78,
        news_count: 8
      },
      {
        ts_code: '600000.SH',
        name: '浦发银行',
        sentiment_score: 0.15,
        sentiment_label: 'NEUTRAL',
        confidence: 0.72,
        news_count: 5
      }
    ];

    const mockRiskEvents: RiskEvent[] = [
      {
        id: '1',
        ts_code: '000002.SZ',
        name: '万科A',
        event_type: '监管风险',
        risk_level: 'HIGH',
        description: '房地产调控政策收紧，可能影响公司业务',
        impact: '预计对股价产生负面影响',
        time: '10:30'
      },
      {
        id: '2',
        ts_code: '000001.SZ',
        name: '平安银行',
        event_type: '业绩预告',
        risk_level: 'LOW',
        description: '三季度业绩超预期，净利润同比增长15%',
        impact: '利好消息，预期股价上涨',
        time: '09:45'
      }
    ];

    const mockReport: DailyReport = {
      date: '2024-01-20',
      market_summary: '今日A股市场整体表现平稳，上证指数微涨0.3%，深证成指下跌0.1%。银行板块表现较好，房地产板块承压。',
      key_events: [
        '央行宣布降准0.25个百分点',
        '房地产调控政策进一步收紧',
        '科技股集体回调',
        '银行股普遍上涨'
      ],
      performance_analysis: '组合今日收益率为+0.8%，跑赢大盘0.5个百分点。主要贡献来自银行股的强势表现，平安银行涨幅达到2.1%。',
      recommendations: [
        '继续持有银行股，关注政策面变化',
        '适当减持房地产相关标的',
        '关注科技股回调后的买入机会',
        '控制整体仓位，保持适度现金比例'
      ]
    };

    setSentimentData(mockSentiment);
    setRiskEvents(mockRiskEvents);
    setDailyReport(mockReport);
  }, []);

  const handleAnalyzeNews = async () => {
    if (!newsInput.trim()) return;

    setAnalyzing(true);
    
    // 模拟AI分析
    setTimeout(() => {
      const mockResult = {
        sentiment_score: Math.random() * 2 - 1, // -1 到 1
        sentiment_label: Math.random() > 0.5 ? 'POSITIVE' : 'NEGATIVE',
        confidence: 0.7 + Math.random() * 0.3,
        key_points: [
          '检测到政策相关关键词',
          '情绪倾向偏向积极',
          '可能对银行板块产生影响'
        ],
        risk_factors: [
          '政策不确定性',
          '市场波动风险'
        ]
      };
      
      setAnalysisResult(mockResult);
      setAnalyzing(false);
    }, 2000);
  };

  const getSentimentColor = (score: number) => {
    if (score > 0.3) return '#52c41a';
    if (score < -0.3) return '#ff4d4f';
    return '#faad14';
  };

  const getSentimentIcon = (label: string) => {
    if (label === 'POSITIVE') return <ArrowUpOutlined style={{ color: '#52c41a' }} />;
    if (label === 'NEGATIVE') return <ArrowDownOutlined style={{ color: '#ff4d4f' }} />;
    return <BulbOutlined style={{ color: '#faad14' }} />;
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <RobotOutlined /> AI智能分析
      </Title>

      {/* 市场情绪分析 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Card title="市场情绪分析" extra={<Tag color="blue">实时更新</Tag>}>
            <Row gutter={16}>
              {sentimentData.map(item => (
                <Col span={8} key={item.ts_code}>
                  <Card size="small" style={{ textAlign: 'center' }}>
                    <div style={{ marginBottom: '8px' }}>
                      {getSentimentIcon(item.sentiment_label)}
                      <Text strong style={{ marginLeft: '8px' }}>{item.name}</Text>
                    </div>
                    <Progress
                      type="circle"
                      percent={Math.abs(item.sentiment_score) * 100}
                      strokeColor={getSentimentColor(item.sentiment_score)}
                      format={() => item.sentiment_score.toFixed(2)}
                      size={80}
                    />
                    <div style={{ marginTop: '8px' }}>
                      <Tag color={getSentimentColor(item.sentiment_score)}>
                        {item.sentiment_label}
                      </Tag>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        置信度: {(item.confidence * 100).toFixed(1)}%
                      </div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        新闻数量: {item.news_count}
                      </div>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 风险事件监控 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={12}>
          <Card title="风险事件监控" extra={<WarningOutlined />}>
            <Timeline>
              {riskEvents.map(event => (
                <Timeline.Item
                  key={event.id}
                  color={event.risk_level === 'HIGH' ? 'red' : event.risk_level === 'MEDIUM' ? 'orange' : 'green'}
                >
                  <div>
                    <Text strong>{event.name}</Text>
                    <Tag color={event.risk_level === 'HIGH' ? 'red' : 'orange'} style={{ marginLeft: '8px' }}>
                      {event.risk_level}
                    </Tag>
                  </div>
                  <div style={{ marginTop: '4px' }}>
                    <Text type="secondary">{event.event_type}</Text>
                    <span style={{ float: 'right', color: '#666' }}>{event.time}</span>
                  </div>
                  <Paragraph style={{ marginTop: '8px', marginBottom: '4px' }}>
                    {event.description}
                  </Paragraph>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    影响评估: {event.impact}
                  </Text>
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="新闻分析工具">
            <Space direction="vertical" style={{ width: '100%' }}>
              <TextArea
                rows={6}
                placeholder="请输入要分析的新闻内容..."
                value={newsInput}
                onChange={(e) => setNewsInput(e.target.value)}
              />
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={handleAnalyzeNews}
                loading={analyzing}
                disabled={!newsInput.trim()}
              >
                AI分析
              </Button>
              
              {analyzing && (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <Spin size="large" />
                  <div style={{ marginTop: '16px' }}>AI正在分析中...</div>
                </div>
              )}
              
              {analysisResult && !analyzing && (
                <Card size="small" title="分析结果">
                  <div style={{ marginBottom: '12px' }}>
                    <Text strong>情绪评分: </Text>
                    <Tag color={getSentimentColor(analysisResult.sentiment_score)}>
                      {analysisResult.sentiment_score.toFixed(2)}
                    </Tag>
                    <Text strong style={{ marginLeft: '16px' }}>置信度: </Text>
                    <span>{(analysisResult.confidence * 100).toFixed(1)}%</span>
                  </div>
                  
                  <div style={{ marginBottom: '12px' }}>
                    <Text strong>关键要点:</Text>
                    <ul style={{ marginTop: '8px' }}>
                      {analysisResult.key_points.map((point: string, index: number) => (
                        <li key={index}>{point}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <Text strong>风险因素:</Text>
                    <ul style={{ marginTop: '8px' }}>
                      {analysisResult.risk_factors.map((factor: string, index: number) => (
                        <li key={index}>{factor}</li>
                      ))}
                    </ul>
                  </div>
                </Card>
              )}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 每日AI报告 */}
      {dailyReport && (
        <Row>
          <Col span={24}>
            <Card title={`每日AI复盘报告 - ${dailyReport.date}`} extra={<RobotOutlined />}>
              <Row gutter={16}>
                <Col span={12}>
                  <Card size="small" title="市场概况">
                    <Paragraph>{dailyReport.market_summary}</Paragraph>
                  </Card>
                  
                  <Card size="small" title="关键事件" style={{ marginTop: '16px' }}>
                    <ul>
                      {dailyReport.key_events.map((event, index) => (
                        <li key={index}>{event}</li>
                      ))}
                    </ul>
                  </Card>
                </Col>
                
                <Col span={12}>
                  <Card size="small" title="组合表现">
                    <Paragraph>{dailyReport.performance_analysis}</Paragraph>
                  </Card>
                  
                  <Card size="small" title="AI建议" style={{ marginTop: '16px' }}>
                    <ul>
                      {dailyReport.recommendations.map((rec, index) => (
                        <li key={index}>{rec}</li>
                      ))}
                    </ul>
                  </Card>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default AIAnalysis;
