{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport SymbolClz from '../helper/Symbol.js';\nimport { radialCoordinate } from './layoutHelper.js';\nimport * as bbox from 'zrender/lib/core/bbox.js';\nimport View from '../../coord/View.js';\nimport * as roamHelper from '../../component/helper/roamHelper.js';\nimport RoamController from '../../component/helper/RoamController.js';\nimport { onIrrelevantElement } from '../../component/helper/cursorHelper.js';\nimport { parsePercent } from '../../util/number.js';\nimport ChartView from '../../view/Chart.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport { setStatesStylesFromModel, setStatesFlag, setDefaultStateProxy, HOVER_STATE_BLUR } from '../../util/states.js';\nvar TreeEdgeShape = /** @class */function () {\n  function TreeEdgeShape() {\n    this.parentPoint = [];\n    this.childPoints = [];\n  }\n  return TreeEdgeShape;\n}();\nvar TreePath = /** @class */function (_super) {\n  __extends(TreePath, _super);\n  function TreePath(opts) {\n    return _super.call(this, opts) || this;\n  }\n  TreePath.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  TreePath.prototype.getDefaultShape = function () {\n    return new TreeEdgeShape();\n  };\n  TreePath.prototype.buildPath = function (ctx, shape) {\n    var childPoints = shape.childPoints;\n    var childLen = childPoints.length;\n    var parentPoint = shape.parentPoint;\n    var firstChildPos = childPoints[0];\n    var lastChildPos = childPoints[childLen - 1];\n    if (childLen === 1) {\n      ctx.moveTo(parentPoint[0], parentPoint[1]);\n      ctx.lineTo(firstChildPos[0], firstChildPos[1]);\n      return;\n    }\n    var orient = shape.orient;\n    var forkDim = orient === 'TB' || orient === 'BT' ? 0 : 1;\n    var otherDim = 1 - forkDim;\n    var forkPosition = parsePercent(shape.forkPosition, 1);\n    var tmpPoint = [];\n    tmpPoint[forkDim] = parentPoint[forkDim];\n    tmpPoint[otherDim] = parentPoint[otherDim] + (lastChildPos[otherDim] - parentPoint[otherDim]) * forkPosition;\n    ctx.moveTo(parentPoint[0], parentPoint[1]);\n    ctx.lineTo(tmpPoint[0], tmpPoint[1]);\n    ctx.moveTo(firstChildPos[0], firstChildPos[1]);\n    tmpPoint[forkDim] = firstChildPos[forkDim];\n    ctx.lineTo(tmpPoint[0], tmpPoint[1]);\n    tmpPoint[forkDim] = lastChildPos[forkDim];\n    ctx.lineTo(tmpPoint[0], tmpPoint[1]);\n    ctx.lineTo(lastChildPos[0], lastChildPos[1]);\n    for (var i = 1; i < childLen - 1; i++) {\n      var point = childPoints[i];\n      ctx.moveTo(point[0], point[1]);\n      tmpPoint[forkDim] = point[forkDim];\n      ctx.lineTo(tmpPoint[0], tmpPoint[1]);\n    }\n  };\n  return TreePath;\n}(Path);\nvar TreeView = /** @class */function (_super) {\n  __extends(TreeView, _super);\n  function TreeView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TreeView.type;\n    _this._mainGroup = new graphic.Group();\n    return _this;\n  }\n  TreeView.prototype.init = function (ecModel, api) {\n    this._controller = new RoamController(api.getZr());\n    this._controllerHost = {\n      target: this.group\n    };\n    this.group.add(this._mainGroup);\n  };\n  TreeView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var layoutInfo = seriesModel.layoutInfo;\n    var group = this._mainGroup;\n    var layout = seriesModel.get('layout');\n    if (layout === 'radial') {\n      group.x = layoutInfo.x + layoutInfo.width / 2;\n      group.y = layoutInfo.y + layoutInfo.height / 2;\n    } else {\n      group.x = layoutInfo.x;\n      group.y = layoutInfo.y;\n    }\n    this._updateViewCoordSys(seriesModel, api);\n    this._updateController(seriesModel, ecModel, api);\n    var oldData = this._data;\n    data.diff(oldData).add(function (newIdx) {\n      if (symbolNeedsDraw(data, newIdx)) {\n        // Create node and edge\n        updateNode(data, newIdx, null, group, seriesModel);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var symbolEl = oldData.getItemGraphicEl(oldIdx);\n      if (!symbolNeedsDraw(data, newIdx)) {\n        symbolEl && removeNode(oldData, oldIdx, symbolEl, group, seriesModel);\n        return;\n      }\n      // Update node and edge\n      updateNode(data, newIdx, symbolEl, group, seriesModel);\n    }).remove(function (oldIdx) {\n      var symbolEl = oldData.getItemGraphicEl(oldIdx);\n      // When remove a collapsed node of subtree, since the collapsed\n      // node haven't been initialized with a symbol element,\n      // you can't found it's symbol element through index.\n      // so if we want to remove the symbol element we should insure\n      // that the symbol element is not null.\n      if (symbolEl) {\n        removeNode(oldData, oldIdx, symbolEl, group, seriesModel);\n      }\n    }).execute();\n    this._nodeScaleRatio = seriesModel.get('nodeScaleRatio');\n    this._updateNodeAndLinkScale(seriesModel);\n    if (seriesModel.get('expandAndCollapse') === true) {\n      data.eachItemGraphicEl(function (el, dataIndex) {\n        el.off('click').on('click', function () {\n          api.dispatchAction({\n            type: 'treeExpandAndCollapse',\n            seriesId: seriesModel.id,\n            dataIndex: dataIndex\n          });\n        });\n      });\n    }\n    this._data = data;\n  };\n  TreeView.prototype._updateViewCoordSys = function (seriesModel, api) {\n    var data = seriesModel.getData();\n    var points = [];\n    data.each(function (idx) {\n      var layout = data.getItemLayout(idx);\n      if (layout && !isNaN(layout.x) && !isNaN(layout.y)) {\n        points.push([+layout.x, +layout.y]);\n      }\n    });\n    var min = [];\n    var max = [];\n    bbox.fromPoints(points, min, max);\n    // If don't Store min max when collapse the root node after roam,\n    // the root node will disappear.\n    var oldMin = this._min;\n    var oldMax = this._max;\n    // If width or height is 0\n    if (max[0] - min[0] === 0) {\n      min[0] = oldMin ? oldMin[0] : min[0] - 1;\n      max[0] = oldMax ? oldMax[0] : max[0] + 1;\n    }\n    if (max[1] - min[1] === 0) {\n      min[1] = oldMin ? oldMin[1] : min[1] - 1;\n      max[1] = oldMax ? oldMax[1] : max[1] + 1;\n    }\n    var viewCoordSys = seriesModel.coordinateSystem = new View();\n    viewCoordSys.zoomLimit = seriesModel.get('scaleLimit');\n    viewCoordSys.setBoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n    viewCoordSys.setCenter(seriesModel.get('center'), api);\n    viewCoordSys.setZoom(seriesModel.get('zoom'));\n    // Here we use viewCoordSys just for computing the 'position' and 'scale' of the group\n    this.group.attr({\n      x: viewCoordSys.x,\n      y: viewCoordSys.y,\n      scaleX: viewCoordSys.scaleX,\n      scaleY: viewCoordSys.scaleY\n    });\n    this._min = min;\n    this._max = max;\n  };\n  TreeView.prototype._updateController = function (seriesModel, ecModel, api) {\n    var _this = this;\n    var controller = this._controller;\n    var controllerHost = this._controllerHost;\n    var group = this.group;\n    controller.setPointerChecker(function (e, x, y) {\n      var rect = group.getBoundingRect();\n      rect.applyTransform(group.transform);\n      return rect.contain(x, y) && !onIrrelevantElement(e, api, seriesModel);\n    });\n    controller.enable(seriesModel.get('roam'));\n    controllerHost.zoomLimit = seriesModel.get('scaleLimit');\n    controllerHost.zoom = seriesModel.coordinateSystem.getZoom();\n    controller.off('pan').off('zoom').on('pan', function (e) {\n      roamHelper.updateViewOnPan(controllerHost, e.dx, e.dy);\n      api.dispatchAction({\n        seriesId: seriesModel.id,\n        type: 'treeRoam',\n        dx: e.dx,\n        dy: e.dy\n      });\n    }).on('zoom', function (e) {\n      roamHelper.updateViewOnZoom(controllerHost, e.scale, e.originX, e.originY);\n      api.dispatchAction({\n        seriesId: seriesModel.id,\n        type: 'treeRoam',\n        zoom: e.scale,\n        originX: e.originX,\n        originY: e.originY\n      });\n      _this._updateNodeAndLinkScale(seriesModel);\n      // Only update label layout on zoom\n      api.updateLabelLayout();\n    });\n  };\n  TreeView.prototype._updateNodeAndLinkScale = function (seriesModel) {\n    var data = seriesModel.getData();\n    var nodeScale = this._getNodeGlobalScale(seriesModel);\n    data.eachItemGraphicEl(function (el, idx) {\n      el.setSymbolScale(nodeScale);\n    });\n  };\n  TreeView.prototype._getNodeGlobalScale = function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys.type !== 'view') {\n      return 1;\n    }\n    var nodeScaleRatio = this._nodeScaleRatio;\n    var groupZoom = coordSys.scaleX || 1;\n    // Scale node when zoom changes\n    var roamZoom = coordSys.getZoom();\n    var nodeScale = (roamZoom - 1) * nodeScaleRatio + 1;\n    return nodeScale / groupZoom;\n  };\n  TreeView.prototype.dispose = function () {\n    this._controller && this._controller.dispose();\n    this._controllerHost = null;\n  };\n  TreeView.prototype.remove = function () {\n    this._mainGroup.removeAll();\n    this._data = null;\n  };\n  TreeView.type = 'tree';\n  return TreeView;\n}(ChartView);\nfunction symbolNeedsDraw(data, dataIndex) {\n  var layout = data.getItemLayout(dataIndex);\n  return layout && !isNaN(layout.x) && !isNaN(layout.y);\n}\nfunction updateNode(data, dataIndex, symbolEl, group, seriesModel) {\n  var isInit = !symbolEl;\n  var node = data.tree.getNodeByDataIndex(dataIndex);\n  var itemModel = node.getModel();\n  var visualColor = node.getVisual('style').fill;\n  var symbolInnerColor = node.isExpand === false && node.children.length !== 0 ? visualColor : '#fff';\n  var virtualRoot = data.tree.root;\n  var source = node.parentNode === virtualRoot ? node : node.parentNode || node;\n  var sourceSymbolEl = data.getItemGraphicEl(source.dataIndex);\n  var sourceLayout = source.getLayout();\n  var sourceOldLayout = sourceSymbolEl ? {\n    x: sourceSymbolEl.__oldX,\n    y: sourceSymbolEl.__oldY,\n    rawX: sourceSymbolEl.__radialOldRawX,\n    rawY: sourceSymbolEl.__radialOldRawY\n  } : sourceLayout;\n  var targetLayout = node.getLayout();\n  if (isInit) {\n    symbolEl = new SymbolClz(data, dataIndex, null, {\n      symbolInnerColor: symbolInnerColor,\n      useNameLabel: true\n    });\n    symbolEl.x = sourceOldLayout.x;\n    symbolEl.y = sourceOldLayout.y;\n  } else {\n    symbolEl.updateData(data, dataIndex, null, {\n      symbolInnerColor: symbolInnerColor,\n      useNameLabel: true\n    });\n  }\n  symbolEl.__radialOldRawX = symbolEl.__radialRawX;\n  symbolEl.__radialOldRawY = symbolEl.__radialRawY;\n  symbolEl.__radialRawX = targetLayout.rawX;\n  symbolEl.__radialRawY = targetLayout.rawY;\n  group.add(symbolEl);\n  data.setItemGraphicEl(dataIndex, symbolEl);\n  symbolEl.__oldX = symbolEl.x;\n  symbolEl.__oldY = symbolEl.y;\n  graphic.updateProps(symbolEl, {\n    x: targetLayout.x,\n    y: targetLayout.y\n  }, seriesModel);\n  var symbolPath = symbolEl.getSymbolPath();\n  if (seriesModel.get('layout') === 'radial') {\n    var realRoot = virtualRoot.children[0];\n    var rootLayout = realRoot.getLayout();\n    var length_1 = realRoot.children.length;\n    var rad = void 0;\n    var isLeft = void 0;\n    if (targetLayout.x === rootLayout.x && node.isExpand === true && realRoot.children.length) {\n      var center = {\n        x: (realRoot.children[0].getLayout().x + realRoot.children[length_1 - 1].getLayout().x) / 2,\n        y: (realRoot.children[0].getLayout().y + realRoot.children[length_1 - 1].getLayout().y) / 2\n      };\n      rad = Math.atan2(center.y - rootLayout.y, center.x - rootLayout.x);\n      if (rad < 0) {\n        rad = Math.PI * 2 + rad;\n      }\n      isLeft = center.x < rootLayout.x;\n      if (isLeft) {\n        rad = rad - Math.PI;\n      }\n    } else {\n      rad = Math.atan2(targetLayout.y - rootLayout.y, targetLayout.x - rootLayout.x);\n      if (rad < 0) {\n        rad = Math.PI * 2 + rad;\n      }\n      if (node.children.length === 0 || node.children.length !== 0 && node.isExpand === false) {\n        isLeft = targetLayout.x < rootLayout.x;\n        if (isLeft) {\n          rad = rad - Math.PI;\n        }\n      } else {\n        isLeft = targetLayout.x > rootLayout.x;\n        if (!isLeft) {\n          rad = rad - Math.PI;\n        }\n      }\n    }\n    var textPosition = isLeft ? 'left' : 'right';\n    var normalLabelModel = itemModel.getModel('label');\n    var rotate = normalLabelModel.get('rotate');\n    var labelRotateRadian = rotate * (Math.PI / 180);\n    var textContent = symbolPath.getTextContent();\n    if (textContent) {\n      symbolPath.setTextConfig({\n        position: normalLabelModel.get('position') || textPosition,\n        rotation: rotate == null ? -rad : labelRotateRadian,\n        origin: 'center'\n      });\n      textContent.setStyle('verticalAlign', 'middle');\n    }\n  }\n  // Handle status\n  var focus = itemModel.get(['emphasis', 'focus']);\n  var focusDataIndices = focus === 'relative' ? zrUtil.concatArray(node.getAncestorsIndices(), node.getDescendantIndices()) : focus === 'ancestor' ? node.getAncestorsIndices() : focus === 'descendant' ? node.getDescendantIndices() : null;\n  if (focusDataIndices) {\n    // Modify the focus to data indices.\n    getECData(symbolEl).focus = focusDataIndices;\n  }\n  drawEdge(seriesModel, node, virtualRoot, symbolEl, sourceOldLayout, sourceLayout, targetLayout, group);\n  if (symbolEl.__edge) {\n    symbolEl.onHoverStateChange = function (toState) {\n      if (toState !== 'blur') {\n        // NOTE: Ensure the parent elements will been blurred firstly.\n        // According to the return of getAncestorsIndices and getDescendantIndices\n        // TODO: A bit tricky.\n        var parentEl = node.parentNode && data.getItemGraphicEl(node.parentNode.dataIndex);\n        if (!(parentEl && parentEl.hoverState === HOVER_STATE_BLUR)) {\n          setStatesFlag(symbolEl.__edge, toState);\n        }\n      }\n    };\n  }\n}\nfunction drawEdge(seriesModel, node, virtualRoot, symbolEl, sourceOldLayout, sourceLayout, targetLayout, group) {\n  var itemModel = node.getModel();\n  var edgeShape = seriesModel.get('edgeShape');\n  var layout = seriesModel.get('layout');\n  var orient = seriesModel.getOrient();\n  var curvature = seriesModel.get(['lineStyle', 'curveness']);\n  var edgeForkPosition = seriesModel.get('edgeForkPosition');\n  var lineStyle = itemModel.getModel('lineStyle').getLineStyle();\n  var edge = symbolEl.__edge;\n  // curve edge from node -> parent\n  // polyline edge from node -> children\n  if (edgeShape === 'curve') {\n    if (node.parentNode && node.parentNode !== virtualRoot) {\n      if (!edge) {\n        edge = symbolEl.__edge = new graphic.BezierCurve({\n          shape: getEdgeShape(layout, orient, curvature, sourceOldLayout, sourceOldLayout)\n        });\n      }\n      graphic.updateProps(edge, {\n        shape: getEdgeShape(layout, orient, curvature, sourceLayout, targetLayout)\n      }, seriesModel);\n    }\n  } else if (edgeShape === 'polyline') {\n    if (layout === 'orthogonal') {\n      if (node !== virtualRoot && node.children && node.children.length !== 0 && node.isExpand === true) {\n        var children = node.children;\n        var childPoints = [];\n        for (var i = 0; i < children.length; i++) {\n          var childLayout = children[i].getLayout();\n          childPoints.push([childLayout.x, childLayout.y]);\n        }\n        if (!edge) {\n          edge = symbolEl.__edge = new TreePath({\n            shape: {\n              parentPoint: [targetLayout.x, targetLayout.y],\n              childPoints: [[targetLayout.x, targetLayout.y]],\n              orient: orient,\n              forkPosition: edgeForkPosition\n            }\n          });\n        }\n        graphic.updateProps(edge, {\n          shape: {\n            parentPoint: [targetLayout.x, targetLayout.y],\n            childPoints: childPoints\n          }\n        }, seriesModel);\n      }\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error('The polyline edgeShape can only be used in orthogonal layout');\n      }\n    }\n  }\n  // show all edge when edgeShape is 'curve', filter node `isExpand` is false when edgeShape is 'polyline'\n  if (edge && !(edgeShape === 'polyline' && !node.isExpand)) {\n    edge.useStyle(zrUtil.defaults({\n      strokeNoScale: true,\n      fill: null\n    }, lineStyle));\n    setStatesStylesFromModel(edge, itemModel, 'lineStyle');\n    setDefaultStateProxy(edge);\n    group.add(edge);\n  }\n}\nfunction removeNodeEdge(node, data, group, seriesModel, removeAnimationOpt) {\n  var virtualRoot = data.tree.root;\n  var _a = getSourceNode(virtualRoot, node),\n    source = _a.source,\n    sourceLayout = _a.sourceLayout;\n  var symbolEl = data.getItemGraphicEl(node.dataIndex);\n  if (!symbolEl) {\n    return;\n  }\n  var sourceSymbolEl = data.getItemGraphicEl(source.dataIndex);\n  var sourceEdge = sourceSymbolEl.__edge;\n  // 1. when expand the sub tree, delete the children node should delete the edge of\n  // the source at the same time. because the polyline edge shape is only owned by the source.\n  // 2.when the node is the only children of the source, delete the node should delete the edge of\n  // the source at the same time. the same reason as above.\n  var edge = symbolEl.__edge || (source.isExpand === false || source.children.length === 1 ? sourceEdge : undefined);\n  var edgeShape = seriesModel.get('edgeShape');\n  var layoutOpt = seriesModel.get('layout');\n  var orient = seriesModel.get('orient');\n  var curvature = seriesModel.get(['lineStyle', 'curveness']);\n  if (edge) {\n    if (edgeShape === 'curve') {\n      graphic.removeElement(edge, {\n        shape: getEdgeShape(layoutOpt, orient, curvature, sourceLayout, sourceLayout),\n        style: {\n          opacity: 0\n        }\n      }, seriesModel, {\n        cb: function () {\n          group.remove(edge);\n        },\n        removeOpt: removeAnimationOpt\n      });\n    } else if (edgeShape === 'polyline' && seriesModel.get('layout') === 'orthogonal') {\n      graphic.removeElement(edge, {\n        shape: {\n          parentPoint: [sourceLayout.x, sourceLayout.y],\n          childPoints: [[sourceLayout.x, sourceLayout.y]]\n        },\n        style: {\n          opacity: 0\n        }\n      }, seriesModel, {\n        cb: function () {\n          group.remove(edge);\n        },\n        removeOpt: removeAnimationOpt\n      });\n    }\n  }\n}\nfunction getSourceNode(virtualRoot, node) {\n  var source = node.parentNode === virtualRoot ? node : node.parentNode || node;\n  var sourceLayout;\n  while (sourceLayout = source.getLayout(), sourceLayout == null) {\n    source = source.parentNode === virtualRoot ? source : source.parentNode || source;\n  }\n  return {\n    source: source,\n    sourceLayout: sourceLayout\n  };\n}\nfunction removeNode(data, dataIndex, symbolEl, group, seriesModel) {\n  var node = data.tree.getNodeByDataIndex(dataIndex);\n  var virtualRoot = data.tree.root;\n  var sourceLayout = getSourceNode(virtualRoot, node).sourceLayout;\n  // Use same duration and easing with update to have more consistent animation.\n  var removeAnimationOpt = {\n    duration: seriesModel.get('animationDurationUpdate'),\n    easing: seriesModel.get('animationEasingUpdate')\n  };\n  graphic.removeElement(symbolEl, {\n    x: sourceLayout.x + 1,\n    y: sourceLayout.y + 1\n  }, seriesModel, {\n    cb: function () {\n      group.remove(symbolEl);\n      data.setItemGraphicEl(dataIndex, null);\n    },\n    removeOpt: removeAnimationOpt\n  });\n  symbolEl.fadeOut(null, data.hostModel, {\n    fadeLabel: true,\n    animation: removeAnimationOpt\n  });\n  // remove edge as parent node\n  node.children.forEach(function (childNode) {\n    removeNodeEdge(childNode, data, group, seriesModel, removeAnimationOpt);\n  });\n  // remove edge as child node\n  removeNodeEdge(node, data, group, seriesModel, removeAnimationOpt);\n}\nfunction getEdgeShape(layoutOpt, orient, curvature, sourceLayout, targetLayout) {\n  var cpx1;\n  var cpy1;\n  var cpx2;\n  var cpy2;\n  var x1;\n  var x2;\n  var y1;\n  var y2;\n  if (layoutOpt === 'radial') {\n    x1 = sourceLayout.rawX;\n    y1 = sourceLayout.rawY;\n    x2 = targetLayout.rawX;\n    y2 = targetLayout.rawY;\n    var radialCoor1 = radialCoordinate(x1, y1);\n    var radialCoor2 = radialCoordinate(x1, y1 + (y2 - y1) * curvature);\n    var radialCoor3 = radialCoordinate(x2, y2 + (y1 - y2) * curvature);\n    var radialCoor4 = radialCoordinate(x2, y2);\n    return {\n      x1: radialCoor1.x || 0,\n      y1: radialCoor1.y || 0,\n      x2: radialCoor4.x || 0,\n      y2: radialCoor4.y || 0,\n      cpx1: radialCoor2.x || 0,\n      cpy1: radialCoor2.y || 0,\n      cpx2: radialCoor3.x || 0,\n      cpy2: radialCoor3.y || 0\n    };\n  } else {\n    x1 = sourceLayout.x;\n    y1 = sourceLayout.y;\n    x2 = targetLayout.x;\n    y2 = targetLayout.y;\n    if (orient === 'LR' || orient === 'RL') {\n      cpx1 = x1 + (x2 - x1) * curvature;\n      cpy1 = y1;\n      cpx2 = x2 + (x1 - x2) * curvature;\n      cpy2 = y2;\n    }\n    if (orient === 'TB' || orient === 'BT') {\n      cpx1 = x1;\n      cpy1 = y1 + (y2 - y1) * curvature;\n      cpx2 = x2;\n      cpy2 = y2 + (y1 - y2) * curvature;\n    }\n  }\n  return {\n    x1: x1,\n    y1: y1,\n    x2: x2,\n    y2: y2,\n    cpx1: cpx1,\n    cpy1: cpy1,\n    cpx2: cpx2,\n    cpy2: cpy2\n  };\n}\nexport default TreeView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}