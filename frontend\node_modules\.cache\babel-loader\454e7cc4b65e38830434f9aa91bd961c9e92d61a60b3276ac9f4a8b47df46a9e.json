{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as numberUtil from './number.js';\nimport { getDefaultLocaleModel, getLocaleModel, SYSTEM_LANG } from '../core/locale.js';\nimport Model from '../model/Model.js';\nexport var ONE_SECOND = 1000;\nexport var ONE_MINUTE = ONE_SECOND * 60;\nexport var ONE_HOUR = ONE_MINUTE * 60;\nexport var ONE_DAY = ONE_HOUR * 24;\nexport var ONE_YEAR = ONE_DAY * 365;\nexport var defaultLeveledFormatter = {\n  year: '{yyyy}',\n  month: '{MMM}',\n  day: '{d}',\n  hour: '{HH}:{mm}',\n  minute: '{HH}:{mm}',\n  second: '{HH}:{mm}:{ss}',\n  millisecond: '{HH}:{mm}:{ss} {SSS}',\n  none: '{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}'\n};\nvar fullDayFormatter = '{yyyy}-{MM}-{dd}';\nexport var fullLeveledFormatter = {\n  year: '{yyyy}',\n  month: '{yyyy}-{MM}',\n  day: fullDayFormatter,\n  hour: fullDayFormatter + ' ' + defaultLeveledFormatter.hour,\n  minute: fullDayFormatter + ' ' + defaultLeveledFormatter.minute,\n  second: fullDayFormatter + ' ' + defaultLeveledFormatter.second,\n  millisecond: defaultLeveledFormatter.none\n};\nexport var primaryTimeUnits = ['year', 'month', 'day', 'hour', 'minute', 'second', 'millisecond'];\nexport var timeUnits = ['year', 'half-year', 'quarter', 'month', 'week', 'half-week', 'day', 'half-day', 'quarter-day', 'hour', 'minute', 'second', 'millisecond'];\nexport function pad(str, len) {\n  str += '';\n  return '0000'.substr(0, len - str.length) + str;\n}\nexport function getPrimaryTimeUnit(timeUnit) {\n  switch (timeUnit) {\n    case 'half-year':\n    case 'quarter':\n      return 'month';\n    case 'week':\n    case 'half-week':\n      return 'day';\n    case 'half-day':\n    case 'quarter-day':\n      return 'hour';\n    default:\n      // year, minutes, second, milliseconds\n      return timeUnit;\n  }\n}\nexport function isPrimaryTimeUnit(timeUnit) {\n  return timeUnit === getPrimaryTimeUnit(timeUnit);\n}\nexport function getDefaultFormatPrecisionOfInterval(timeUnit) {\n  switch (timeUnit) {\n    case 'year':\n    case 'month':\n      return 'day';\n    case 'millisecond':\n      return 'millisecond';\n    default:\n      // Also for day, hour, minute, second\n      return 'second';\n  }\n}\nexport function format(\n// Note: The result based on `isUTC` are totally different, which can not be just simply\n// substituted by the result without `isUTC`. So we make the param `isUTC` mandatory.\ntime, template, isUTC, lang) {\n  var date = numberUtil.parseDate(time);\n  var y = date[fullYearGetterName(isUTC)]();\n  var M = date[monthGetterName(isUTC)]() + 1;\n  var q = Math.floor((M - 1) / 3) + 1;\n  var d = date[dateGetterName(isUTC)]();\n  var e = date['get' + (isUTC ? 'UTC' : '') + 'Day']();\n  var H = date[hoursGetterName(isUTC)]();\n  var h = (H - 1) % 12 + 1;\n  var m = date[minutesGetterName(isUTC)]();\n  var s = date[secondsGetterName(isUTC)]();\n  var S = date[millisecondsGetterName(isUTC)]();\n  var a = H >= 12 ? 'pm' : 'am';\n  var A = a.toUpperCase();\n  var localeModel = lang instanceof Model ? lang : getLocaleModel(lang || SYSTEM_LANG) || getDefaultLocaleModel();\n  var timeModel = localeModel.getModel('time');\n  var month = timeModel.get('month');\n  var monthAbbr = timeModel.get('monthAbbr');\n  var dayOfWeek = timeModel.get('dayOfWeek');\n  var dayOfWeekAbbr = timeModel.get('dayOfWeekAbbr');\n  return (template || '').replace(/{a}/g, a + '').replace(/{A}/g, A + '').replace(/{yyyy}/g, y + '').replace(/{yy}/g, pad(y % 100 + '', 2)).replace(/{Q}/g, q + '').replace(/{MMMM}/g, month[M - 1]).replace(/{MMM}/g, monthAbbr[M - 1]).replace(/{MM}/g, pad(M, 2)).replace(/{M}/g, M + '').replace(/{dd}/g, pad(d, 2)).replace(/{d}/g, d + '').replace(/{eeee}/g, dayOfWeek[e]).replace(/{ee}/g, dayOfWeekAbbr[e]).replace(/{e}/g, e + '').replace(/{HH}/g, pad(H, 2)).replace(/{H}/g, H + '').replace(/{hh}/g, pad(h + '', 2)).replace(/{h}/g, h + '').replace(/{mm}/g, pad(m, 2)).replace(/{m}/g, m + '').replace(/{ss}/g, pad(s, 2)).replace(/{s}/g, s + '').replace(/{SSS}/g, pad(S, 3)).replace(/{S}/g, S + '');\n}\nexport function leveledFormat(tick, idx, formatter, lang, isUTC) {\n  var template = null;\n  if (zrUtil.isString(formatter)) {\n    // Single formatter for all units at all levels\n    template = formatter;\n  } else if (zrUtil.isFunction(formatter)) {\n    // Callback formatter\n    template = formatter(tick.value, idx, {\n      level: tick.level\n    });\n  } else {\n    var defaults = zrUtil.extend({}, defaultLeveledFormatter);\n    if (tick.level > 0) {\n      for (var i = 0; i < primaryTimeUnits.length; ++i) {\n        defaults[primaryTimeUnits[i]] = \"{primary|\" + defaults[primaryTimeUnits[i]] + \"}\";\n      }\n    }\n    var mergedFormatter = formatter ? formatter.inherit === false ? formatter // Use formatter with bigger units\n    : zrUtil.defaults(formatter, defaults) : defaults;\n    var unit = getUnitFromValue(tick.value, isUTC);\n    if (mergedFormatter[unit]) {\n      template = mergedFormatter[unit];\n    } else if (mergedFormatter.inherit) {\n      // Unit formatter is not defined and should inherit from bigger units\n      var targetId = timeUnits.indexOf(unit);\n      for (var i = targetId - 1; i >= 0; --i) {\n        if (mergedFormatter[unit]) {\n          template = mergedFormatter[unit];\n          break;\n        }\n      }\n      template = template || defaults.none;\n    }\n    if (zrUtil.isArray(template)) {\n      var levelId = tick.level == null ? 0 : tick.level >= 0 ? tick.level : template.length + tick.level;\n      levelId = Math.min(levelId, template.length - 1);\n      template = template[levelId];\n    }\n  }\n  return format(new Date(tick.value), template, isUTC, lang);\n}\nexport function getUnitFromValue(value, isUTC) {\n  var date = numberUtil.parseDate(value);\n  var M = date[monthGetterName(isUTC)]() + 1;\n  var d = date[dateGetterName(isUTC)]();\n  var h = date[hoursGetterName(isUTC)]();\n  var m = date[minutesGetterName(isUTC)]();\n  var s = date[secondsGetterName(isUTC)]();\n  var S = date[millisecondsGetterName(isUTC)]();\n  var isSecond = S === 0;\n  var isMinute = isSecond && s === 0;\n  var isHour = isMinute && m === 0;\n  var isDay = isHour && h === 0;\n  var isMonth = isDay && d === 1;\n  var isYear = isMonth && M === 1;\n  if (isYear) {\n    return 'year';\n  } else if (isMonth) {\n    return 'month';\n  } else if (isDay) {\n    return 'day';\n  } else if (isHour) {\n    return 'hour';\n  } else if (isMinute) {\n    return 'minute';\n  } else if (isSecond) {\n    return 'second';\n  } else {\n    return 'millisecond';\n  }\n}\nexport function getUnitValue(value, unit, isUTC) {\n  var date = zrUtil.isNumber(value) ? numberUtil.parseDate(value) : value;\n  unit = unit || getUnitFromValue(value, isUTC);\n  switch (unit) {\n    case 'year':\n      return date[fullYearGetterName(isUTC)]();\n    case 'half-year':\n      return date[monthGetterName(isUTC)]() >= 6 ? 1 : 0;\n    case 'quarter':\n      return Math.floor((date[monthGetterName(isUTC)]() + 1) / 4);\n    case 'month':\n      return date[monthGetterName(isUTC)]();\n    case 'day':\n      return date[dateGetterName(isUTC)]();\n    case 'half-day':\n      return date[hoursGetterName(isUTC)]() / 24;\n    case 'hour':\n      return date[hoursGetterName(isUTC)]();\n    case 'minute':\n      return date[minutesGetterName(isUTC)]();\n    case 'second':\n      return date[secondsGetterName(isUTC)]();\n    case 'millisecond':\n      return date[millisecondsGetterName(isUTC)]();\n  }\n}\nexport function fullYearGetterName(isUTC) {\n  return isUTC ? 'getUTCFullYear' : 'getFullYear';\n}\nexport function monthGetterName(isUTC) {\n  return isUTC ? 'getUTCMonth' : 'getMonth';\n}\nexport function dateGetterName(isUTC) {\n  return isUTC ? 'getUTCDate' : 'getDate';\n}\nexport function hoursGetterName(isUTC) {\n  return isUTC ? 'getUTCHours' : 'getHours';\n}\nexport function minutesGetterName(isUTC) {\n  return isUTC ? 'getUTCMinutes' : 'getMinutes';\n}\nexport function secondsGetterName(isUTC) {\n  return isUTC ? 'getUTCSeconds' : 'getSeconds';\n}\nexport function millisecondsGetterName(isUTC) {\n  return isUTC ? 'getUTCMilliseconds' : 'getMilliseconds';\n}\nexport function fullYearSetterName(isUTC) {\n  return isUTC ? 'setUTCFullYear' : 'setFullYear';\n}\nexport function monthSetterName(isUTC) {\n  return isUTC ? 'setUTCMonth' : 'setMonth';\n}\nexport function dateSetterName(isUTC) {\n  return isUTC ? 'setUTCDate' : 'setDate';\n}\nexport function hoursSetterName(isUTC) {\n  return isUTC ? 'setUTCHours' : 'setHours';\n}\nexport function minutesSetterName(isUTC) {\n  return isUTC ? 'setUTCMinutes' : 'setMinutes';\n}\nexport function secondsSetterName(isUTC) {\n  return isUTC ? 'setUTCSeconds' : 'setSeconds';\n}\nexport function millisecondsSetterName(isUTC) {\n  return isUTC ? 'setUTCMilliseconds' : 'setMilliseconds';\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}