{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n// Poly path support NaN point\nimport Path from 'zrender/lib/graphic/Path.js';\nimport PathProxy from 'zrender/lib/core/PathProxy.js';\nimport { cubicRootAt, cubicAt } from 'zrender/lib/core/curve.js';\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nfunction isPointNull(x, y) {\n  return isNaN(x) || isNaN(y);\n}\n/**\r\n * Draw smoothed line in non-monotone, in may cause undesired curve in extreme\r\n * situations. This should be used when points are non-monotone neither in x or\r\n * y dimension.\r\n */\nfunction drawSegment(ctx, points, start, segLen, allLen, dir, smooth, smoothMonotone, connectNulls) {\n  var prevX;\n  var prevY;\n  var cpx0;\n  var cpy0;\n  var cpx1;\n  var cpy1;\n  var idx = start;\n  var k = 0;\n  for (; k < segLen; k++) {\n    var x = points[idx * 2];\n    var y = points[idx * 2 + 1];\n    if (idx >= allLen || idx < 0) {\n      break;\n    }\n    if (isPointNull(x, y)) {\n      if (connectNulls) {\n        idx += dir;\n        continue;\n      }\n      break;\n    }\n    if (idx === start) {\n      ctx[dir > 0 ? 'moveTo' : 'lineTo'](x, y);\n      cpx0 = x;\n      cpy0 = y;\n    } else {\n      var dx = x - prevX;\n      var dy = y - prevY;\n      // Ignore tiny segment.\n      if (dx * dx + dy * dy < 0.5) {\n        idx += dir;\n        continue;\n      }\n      if (smooth > 0) {\n        var nextIdx = idx + dir;\n        var nextX = points[nextIdx * 2];\n        var nextY = points[nextIdx * 2 + 1];\n        // Ignore duplicate point\n        while (nextX === x && nextY === y && k < segLen) {\n          k++;\n          nextIdx += dir;\n          idx += dir;\n          nextX = points[nextIdx * 2];\n          nextY = points[nextIdx * 2 + 1];\n          x = points[idx * 2];\n          y = points[idx * 2 + 1];\n          dx = x - prevX;\n          dy = y - prevY;\n        }\n        var tmpK = k + 1;\n        if (connectNulls) {\n          // Find next point not null\n          while (isPointNull(nextX, nextY) && tmpK < segLen) {\n            tmpK++;\n            nextIdx += dir;\n            nextX = points[nextIdx * 2];\n            nextY = points[nextIdx * 2 + 1];\n          }\n        }\n        var ratioNextSeg = 0.5;\n        var vx = 0;\n        var vy = 0;\n        var nextCpx0 = void 0;\n        var nextCpy0 = void 0;\n        // Is last point\n        if (tmpK >= segLen || isPointNull(nextX, nextY)) {\n          cpx1 = x;\n          cpy1 = y;\n        } else {\n          vx = nextX - prevX;\n          vy = nextY - prevY;\n          var dx0 = x - prevX;\n          var dx1 = nextX - x;\n          var dy0 = y - prevY;\n          var dy1 = nextY - y;\n          var lenPrevSeg = void 0;\n          var lenNextSeg = void 0;\n          if (smoothMonotone === 'x') {\n            lenPrevSeg = Math.abs(dx0);\n            lenNextSeg = Math.abs(dx1);\n            var dir_1 = vx > 0 ? 1 : -1;\n            cpx1 = x - dir_1 * lenPrevSeg * smooth;\n            cpy1 = y;\n            nextCpx0 = x + dir_1 * lenNextSeg * smooth;\n            nextCpy0 = y;\n          } else if (smoothMonotone === 'y') {\n            lenPrevSeg = Math.abs(dy0);\n            lenNextSeg = Math.abs(dy1);\n            var dir_2 = vy > 0 ? 1 : -1;\n            cpx1 = x;\n            cpy1 = y - dir_2 * lenPrevSeg * smooth;\n            nextCpx0 = x;\n            nextCpy0 = y + dir_2 * lenNextSeg * smooth;\n          } else {\n            lenPrevSeg = Math.sqrt(dx0 * dx0 + dy0 * dy0);\n            lenNextSeg = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n            // Use ratio of seg length\n            ratioNextSeg = lenNextSeg / (lenNextSeg + lenPrevSeg);\n            cpx1 = x - vx * smooth * (1 - ratioNextSeg);\n            cpy1 = y - vy * smooth * (1 - ratioNextSeg);\n            // cp0 of next segment\n            nextCpx0 = x + vx * smooth * ratioNextSeg;\n            nextCpy0 = y + vy * smooth * ratioNextSeg;\n            // Smooth constraint between point and next point.\n            // Avoid exceeding extreme after smoothing.\n            nextCpx0 = mathMin(nextCpx0, mathMax(nextX, x));\n            nextCpy0 = mathMin(nextCpy0, mathMax(nextY, y));\n            nextCpx0 = mathMax(nextCpx0, mathMin(nextX, x));\n            nextCpy0 = mathMax(nextCpy0, mathMin(nextY, y));\n            // Reclaculate cp1 based on the adjusted cp0 of next seg.\n            vx = nextCpx0 - x;\n            vy = nextCpy0 - y;\n            cpx1 = x - vx * lenPrevSeg / lenNextSeg;\n            cpy1 = y - vy * lenPrevSeg / lenNextSeg;\n            // Smooth constraint between point and prev point.\n            // Avoid exceeding extreme after smoothing.\n            cpx1 = mathMin(cpx1, mathMax(prevX, x));\n            cpy1 = mathMin(cpy1, mathMax(prevY, y));\n            cpx1 = mathMax(cpx1, mathMin(prevX, x));\n            cpy1 = mathMax(cpy1, mathMin(prevY, y));\n            // Adjust next cp0 again.\n            vx = x - cpx1;\n            vy = y - cpy1;\n            nextCpx0 = x + vx * lenNextSeg / lenPrevSeg;\n            nextCpy0 = y + vy * lenNextSeg / lenPrevSeg;\n          }\n        }\n        ctx.bezierCurveTo(cpx0, cpy0, cpx1, cpy1, x, y);\n        cpx0 = nextCpx0;\n        cpy0 = nextCpy0;\n      } else {\n        ctx.lineTo(x, y);\n      }\n    }\n    prevX = x;\n    prevY = y;\n    idx += dir;\n  }\n  return k;\n}\nvar ECPolylineShape = /** @class */function () {\n  function ECPolylineShape() {\n    this.smooth = 0;\n    this.smoothConstraint = true;\n  }\n  return ECPolylineShape;\n}();\nvar ECPolyline = /** @class */function (_super) {\n  __extends(ECPolyline, _super);\n  function ECPolyline(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'ec-polyline';\n    return _this;\n  }\n  ECPolyline.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  ECPolyline.prototype.getDefaultShape = function () {\n    return new ECPolylineShape();\n  };\n  ECPolyline.prototype.buildPath = function (ctx, shape) {\n    var points = shape.points;\n    var i = 0;\n    var len = points.length / 2;\n    // const result = getBoundingBox(points, shape.smoothConstraint);\n    if (shape.connectNulls) {\n      // Must remove first and last null values avoid draw error in polygon\n      for (; len > 0; len--) {\n        if (!isPointNull(points[len * 2 - 2], points[len * 2 - 1])) {\n          break;\n        }\n      }\n      for (; i < len; i++) {\n        if (!isPointNull(points[i * 2], points[i * 2 + 1])) {\n          break;\n        }\n      }\n    }\n    while (i < len) {\n      i += drawSegment(ctx, points, i, len, len, 1, shape.smooth, shape.smoothMonotone, shape.connectNulls) + 1;\n    }\n  };\n  ECPolyline.prototype.getPointOn = function (xOrY, dim) {\n    if (!this.path) {\n      this.createPathProxy();\n      this.buildPath(this.path, this.shape);\n    }\n    var path = this.path;\n    var data = path.data;\n    var CMD = PathProxy.CMD;\n    var x0;\n    var y0;\n    var isDimX = dim === 'x';\n    var roots = [];\n    for (var i = 0; i < data.length;) {\n      var cmd = data[i++];\n      var x = void 0;\n      var y = void 0;\n      var x2 = void 0;\n      var y2 = void 0;\n      var x3 = void 0;\n      var y3 = void 0;\n      var t = void 0;\n      switch (cmd) {\n        case CMD.M:\n          x0 = data[i++];\n          y0 = data[i++];\n          break;\n        case CMD.L:\n          x = data[i++];\n          y = data[i++];\n          t = isDimX ? (xOrY - x0) / (x - x0) : (xOrY - y0) / (y - y0);\n          if (t <= 1 && t >= 0) {\n            var val = isDimX ? (y - y0) * t + y0 : (x - x0) * t + x0;\n            return isDimX ? [xOrY, val] : [val, xOrY];\n          }\n          x0 = x;\n          y0 = y;\n          break;\n        case CMD.C:\n          x = data[i++];\n          y = data[i++];\n          x2 = data[i++];\n          y2 = data[i++];\n          x3 = data[i++];\n          y3 = data[i++];\n          var nRoot = isDimX ? cubicRootAt(x0, x, x2, x3, xOrY, roots) : cubicRootAt(y0, y, y2, y3, xOrY, roots);\n          if (nRoot > 0) {\n            for (var i_1 = 0; i_1 < nRoot; i_1++) {\n              var t_1 = roots[i_1];\n              if (t_1 <= 1 && t_1 >= 0) {\n                var val = isDimX ? cubicAt(y0, y, y2, y3, t_1) : cubicAt(x0, x, x2, x3, t_1);\n                return isDimX ? [xOrY, val] : [val, xOrY];\n              }\n            }\n          }\n          x0 = x3;\n          y0 = y3;\n          break;\n      }\n    }\n  };\n  return ECPolyline;\n}(Path);\nexport { ECPolyline };\nvar ECPolygonShape = /** @class */function (_super) {\n  __extends(ECPolygonShape, _super);\n  function ECPolygonShape() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return ECPolygonShape;\n}(ECPolylineShape);\nvar ECPolygon = /** @class */function (_super) {\n  __extends(ECPolygon, _super);\n  function ECPolygon(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'ec-polygon';\n    return _this;\n  }\n  ECPolygon.prototype.getDefaultShape = function () {\n    return new ECPolygonShape();\n  };\n  ECPolygon.prototype.buildPath = function (ctx, shape) {\n    var points = shape.points;\n    var stackedOnPoints = shape.stackedOnPoints;\n    var i = 0;\n    var len = points.length / 2;\n    var smoothMonotone = shape.smoothMonotone;\n    if (shape.connectNulls) {\n      // Must remove first and last null values avoid draw error in polygon\n      for (; len > 0; len--) {\n        if (!isPointNull(points[len * 2 - 2], points[len * 2 - 1])) {\n          break;\n        }\n      }\n      for (; i < len; i++) {\n        if (!isPointNull(points[i * 2], points[i * 2 + 1])) {\n          break;\n        }\n      }\n    }\n    while (i < len) {\n      var k = drawSegment(ctx, points, i, len, len, 1, shape.smooth, smoothMonotone, shape.connectNulls);\n      drawSegment(ctx, stackedOnPoints, i + k - 1, k, len, -1, shape.stackedOnSmooth, smoothMonotone, shape.connectNulls);\n      i += k + 1;\n      ctx.closePath();\n    }\n  };\n  return ECPolygon;\n}(Path);\nexport { ECPolygon };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}