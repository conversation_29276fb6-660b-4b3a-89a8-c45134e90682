"""
简化的风险管理测试
"""
import pandas as pd
import numpy as np
from datetime import datetime

# 简单测试风险计算
def test_basic_risk_calculations():
    print("🚀 风险管理基础功能测试")
    
    # 测试1: 回撤计算
    print("\n=== 测试回撤计算 ===")
    values = [1000, 1100, 1050, 950, 1000, 1200]
    portfolio_values = pd.Series(values)
    
    # 计算回撤
    cumulative_max = portfolio_values.expanding().max()
    drawdown = (portfolio_values - cumulative_max) / cumulative_max
    max_drawdown = drawdown.min()
    current_drawdown = drawdown.iloc[-1]
    
    print(f"组合净值: {values}")
    print(f"当前回撤: {current_drawdown:.2%}")
    print(f"最大回撤: {max_drawdown:.2%}")
    
    # 测试2: 波动率计算
    print("\n=== 测试波动率计算 ===")
    np.random.seed(42)
    returns = pd.Series(np.random.normal(0.001, 0.02, 20))
    volatility = returns.std() * np.sqrt(252)  # 年化波动率
    
    print(f"收益率样本: {len(returns)} 个")
    print(f"平均收益: {returns.mean():.4f}")
    print(f"年化波动率: {volatility:.2%}")
    
    # 测试3: 持仓集中度
    print("\n=== 测试持仓集中度 ===")
    positions = {
        "000001.SZ": 120000,
        "000002.SZ": 90000,
        "600000.SH": 135000
    }
    total_assets = 1000000
    
    print("持仓分布:")
    for stock, value in positions.items():
        ratio = value / total_assets
        print(f"- {stock}: {value:,} ({ratio:.1%})")
        
        if ratio > 0.1:  # 超过10%
            print(f"  ⚠️  持仓比例较高")
    
    # 测试4: 行业集中度
    print("\n=== 测试行业集中度 ===")
    sector_mapping = {
        "000001.SZ": "银行",
        "000002.SZ": "房地产", 
        "600000.SH": "银行"
    }
    
    sector_exposure = {}
    for stock, value in positions.items():
        sector = sector_mapping.get(stock, "未知")
        if sector not in sector_exposure:
            sector_exposure[sector] = 0
        sector_exposure[sector] += value
    
    print("行业分布:")
    for sector, exposure in sector_exposure.items():
        ratio = exposure / total_assets
        print(f"- {sector}: {exposure:,} ({ratio:.1%})")
        
        if ratio > 0.3:  # 超过30%
            print(f"  ⚠️  行业集中度较高")
    
    # 测试5: 风险评分
    print("\n=== 测试风险评分 ===")
    risk_factors = []
    
    # 检查各种风险
    if abs(max_drawdown) > 0.1:
        risk_factors.append("回撤过大")
    
    if volatility > 0.25:
        risk_factors.append("波动率过高")
    
    for stock, value in positions.items():
        if value / total_assets > 0.15:
            risk_factors.append(f"{stock}持仓过重")
    
    for sector, exposure in sector_exposure.items():
        if exposure / total_assets > 0.35:
            risk_factors.append(f"{sector}行业集中")
    
    risk_score = len(risk_factors) * 20  # 每个风险因子20分
    
    print(f"识别的风险因子: {len(risk_factors)}")
    for factor in risk_factors:
        print(f"- {factor}")
    
    print(f"风险评分: {risk_score}/100")
    
    if risk_score < 20:
        risk_level = "低风险"
    elif risk_score < 40:
        risk_level = "中等风险"
    elif risk_score < 60:
        risk_level = "高风险"
    else:
        risk_level = "极高风险"
    
    print(f"风险等级: {risk_level}")
    
    print("\n✅ 风险管理基础功能测试完成")
    return True

if __name__ == "__main__":
    test_basic_risk_calculations()
