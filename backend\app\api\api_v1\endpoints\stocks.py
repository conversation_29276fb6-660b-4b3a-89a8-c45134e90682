"""
股票管理相关API
"""
from fastapi import APIRouter, HTTPException
from typing import List
from pydantic import BaseModel

router = APIRouter()

class StockAdd(BaseModel):
    symbol: str
    name: str
    market: str = "A股"  # A股 或 港股

class StockInfo(BaseModel):
    id: int
    symbol: str
    name: str
    market: str
    is_active: bool

@router.get("/", response_model=List[StockInfo])
async def get_stocks():
    """获取所有监控的股票列表"""
    # TODO: 从数据库获取股票列表
    return []

@router.post("/", response_model=StockInfo)
async def add_stock(stock: StockAdd):
    """添加新的监控股票"""
    # TODO: 验证股票代码有效性
    # TODO: 保存到数据库
    # TODO: 启动数据获取任务
    return StockInfo(
        id=1,
        symbol=stock.symbol,
        name=stock.name,
        market=stock.market,
        is_active=True
    )

@router.delete("/{stock_id}")
async def remove_stock(stock_id: int):
    """移除监控股票"""
    # TODO: 从数据库删除
    # TODO: 停止相关监控任务
    return {"message": f"股票 {stock_id} 已移除监控"}

@router.put("/{stock_id}/toggle")
async def toggle_stock_monitoring(stock_id: int):
    """切换股票监控状态"""
    # TODO: 更新数据库状态
    # TODO: 启动或停止监控任务
    return {"message": f"股票 {stock_id} 监控状态已切换"}
