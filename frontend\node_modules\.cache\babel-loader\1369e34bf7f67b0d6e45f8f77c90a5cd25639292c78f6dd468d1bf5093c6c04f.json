{"ast": null, "code": "var _jsxFileName = \"C:\\\\<PERSON>_Zhang\\\\\\u91CF\\u5316\\u4EA4\\u6613\\u7CFB\\u7EDF\\\\frontend\\\\src\\\\index.tsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport App from './App.tsx';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    locale: zhCN,\n    children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 13,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "App", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "locale", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/<PERSON>_<PERSON>/量化交易系统/frontend/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport App from './App.tsx';\nimport './index.css';\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n);\n\nroot.render(\n  <React.StrictMode>\n    <ConfigProvider locale={zhCN}>\n      <App />\n    </ConfigProvider>\n  </React.StrictMode>\n);\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,cAAc,QAAQ,MAAM;AACrC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,IAAI,GAAGN,QAAQ,CAACO,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC;AAEDH,IAAI,CAACI,MAAM,cACTL,OAAA,CAACN,KAAK,CAACY,UAAU;EAAAC,QAAA,eACfP,OAAA,CAACJ,cAAc;IAACY,MAAM,EAAEX,IAAK;IAAAU,QAAA,eAC3BP,OAAA,CAACF,GAAG;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACD,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}