{"ast": null, "code": "import PathProxy from '../core/PathProxy.js';\nimport { applyTransform as v2ApplyTransform } from '../core/vector.js';\nvar CMD = PathProxy.CMD;\nvar points = [[], [], []];\nvar mathSqrt = Math.sqrt;\nvar mathAtan2 = Math.atan2;\nexport default function transformPath(path, m) {\n  if (!m) {\n    return;\n  }\n  var data = path.data;\n  var len = path.len();\n  var cmd;\n  var nPoint;\n  var i;\n  var j;\n  var k;\n  var p;\n  var M = CMD.M;\n  var C = CMD.C;\n  var L = CMD.L;\n  var R = CMD.R;\n  var A = CMD.A;\n  var Q = CMD.Q;\n  for (i = 0, j = 0; i < len;) {\n    cmd = data[i++];\n    j = i;\n    nPoint = 0;\n    switch (cmd) {\n      case M:\n        nPoint = 1;\n        break;\n      case L:\n        nPoint = 1;\n        break;\n      case C:\n        nPoint = 3;\n        break;\n      case Q:\n        nPoint = 2;\n        break;\n      case A:\n        var x = m[4];\n        var y = m[5];\n        var sx = mathSqrt(m[0] * m[0] + m[1] * m[1]);\n        var sy = mathSqrt(m[2] * m[2] + m[3] * m[3]);\n        var angle = mathAtan2(-m[1] / sy, m[0] / sx);\n        data[i] *= sx;\n        data[i++] += x;\n        data[i] *= sy;\n        data[i++] += y;\n        data[i++] *= sx;\n        data[i++] *= sy;\n        data[i++] += angle;\n        data[i++] += angle;\n        i += 2;\n        j = i;\n        break;\n      case R:\n        p[0] = data[i++];\n        p[1] = data[i++];\n        v2ApplyTransform(p, p, m);\n        data[j++] = p[0];\n        data[j++] = p[1];\n        p[0] += data[i++];\n        p[1] += data[i++];\n        v2ApplyTransform(p, p, m);\n        data[j++] = p[0];\n        data[j++] = p[1];\n    }\n    for (k = 0; k < nPoint; k++) {\n      var p_1 = points[k];\n      p_1[0] = data[i++];\n      p_1[1] = data[i++];\n      v2ApplyTransform(p_1, p_1, m);\n      data[j++] = p_1[0];\n      data[j++] = p_1[1];\n    }\n  }\n  path.increaseVersion();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}