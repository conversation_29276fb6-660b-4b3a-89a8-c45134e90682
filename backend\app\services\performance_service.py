"""
性能优化服务
"""
import asyncio
import psutil
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging

from core.cache_manager import get_cache_manager, get_performance_monitor, get_data_cache
from core.database_optimizer import get_database_optimizer

logger = logging.getLogger(__name__)

class PerformanceService:
    """性能优化服务"""
    
    def __init__(self):
        self.cache_manager = get_cache_manager()
        self.performance_monitor = get_performance_monitor()
        self.data_cache = get_data_cache()
        self.db_optimizer = get_database_optimizer()
        
        # 性能阈值
        self.thresholds = {
            'cpu_usage': 80.0,
            'memory_usage': 85.0,
            'cache_hit_rate': 0.7,
            'avg_response_time': 2.0
        }
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统性能指标"""
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available / (1024**3)  # GB
            memory_total = memory.total / (1024**3)  # GB
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_free = disk.free / (1024**3)  # GB
            disk_total = disk.total / (1024**3)  # GB
            
            # 网络统计
            network = psutil.net_io_counters()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'cpu': {
                    'usage_percent': round(cpu_percent, 2),
                    'core_count': cpu_count,
                    'load_average': list(psutil.getloadavg()) if hasattr(psutil, 'getloadavg') else None
                },
                'memory': {
                    'usage_percent': round(memory_percent, 2),
                    'available_gb': round(memory_available, 2),
                    'total_gb': round(memory_total, 2),
                    'used_gb': round(memory_total - memory_available, 2)
                },
                'disk': {
                    'usage_percent': round(disk_percent, 2),
                    'free_gb': round(disk_free, 2),
                    'total_gb': round(disk_total, 2),
                    'used_gb': round(disk_total - disk_free, 2)
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                }
            }
            
        except Exception as e:
            logger.error(f"获取系统指标失败: {e}")
            return {'error': str(e)}
    
    def get_cache_metrics(self) -> Dict[str, Any]:
        """获取缓存性能指标"""
        
        try:
            cache_stats = self.cache_manager.get_stats()
            
            # 计算额外指标
            total_operations = cache_stats['total_hits'] + cache_stats['total_misses']
            hit_rate = cache_stats['hit_rate']
            
            # 缓存效率评估
            efficiency_score = self._calculate_cache_efficiency(cache_stats)
            
            return {
                'timestamp': datetime.now().isoformat(),
                'basic_stats': cache_stats,
                'efficiency': {
                    'hit_rate': hit_rate,
                    'total_operations': total_operations,
                    'efficiency_score': efficiency_score,
                    'recommendation': self._get_cache_recommendation(hit_rate, efficiency_score)
                },
                'memory_usage': {
                    'cache_size': cache_stats['cache_size'],
                    'max_size': cache_stats['max_size'],
                    'utilization': cache_stats['cache_size'] / cache_stats['max_size'] if cache_stats['max_size'] > 0 else 0
                }
            }
            
        except Exception as e:
            logger.error(f"获取缓存指标失败: {e}")
            return {'error': str(e)}
    
    def get_application_metrics(self) -> Dict[str, Any]:
        """获取应用性能指标"""
        
        try:
            perf_metrics = self.performance_monitor.get_metrics()
            
            # 计算汇总统计
            total_operations = sum(metric['count'] for metric in perf_metrics.values())
            avg_response_time = sum(metric['avg_time'] for metric in perf_metrics.values()) / len(perf_metrics) if perf_metrics else 0
            
            # 找出最慢的操作
            slowest_operations = sorted(
                [(op, metric) for op, metric in perf_metrics.items()],
                key=lambda x: x[1]['avg_time'],
                reverse=True
            )[:5]
            
            return {
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total_operations': total_operations,
                    'avg_response_time': round(avg_response_time, 4),
                    'operation_types': len(perf_metrics)
                },
                'detailed_metrics': perf_metrics,
                'slowest_operations': [
                    {
                        'operation': op,
                        'avg_time': round(metric['avg_time'], 4),
                        'count': metric['count'],
                        'max_time': round(metric['max_time'], 4)
                    }
                    for op, metric in slowest_operations
                ],
                'performance_score': self._calculate_performance_score(perf_metrics)
            }
            
        except Exception as e:
            logger.error(f"获取应用指标失败: {e}")
            return {'error': str(e)}
    
    async def get_database_metrics(self) -> Dict[str, Any]:
        """获取数据库性能指标"""
        
        try:
            if not self.db_optimizer:
                return {'error': '数据库优化器未初始化'}
            
            # 获取数据库连接池状态
            pool_stats = {
                'size': self.db_optimizer.connection_pool.get_size(),
                'max_size': self.db_optimizer.connection_pool.get_max_size(),
                'min_size': self.db_optimizer.connection_pool.get_min_size()
            }
            
            # 获取慢查询统计（模拟）
            slow_queries = await self._get_slow_queries()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'connection_pool': pool_stats,
                'slow_queries': slow_queries,
                'recommendations': self._get_database_recommendations(pool_stats, slow_queries)
            }
            
        except Exception as e:
            logger.error(f"获取数据库指标失败: {e}")
            return {'error': str(e)}
    
    def optimize_cache_settings(self) -> Dict[str, Any]:
        """优化缓存设置"""
        
        try:
            cache_stats = self.cache_manager.get_stats()
            hit_rate = cache_stats['hit_rate']
            
            recommendations = []
            
            # 根据命中率调整缓存大小
            if hit_rate < 0.5:
                recommendations.append({
                    'type': 'increase_cache_size',
                    'current_size': cache_stats['max_size'],
                    'recommended_size': int(cache_stats['max_size'] * 1.5),
                    'reason': '缓存命中率过低，建议增加缓存大小'
                })
            
            # 清理过期缓存
            self.cache_manager.cleanup_expired()
            recommendations.append({
                'type': 'cleanup_expired',
                'action': 'completed',
                'reason': '清理过期缓存项'
            })
            
            # 调整TTL策略
            if cache_stats['total_evictions'] > cache_stats['total_hits'] * 0.1:
                recommendations.append({
                    'type': 'adjust_ttl',
                    'current_ttl': self.cache_manager.default_ttl,
                    'recommended_ttl': int(self.cache_manager.default_ttl * 0.8),
                    'reason': '缓存淘汰过于频繁，建议降低TTL'
                })
            
            return {
                'success': True,
                'optimizations_applied': len(recommendations),
                'recommendations': recommendations,
                'new_cache_stats': self.cache_manager.get_stats()
            }
            
        except Exception as e:
            logger.error(f"优化缓存设置失败: {e}")
            return {'success': False, 'error': str(e)}
    
    async def optimize_database_performance(self) -> Dict[str, Any]:
        """优化数据库性能"""
        
        try:
            if not self.db_optimizer:
                return {'success': False, 'error': '数据库优化器未初始化'}
            
            optimizations = []
            
            # 优化主要表
            tables_to_optimize = ['daily_data', 'technical_indicators', 'risk_alerts']
            
            for table in tables_to_optimize:
                try:
                    await self.db_optimizer.optimize_table(table)
                    optimizations.append({
                        'type': 'table_optimization',
                        'table': table,
                        'status': 'completed'
                    })
                except Exception as e:
                    optimizations.append({
                        'type': 'table_optimization',
                        'table': table,
                        'status': 'failed',
                        'error': str(e)
                    })
            
            # 创建推荐索引
            from core.database_optimizer import QueryOptimizer
            query_optimizer = QueryOptimizer(self.db_optimizer)
            recommended_indexes = query_optimizer.get_recommended_indexes()
            
            for table_indexes in recommended_indexes:
                try:
                    await self.db_optimizer.create_indexes(
                        table_indexes['table'],
                        [table_indexes]
                    )
                    optimizations.append({
                        'type': 'index_creation',
                        'table': table_indexes['table'],
                        'index': table_indexes['name'],
                        'status': 'completed'
                    })
                except Exception as e:
                    optimizations.append({
                        'type': 'index_creation',
                        'table': table_indexes['table'],
                        'index': table_indexes['name'],
                        'status': 'failed',
                        'error': str(e)
                    })
            
            return {
                'success': True,
                'optimizations_applied': len([o for o in optimizations if o['status'] == 'completed']),
                'optimizations': optimizations
            }
            
        except Exception as e:
            logger.error(f"优化数据库性能失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        
        try:
            system_metrics = self.get_system_metrics()
            cache_metrics = self.get_cache_metrics()
            app_metrics = self.get_application_metrics()
            
            # 计算总体性能评分
            overall_score = self._calculate_overall_performance_score(
                system_metrics, cache_metrics, app_metrics
            )
            
            # 生成建议
            recommendations = self._generate_performance_recommendations(
                system_metrics, cache_metrics, app_metrics
            )
            
            return {
                'timestamp': datetime.now().isoformat(),
                'overall_score': overall_score,
                'system_metrics': system_metrics,
                'cache_metrics': cache_metrics,
                'application_metrics': app_metrics,
                'recommendations': recommendations,
                'health_status': self._get_health_status(overall_score)
            }
            
        except Exception as e:
            logger.error(f"生成性能报告失败: {e}")
            return {'error': str(e)}
    
    def _calculate_cache_efficiency(self, cache_stats: Dict) -> float:
        """计算缓存效率评分"""
        hit_rate = cache_stats['hit_rate']
        utilization = cache_stats['cache_size'] / cache_stats['max_size'] if cache_stats['max_size'] > 0 else 0
        
        # 综合命中率和利用率
        efficiency = (hit_rate * 0.7 + utilization * 0.3) * 100
        return round(efficiency, 2)
    
    def _get_cache_recommendation(self, hit_rate: float, efficiency_score: float) -> str:
        """获取缓存建议"""
        if hit_rate < 0.5:
            return "缓存命中率过低，建议增加缓存大小或调整缓存策略"
        elif hit_rate > 0.9:
            return "缓存性能优秀"
        elif efficiency_score < 60:
            return "缓存效率有待提升，建议优化缓存键设计"
        else:
            return "缓存性能良好"
    
    def _calculate_performance_score(self, perf_metrics: Dict) -> float:
        """计算性能评分"""
        if not perf_metrics:
            return 100.0
        
        avg_times = [metric['avg_time'] for metric in perf_metrics.values()]
        overall_avg = sum(avg_times) / len(avg_times)
        
        # 基于平均响应时间计算评分
        if overall_avg < 0.1:
            return 100.0
        elif overall_avg < 0.5:
            return 90.0
        elif overall_avg < 1.0:
            return 80.0
        elif overall_avg < 2.0:
            return 70.0
        else:
            return 60.0
    
    async def _get_slow_queries(self) -> List[Dict]:
        """获取慢查询（模拟）"""
        # 实际实现中应该查询 pg_stat_statements
        return [
            {
                'query': 'SELECT * FROM daily_data WHERE...',
                'avg_time': 1.5,
                'calls': 100,
                'total_time': 150.0
            }
        ]
    
    def _get_database_recommendations(self, pool_stats: Dict, slow_queries: List) -> List[str]:
        """获取数据库建议"""
        recommendations = []
        
        utilization = pool_stats['size'] / pool_stats['max_size']
        if utilization > 0.8:
            recommendations.append("连接池使用率过高，建议增加最大连接数")
        
        if slow_queries:
            recommendations.append("存在慢查询，建议优化查询语句或添加索引")
        
        return recommendations
    
    def _calculate_overall_performance_score(self, system: Dict, cache: Dict, app: Dict) -> float:
        """计算总体性能评分"""
        scores = []
        
        # 系统评分
        if 'cpu' in system:
            cpu_score = max(0, 100 - system['cpu']['usage_percent'])
            scores.append(cpu_score)
        
        # 缓存评分
        if 'efficiency' in cache:
            cache_score = cache['efficiency']['efficiency_score']
            scores.append(cache_score)
        
        # 应用评分
        if 'performance_score' in app:
            app_score = app['performance_score']
            scores.append(app_score)
        
        return round(sum(scores) / len(scores), 2) if scores else 0.0
    
    def _generate_performance_recommendations(self, system: Dict, cache: Dict, app: Dict) -> List[str]:
        """生成性能建议"""
        recommendations = []
        
        # 系统建议
        if 'cpu' in system and system['cpu']['usage_percent'] > self.thresholds['cpu_usage']:
            recommendations.append("CPU使用率过高，建议优化计算密集型操作")
        
        if 'memory' in system and system['memory']['usage_percent'] > self.thresholds['memory_usage']:
            recommendations.append("内存使用率过高，建议增加内存或优化内存使用")
        
        # 缓存建议
        if 'efficiency' in cache and cache['efficiency']['hit_rate'] < self.thresholds['cache_hit_rate']:
            recommendations.append("缓存命中率偏低，建议调整缓存策略")
        
        # 应用建议
        if 'summary' in app and app['summary']['avg_response_time'] > self.thresholds['avg_response_time']:
            recommendations.append("平均响应时间过长，建议优化慢操作")
        
        return recommendations
    
    def _get_health_status(self, overall_score: float) -> str:
        """获取健康状态"""
        if overall_score >= 90:
            return "EXCELLENT"
        elif overall_score >= 80:
            return "GOOD"
        elif overall_score >= 70:
            return "FAIR"
        elif overall_score >= 60:
            return "POOR"
        else:
            return "CRITICAL"

# 全局性能服务实例
performance_service = PerformanceService()

def get_performance_service() -> PerformanceService:
    """获取性能服务实例"""
    return performance_service
