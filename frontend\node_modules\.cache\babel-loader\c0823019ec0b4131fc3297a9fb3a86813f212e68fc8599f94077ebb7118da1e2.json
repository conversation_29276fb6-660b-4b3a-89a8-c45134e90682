{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useLayoutUpdateEffect } from \"rc-util/es/hooks/useLayoutEffect\";\nimport { useRef, useState } from 'react';\n\n/**\n * Help to merge callback with `useLayoutEffect`.\n * One time will only trigger once.\n */\nexport default function useUpdate(callback) {\n  var _useState = useState(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    count = _useState2[0],\n    setCount = _useState2[1];\n  var effectRef = useRef(0);\n  var callbackRef = useRef();\n  callbackRef.current = callback;\n\n  // Trigger on `useLayoutEffect`\n  useLayoutUpdateEffect(function () {\n    var _callbackRef$current;\n    (_callbackRef$current = callbackRef.current) === null || _callbackRef$current === void 0 || _callbackRef$current.call(callbackRef);\n  }, [count]);\n\n  // Trigger to update count\n  return function () {\n    if (effectRef.current !== count) {\n      return;\n    }\n    effectRef.current += 1;\n    setCount(effectRef.current);\n  };\n}\nexport function useUpdateState(defaultState) {\n  var batchRef = useRef([]);\n  var _useState3 = useState({}),\n    _useState4 = _slicedToArray(_useState3, 2),\n    forceUpdate = _useState4[1];\n  var state = useRef(typeof defaultState === 'function' ? defaultState() : defaultState);\n  var flushUpdate = useUpdate(function () {\n    var current = state.current;\n    batchRef.current.forEach(function (callback) {\n      current = callback(current);\n    });\n    batchRef.current = [];\n    state.current = current;\n    forceUpdate({});\n  });\n  function updater(callback) {\n    batchRef.current.push(callback);\n    flushUpdate();\n  }\n  return [state.current, updater];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}