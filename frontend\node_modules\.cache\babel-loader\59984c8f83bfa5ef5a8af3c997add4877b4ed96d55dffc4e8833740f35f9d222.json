{"ast": null, "code": "import { liftColor } from '../tool/color.js';\nimport { getClassId } from './cssClassId.js';\nexport function createCSSEmphasis(el, attrs, scope) {\n  if (!el.ignore) {\n    if (el.isSilent()) {\n      var style = {\n        'pointer-events': 'none'\n      };\n      setClassAttribute(style, attrs, scope, true);\n    } else {\n      var emphasisStyle = el.states.emphasis && el.states.emphasis.style ? el.states.emphasis.style : {};\n      var fill = emphasisStyle.fill;\n      if (!fill) {\n        var normalFill = el.style && el.style.fill;\n        var selectFill = el.states.select && el.states.select.style && el.states.select.style.fill;\n        var fromFill = el.currentStates.indexOf('select') >= 0 ? selectFill || normalFill : normalFill;\n        if (fromFill) {\n          fill = liftColor(fromFill);\n        }\n      }\n      var lineWidth = emphasisStyle.lineWidth;\n      if (lineWidth) {\n        var scaleX = !emphasisStyle.strokeNoScale && el.transform ? el.transform[0] : 1;\n        lineWidth = lineWidth / scaleX;\n      }\n      var style = {\n        cursor: 'pointer'\n      };\n      if (fill) {\n        style.fill = fill;\n      }\n      if (emphasisStyle.stroke) {\n        style.stroke = emphasisStyle.stroke;\n      }\n      if (lineWidth) {\n        style['stroke-width'] = lineWidth;\n      }\n      setClassAttribute(style, attrs, scope, true);\n    }\n  }\n}\nfunction setClassAttribute(style, attrs, scope, withHover) {\n  var styleKey = JSON.stringify(style);\n  var className = scope.cssStyleCache[styleKey];\n  if (!className) {\n    className = scope.zrId + '-cls-' + getClassId();\n    scope.cssStyleCache[styleKey] = className;\n    scope.cssNodes['.' + className + (withHover ? ':hover' : '')] = style;\n  }\n  attrs[\"class\"] = attrs[\"class\"] ? attrs[\"class\"] + ' ' + className : className;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}