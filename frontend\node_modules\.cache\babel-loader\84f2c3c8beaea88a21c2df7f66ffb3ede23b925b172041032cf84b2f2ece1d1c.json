{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Grid is a region which contains at most 4 cartesian systems\r\n *\r\n * TODO Default cartesian\r\n */\nimport { isObject, each, indexOf, retrieve3, keys } from 'zrender/lib/core/util.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport { createScaleByModel, ifAxisCrossZero, niceScaleExtent, estimateLabelUnionRect, getDataDimensionsOnAxis } from '../../coord/axisHelper.js';\nimport Cartesian2D, { cartesian2DDimensions } from './Cartesian2D.js';\nimport Axis2D from './Axis2D.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nimport { isCartesian2DSeries, findAxisModels } from './cartesianAxisHelper.js';\nimport { isIntervalOrLogScale } from '../../scale/helper.js';\nimport { alignScaleTicks } from '../axisAlignTicks.js';\nvar Grid = /** @class */function () {\n  function Grid(gridModel, ecModel, api) {\n    // FIXME:TS where used (different from registered type 'cartesian2d')?\n    this.type = 'grid';\n    this._coordsMap = {};\n    this._coordsList = [];\n    this._axesMap = {};\n    this._axesList = [];\n    this.axisPointerEnabled = true;\n    this.dimensions = cartesian2DDimensions;\n    this._initCartesian(gridModel, ecModel, api);\n    this.model = gridModel;\n  }\n  Grid.prototype.getRect = function () {\n    return this._rect;\n  };\n  Grid.prototype.update = function (ecModel, api) {\n    var axesMap = this._axesMap;\n    this._updateScale(ecModel, this.model);\n    function updateAxisTicks(axes) {\n      var alignTo;\n      // Axis is added in order of axisIndex.\n      var axesIndices = keys(axes);\n      var len = axesIndices.length;\n      if (!len) {\n        return;\n      }\n      var axisNeedsAlign = [];\n      // Process once and calculate the ticks for those don't use alignTicks.\n      for (var i = len - 1; i >= 0; i--) {\n        var idx = +axesIndices[i]; // Convert to number.\n        var axis = axes[idx];\n        var model = axis.model;\n        var scale = axis.scale;\n        if (\n        // Only value and log axis without interval support alignTicks.\n        isIntervalOrLogScale(scale) && model.get('alignTicks') && model.get('interval') == null) {\n          axisNeedsAlign.push(axis);\n        } else {\n          niceScaleExtent(scale, model);\n          if (isIntervalOrLogScale(scale)) {\n            // Can only align to interval or log axis.\n            alignTo = axis;\n          }\n        }\n      }\n      ;\n      // All axes has set alignTicks. Pick the first one.\n      // PENDING. Should we find the axis that both set interval, min, max and align to this one?\n      if (axisNeedsAlign.length) {\n        if (!alignTo) {\n          alignTo = axisNeedsAlign.pop();\n          niceScaleExtent(alignTo.scale, alignTo.model);\n        }\n        each(axisNeedsAlign, function (axis) {\n          alignScaleTicks(axis.scale, axis.model, alignTo.scale);\n        });\n      }\n    }\n    updateAxisTicks(axesMap.x);\n    updateAxisTicks(axesMap.y);\n    // Key: axisDim_axisIndex, value: boolean, whether onZero target.\n    var onZeroRecords = {};\n    each(axesMap.x, function (xAxis) {\n      fixAxisOnZero(axesMap, 'y', xAxis, onZeroRecords);\n    });\n    each(axesMap.y, function (yAxis) {\n      fixAxisOnZero(axesMap, 'x', yAxis, onZeroRecords);\n    });\n    // Resize again if containLabel is enabled\n    // FIXME It may cause getting wrong grid size in data processing stage\n    this.resize(this.model, api);\n  };\n  /**\r\n   * Resize the grid\r\n   */\n  Grid.prototype.resize = function (gridModel, api, ignoreContainLabel) {\n    var boxLayoutParams = gridModel.getBoxLayoutParams();\n    var isContainLabel = !ignoreContainLabel && gridModel.get('containLabel');\n    var gridRect = getLayoutRect(boxLayoutParams, {\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n    this._rect = gridRect;\n    var axesList = this._axesList;\n    adjustAxes();\n    // Minus label size\n    if (isContainLabel) {\n      each(axesList, function (axis) {\n        if (!axis.model.get(['axisLabel', 'inside'])) {\n          var labelUnionRect = estimateLabelUnionRect(axis);\n          if (labelUnionRect) {\n            var dim = axis.isHorizontal() ? 'height' : 'width';\n            var margin = axis.model.get(['axisLabel', 'margin']);\n            gridRect[dim] -= labelUnionRect[dim] + margin;\n            if (axis.position === 'top') {\n              gridRect.y += labelUnionRect.height + margin;\n            } else if (axis.position === 'left') {\n              gridRect.x += labelUnionRect.width + margin;\n            }\n          }\n        }\n      });\n      adjustAxes();\n    }\n    each(this._coordsList, function (coord) {\n      // Calculate affine matrix to accelerate the data to point transform.\n      // If all the axes scales are time or value.\n      coord.calcAffineTransform();\n    });\n    function adjustAxes() {\n      each(axesList, function (axis) {\n        var isHorizontal = axis.isHorizontal();\n        var extent = isHorizontal ? [0, gridRect.width] : [0, gridRect.height];\n        var idx = axis.inverse ? 1 : 0;\n        axis.setExtent(extent[idx], extent[1 - idx]);\n        updateAxisTransform(axis, isHorizontal ? gridRect.x : gridRect.y);\n      });\n    }\n  };\n  Grid.prototype.getAxis = function (dim, axisIndex) {\n    var axesMapOnDim = this._axesMap[dim];\n    if (axesMapOnDim != null) {\n      return axesMapOnDim[axisIndex || 0];\n    }\n  };\n  Grid.prototype.getAxes = function () {\n    return this._axesList.slice();\n  };\n  Grid.prototype.getCartesian = function (xAxisIndex, yAxisIndex) {\n    if (xAxisIndex != null && yAxisIndex != null) {\n      var key = 'x' + xAxisIndex + 'y' + yAxisIndex;\n      return this._coordsMap[key];\n    }\n    if (isObject(xAxisIndex)) {\n      yAxisIndex = xAxisIndex.yAxisIndex;\n      xAxisIndex = xAxisIndex.xAxisIndex;\n    }\n    for (var i = 0, coordList = this._coordsList; i < coordList.length; i++) {\n      if (coordList[i].getAxis('x').index === xAxisIndex || coordList[i].getAxis('y').index === yAxisIndex) {\n        return coordList[i];\n      }\n    }\n  };\n  Grid.prototype.getCartesians = function () {\n    return this._coordsList.slice();\n  };\n  /**\r\n   * @implements\r\n   */\n  Grid.prototype.convertToPixel = function (ecModel, finder, value) {\n    var target = this._findConvertTarget(finder);\n    return target.cartesian ? target.cartesian.dataToPoint(value) : target.axis ? target.axis.toGlobalCoord(target.axis.dataToCoord(value)) : null;\n  };\n  /**\r\n   * @implements\r\n   */\n  Grid.prototype.convertFromPixel = function (ecModel, finder, value) {\n    var target = this._findConvertTarget(finder);\n    return target.cartesian ? target.cartesian.pointToData(value) : target.axis ? target.axis.coordToData(target.axis.toLocalCoord(value)) : null;\n  };\n  Grid.prototype._findConvertTarget = function (finder) {\n    var seriesModel = finder.seriesModel;\n    var xAxisModel = finder.xAxisModel || seriesModel && seriesModel.getReferringComponents('xAxis', SINGLE_REFERRING).models[0];\n    var yAxisModel = finder.yAxisModel || seriesModel && seriesModel.getReferringComponents('yAxis', SINGLE_REFERRING).models[0];\n    var gridModel = finder.gridModel;\n    var coordsList = this._coordsList;\n    var cartesian;\n    var axis;\n    if (seriesModel) {\n      cartesian = seriesModel.coordinateSystem;\n      indexOf(coordsList, cartesian) < 0 && (cartesian = null);\n    } else if (xAxisModel && yAxisModel) {\n      cartesian = this.getCartesian(xAxisModel.componentIndex, yAxisModel.componentIndex);\n    } else if (xAxisModel) {\n      axis = this.getAxis('x', xAxisModel.componentIndex);\n    } else if (yAxisModel) {\n      axis = this.getAxis('y', yAxisModel.componentIndex);\n    }\n    // Lowest priority.\n    else if (gridModel) {\n      var grid = gridModel.coordinateSystem;\n      if (grid === this) {\n        cartesian = this._coordsList[0];\n      }\n    }\n    return {\n      cartesian: cartesian,\n      axis: axis\n    };\n  };\n  /**\r\n   * @implements\r\n   */\n  Grid.prototype.containPoint = function (point) {\n    var coord = this._coordsList[0];\n    if (coord) {\n      return coord.containPoint(point);\n    }\n  };\n  /**\r\n   * Initialize cartesian coordinate systems\r\n   */\n  Grid.prototype._initCartesian = function (gridModel, ecModel, api) {\n    var _this = this;\n    var grid = this;\n    var axisPositionUsed = {\n      left: false,\n      right: false,\n      top: false,\n      bottom: false\n    };\n    var axesMap = {\n      x: {},\n      y: {}\n    };\n    var axesCount = {\n      x: 0,\n      y: 0\n    };\n    // Create axis\n    ecModel.eachComponent('xAxis', createAxisCreator('x'), this);\n    ecModel.eachComponent('yAxis', createAxisCreator('y'), this);\n    if (!axesCount.x || !axesCount.y) {\n      // Roll back when there no either x or y axis\n      this._axesMap = {};\n      this._axesList = [];\n      return;\n    }\n    this._axesMap = axesMap;\n    // Create cartesian2d\n    each(axesMap.x, function (xAxis, xAxisIndex) {\n      each(axesMap.y, function (yAxis, yAxisIndex) {\n        var key = 'x' + xAxisIndex + 'y' + yAxisIndex;\n        var cartesian = new Cartesian2D(key);\n        cartesian.master = _this;\n        cartesian.model = gridModel;\n        _this._coordsMap[key] = cartesian;\n        _this._coordsList.push(cartesian);\n        cartesian.addAxis(xAxis);\n        cartesian.addAxis(yAxis);\n      });\n    });\n    function createAxisCreator(dimName) {\n      return function (axisModel, idx) {\n        if (!isAxisUsedInTheGrid(axisModel, gridModel)) {\n          return;\n        }\n        var axisPosition = axisModel.get('position');\n        if (dimName === 'x') {\n          // Fix position\n          if (axisPosition !== 'top' && axisPosition !== 'bottom') {\n            // Default bottom of X\n            axisPosition = axisPositionUsed.bottom ? 'top' : 'bottom';\n          }\n        } else {\n          // Fix position\n          if (axisPosition !== 'left' && axisPosition !== 'right') {\n            // Default left of Y\n            axisPosition = axisPositionUsed.left ? 'right' : 'left';\n          }\n        }\n        axisPositionUsed[axisPosition] = true;\n        var axis = new Axis2D(dimName, createScaleByModel(axisModel), [0, 0], axisModel.get('type'), axisPosition);\n        var isCategory = axis.type === 'category';\n        axis.onBand = isCategory && axisModel.get('boundaryGap');\n        axis.inverse = axisModel.get('inverse');\n        // Inject axis into axisModel\n        axisModel.axis = axis;\n        // Inject axisModel into axis\n        axis.model = axisModel;\n        // Inject grid info axis\n        axis.grid = grid;\n        // Index of axis, can be used as key\n        axis.index = idx;\n        grid._axesList.push(axis);\n        axesMap[dimName][idx] = axis;\n        axesCount[dimName]++;\n      };\n    }\n  };\n  /**\r\n   * Update cartesian properties from series.\r\n   */\n  Grid.prototype._updateScale = function (ecModel, gridModel) {\n    // Reset scale\n    each(this._axesList, function (axis) {\n      axis.scale.setExtent(Infinity, -Infinity);\n      if (axis.type === 'category') {\n        var categorySortInfo = axis.model.get('categorySortInfo');\n        axis.scale.setSortInfo(categorySortInfo);\n      }\n    });\n    ecModel.eachSeries(function (seriesModel) {\n      if (isCartesian2DSeries(seriesModel)) {\n        var axesModelMap = findAxisModels(seriesModel);\n        var xAxisModel = axesModelMap.xAxisModel;\n        var yAxisModel = axesModelMap.yAxisModel;\n        if (!isAxisUsedInTheGrid(xAxisModel, gridModel) || !isAxisUsedInTheGrid(yAxisModel, gridModel)) {\n          return;\n        }\n        var cartesian = this.getCartesian(xAxisModel.componentIndex, yAxisModel.componentIndex);\n        var data = seriesModel.getData();\n        var xAxis = cartesian.getAxis('x');\n        var yAxis = cartesian.getAxis('y');\n        unionExtent(data, xAxis);\n        unionExtent(data, yAxis);\n      }\n    }, this);\n    function unionExtent(data, axis) {\n      each(getDataDimensionsOnAxis(data, axis.dim), function (dim) {\n        axis.scale.unionExtentFromData(data, dim);\n      });\n    }\n  };\n  /**\r\n   * @param dim 'x' or 'y' or 'auto' or null/undefined\r\n   */\n  Grid.prototype.getTooltipAxes = function (dim) {\n    var baseAxes = [];\n    var otherAxes = [];\n    each(this.getCartesians(), function (cartesian) {\n      var baseAxis = dim != null && dim !== 'auto' ? cartesian.getAxis(dim) : cartesian.getBaseAxis();\n      var otherAxis = cartesian.getOtherAxis(baseAxis);\n      indexOf(baseAxes, baseAxis) < 0 && baseAxes.push(baseAxis);\n      indexOf(otherAxes, otherAxis) < 0 && otherAxes.push(otherAxis);\n    });\n    return {\n      baseAxes: baseAxes,\n      otherAxes: otherAxes\n    };\n  };\n  Grid.create = function (ecModel, api) {\n    var grids = [];\n    ecModel.eachComponent('grid', function (gridModel, idx) {\n      var grid = new Grid(gridModel, ecModel, api);\n      grid.name = 'grid_' + idx;\n      // dataSampling requires axis extent, so resize\n      // should be performed in create stage.\n      grid.resize(gridModel, api, true);\n      gridModel.coordinateSystem = grid;\n      grids.push(grid);\n    });\n    // Inject the coordinateSystems into seriesModel\n    ecModel.eachSeries(function (seriesModel) {\n      if (!isCartesian2DSeries(seriesModel)) {\n        return;\n      }\n      var axesModelMap = findAxisModels(seriesModel);\n      var xAxisModel = axesModelMap.xAxisModel;\n      var yAxisModel = axesModelMap.yAxisModel;\n      var gridModel = xAxisModel.getCoordSysModel();\n      if (process.env.NODE_ENV !== 'production') {\n        if (!gridModel) {\n          throw new Error('Grid \"' + retrieve3(xAxisModel.get('gridIndex'), xAxisModel.get('gridId'), 0) + '\" not found');\n        }\n        if (xAxisModel.getCoordSysModel() !== yAxisModel.getCoordSysModel()) {\n          throw new Error('xAxis and yAxis must use the same grid');\n        }\n      }\n      var grid = gridModel.coordinateSystem;\n      seriesModel.coordinateSystem = grid.getCartesian(xAxisModel.componentIndex, yAxisModel.componentIndex);\n    });\n    return grids;\n  };\n  // For deciding which dimensions to use when creating list data\n  Grid.dimensions = cartesian2DDimensions;\n  return Grid;\n}();\n/**\r\n * Check if the axis is used in the specified grid.\r\n */\nfunction isAxisUsedInTheGrid(axisModel, gridModel) {\n  return axisModel.getCoordSysModel() === gridModel;\n}\nfunction fixAxisOnZero(axesMap, otherAxisDim, axis,\n// Key: see `getOnZeroRecordKey`\nonZeroRecords) {\n  axis.getAxesOnZeroOf = function () {\n    // TODO: onZero of multiple axes.\n    return otherAxisOnZeroOf ? [otherAxisOnZeroOf] : [];\n  };\n  // onZero can not be enabled in these two situations:\n  // 1. When any other axis is a category axis.\n  // 2. When no axis is cross 0 point.\n  var otherAxes = axesMap[otherAxisDim];\n  var otherAxisOnZeroOf;\n  var axisModel = axis.model;\n  var onZero = axisModel.get(['axisLine', 'onZero']);\n  var onZeroAxisIndex = axisModel.get(['axisLine', 'onZeroAxisIndex']);\n  if (!onZero) {\n    return;\n  }\n  // If target axis is specified.\n  if (onZeroAxisIndex != null) {\n    if (canOnZeroToAxis(otherAxes[onZeroAxisIndex])) {\n      otherAxisOnZeroOf = otherAxes[onZeroAxisIndex];\n    }\n  } else {\n    // Find the first available other axis.\n    for (var idx in otherAxes) {\n      if (otherAxes.hasOwnProperty(idx) && canOnZeroToAxis(otherAxes[idx])\n      // Consider that two Y axes on one value axis,\n      // if both onZero, the two Y axes overlap.\n      && !onZeroRecords[getOnZeroRecordKey(otherAxes[idx])]) {\n        otherAxisOnZeroOf = otherAxes[idx];\n        break;\n      }\n    }\n  }\n  if (otherAxisOnZeroOf) {\n    onZeroRecords[getOnZeroRecordKey(otherAxisOnZeroOf)] = true;\n  }\n  function getOnZeroRecordKey(axis) {\n    return axis.dim + '_' + axis.index;\n  }\n}\nfunction canOnZeroToAxis(axis) {\n  return axis && axis.type !== 'category' && axis.type !== 'time' && ifAxisCrossZero(axis);\n}\nfunction updateAxisTransform(axis, coordBase) {\n  var axisExtent = axis.getExtent();\n  var axisExtentSum = axisExtent[0] + axisExtent[1];\n  // Fast transform\n  axis.toGlobalCoord = axis.dim === 'x' ? function (coord) {\n    return coord + coordBase;\n  } : function (coord) {\n    return axisExtentSum - coord + coordBase;\n  };\n  axis.toLocalCoord = axis.dim === 'x' ? function (coord) {\n    return coord - coordBase;\n  } : function (coord) {\n    return axisExtentSum - coord + coordBase;\n  };\n}\nexport default Grid;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}