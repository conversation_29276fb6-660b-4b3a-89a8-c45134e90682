"""
缓存管理器
"""
import json
import pickle
import hashlib
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta
import logging
import threading
from functools import wraps

logger = logging.getLogger(__name__)

class CacheManager:
    """缓存管理器"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, Dict] = {}
        self._access_times: Dict[str, datetime] = {}
        self._lock = threading.RLock()
        
        # 缓存统计
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'evictions': 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                self.stats['misses'] += 1
                return None
            
            cache_item = self._cache[key]
            
            # 检查是否过期
            if self._is_expired(cache_item):
                self.delete(key)
                self.stats['misses'] += 1
                return None
            
            # 更新访问时间
            self._access_times[key] = datetime.now()
            self.stats['hits'] += 1
            
            return cache_item['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        with self._lock:
            try:
                # 检查缓存大小限制
                if len(self._cache) >= self.max_size and key not in self._cache:
                    self._evict_lru()
                
                ttl = ttl or self.default_ttl
                expire_time = datetime.now() + timedelta(seconds=ttl)
                
                self._cache[key] = {
                    'value': value,
                    'expire_time': expire_time,
                    'created_time': datetime.now()
                }
                
                self._access_times[key] = datetime.now()
                self.stats['sets'] += 1
                
                return True
                
            except Exception as e:
                logger.error(f"设置缓存失败: {e}")
                return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                if key in self._access_times:
                    del self._access_times[key]
                self.stats['deletes'] += 1
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._access_times.clear()
            logger.info("缓存已清空")
    
    def exists(self, key: str) -> bool:
        """检查键是否存在且未过期"""
        with self._lock:
            if key not in self._cache:
                return False
            
            cache_item = self._cache[key]
            if self._is_expired(cache_item):
                self.delete(key)
                return False
            
            return True
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0
            
            return {
                'cache_size': len(self._cache),
                'max_size': self.max_size,
                'hit_rate': round(hit_rate, 3),
                'total_hits': self.stats['hits'],
                'total_misses': self.stats['misses'],
                'total_sets': self.stats['sets'],
                'total_deletes': self.stats['deletes'],
                'total_evictions': self.stats['evictions']
            }
    
    def cleanup_expired(self):
        """清理过期缓存"""
        with self._lock:
            expired_keys = []
            
            for key, cache_item in self._cache.items():
                if self._is_expired(cache_item):
                    expired_keys.append(key)
            
            for key in expired_keys:
                self.delete(key)
            
            if expired_keys:
                logger.info(f"清理了 {len(expired_keys)} 个过期缓存项")
    
    def _is_expired(self, cache_item: Dict) -> bool:
        """检查缓存项是否过期"""
        return datetime.now() > cache_item['expire_time']
    
    def _evict_lru(self):
        """LRU淘汰策略"""
        if not self._access_times:
            return
        
        # 找到最久未访问的键
        lru_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])
        self.delete(lru_key)
        self.stats['evictions'] += 1
        
        logger.debug(f"LRU淘汰缓存项: {lru_key}")

class DataCache:
    """数据缓存类"""
    
    def __init__(self):
        self.cache_manager = CacheManager(max_size=500, default_ttl=1800)  # 30分钟
    
    def get_stock_data(self, ts_code: str, start_date: str, end_date: str) -> Optional[Any]:
        """获取股票数据缓存"""
        key = self._generate_stock_data_key(ts_code, start_date, end_date)
        return self.cache_manager.get(key)
    
    def set_stock_data(self, ts_code: str, start_date: str, end_date: str, data: Any, ttl: int = 1800):
        """设置股票数据缓存"""
        key = self._generate_stock_data_key(ts_code, start_date, end_date)
        return self.cache_manager.set(key, data, ttl)
    
    def get_indicators(self, ts_code: str, data_hash: str) -> Optional[Any]:
        """获取技术指标缓存"""
        key = f"indicators:{ts_code}:{data_hash}"
        return self.cache_manager.get(key)
    
    def set_indicators(self, ts_code: str, data_hash: str, indicators: Any, ttl: int = 3600):
        """设置技术指标缓存"""
        key = f"indicators:{ts_code}:{data_hash}"
        return self.cache_manager.set(key, indicators, ttl)
    
    def get_ai_prediction(self, ts_code: str, model_version: str) -> Optional[Any]:
        """获取AI预测缓存"""
        key = f"ai_prediction:{ts_code}:{model_version}"
        return self.cache_manager.get(key)
    
    def set_ai_prediction(self, ts_code: str, model_version: str, prediction: Any, ttl: int = 7200):
        """设置AI预测缓存"""
        key = f"ai_prediction:{ts_code}:{model_version}"
        return self.cache_manager.set(key, prediction, ttl)
    
    def _generate_stock_data_key(self, ts_code: str, start_date: str, end_date: str) -> str:
        """生成股票数据缓存键"""
        return f"stock_data:{ts_code}:{start_date}:{end_date}"
    
    def _generate_data_hash(self, data: Any) -> str:
        """生成数据哈希"""
        try:
            data_str = json.dumps(data, sort_keys=True, default=str)
            return hashlib.md5(data_str.encode()).hexdigest()
        except:
            return hashlib.md5(str(data).encode()).hexdigest()

def cache_result(ttl: int = 3600, key_func: Optional[callable] = None):
    """缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = global_cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            global_cache_manager.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
        self._lock = threading.Lock()
    
    def record_execution_time(self, operation: str, execution_time: float):
        """记录执行时间"""
        with self._lock:
            if operation not in self.metrics:
                self.metrics[operation] = {
                    'count': 0,
                    'total_time': 0,
                    'min_time': float('inf'),
                    'max_time': 0,
                    'avg_time': 0
                }
            
            metric = self.metrics[operation]
            metric['count'] += 1
            metric['total_time'] += execution_time
            metric['min_time'] = min(metric['min_time'], execution_time)
            metric['max_time'] = max(metric['max_time'], execution_time)
            metric['avg_time'] = metric['total_time'] / metric['count']
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        with self._lock:
            return dict(self.metrics)
    
    def reset_metrics(self):
        """重置性能指标"""
        with self._lock:
            self.metrics.clear()

def monitor_performance(operation_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            import time
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                end_time = time.time()
                execution_time = end_time - start_time
                
                op_name = operation_name or func.__name__
                global_performance_monitor.record_execution_time(op_name, execution_time)
        
        return wrapper
    return decorator

# 全局实例
global_cache_manager = CacheManager()
global_data_cache = DataCache()
global_performance_monitor = PerformanceMonitor()

def get_cache_manager() -> CacheManager:
    """获取缓存管理器"""
    return global_cache_manager

def get_data_cache() -> DataCache:
    """获取数据缓存"""
    return global_data_cache

def get_performance_monitor() -> PerformanceMonitor:
    """获取性能监控器"""
    return global_performance_monitor
