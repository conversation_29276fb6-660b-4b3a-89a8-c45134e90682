{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"height\", \"itemHeight\", \"fullHeight\", \"style\", \"data\", \"children\", \"itemKey\", \"virtual\", \"direction\", \"scrollWidth\", \"component\", \"onScroll\", \"onVirtualScroll\", \"onVisibleChange\", \"innerProps\", \"extraRender\", \"styles\", \"showScrollBar\"];\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { useEvent } from 'rc-util';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { useRef, useState } from 'react';\nimport { flushSync } from 'react-dom';\nimport Filler from \"./Filler\";\nimport useChildren from \"./hooks/useChildren\";\nimport useDiffItem from \"./hooks/useDiffItem\";\nimport useFrameWheel from \"./hooks/useFrameWheel\";\nimport { useGetSize } from \"./hooks/useGetSize\";\nimport useHeights from \"./hooks/useHeights\";\nimport useMobileTouchMove from \"./hooks/useMobileTouchMove\";\nimport useOriginScroll from \"./hooks/useOriginScroll\";\nimport useScrollDrag from \"./hooks/useScrollDrag\";\nimport useScrollTo from \"./hooks/useScrollTo\";\nimport ScrollBar from \"./ScrollBar\";\nimport { getSpinSize } from \"./utils/scrollbarUtil\";\nvar EMPTY_DATA = [];\nvar ScrollStyle = {\n  overflowY: 'auto',\n  overflowAnchor: 'none'\n};\nexport function RawList(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-virtual-list' : _props$prefixCls,\n    className = props.className,\n    height = props.height,\n    itemHeight = props.itemHeight,\n    _props$fullHeight = props.fullHeight,\n    fullHeight = _props$fullHeight === void 0 ? true : _props$fullHeight,\n    style = props.style,\n    data = props.data,\n    children = props.children,\n    itemKey = props.itemKey,\n    virtual = props.virtual,\n    direction = props.direction,\n    scrollWidth = props.scrollWidth,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    onScroll = props.onScroll,\n    onVirtualScroll = props.onVirtualScroll,\n    onVisibleChange = props.onVisibleChange,\n    innerProps = props.innerProps,\n    extraRender = props.extraRender,\n    styles = props.styles,\n    _props$showScrollBar = props.showScrollBar,\n    showScrollBar = _props$showScrollBar === void 0 ? 'optional' : _props$showScrollBar,\n    restProps = _objectWithoutProperties(props, _excluded);\n\n  // =============================== Item Key ===============================\n  var getKey = React.useCallback(function (item) {\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n    return item === null || item === void 0 ? void 0 : item[itemKey];\n  }, [itemKey]);\n\n  // ================================ Height ================================\n  var _useHeights = useHeights(getKey, null, null),\n    _useHeights2 = _slicedToArray(_useHeights, 4),\n    setInstanceRef = _useHeights2[0],\n    collectHeight = _useHeights2[1],\n    heights = _useHeights2[2],\n    heightUpdatedMark = _useHeights2[3];\n\n  // ================================= MISC =================================\n  var useVirtual = !!(virtual !== false && height && itemHeight);\n  var containerHeight = React.useMemo(function () {\n    return Object.values(heights.maps).reduce(function (total, curr) {\n      return total + curr;\n    }, 0);\n  }, [heights.id, heights.maps]);\n  var inVirtual = useVirtual && data && (Math.max(itemHeight * data.length, containerHeight) > height || !!scrollWidth);\n  var isRTL = direction === 'rtl';\n  var mergedClassName = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), isRTL), className);\n  var mergedData = data || EMPTY_DATA;\n  var componentRef = useRef();\n  var fillerInnerRef = useRef();\n  var containerRef = useRef();\n\n  // =============================== Item Key ===============================\n\n  var _useState = useState(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    offsetTop = _useState2[0],\n    setOffsetTop = _useState2[1];\n  var _useState3 = useState(0),\n    _useState4 = _slicedToArray(_useState3, 2),\n    offsetLeft = _useState4[0],\n    setOffsetLeft = _useState4[1];\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    scrollMoving = _useState6[0],\n    setScrollMoving = _useState6[1];\n  var onScrollbarStartMove = function onScrollbarStartMove() {\n    setScrollMoving(true);\n  };\n  var onScrollbarStopMove = function onScrollbarStopMove() {\n    setScrollMoving(false);\n  };\n  var sharedConfig = {\n    getKey: getKey\n  };\n\n  // ================================ Scroll ================================\n  function syncScrollTop(newTop) {\n    setOffsetTop(function (origin) {\n      var value;\n      if (typeof newTop === 'function') {\n        value = newTop(origin);\n      } else {\n        value = newTop;\n      }\n      var alignedTop = keepInRange(value);\n      componentRef.current.scrollTop = alignedTop;\n      return alignedTop;\n    });\n  }\n\n  // ================================ Legacy ================================\n  // Put ref here since the range is generate by follow\n  var rangeRef = useRef({\n    start: 0,\n    end: mergedData.length\n  });\n  var diffItemRef = useRef();\n  var _useDiffItem = useDiffItem(mergedData, getKey),\n    _useDiffItem2 = _slicedToArray(_useDiffItem, 1),\n    diffItem = _useDiffItem2[0];\n  diffItemRef.current = diffItem;\n\n  // ========================== Visible Calculation =========================\n  var _React$useMemo = React.useMemo(function () {\n      if (!useVirtual) {\n        return {\n          scrollHeight: undefined,\n          start: 0,\n          end: mergedData.length - 1,\n          offset: undefined\n        };\n      }\n\n      // Always use virtual scroll bar in avoid shaking\n      if (!inVirtual) {\n        var _fillerInnerRef$curre;\n        return {\n          scrollHeight: ((_fillerInnerRef$curre = fillerInnerRef.current) === null || _fillerInnerRef$curre === void 0 ? void 0 : _fillerInnerRef$curre.offsetHeight) || 0,\n          start: 0,\n          end: mergedData.length - 1,\n          offset: undefined\n        };\n      }\n      var itemTop = 0;\n      var startIndex;\n      var startOffset;\n      var endIndex;\n      var dataLen = mergedData.length;\n      for (var i = 0; i < dataLen; i += 1) {\n        var _item = mergedData[i];\n        var key = getKey(_item);\n        var cacheHeight = heights.get(key);\n        var currentItemBottom = itemTop + (cacheHeight === undefined ? itemHeight : cacheHeight);\n\n        // Check item top in the range\n        if (currentItemBottom >= offsetTop && startIndex === undefined) {\n          startIndex = i;\n          startOffset = itemTop;\n        }\n\n        // Check item bottom in the range. We will render additional one item for motion usage\n        if (currentItemBottom > offsetTop + height && endIndex === undefined) {\n          endIndex = i;\n        }\n        itemTop = currentItemBottom;\n      }\n\n      // When scrollTop at the end but data cut to small count will reach this\n      if (startIndex === undefined) {\n        startIndex = 0;\n        startOffset = 0;\n        endIndex = Math.ceil(height / itemHeight);\n      }\n      if (endIndex === undefined) {\n        endIndex = mergedData.length - 1;\n      }\n\n      // Give cache to improve scroll experience\n      endIndex = Math.min(endIndex + 1, mergedData.length - 1);\n      return {\n        scrollHeight: itemTop,\n        start: startIndex,\n        end: endIndex,\n        offset: startOffset\n      };\n    }, [inVirtual, useVirtual, offsetTop, mergedData, heightUpdatedMark, height]),\n    scrollHeight = _React$useMemo.scrollHeight,\n    start = _React$useMemo.start,\n    end = _React$useMemo.end,\n    fillerOffset = _React$useMemo.offset;\n  rangeRef.current.start = start;\n  rangeRef.current.end = end;\n\n  // When scroll up, first visible item get real height may not same as `itemHeight`,\n  // Which will make scroll jump.\n  // Let's sync scroll top to avoid jump\n  React.useLayoutEffect(function () {\n    var changedRecord = heights.getRecord();\n    if (changedRecord.size === 1) {\n      var recordKey = Array.from(changedRecord.keys())[0];\n      var prevCacheHeight = changedRecord.get(recordKey);\n\n      // Quick switch data may cause `start` not in `mergedData` anymore\n      var startItem = mergedData[start];\n      if (startItem && prevCacheHeight === undefined) {\n        var startIndexKey = getKey(startItem);\n        if (startIndexKey === recordKey) {\n          var realStartHeight = heights.get(recordKey);\n          var diffHeight = realStartHeight - itemHeight;\n          syncScrollTop(function (ori) {\n            return ori + diffHeight;\n          });\n        }\n      }\n    }\n    heights.resetRecord();\n  }, [scrollHeight]);\n\n  // ================================= Size =================================\n  var _React$useState = React.useState({\n      width: 0,\n      height: height\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    size = _React$useState2[0],\n    setSize = _React$useState2[1];\n  var onHolderResize = function onHolderResize(sizeInfo) {\n    setSize({\n      width: sizeInfo.offsetWidth,\n      height: sizeInfo.offsetHeight\n    });\n  };\n\n  // Hack on scrollbar to enable flash call\n  var verticalScrollBarRef = useRef();\n  var horizontalScrollBarRef = useRef();\n  var horizontalScrollBarSpinSize = React.useMemo(function () {\n    return getSpinSize(size.width, scrollWidth);\n  }, [size.width, scrollWidth]);\n  var verticalScrollBarSpinSize = React.useMemo(function () {\n    return getSpinSize(size.height, scrollHeight);\n  }, [size.height, scrollHeight]);\n\n  // =============================== In Range ===============================\n  var maxScrollHeight = scrollHeight - height;\n  var maxScrollHeightRef = useRef(maxScrollHeight);\n  maxScrollHeightRef.current = maxScrollHeight;\n  function keepInRange(newScrollTop) {\n    var newTop = newScrollTop;\n    if (!Number.isNaN(maxScrollHeightRef.current)) {\n      newTop = Math.min(newTop, maxScrollHeightRef.current);\n    }\n    newTop = Math.max(newTop, 0);\n    return newTop;\n  }\n  var isScrollAtTop = offsetTop <= 0;\n  var isScrollAtBottom = offsetTop >= maxScrollHeight;\n  var isScrollAtLeft = offsetLeft <= 0;\n  var isScrollAtRight = offsetLeft >= scrollWidth;\n  var originScroll = useOriginScroll(isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight);\n\n  // ================================ Scroll ================================\n  var getVirtualScrollInfo = function getVirtualScrollInfo() {\n    return {\n      x: isRTL ? -offsetLeft : offsetLeft,\n      y: offsetTop\n    };\n  };\n  var lastVirtualScrollInfoRef = useRef(getVirtualScrollInfo());\n  var triggerScroll = useEvent(function (params) {\n    if (onVirtualScroll) {\n      var nextInfo = _objectSpread(_objectSpread({}, getVirtualScrollInfo()), params);\n\n      // Trigger when offset changed\n      if (lastVirtualScrollInfoRef.current.x !== nextInfo.x || lastVirtualScrollInfoRef.current.y !== nextInfo.y) {\n        onVirtualScroll(nextInfo);\n        lastVirtualScrollInfoRef.current = nextInfo;\n      }\n    }\n  });\n  function onScrollBar(newScrollOffset, horizontal) {\n    var newOffset = newScrollOffset;\n    if (horizontal) {\n      flushSync(function () {\n        setOffsetLeft(newOffset);\n      });\n      triggerScroll();\n    } else {\n      syncScrollTop(newOffset);\n    }\n  }\n\n  // When data size reduce. It may trigger native scroll event back to fit scroll position\n  function onFallbackScroll(e) {\n    var newScrollTop = e.currentTarget.scrollTop;\n    if (newScrollTop !== offsetTop) {\n      syncScrollTop(newScrollTop);\n    }\n\n    // Trigger origin onScroll\n    onScroll === null || onScroll === void 0 || onScroll(e);\n    triggerScroll();\n  }\n  var keepInHorizontalRange = function keepInHorizontalRange(nextOffsetLeft) {\n    var tmpOffsetLeft = nextOffsetLeft;\n    var max = !!scrollWidth ? scrollWidth - size.width : 0;\n    tmpOffsetLeft = Math.max(tmpOffsetLeft, 0);\n    tmpOffsetLeft = Math.min(tmpOffsetLeft, max);\n    return tmpOffsetLeft;\n  };\n  var onWheelDelta = useEvent(function (offsetXY, fromHorizontal) {\n    if (fromHorizontal) {\n      flushSync(function () {\n        setOffsetLeft(function (left) {\n          var nextOffsetLeft = left + (isRTL ? -offsetXY : offsetXY);\n          return keepInHorizontalRange(nextOffsetLeft);\n        });\n      });\n      triggerScroll();\n    } else {\n      syncScrollTop(function (top) {\n        var newTop = top + offsetXY;\n        return newTop;\n      });\n    }\n  });\n\n  // Since this added in global,should use ref to keep update\n  var _useFrameWheel = useFrameWheel(useVirtual, isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight, !!scrollWidth, onWheelDelta),\n    _useFrameWheel2 = _slicedToArray(_useFrameWheel, 2),\n    onRawWheel = _useFrameWheel2[0],\n    onFireFoxScroll = _useFrameWheel2[1];\n\n  // Mobile touch move\n  useMobileTouchMove(useVirtual, componentRef, function (isHorizontal, delta, smoothOffset, e) {\n    var event = e;\n    if (originScroll(isHorizontal, delta, smoothOffset)) {\n      return false;\n    }\n\n    // Fix nest List trigger TouchMove event\n    if (!event || !event._virtualHandled) {\n      if (event) {\n        event._virtualHandled = true;\n      }\n      onRawWheel({\n        preventDefault: function preventDefault() {},\n        deltaX: isHorizontal ? delta : 0,\n        deltaY: isHorizontal ? 0 : delta\n      });\n      return true;\n    }\n    return false;\n  });\n\n  // MouseDown drag for scroll\n  useScrollDrag(inVirtual, componentRef, function (offset) {\n    syncScrollTop(function (top) {\n      return top + offset;\n    });\n  });\n  useLayoutEffect(function () {\n    // Firefox only\n    function onMozMousePixelScroll(e) {\n      // scrolling at top/bottom limit\n      var scrollingUpAtTop = isScrollAtTop && e.detail < 0;\n      var scrollingDownAtBottom = isScrollAtBottom && e.detail > 0;\n      if (useVirtual && !scrollingUpAtTop && !scrollingDownAtBottom) {\n        e.preventDefault();\n      }\n    }\n    var componentEle = componentRef.current;\n    componentEle.addEventListener('wheel', onRawWheel, {\n      passive: false\n    });\n    componentEle.addEventListener('DOMMouseScroll', onFireFoxScroll, {\n      passive: true\n    });\n    componentEle.addEventListener('MozMousePixelScroll', onMozMousePixelScroll, {\n      passive: false\n    });\n    return function () {\n      componentEle.removeEventListener('wheel', onRawWheel);\n      componentEle.removeEventListener('DOMMouseScroll', onFireFoxScroll);\n      componentEle.removeEventListener('MozMousePixelScroll', onMozMousePixelScroll);\n    };\n  }, [useVirtual, isScrollAtTop, isScrollAtBottom]);\n\n  // Sync scroll left\n  useLayoutEffect(function () {\n    if (scrollWidth) {\n      var newOffsetLeft = keepInHorizontalRange(offsetLeft);\n      setOffsetLeft(newOffsetLeft);\n      triggerScroll({\n        x: newOffsetLeft\n      });\n    }\n  }, [size.width, scrollWidth]);\n\n  // ================================= Ref ==================================\n  var delayHideScrollBar = function delayHideScrollBar() {\n    var _verticalScrollBarRef, _horizontalScrollBarR;\n    (_verticalScrollBarRef = verticalScrollBarRef.current) === null || _verticalScrollBarRef === void 0 || _verticalScrollBarRef.delayHidden();\n    (_horizontalScrollBarR = horizontalScrollBarRef.current) === null || _horizontalScrollBarR === void 0 || _horizontalScrollBarR.delayHidden();\n  };\n  var _scrollTo = useScrollTo(componentRef, mergedData, heights, itemHeight, getKey, function () {\n    return collectHeight(true);\n  }, syncScrollTop, delayHideScrollBar);\n  React.useImperativeHandle(ref, function () {\n    return {\n      nativeElement: containerRef.current,\n      getScrollInfo: getVirtualScrollInfo,\n      scrollTo: function scrollTo(config) {\n        function isPosScroll(arg) {\n          return arg && _typeof(arg) === 'object' && ('left' in arg || 'top' in arg);\n        }\n        if (isPosScroll(config)) {\n          // Scroll X\n          if (config.left !== undefined) {\n            setOffsetLeft(keepInHorizontalRange(config.left));\n          }\n\n          // Scroll Y\n          _scrollTo(config.top);\n        } else {\n          _scrollTo(config);\n        }\n      }\n    };\n  });\n\n  // ================================ Effect ================================\n  /** We need told outside that some list not rendered */\n  useLayoutEffect(function () {\n    if (onVisibleChange) {\n      var renderList = mergedData.slice(start, end + 1);\n      onVisibleChange(renderList, mergedData);\n    }\n  }, [start, end, mergedData]);\n\n  // ================================ Extra =================================\n  var getSize = useGetSize(mergedData, getKey, heights, itemHeight);\n  var extraContent = extraRender === null || extraRender === void 0 ? void 0 : extraRender({\n    start: start,\n    end: end,\n    virtual: inVirtual,\n    offsetX: offsetLeft,\n    offsetY: fillerOffset,\n    rtl: isRTL,\n    getSize: getSize\n  });\n\n  // ================================ Render ================================\n  var listChildren = useChildren(mergedData, start, end, scrollWidth, offsetLeft, setInstanceRef, children, sharedConfig);\n  var componentStyle = null;\n  if (height) {\n    componentStyle = _objectSpread(_defineProperty({}, fullHeight ? 'height' : 'maxHeight', height), ScrollStyle);\n    if (useVirtual) {\n      componentStyle.overflowY = 'hidden';\n      if (scrollWidth) {\n        componentStyle.overflowX = 'hidden';\n      }\n      if (scrollMoving) {\n        componentStyle.pointerEvents = 'none';\n      }\n    }\n  }\n  var containerProps = {};\n  if (isRTL) {\n    containerProps.dir = 'rtl';\n  }\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: containerRef,\n    style: _objectSpread(_objectSpread({}, style), {}, {\n      position: 'relative'\n    }),\n    className: mergedClassName\n  }, containerProps, restProps), /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onHolderResize\n  }, /*#__PURE__*/React.createElement(Component, {\n    className: \"\".concat(prefixCls, \"-holder\"),\n    style: componentStyle,\n    ref: componentRef,\n    onScroll: onFallbackScroll,\n    onMouseEnter: delayHideScrollBar\n  }, /*#__PURE__*/React.createElement(Filler, {\n    prefixCls: prefixCls,\n    height: scrollHeight,\n    offsetX: offsetLeft,\n    offsetY: fillerOffset,\n    scrollWidth: scrollWidth,\n    onInnerResize: collectHeight,\n    ref: fillerInnerRef,\n    innerProps: innerProps,\n    rtl: isRTL,\n    extra: extraContent\n  }, listChildren))), inVirtual && scrollHeight > height && /*#__PURE__*/React.createElement(ScrollBar, {\n    ref: verticalScrollBarRef,\n    prefixCls: prefixCls,\n    scrollOffset: offsetTop,\n    scrollRange: scrollHeight,\n    rtl: isRTL,\n    onScroll: onScrollBar,\n    onStartMove: onScrollbarStartMove,\n    onStopMove: onScrollbarStopMove,\n    spinSize: verticalScrollBarSpinSize,\n    containerSize: size.height,\n    style: styles === null || styles === void 0 ? void 0 : styles.verticalScrollBar,\n    thumbStyle: styles === null || styles === void 0 ? void 0 : styles.verticalScrollBarThumb,\n    showScrollBar: showScrollBar\n  }), inVirtual && scrollWidth > size.width && /*#__PURE__*/React.createElement(ScrollBar, {\n    ref: horizontalScrollBarRef,\n    prefixCls: prefixCls,\n    scrollOffset: offsetLeft,\n    scrollRange: scrollWidth,\n    rtl: isRTL,\n    onScroll: onScrollBar,\n    onStartMove: onScrollbarStartMove,\n    onStopMove: onScrollbarStopMove,\n    spinSize: horizontalScrollBarSpinSize,\n    containerSize: size.width,\n    horizontal: true,\n    style: styles === null || styles === void 0 ? void 0 : styles.horizontalScrollBar,\n    thumbStyle: styles === null || styles === void 0 ? void 0 : styles.horizontalScrollBarThumb,\n    showScrollBar: showScrollBar\n  }));\n}\nvar List = /*#__PURE__*/React.forwardRef(RawList);\nList.displayName = 'List';\nexport default List;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}