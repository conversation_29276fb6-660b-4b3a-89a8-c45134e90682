{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as axisPointerModelHelper from '../axisPointer/modelHelper.js';\nimport ComponentView from '../../view/Component.js';\nvar axisPointerClazz = {};\n/**\r\n * Base class of AxisView.\r\n */\nvar AxisView = /** @class */function (_super) {\n  __extends(AxisView, _super);\n  function AxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AxisView.type;\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  AxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    // FIXME\n    // This process should proformed after coordinate systems updated\n    // (axis scale updated), and should be performed each time update.\n    // So put it here temporarily, although it is not appropriate to\n    // put a model-writing procedure in `view`.\n    this.axisPointerClass && axisPointerModelHelper.fixValue(axisModel);\n    _super.prototype.render.apply(this, arguments);\n    this._doUpdateAxisPointerClass(axisModel, api, true);\n  };\n  /**\r\n   * Action handler.\r\n   */\n  AxisView.prototype.updateAxisPointer = function (axisModel, ecModel, api, payload) {\n    this._doUpdateAxisPointerClass(axisModel, api, false);\n  };\n  /**\r\n   * @override\r\n   */\n  AxisView.prototype.remove = function (ecModel, api) {\n    var axisPointer = this._axisPointer;\n    axisPointer && axisPointer.remove(api);\n  };\n  /**\r\n   * @override\r\n   */\n  AxisView.prototype.dispose = function (ecModel, api) {\n    this._disposeAxisPointer(api);\n    _super.prototype.dispose.apply(this, arguments);\n  };\n  AxisView.prototype._doUpdateAxisPointerClass = function (axisModel, api, forceRender) {\n    var Clazz = AxisView.getAxisPointerClass(this.axisPointerClass);\n    if (!Clazz) {\n      return;\n    }\n    var axisPointerModel = axisPointerModelHelper.getAxisPointerModel(axisModel);\n    axisPointerModel ? (this._axisPointer || (this._axisPointer = new Clazz())).render(axisModel, axisPointerModel, api, forceRender) : this._disposeAxisPointer(api);\n  };\n  AxisView.prototype._disposeAxisPointer = function (api) {\n    this._axisPointer && this._axisPointer.dispose(api);\n    this._axisPointer = null;\n  };\n  AxisView.registerAxisPointerClass = function (type, clazz) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (axisPointerClazz[type]) {\n        throw new Error('axisPointer ' + type + ' exists');\n      }\n    }\n    axisPointerClazz[type] = clazz;\n  };\n  ;\n  AxisView.getAxisPointerClass = function (type) {\n    return type && axisPointerClazz[type];\n  };\n  ;\n  AxisView.type = 'axis';\n  return AxisView;\n}(ComponentView);\nexport default AxisView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}