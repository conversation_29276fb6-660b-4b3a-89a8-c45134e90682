{"ast": null, "code": "var MIN_SIZE = 20;\nexport function getSpinSize() {\n  var containerSize = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  var scrollRange = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var baseSize = containerSize / scrollRange * containerSize;\n  if (isNaN(baseSize)) {\n    baseSize = 0;\n  }\n  baseSize = Math.max(baseSize, MIN_SIZE);\n  return Math.floor(baseSize);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}