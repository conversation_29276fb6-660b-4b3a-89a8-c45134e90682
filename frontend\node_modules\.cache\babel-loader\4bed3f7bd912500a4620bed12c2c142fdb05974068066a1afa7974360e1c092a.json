{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport createGraphFromNodeEdge from '../helper/createGraphFromNodeEdge.js';\nimport Model from '../../model/Model.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nvar SankeySeriesModel = /** @class */function (_super) {\n  __extends(SankeySeriesModel, _super);\n  function SankeySeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SankeySeriesModel.type;\n    return _this;\n  }\n  /**\r\n   * Init a graph data structure from data in option series\r\n   */\n  SankeySeriesModel.prototype.getInitialData = function (option, ecModel) {\n    var links = option.edges || option.links || [];\n    var nodes = option.data || option.nodes || [];\n    var levels = option.levels || [];\n    this.levelModels = [];\n    var levelModels = this.levelModels;\n    for (var i = 0; i < levels.length; i++) {\n      if (levels[i].depth != null && levels[i].depth >= 0) {\n        levelModels[levels[i].depth] = new Model(levels[i], this, ecModel);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          throw new Error('levels[i].depth is mandatory and should be natural number');\n        }\n      }\n    }\n    var graph = createGraphFromNodeEdge(nodes, links, this, true, beforeLink);\n    return graph.data;\n    function beforeLink(nodeData, edgeData) {\n      nodeData.wrapMethod('getItemModel', function (model, idx) {\n        var seriesModel = model.parentModel;\n        var layout = seriesModel.getData().getItemLayout(idx);\n        if (layout) {\n          var nodeDepth = layout.depth;\n          var levelModel = seriesModel.levelModels[nodeDepth];\n          if (levelModel) {\n            model.parentModel = levelModel;\n          }\n        }\n        return model;\n      });\n      edgeData.wrapMethod('getItemModel', function (model, idx) {\n        var seriesModel = model.parentModel;\n        var edge = seriesModel.getGraph().getEdgeByIndex(idx);\n        var layout = edge.node1.getLayout();\n        if (layout) {\n          var depth = layout.depth;\n          var levelModel = seriesModel.levelModels[depth];\n          if (levelModel) {\n            model.parentModel = levelModel;\n          }\n        }\n        return model;\n      });\n    }\n  };\n  SankeySeriesModel.prototype.setNodePosition = function (dataIndex, localPosition) {\n    var nodes = this.option.data || this.option.nodes;\n    var dataItem = nodes[dataIndex];\n    dataItem.localX = localPosition[0];\n    dataItem.localY = localPosition[1];\n  };\n  /**\r\n   * Return the graphic data structure\r\n   *\r\n   * @return graphic data structure\r\n   */\n  SankeySeriesModel.prototype.getGraph = function () {\n    return this.getData().graph;\n  };\n  /**\r\n   * Get edge data of graphic data structure\r\n   *\r\n   * @return data structure of list\r\n   */\n  SankeySeriesModel.prototype.getEdgeData = function () {\n    return this.getGraph().edgeData;\n  };\n  SankeySeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    function noValue(val) {\n      return isNaN(val) || val == null;\n    }\n    // dataType === 'node' or empty do not show tooltip by default\n    if (dataType === 'edge') {\n      var params = this.getDataParams(dataIndex, dataType);\n      var rawDataOpt = params.data;\n      var edgeValue = params.value;\n      var edgeName = rawDataOpt.source + ' -- ' + rawDataOpt.target;\n      return createTooltipMarkup('nameValue', {\n        name: edgeName,\n        value: edgeValue,\n        noValue: noValue(edgeValue)\n      });\n    }\n    // dataType === 'node'\n    else {\n      var node = this.getGraph().getNodeByIndex(dataIndex);\n      var value = node.getLayout().value;\n      var name_1 = this.getDataParams(dataIndex, dataType).data.name;\n      return createTooltipMarkup('nameValue', {\n        name: name_1 != null ? name_1 + '' : null,\n        value: value,\n        noValue: noValue(value)\n      });\n    }\n  };\n  SankeySeriesModel.prototype.optionUpdated = function () {};\n  // Override Series.getDataParams()\n  SankeySeriesModel.prototype.getDataParams = function (dataIndex, dataType) {\n    var params = _super.prototype.getDataParams.call(this, dataIndex, dataType);\n    if (params.value == null && dataType === 'node') {\n      var node = this.getGraph().getNodeByIndex(dataIndex);\n      var nodeValue = node.getLayout().value;\n      params.value = nodeValue;\n    }\n    return params;\n  };\n  SankeySeriesModel.type = 'series.sankey';\n  SankeySeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'view',\n    left: '5%',\n    top: '5%',\n    right: '20%',\n    bottom: '5%',\n    orient: 'horizontal',\n    nodeWidth: 20,\n    nodeGap: 8,\n    draggable: true,\n    layoutIterations: 32,\n    label: {\n      show: true,\n      position: 'right',\n      fontSize: 12\n    },\n    edgeLabel: {\n      show: false,\n      fontSize: 12\n    },\n    levels: [],\n    nodeAlign: 'justify',\n    lineStyle: {\n      color: '#314656',\n      opacity: 0.2,\n      curveness: 0.5\n    },\n    emphasis: {\n      label: {\n        show: true\n      },\n      lineStyle: {\n        opacity: 0.5\n      }\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    },\n    animationEasing: 'linear',\n    animationDuration: 1000\n  };\n  return SankeySeriesModel;\n}(SeriesModel);\nexport default SankeySeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}