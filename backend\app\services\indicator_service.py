"""
技术指标服务
"""
import pandas as pd
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging

from strategy.indicators import get_technical_indicators
from data.tushare_client import get_tushare_client
from app.core.influxdb_client import get_influx_manager
from app.models.market_data import TechnicalIndicator

logger = logging.getLogger(__name__)

class IndicatorService:
    """技术指标服务类"""
    
    def __init__(self):
        self.calculator = get_technical_indicators()
        self.tushare_client = get_tushare_client()
        self.influx_manager = get_influx_manager()
    
    def calculate_stock_indicators(self, ts_code: str, days: int = 60) -> Dict[str, Any]:
        """计算单只股票的技术指标"""
        try:
            # 获取历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            df = self.tushare_client.get_daily_data(
                ts_code,
                start_date.strftime('%Y%m%d'),
                end_date.strftime('%Y%m%d')
            )
            
            if df.empty:
                logger.warning(f"未获取到 {ts_code} 的历史数据")
                return {}
            
            # 计算技术指标
            result_df = self.calculator.calculate_all_indicators(df)
            
            # 获取最新指标值
            latest = result_df.iloc[-1] if not result_df.empty else {}
            
            indicators = {
                'ts_code': ts_code,
                'trade_date': latest.get('trade_date', ''),
                'price_data': {
                    'close': float(latest.get('close', 0)),
                    'open': float(latest.get('open', 0)),
                    'high': float(latest.get('high', 0)),
                    'low': float(latest.get('low', 0)),
                    'volume': float(latest.get('vol', 0))
                },
                'moving_averages': {
                    'ma5': float(latest.get('ma_5', 0)) if pd.notna(latest.get('ma_5')) else None,
                    'ma10': float(latest.get('ma_10', 0)) if pd.notna(latest.get('ma_10')) else None,
                    'ma20': float(latest.get('ma_20', 0)) if pd.notna(latest.get('ma_20')) else None,
                    'ma60': float(latest.get('ma_60', 0)) if pd.notna(latest.get('ma_60')) else None
                },
                'macd': {
                    'macd': float(latest.get('macd', 0)) if pd.notna(latest.get('macd')) else None,
                    'signal': float(latest.get('macd_signal', 0)) if pd.notna(latest.get('macd_signal')) else None,
                    'histogram': float(latest.get('macd_histogram', 0)) if pd.notna(latest.get('macd_histogram')) else None,
                    'signal_flag': latest.get('macd_signal_flag', 'HOLD')
                },
                'rsi': {
                    'value': float(latest.get('rsi', 0)) if pd.notna(latest.get('rsi')) else None,
                    'signal': latest.get('rsi_signal', 'HOLD')
                },
                'bollinger_bands': {
                    'upper': float(latest.get('bb_upper', 0)) if pd.notna(latest.get('bb_upper')) else None,
                    'middle': float(latest.get('bb_middle', 0)) if pd.notna(latest.get('bb_middle')) else None,
                    'lower': float(latest.get('bb_lower', 0)) if pd.notna(latest.get('bb_lower')) else None,
                    'width': float(latest.get('bb_width', 0)) if pd.notna(latest.get('bb_width')) else None,
                    'signal': latest.get('bb_signal', 'HOLD')
                },
                'kdj': {
                    'k': float(latest.get('k', 0)) if pd.notna(latest.get('k')) else None,
                    'd': float(latest.get('d', 0)) if pd.notna(latest.get('d')) else None,
                    'j': float(latest.get('j', 0)) if pd.notna(latest.get('j')) else None,
                    'signal': latest.get('kdj_signal', 'HOLD')
                },
                'volume_indicators': {
                    'vol_ma5': float(latest.get('vol_ma5', 0)) if pd.notna(latest.get('vol_ma5')) else None,
                    'vol_ma10': float(latest.get('vol_ma10', 0)) if pd.notna(latest.get('vol_ma10')) else None,
                    'volume_ratio': float(latest.get('volume_ratio', 0)) if pd.notna(latest.get('volume_ratio')) else None,
                    'obv': float(latest.get('obv', 0)) if pd.notna(latest.get('obv')) else None
                },
                'composite_signal': latest.get('综合信号', 'HOLD'),
                'calculation_time': datetime.now().isoformat()
            }
            
            # 保存到InfluxDB
            self._save_indicators_to_influx(ts_code, result_df)
            
            logger.info(f"计算 {ts_code} 技术指标完成")
            return indicators
            
        except Exception as e:
            logger.error(f"计算 {ts_code} 技术指标失败: {e}")
            return {}
    
    def batch_calculate_indicators(self, ts_codes: List[str], days: int = 60) -> Dict[str, Dict[str, Any]]:
        """批量计算技术指标"""
        results = {}
        
        logger.info(f"开始批量计算 {len(ts_codes)} 只股票的技术指标")
        
        for i, ts_code in enumerate(ts_codes):
            try:
                indicators = self.calculate_stock_indicators(ts_code, days)
                results[ts_code] = indicators
                
                # 控制调用频率
                if i < len(ts_codes) - 1:
                    import time
                    time.sleep(0.1)
                    
            except Exception as e:
                logger.error(f"批量计算 {ts_code} 指标失败: {e}")
                results[ts_code] = {}
        
        success_count = len([k for k, v in results.items() if v])
        logger.info(f"批量计算完成：{success_count}/{len(ts_codes)} 成功")
        
        return results
    
    def get_stock_signals(self, ts_code: str) -> Dict[str, Any]:
        """获取股票交易信号"""
        try:
            indicators = self.calculate_stock_indicators(ts_code)
            
            if not indicators:
                return {'ts_code': ts_code, 'signals': [], 'composite_signal': 'HOLD'}
            
            signals = []
            
            # 收集各指标信号
            if indicators.get('macd', {}).get('signal_flag') != 'HOLD':
                signals.append({
                    'indicator': 'MACD',
                    'signal': indicators['macd']['signal_flag'],
                    'value': indicators['macd']['macd'],
                    'description': f"MACD {indicators['macd']['signal_flag']}"
                })
            
            if indicators.get('rsi', {}).get('signal') != 'HOLD':
                signals.append({
                    'indicator': 'RSI',
                    'signal': indicators['rsi']['signal'],
                    'value': indicators['rsi']['value'],
                    'description': f"RSI {indicators['rsi']['signal']} ({indicators['rsi']['value']:.2f})"
                })
            
            if indicators.get('bollinger_bands', {}).get('signal') != 'HOLD':
                signals.append({
                    'indicator': 'Bollinger Bands',
                    'signal': indicators['bollinger_bands']['signal'],
                    'value': indicators['price_data']['close'],
                    'description': f"布林带 {indicators['bollinger_bands']['signal']}"
                })
            
            if indicators.get('kdj', {}).get('signal') != 'HOLD':
                signals.append({
                    'indicator': 'KDJ',
                    'signal': indicators['kdj']['signal'],
                    'value': indicators['kdj']['k'],
                    'description': f"KDJ {indicators['kdj']['signal']} (K:{indicators['kdj']['k']:.2f})"
                })
            
            return {
                'ts_code': ts_code,
                'signals': signals,
                'composite_signal': indicators.get('composite_signal', 'HOLD'),
                'signal_count': len(signals),
                'calculation_time': indicators.get('calculation_time')
            }
            
        except Exception as e:
            logger.error(f"获取 {ts_code} 交易信号失败: {e}")
            return {'ts_code': ts_code, 'signals': [], 'composite_signal': 'HOLD'}
    
    def get_market_overview(self, ts_codes: List[str]) -> Dict[str, Any]:
        """获取市场概览"""
        try:
            signals_summary = {'BUY': 0, 'SELL': 0, 'HOLD': 0}
            active_signals = []
            
            for ts_code in ts_codes[:20]:  # 限制数量避免超时
                stock_signals = self.get_stock_signals(ts_code)
                
                # 统计综合信号
                composite_signal = stock_signals.get('composite_signal', 'HOLD')
                signals_summary[composite_signal] += 1
                
                # 收集活跃信号
                if stock_signals.get('signals'):
                    active_signals.append({
                        'ts_code': ts_code,
                        'signal_count': stock_signals['signal_count'],
                        'composite_signal': composite_signal,
                        'signals': stock_signals['signals'][:3]  # 只取前3个信号
                    })
            
            # 按信号数量排序
            active_signals.sort(key=lambda x: x['signal_count'], reverse=True)
            
            return {
                'total_stocks': len(ts_codes),
                'signals_summary': signals_summary,
                'active_signals': active_signals[:10],  # 只返回前10个
                'market_sentiment': self._calculate_market_sentiment(signals_summary),
                'update_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取市场概览失败: {e}")
            return {}
    
    def _save_indicators_to_influx(self, ts_code: str, data: pd.DataFrame):
        """保存技术指标到InfluxDB"""
        try:
            points = []
            
            # 只保存最近的数据点
            recent_data = data.tail(10)
            
            for _, row in recent_data.iterrows():
                # 保存主要指标
                indicators_to_save = {
                    'ma_5': row.get('ma_5'),
                    'ma_20': row.get('ma_20'),
                    'macd': row.get('macd'),
                    'rsi': row.get('rsi'),
                    'bb_upper': row.get('bb_upper'),
                    'bb_lower': row.get('bb_lower')
                }
                
                for indicator_name, value in indicators_to_save.items():
                    if pd.notna(value):
                        indicator = TechnicalIndicator(
                            ts_code=ts_code,
                            trade_date=row.get('trade_date', ''),
                            indicator_name=indicator_name,
                            value=float(value)
                        )
                        points.append(indicator.to_influx_point())
            
            if points:
                self.influx_manager.write_points(points)
                logger.debug(f"保存 {ts_code} 技术指标到InfluxDB: {len(points)} 个数据点")
                
        except Exception as e:
            logger.error(f"保存 {ts_code} 技术指标到InfluxDB失败: {e}")
    
    def _calculate_market_sentiment(self, signals_summary: Dict[str, int]) -> str:
        """计算市场情绪"""
        total = sum(signals_summary.values())
        if total == 0:
            return 'NEUTRAL'
        
        buy_ratio = signals_summary['BUY'] / total
        sell_ratio = signals_summary['SELL'] / total
        
        if buy_ratio > 0.6:
            return 'BULLISH'
        elif sell_ratio > 0.6:
            return 'BEARISH'
        elif buy_ratio > sell_ratio:
            return 'SLIGHTLY_BULLISH'
        elif sell_ratio > buy_ratio:
            return 'SLIGHTLY_BEARISH'
        else:
            return 'NEUTRAL'

# 全局指标服务实例
indicator_service = IndicatorService()

def get_indicator_service() -> IndicatorService:
    """获取指标服务实例"""
    return indicator_service
