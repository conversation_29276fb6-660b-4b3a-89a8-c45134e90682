{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each } from 'zrender/lib/core/util.js';\nimport Group from 'zrender/lib/graphic/Group.js';\nimport * as componentUtil from '../util/component.js';\nimport * as clazzUtil from '../util/clazz.js';\nimport * as modelUtil from '../util/model.js';\nimport { enterEmphasis, leaveEmphasis, getHighlightDigit, isHighDownDispatcher } from '../util/states.js';\nimport { createTask } from '../core/task.js';\nimport createRenderPlanner from '../chart/helper/createRenderPlanner.js';\nimport { traverseElements } from '../util/graphic.js';\nimport { error } from '../util/log.js';\nvar inner = modelUtil.makeInner();\nvar renderPlanner = createRenderPlanner();\nvar ChartView = /** @class */function () {\n  function ChartView() {\n    this.group = new Group();\n    this.uid = componentUtil.getUID('viewChart');\n    this.renderTask = createTask({\n      plan: renderTaskPlan,\n      reset: renderTaskReset\n    });\n    this.renderTask.context = {\n      view: this\n    };\n  }\n  ChartView.prototype.init = function (ecModel, api) {};\n  ChartView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error('render method must been implemented');\n    }\n  };\n  /**\r\n   * Highlight series or specified data item.\r\n   */\n  ChartView.prototype.highlight = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData(payload && payload.dataType);\n    if (!data) {\n      if (process.env.NODE_ENV !== 'production') {\n        error(\"Unknown dataType \" + payload.dataType);\n      }\n      return;\n    }\n    toggleHighlight(data, payload, 'emphasis');\n  };\n  /**\r\n   * Downplay series or specified data item.\r\n   */\n  ChartView.prototype.downplay = function (seriesModel, ecModel, api, payload) {\n    var data = seriesModel.getData(payload && payload.dataType);\n    if (!data) {\n      if (process.env.NODE_ENV !== 'production') {\n        error(\"Unknown dataType \" + payload.dataType);\n      }\n      return;\n    }\n    toggleHighlight(data, payload, 'normal');\n  };\n  /**\r\n   * Remove self.\r\n   */\n  ChartView.prototype.remove = function (ecModel, api) {\n    this.group.removeAll();\n  };\n  /**\r\n   * Dispose self.\r\n   */\n  ChartView.prototype.dispose = function (ecModel, api) {};\n  ChartView.prototype.updateView = function (seriesModel, ecModel, api, payload) {\n    this.render(seriesModel, ecModel, api, payload);\n  };\n  // FIXME never used?\n  ChartView.prototype.updateLayout = function (seriesModel, ecModel, api, payload) {\n    this.render(seriesModel, ecModel, api, payload);\n  };\n  // FIXME never used?\n  ChartView.prototype.updateVisual = function (seriesModel, ecModel, api, payload) {\n    this.render(seriesModel, ecModel, api, payload);\n  };\n  /**\r\n   * Traverse the new rendered elements.\r\n   *\r\n   * It will traverse the new added element in progressive rendering.\r\n   * And traverse all in normal rendering.\r\n   */\n  ChartView.prototype.eachRendered = function (cb) {\n    traverseElements(this.group, cb);\n  };\n  ChartView.markUpdateMethod = function (payload, methodName) {\n    inner(payload).updateMethod = methodName;\n  };\n  ChartView.protoInitialize = function () {\n    var proto = ChartView.prototype;\n    proto.type = 'chart';\n  }();\n  return ChartView;\n}();\n;\n/**\r\n * Set state of single element\r\n */\nfunction elSetState(el, state, highlightDigit) {\n  if (el && isHighDownDispatcher(el)) {\n    (state === 'emphasis' ? enterEmphasis : leaveEmphasis)(el, highlightDigit);\n  }\n}\nfunction toggleHighlight(data, payload, state) {\n  var dataIndex = modelUtil.queryDataIndex(data, payload);\n  var highlightDigit = payload && payload.highlightKey != null ? getHighlightDigit(payload.highlightKey) : null;\n  if (dataIndex != null) {\n    each(modelUtil.normalizeToArray(dataIndex), function (dataIdx) {\n      elSetState(data.getItemGraphicEl(dataIdx), state, highlightDigit);\n    });\n  } else {\n    data.eachItemGraphicEl(function (el) {\n      elSetState(el, state, highlightDigit);\n    });\n  }\n}\nclazzUtil.enableClassExtend(ChartView, ['dispose']);\nclazzUtil.enableClassManagement(ChartView);\nfunction renderTaskPlan(context) {\n  return renderPlanner(context.model);\n}\nfunction renderTaskReset(context) {\n  var seriesModel = context.model;\n  var ecModel = context.ecModel;\n  var api = context.api;\n  var payload = context.payload;\n  // FIXME: remove updateView updateVisual\n  var progressiveRender = seriesModel.pipelineContext.progressiveRender;\n  var view = context.view;\n  var updateMethod = payload && inner(payload).updateMethod;\n  var methodName = progressiveRender ? 'incrementalPrepareRender' : updateMethod && view[updateMethod] ? updateMethod\n  // `appendData` is also supported when data amount\n  // is less than progressive threshold.\n  : 'render';\n  if (methodName !== 'render') {\n    view[methodName](seriesModel, ecModel, api, payload);\n  }\n  return progressMethodMap[methodName];\n}\nvar progressMethodMap = {\n  incrementalPrepareRender: {\n    progress: function (params, context) {\n      context.view.incrementalRender(params, context.model, context.ecModel, context.api, context.payload);\n    }\n  },\n  render: {\n    // Put view.render in `progress` to support appendData. But in this case\n    // view.render should not be called in reset, otherwise it will be called\n    // twise. Use `forceFirstProgress` to make sure that view.render is called\n    // in any cases.\n    forceFirstProgress: true,\n    progress: function (params, context) {\n      context.view.render(context.model, context.ecModel, context.api, context.payload);\n    }\n  }\n};\nexport default ChartView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}