"""
WebSocket连接管理器
"""
from typing import List, Dict, Any
from fastapi import WebSocket
import json
import asyncio
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.client_data: Dict[WebSocket, Dict[str, Any]] = {}
        self.messages_sent: int = 0  # 发送消息计数
    
    async def connect(self, websocket: WebSocket, client_id: str = None):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
        
        # 存储客户端信息
        self.client_data[websocket] = {
            'client_id': client_id or f"client_{len(self.active_connections)}",
            'connected_at': datetime.now(),
            'subscriptions': set()  # 订阅的数据类型
        }
        
        logger.info(f"WebSocket客户端连接: {self.client_data[websocket]['client_id']}")
        
        # 发送连接成功消息
        await self.send_personal_message({
            'type': 'connection',
            'status': 'connected',
            'client_id': self.client_data[websocket]['client_id'],
            'timestamp': datetime.now().isoformat()
        }, websocket)
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            client_info = self.client_data.get(websocket, {})
            client_id = client_info.get('client_id', 'unknown')
            
            self.active_connections.remove(websocket)
            if websocket in self.client_data:
                del self.client_data[websocket]
            
            logger.info(f"WebSocket客户端断开: {client_id}")
    
    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(json.dumps(message, ensure_ascii=False))
            self.messages_sent += 1  # 增加消息计数
        except Exception as e:
            logger.error(f"发送个人消息失败: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: Dict[str, Any], message_type: str = None):
        """广播消息给所有连接的客户端"""
        if not self.active_connections:
            return
        
        # 添加时间戳
        message['timestamp'] = datetime.now().isoformat()
        
        disconnected = []
        for connection in self.active_connections:
            try:
                # 检查客户端是否订阅了此类型的消息
                client_info = self.client_data.get(connection, {})
                subscriptions = client_info.get('subscriptions', set())
                
                if message_type is None or message_type in subscriptions or len(subscriptions) == 0:
                    await connection.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected.append(connection)
        
        # 清理断开的连接
        for connection in disconnected:
            self.disconnect(connection)
    
    async def subscribe(self, websocket: WebSocket, data_types: List[str]):
        """订阅数据类型"""
        if websocket in self.client_data:
            self.client_data[websocket]['subscriptions'].update(data_types)
            
            await self.send_personal_message({
                'type': 'subscription',
                'status': 'success',
                'subscriptions': list(self.client_data[websocket]['subscriptions']),
                'message': f'已订阅: {", ".join(data_types)}'
            }, websocket)
            
            logger.info(f"客户端 {self.client_data[websocket]['client_id']} 订阅: {data_types}")
    
    async def unsubscribe(self, websocket: WebSocket, data_types: List[str]):
        """取消订阅数据类型"""
        if websocket in self.client_data:
            self.client_data[websocket]['subscriptions'].difference_update(data_types)
            
            await self.send_personal_message({
                'type': 'subscription',
                'status': 'success',
                'subscriptions': list(self.client_data[websocket]['subscriptions']),
                'message': f'已取消订阅: {", ".join(data_types)}'
            }, websocket)
            
            logger.info(f"客户端 {self.client_data[websocket]['client_id']} 取消订阅: {data_types}")
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        active_count = len(self.active_connections)
        return {
            'active_connections': active_count,  # 当前活跃连接数
            'total_connections': active_count,   # 总连接数（兼容性）
            'messages_sent': self.messages_sent,  # 发送消息数
            'clients': [
                {
                    'client_id': info['client_id'],
                    'connected_at': info['connected_at'].isoformat(),
                    'subscriptions': list(info['subscriptions'])
                }
                for info in self.client_data.values()
            ]
        }

# 全局连接管理器实例
manager = ConnectionManager()

def get_connection_manager() -> ConnectionManager:
    """获取连接管理器实例"""
    return manager
