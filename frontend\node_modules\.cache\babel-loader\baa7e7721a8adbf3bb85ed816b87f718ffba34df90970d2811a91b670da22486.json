{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* The method \"quantile\" was copied from \"d3.js\".\r\n* (See more details in the comment of the method below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar RADIAN_EPSILON = 1e-4;\n// Although chrome already enlarge this number to 100 for `toFixed`, but\n// we sill follow the spec for compatibility.\nvar ROUND_SUPPORTED_PRECISION_MAX = 20;\nfunction _trim(str) {\n  return str.replace(/^\\s+|\\s+$/g, '');\n}\n/**\r\n * Linear mapping a value from domain to range\r\n * @param  val\r\n * @param  domain Domain extent domain[0] can be bigger than domain[1]\r\n * @param  range  Range extent range[0] can be bigger than range[1]\r\n * @param  clamp Default to be false\r\n */\nexport function linearMap(val, domain, range, clamp) {\n  var d0 = domain[0];\n  var d1 = domain[1];\n  var r0 = range[0];\n  var r1 = range[1];\n  var subDomain = d1 - d0;\n  var subRange = r1 - r0;\n  if (subDomain === 0) {\n    return subRange === 0 ? r0 : (r0 + r1) / 2;\n  }\n  // Avoid accuracy problem in edge, such as\n  // 146.39 - 62.83 === 83.55999999999999.\n  // See echarts/test/ut/spec/util/number.js#linearMap#accuracyError\n  // It is a little verbose for efficiency considering this method\n  // is a hotspot.\n  if (clamp) {\n    if (subDomain > 0) {\n      if (val <= d0) {\n        return r0;\n      } else if (val >= d1) {\n        return r1;\n      }\n    } else {\n      if (val >= d0) {\n        return r0;\n      } else if (val <= d1) {\n        return r1;\n      }\n    }\n  } else {\n    if (val === d0) {\n      return r0;\n    }\n    if (val === d1) {\n      return r1;\n    }\n  }\n  return (val - d0) / subDomain * subRange + r0;\n}\n/**\r\n * Convert a percent string to absolute number.\r\n * Returns NaN if percent is not a valid string or number\r\n */\nexport function parsePercent(percent, all) {\n  switch (percent) {\n    case 'center':\n    case 'middle':\n      percent = '50%';\n      break;\n    case 'left':\n    case 'top':\n      percent = '0%';\n      break;\n    case 'right':\n    case 'bottom':\n      percent = '100%';\n      break;\n  }\n  if (zrUtil.isString(percent)) {\n    if (_trim(percent).match(/%$/)) {\n      return parseFloat(percent) / 100 * all;\n    }\n    return parseFloat(percent);\n  }\n  return percent == null ? NaN : +percent;\n}\nexport function round(x, precision, returnStr) {\n  if (precision == null) {\n    precision = 10;\n  }\n  // Avoid range error\n  precision = Math.min(Math.max(0, precision), ROUND_SUPPORTED_PRECISION_MAX);\n  // PENDING: 1.005.toFixed(2) is '1.00' rather than '1.01'\n  x = (+x).toFixed(precision);\n  return returnStr ? x : +x;\n}\n/**\r\n * Inplacd asc sort arr.\r\n * The input arr will be modified.\r\n */\nexport function asc(arr) {\n  arr.sort(function (a, b) {\n    return a - b;\n  });\n  return arr;\n}\n/**\r\n * Get precision.\r\n */\nexport function getPrecision(val) {\n  val = +val;\n  if (isNaN(val)) {\n    return 0;\n  }\n  // It is much faster than methods converting number to string as follows\n  //      let tmp = val.toString();\n  //      return tmp.length - 1 - tmp.indexOf('.');\n  // especially when precision is low\n  // Notice:\n  // (1) If the loop count is over about 20, it is slower than `getPrecisionSafe`.\n  //     (see https://jsbench.me/2vkpcekkvw/1)\n  // (2) If the val is less than for example 1e-15, the result may be incorrect.\n  //     (see test/ut/spec/util/number.test.ts `getPrecision_equal_random`)\n  if (val > 1e-14) {\n    var e = 1;\n    for (var i = 0; i < 15; i++, e *= 10) {\n      if (Math.round(val * e) / e === val) {\n        return i;\n      }\n    }\n  }\n  return getPrecisionSafe(val);\n}\n/**\r\n * Get precision with slow but safe method\r\n */\nexport function getPrecisionSafe(val) {\n  // toLowerCase for: '3.4E-12'\n  var str = val.toString().toLowerCase();\n  // Consider scientific notation: '3.4e-12' '3.4e+12'\n  var eIndex = str.indexOf('e');\n  var exp = eIndex > 0 ? +str.slice(eIndex + 1) : 0;\n  var significandPartLen = eIndex > 0 ? eIndex : str.length;\n  var dotIndex = str.indexOf('.');\n  var decimalPartLen = dotIndex < 0 ? 0 : significandPartLen - 1 - dotIndex;\n  return Math.max(0, decimalPartLen - exp);\n}\n/**\r\n * Minimal dicernible data precisioin according to a single pixel.\r\n */\nexport function getPixelPrecision(dataExtent, pixelExtent) {\n  var log = Math.log;\n  var LN10 = Math.LN10;\n  var dataQuantity = Math.floor(log(dataExtent[1] - dataExtent[0]) / LN10);\n  var sizeQuantity = Math.round(log(Math.abs(pixelExtent[1] - pixelExtent[0])) / LN10);\n  // toFixed() digits argument must be between 0 and 20.\n  var precision = Math.min(Math.max(-dataQuantity + sizeQuantity, 0), 20);\n  return !isFinite(precision) ? 20 : precision;\n}\n/**\r\n * Get a data of given precision, assuring the sum of percentages\r\n * in valueList is 1.\r\n * The largest remainder method is used.\r\n * https://en.wikipedia.org/wiki/Largest_remainder_method\r\n *\r\n * @param valueList a list of all data\r\n * @param idx index of the data to be processed in valueList\r\n * @param precision integer number showing digits of precision\r\n * @return percent ranging from 0 to 100\r\n */\nexport function getPercentWithPrecision(valueList, idx, precision) {\n  if (!valueList[idx]) {\n    return 0;\n  }\n  var seats = getPercentSeats(valueList, precision);\n  return seats[idx] || 0;\n}\n/**\r\n * Get a data of given precision, assuring the sum of percentages\r\n * in valueList is 1.\r\n * The largest remainder method is used.\r\n * https://en.wikipedia.org/wiki/Largest_remainder_method\r\n *\r\n * @param valueList a list of all data\r\n * @param precision integer number showing digits of precision\r\n * @return {Array<number>}\r\n */\nexport function getPercentSeats(valueList, precision) {\n  var sum = zrUtil.reduce(valueList, function (acc, val) {\n    return acc + (isNaN(val) ? 0 : val);\n  }, 0);\n  if (sum === 0) {\n    return [];\n  }\n  var digits = Math.pow(10, precision);\n  var votesPerQuota = zrUtil.map(valueList, function (val) {\n    return (isNaN(val) ? 0 : val) / sum * digits * 100;\n  });\n  var targetSeats = digits * 100;\n  var seats = zrUtil.map(votesPerQuota, function (votes) {\n    // Assign automatic seats.\n    return Math.floor(votes);\n  });\n  var currentSum = zrUtil.reduce(seats, function (acc, val) {\n    return acc + val;\n  }, 0);\n  var remainder = zrUtil.map(votesPerQuota, function (votes, idx) {\n    return votes - seats[idx];\n  });\n  // Has remainding votes.\n  while (currentSum < targetSeats) {\n    // Find next largest remainder.\n    var max = Number.NEGATIVE_INFINITY;\n    var maxId = null;\n    for (var i = 0, len = remainder.length; i < len; ++i) {\n      if (remainder[i] > max) {\n        max = remainder[i];\n        maxId = i;\n      }\n    }\n    // Add a vote to max remainder.\n    ++seats[maxId];\n    remainder[maxId] = 0;\n    ++currentSum;\n  }\n  return zrUtil.map(seats, function (seat) {\n    return seat / digits;\n  });\n}\n/**\r\n * Solve the floating point adding problem like 0.1 + 0.2 === 0.30000000000000004\r\n * See <http://0.30000000000000004.com/>\r\n */\nexport function addSafe(val0, val1) {\n  var maxPrecision = Math.max(getPrecision(val0), getPrecision(val1));\n  // const multiplier = Math.pow(10, maxPrecision);\n  // return (Math.round(val0 * multiplier) + Math.round(val1 * multiplier)) / multiplier;\n  var sum = val0 + val1;\n  // // PENDING: support more?\n  return maxPrecision > ROUND_SUPPORTED_PRECISION_MAX ? sum : round(sum, maxPrecision);\n}\n// Number.MAX_SAFE_INTEGER, ie do not support.\nexport var MAX_SAFE_INTEGER = 9007199254740991;\n/**\r\n * To 0 - 2 * PI, considering negative radian.\r\n */\nexport function remRadian(radian) {\n  var pi2 = Math.PI * 2;\n  return (radian % pi2 + pi2) % pi2;\n}\n/**\r\n * @param {type} radian\r\n * @return {boolean}\r\n */\nexport function isRadianAroundZero(val) {\n  return val > -RADIAN_EPSILON && val < RADIAN_EPSILON;\n}\n// eslint-disable-next-line\nvar TIME_REG = /^(?:(\\d{4})(?:[-\\/](\\d{1,2})(?:[-\\/](\\d{1,2})(?:[T ](\\d{1,2})(?::(\\d{1,2})(?::(\\d{1,2})(?:[.,](\\d+))?)?)?(Z|[\\+\\-]\\d\\d:?\\d\\d)?)?)?)?)?$/; // jshint ignore:line\n/**\r\n * @param value valid type: number | string | Date, otherwise return `new Date(NaN)`\r\n *   These values can be accepted:\r\n *   + An instance of Date, represent a time in its own time zone.\r\n *   + Or string in a subset of ISO 8601, only including:\r\n *     + only year, month, date: '2012-03', '2012-03-01', '2012-03-01 05', '2012-03-01 05:06',\r\n *     + separated with T or space: '2012-03-01T12:22:33.123', '2012-03-01 12:22:33.123',\r\n *     + time zone: '2012-03-01T12:22:33Z', '2012-03-01T12:22:33+8000', '2012-03-01T12:22:33-05:00',\r\n *     all of which will be treated as local time if time zone is not specified\r\n *     (see <https://momentjs.com/>).\r\n *   + Or other string format, including (all of which will be treated as local time):\r\n *     '2012', '2012-3-1', '2012/3/1', '2012/03/01',\r\n *     '2009/6/12 2:00', '2009/6/12 2:05:08', '2009/6/12 2:05:08.123'\r\n *   + a timestamp, which represent a time in UTC.\r\n * @return date Never be null/undefined. If invalid, return `new Date(NaN)`.\r\n */\nexport function parseDate(value) {\n  if (value instanceof Date) {\n    return value;\n  } else if (zrUtil.isString(value)) {\n    // Different browsers parse date in different way, so we parse it manually.\n    // Some other issues:\n    // new Date('1970-01-01') is UTC,\n    // new Date('1970/01/01') and new Date('1970-1-01') is local.\n    // See issue #3623\n    var match = TIME_REG.exec(value);\n    if (!match) {\n      // return Invalid Date.\n      return new Date(NaN);\n    }\n    // Use local time when no timezone offset is specified.\n    if (!match[8]) {\n      // match[n] can only be string or undefined.\n      // But take care of '12' + 1 => '121'.\n      return new Date(+match[1], +(match[2] || 1) - 1, +match[3] || 1, +match[4] || 0, +(match[5] || 0), +match[6] || 0, match[7] ? +match[7].substring(0, 3) : 0);\n    }\n    // Timezoneoffset of Javascript Date has considered DST (Daylight Saving Time,\n    // https://tc39.github.io/ecma262/#sec-daylight-saving-time-adjustment).\n    // For example, system timezone is set as \"Time Zone: America/Toronto\",\n    // then these code will get different result:\n    // `new Date(1478411999999).getTimezoneOffset();  // get 240`\n    // `new Date(1478412000000).getTimezoneOffset();  // get 300`\n    // So we should not use `new Date`, but use `Date.UTC`.\n    else {\n      var hour = +match[4] || 0;\n      if (match[8].toUpperCase() !== 'Z') {\n        hour -= +match[8].slice(0, 3);\n      }\n      return new Date(Date.UTC(+match[1], +(match[2] || 1) - 1, +match[3] || 1, hour, +(match[5] || 0), +match[6] || 0, match[7] ? +match[7].substring(0, 3) : 0));\n    }\n  } else if (value == null) {\n    return new Date(NaN);\n  }\n  return new Date(Math.round(value));\n}\n/**\r\n * Quantity of a number. e.g. 0.1, 1, 10, 100\r\n *\r\n * @param val\r\n * @return\r\n */\nexport function quantity(val) {\n  return Math.pow(10, quantityExponent(val));\n}\n/**\r\n * Exponent of the quantity of a number\r\n * e.g., 1234 equals to 1.234*10^3, so quantityExponent(1234) is 3\r\n *\r\n * @param val non-negative value\r\n * @return\r\n */\nexport function quantityExponent(val) {\n  if (val === 0) {\n    return 0;\n  }\n  var exp = Math.floor(Math.log(val) / Math.LN10);\n  /**\r\n   * exp is expected to be the rounded-down result of the base-10 log of val.\r\n   * But due to the precision loss with Math.log(val), we need to restore it\r\n   * using 10^exp to make sure we can get val back from exp. #11249\r\n   */\n  if (val / Math.pow(10, exp) >= 10) {\n    exp++;\n  }\n  return exp;\n}\n/**\r\n * find a “nice” number approximately equal to x. Round the number if round = true,\r\n * take ceiling if round = false. The primary observation is that the “nicest”\r\n * numbers in decimal are 1, 2, and 5, and all power-of-ten multiples of these numbers.\r\n *\r\n * See \"Nice Numbers for Graph Labels\" of Graphic Gems.\r\n *\r\n * @param  val Non-negative value.\r\n * @param  round\r\n * @return Niced number\r\n */\nexport function nice(val, round) {\n  var exponent = quantityExponent(val);\n  var exp10 = Math.pow(10, exponent);\n  var f = val / exp10; // 1 <= f < 10\n  var nf;\n  if (round) {\n    if (f < 1.5) {\n      nf = 1;\n    } else if (f < 2.5) {\n      nf = 2;\n    } else if (f < 4) {\n      nf = 3;\n    } else if (f < 7) {\n      nf = 5;\n    } else {\n      nf = 10;\n    }\n  } else {\n    if (f < 1) {\n      nf = 1;\n    } else if (f < 2) {\n      nf = 2;\n    } else if (f < 3) {\n      nf = 3;\n    } else if (f < 5) {\n      nf = 5;\n    } else {\n      nf = 10;\n    }\n  }\n  val = nf * exp10;\n  // Fix 3 * 0.1 === 0.30000000000000004 issue (see IEEE 754).\n  // 20 is the uppper bound of toFixed.\n  return exponent >= -20 ? +val.toFixed(exponent < 0 ? -exponent : 0) : val;\n}\n/**\r\n * This code was copied from \"d3.js\"\r\n * <https://github.com/d3/d3/blob/9cc9a875e636a1dcf36cc1e07bdf77e1ad6e2c74/src/arrays/quantile.js>.\r\n * See the license statement at the head of this file.\r\n * @param ascArr\r\n */\nexport function quantile(ascArr, p) {\n  var H = (ascArr.length - 1) * p + 1;\n  var h = Math.floor(H);\n  var v = +ascArr[h - 1];\n  var e = H - h;\n  return e ? v + e * (ascArr[h] - v) : v;\n}\n/**\r\n * Order intervals asc, and split them when overlap.\r\n * expect(numberUtil.reformIntervals([\r\n *     {interval: [18, 62], close: [1, 1]},\r\n *     {interval: [-Infinity, -70], close: [0, 0]},\r\n *     {interval: [-70, -26], close: [1, 1]},\r\n *     {interval: [-26, 18], close: [1, 1]},\r\n *     {interval: [62, 150], close: [1, 1]},\r\n *     {interval: [106, 150], close: [1, 1]},\r\n *     {interval: [150, Infinity], close: [0, 0]}\r\n * ])).toEqual([\r\n *     {interval: [-Infinity, -70], close: [0, 0]},\r\n *     {interval: [-70, -26], close: [1, 1]},\r\n *     {interval: [-26, 18], close: [0, 1]},\r\n *     {interval: [18, 62], close: [0, 1]},\r\n *     {interval: [62, 150], close: [0, 1]},\r\n *     {interval: [150, Infinity], close: [0, 0]}\r\n * ]);\r\n * @param list, where `close` mean open or close\r\n *        of the interval, and Infinity can be used.\r\n * @return The origin list, which has been reformed.\r\n */\nexport function reformIntervals(list) {\n  list.sort(function (a, b) {\n    return littleThan(a, b, 0) ? -1 : 1;\n  });\n  var curr = -Infinity;\n  var currClose = 1;\n  for (var i = 0; i < list.length;) {\n    var interval = list[i].interval;\n    var close_1 = list[i].close;\n    for (var lg = 0; lg < 2; lg++) {\n      if (interval[lg] <= curr) {\n        interval[lg] = curr;\n        close_1[lg] = !lg ? 1 - currClose : 1;\n      }\n      curr = interval[lg];\n      currClose = close_1[lg];\n    }\n    if (interval[0] === interval[1] && close_1[0] * close_1[1] !== 1) {\n      list.splice(i, 1);\n    } else {\n      i++;\n    }\n  }\n  return list;\n  function littleThan(a, b, lg) {\n    return a.interval[lg] < b.interval[lg] || a.interval[lg] === b.interval[lg] && (a.close[lg] - b.close[lg] === (!lg ? 1 : -1) || !lg && littleThan(a, b, 1));\n  }\n}\n/**\r\n * [Numeric is defined as]:\r\n *     `parseFloat(val) == val`\r\n * For example:\r\n * numeric:\r\n *     typeof number except NaN, '-123', '123', '2e3', '-2e3', '011', 'Infinity', Infinity,\r\n *     and they rounded by white-spaces or line-terminal like ' -123 \\n ' (see es spec)\r\n * not-numeric:\r\n *     null, undefined, [], {}, true, false, 'NaN', NaN, '123ab',\r\n *     empty string, string with only white-spaces or line-terminal (see es spec),\r\n *     0x12, '0x12', '-0x12', 012, '012', '-012',\r\n *     non-string, ...\r\n *\r\n * @test See full test cases in `test/ut/spec/util/number.js`.\r\n * @return Must be a typeof number. If not numeric, return NaN.\r\n */\nexport function numericToNumber(val) {\n  var valFloat = parseFloat(val);\n  return valFloat == val // eslint-disable-line eqeqeq\n  && (valFloat !== 0 || !zrUtil.isString(val) || val.indexOf('x') <= 0) // For case ' 0x0 '.\n  ? valFloat : NaN;\n}\n/**\r\n * Definition of \"numeric\": see `numericToNumber`.\r\n */\nexport function isNumeric(val) {\n  return !isNaN(numericToNumber(val));\n}\n/**\r\n * Use random base to prevent users hard code depending on\r\n * this auto generated marker id.\r\n * @return An positive integer.\r\n */\nexport function getRandomIdBase() {\n  return Math.round(Math.random() * 9);\n}\n/**\r\n * Get the greatest common divisor.\r\n *\r\n * @param {number} a one number\r\n * @param {number} b the other number\r\n */\nexport function getGreatestCommonDividor(a, b) {\n  if (b === 0) {\n    return a;\n  }\n  return getGreatestCommonDividor(b, a % b);\n}\n/**\r\n * Get the least common multiple.\r\n *\r\n * @param {number} a one number\r\n * @param {number} b the other number\r\n */\nexport function getLeastCommonMultiple(a, b) {\n  if (a == null) {\n    return b;\n  }\n  if (b == null) {\n    return a;\n  }\n  return a * b / getGreatestCommonDividor(a, b);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}