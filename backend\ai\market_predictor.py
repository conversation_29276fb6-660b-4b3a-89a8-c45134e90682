"""
市场预测模块
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score

logger = logging.getLogger(__name__)

@dataclass
class PredictionResult:
    """预测结果"""
    ts_code: str
    current_price: float
    predicted_price: float
    prediction_change: float
    confidence: float
    prediction_date: str
    features_importance: Dict[str, float]
    model_accuracy: float

@dataclass
class MarketTrend:
    """市场趋势"""
    trend_direction: str  # UP/DOWN/SIDEWAYS
    trend_strength: float  # 0-1
    support_level: float
    resistance_level: float
    key_factors: List[str]
    confidence: float

class MarketPredictor:
    """市场预测器"""
    
    def __init__(self):
        self.models = {}  # 存储训练好的模型
        self.scalers = {}  # 存储特征缩放器
        self.feature_names = [
            'ma_5', 'ma_20', 'ma_60',
            'macd', 'macd_signal', 'macd_histogram',
            'rsi', 'bb_upper', 'bb_middle', 'bb_lower',
            'volume_ratio', 'price_change_5d', 'price_change_20d'
        ]
    
    def predict_stock_price(self, ts_code: str, data: pd.DataFrame, days_ahead: int = 5) -> Optional[PredictionResult]:
        """预测股票价格"""
        
        try:
            if len(data) < 30:
                logger.warning(f"{ts_code} 数据不足，无法预测")
                return None
            
            # 准备特征数据
            features_df = self._prepare_features(data)
            
            if features_df.empty:
                return None
            
            # 训练或获取模型
            model, scaler, accuracy = self._get_or_train_model(ts_code, features_df)
            
            if model is None:
                return None
            
            # 预测
            latest_features = features_df.iloc[-1:][self.feature_names]
            latest_features_scaled = scaler.transform(latest_features)
            
            predicted_price = model.predict(latest_features_scaled)[0]
            current_price = data.iloc[-1]['close']
            
            prediction_change = (predicted_price - current_price) / current_price
            
            # 计算置信度
            confidence = self._calculate_confidence(model, features_df, accuracy)
            
            # 特征重要性
            feature_importance = dict(zip(self.feature_names, model.feature_importances_))
            
            return PredictionResult(
                ts_code=ts_code,
                current_price=current_price,
                predicted_price=predicted_price,
                prediction_change=prediction_change,
                confidence=confidence,
                prediction_date=(datetime.now() + timedelta(days=days_ahead)).strftime('%Y-%m-%d'),
                features_importance=feature_importance,
                model_accuracy=accuracy
            )
            
        except Exception as e:
            logger.error(f"预测 {ts_code} 价格失败: {e}")
            return None
    
    def analyze_market_trend(self, market_data: Dict[str, pd.DataFrame]) -> MarketTrend:
        """分析市场趋势"""
        
        try:
            # 计算市场指数（简化为平均价格变化）
            all_changes = []
            all_volumes = []
            
            for ts_code, data in market_data.items():
                if len(data) >= 20:
                    recent_change = data.tail(5)['pct_chg'].mean()
                    recent_volume = data.tail(5)['volume_ratio'].mean()
                    
                    all_changes.append(recent_change)
                    if pd.notna(recent_volume):
                        all_volumes.append(recent_volume)
            
            if not all_changes:
                return self._default_market_trend()
            
            avg_change = np.mean(all_changes)
            avg_volume = np.mean(all_volumes) if all_volumes else 1.0
            
            # 判断趋势方向
            if avg_change > 1:
                trend_direction = "UP"
                trend_strength = min(abs(avg_change) / 3, 1.0)
            elif avg_change < -1:
                trend_direction = "DOWN"
                trend_strength = min(abs(avg_change) / 3, 1.0)
            else:
                trend_direction = "SIDEWAYS"
                trend_strength = 0.3
            
            # 计算支撑阻力位（简化计算）
            all_prices = []
            for data in market_data.values():
                if len(data) >= 20:
                    all_prices.extend(data.tail(20)['close'].tolist())
            
            if all_prices:
                support_level = np.percentile(all_prices, 20)
                resistance_level = np.percentile(all_prices, 80)
            else:
                support_level = 0
                resistance_level = 0
            
            # 关键因素分析
            key_factors = []
            if avg_volume > 1.5:
                key_factors.append("成交量放大")
            if abs(avg_change) > 2:
                key_factors.append("价格波动加剧")
            if trend_direction == "UP":
                key_factors.append("多头趋势")
            elif trend_direction == "DOWN":
                key_factors.append("空头趋势")
            
            # 计算置信度
            confidence = min(trend_strength * 0.7 + (len(market_data) / 50) * 0.3, 1.0)
            
            return MarketTrend(
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                support_level=support_level,
                resistance_level=resistance_level,
                key_factors=key_factors,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"分析市场趋势失败: {e}")
            return self._default_market_trend()
    
    def predict_market_volatility(self, market_data: Dict[str, pd.DataFrame], days_ahead: int = 5) -> Dict[str, Any]:
        """预测市场波动率"""
        
        try:
            # 计算历史波动率
            all_returns = []
            
            for data in market_data.values():
                if len(data) >= 20:
                    returns = data['close'].pct_change().dropna()
                    all_returns.extend(returns.tolist())
            
            if not all_returns:
                return {'error': '数据不足'}
            
            returns_series = pd.Series(all_returns)
            
            # 历史波动率
            historical_vol = returns_series.std() * np.sqrt(252)
            
            # 简单的波动率预测（基于GARCH思想的简化版本）
            recent_vol = returns_series.tail(20).std() * np.sqrt(252)
            
            # 预测波动率（简化为历史波动率和近期波动率的加权平均）
            predicted_vol = 0.7 * historical_vol + 0.3 * recent_vol
            
            # 波动率变化
            vol_change = (predicted_vol - historical_vol) / historical_vol
            
            # 波动率等级
            if predicted_vol < 0.15:
                vol_level = "LOW"
            elif predicted_vol < 0.25:
                vol_level = "MEDIUM"
            else:
                vol_level = "HIGH"
            
            return {
                'historical_volatility': historical_vol,
                'predicted_volatility': predicted_vol,
                'volatility_change': vol_change,
                'volatility_level': vol_level,
                'prediction_period': f"{days_ahead} days",
                'confidence': 0.7
            }
            
        except Exception as e:
            logger.error(f"预测市场波动率失败: {e}")
            return {'error': str(e)}
    
    def _prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """准备特征数据"""
        
        try:
            # 计算技术指标（如果还没有）
            from strategy.indicators import get_technical_indicators
            indicators = get_technical_indicators()
            data_with_indicators = indicators.calculate_all_indicators(data)
            
            # 添加价格变化特征
            data_with_indicators['price_change_5d'] = data_with_indicators['close'].pct_change(5)
            data_with_indicators['price_change_20d'] = data_with_indicators['close'].pct_change(20)
            
            # 添加目标变量（未来5天的价格）
            data_with_indicators['target'] = data_with_indicators['close'].shift(-5)
            
            # 删除包含NaN的行
            features_df = data_with_indicators.dropna()
            
            return features_df
            
        except Exception as e:
            logger.error(f"准备特征数据失败: {e}")
            return pd.DataFrame()
    
    def _get_or_train_model(self, ts_code: str, features_df: pd.DataFrame) -> Tuple[Optional[Any], Optional[Any], float]:
        """获取或训练模型"""
        
        try:
            # 检查是否已有训练好的模型
            if ts_code in self.models:
                return self.models[ts_code], self.scalers[ts_code], 0.7
            
            # 准备训练数据
            if len(features_df) < 50:
                logger.warning(f"{ts_code} 训练数据不足")
                return None, None, 0
            
            # 特征和目标
            X = features_df[self.feature_names]
            y = features_df['target']
            
            # 分割训练和测试数据
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # 特征缩放
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # 训练随机森林模型
            model = RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10)
            model.fit(X_train_scaled, y_train)
            
            # 评估模型
            y_pred = model.predict(X_test_scaled)
            accuracy = r2_score(y_test, y_pred)
            
            # 存储模型
            self.models[ts_code] = model
            self.scalers[ts_code] = scaler
            
            logger.info(f"{ts_code} 模型训练完成，准确率: {accuracy:.3f}")
            
            return model, scaler, max(accuracy, 0)
            
        except Exception as e:
            logger.error(f"训练 {ts_code} 模型失败: {e}")
            return None, None, 0
    
    def _calculate_confidence(self, model: Any, features_df: pd.DataFrame, accuracy: float) -> float:
        """计算预测置信度"""
        
        try:
            # 基于模型准确率和数据质量计算置信度
            data_quality = min(len(features_df) / 100, 1.0)  # 数据量质量
            model_quality = max(accuracy, 0)  # 模型质量
            
            confidence = 0.6 * model_quality + 0.4 * data_quality
            
            return min(max(confidence, 0.1), 0.9)  # 限制在0.1-0.9之间
            
        except Exception:
            return 0.5
    
    def _default_market_trend(self) -> MarketTrend:
        """默认市场趋势"""
        return MarketTrend(
            trend_direction="SIDEWAYS",
            trend_strength=0.3,
            support_level=0,
            resistance_level=0,
            key_factors=["数据不足"],
            confidence=0.3
        )

# 全局预测器实例
market_predictor = MarketPredictor()

def get_market_predictor() -> MarketPredictor:
    """获取市场预测器实例"""
    return market_predictor
