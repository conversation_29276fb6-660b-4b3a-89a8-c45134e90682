{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport prepareSeriesDataSchema from '../../data/helper/createDimensions.js';\nimport { getDimensionTypeByAxis } from '../../data/helper/dimensionHelper.js';\nimport { getDataItemValue } from '../../util/model.js';\nimport CoordinateSystem from '../../core/CoordinateSystem.js';\nimport { getCoordSysInfoBySeries } from '../../model/referHelper.js';\nimport { createSourceFromSeriesDataOption } from '../../data/Source.js';\nimport { enableDataStack } from '../../data/helper/dataStackHelper.js';\nimport { makeSeriesEncodeForAxisCoordSys } from '../../data/helper/sourceHelper.js';\nimport { SOURCE_FORMAT_ORIGINAL } from '../../util/types.js';\nfunction getCoordSysDimDefs(seriesModel, coordSysInfo) {\n  var coordSysName = seriesModel.get('coordinateSystem');\n  var registeredCoordSys = CoordinateSystem.get(coordSysName);\n  var coordSysDimDefs;\n  if (coordSysInfo && coordSysInfo.coordSysDims) {\n    coordSysDimDefs = zrUtil.map(coordSysInfo.coordSysDims, function (dim) {\n      var dimInfo = {\n        name: dim\n      };\n      var axisModel = coordSysInfo.axisMap.get(dim);\n      if (axisModel) {\n        var axisType = axisModel.get('type');\n        dimInfo.type = getDimensionTypeByAxis(axisType);\n      }\n      return dimInfo;\n    });\n  }\n  if (!coordSysDimDefs) {\n    // Get dimensions from registered coordinate system\n    coordSysDimDefs = registeredCoordSys && (registeredCoordSys.getDimensionsInfo ? registeredCoordSys.getDimensionsInfo() : registeredCoordSys.dimensions.slice()) || ['x', 'y'];\n  }\n  return coordSysDimDefs;\n}\nfunction injectOrdinalMeta(dimInfoList, createInvertedIndices, coordSysInfo) {\n  var firstCategoryDimIndex;\n  var hasNameEncode;\n  coordSysInfo && zrUtil.each(dimInfoList, function (dimInfo, dimIndex) {\n    var coordDim = dimInfo.coordDim;\n    var categoryAxisModel = coordSysInfo.categoryAxisMap.get(coordDim);\n    if (categoryAxisModel) {\n      if (firstCategoryDimIndex == null) {\n        firstCategoryDimIndex = dimIndex;\n      }\n      dimInfo.ordinalMeta = categoryAxisModel.getOrdinalMeta();\n      if (createInvertedIndices) {\n        dimInfo.createInvertedIndices = true;\n      }\n    }\n    if (dimInfo.otherDims.itemName != null) {\n      hasNameEncode = true;\n    }\n  });\n  if (!hasNameEncode && firstCategoryDimIndex != null) {\n    dimInfoList[firstCategoryDimIndex].otherDims.itemName = 0;\n  }\n  return firstCategoryDimIndex;\n}\n/**\r\n * Caution: there are side effects to `sourceManager` in this method.\r\n * Should better only be called in `Series['getInitialData']`.\r\n */\nfunction createSeriesData(sourceRaw, seriesModel, opt) {\n  opt = opt || {};\n  var sourceManager = seriesModel.getSourceManager();\n  var source;\n  var isOriginalSource = false;\n  if (sourceRaw) {\n    isOriginalSource = true;\n    source = createSourceFromSeriesDataOption(sourceRaw);\n  } else {\n    source = sourceManager.getSource();\n    // Is series.data. not dataset.\n    isOriginalSource = source.sourceFormat === SOURCE_FORMAT_ORIGINAL;\n  }\n  var coordSysInfo = getCoordSysInfoBySeries(seriesModel);\n  var coordSysDimDefs = getCoordSysDimDefs(seriesModel, coordSysInfo);\n  var useEncodeDefaulter = opt.useEncodeDefaulter;\n  var encodeDefaulter = zrUtil.isFunction(useEncodeDefaulter) ? useEncodeDefaulter : useEncodeDefaulter ? zrUtil.curry(makeSeriesEncodeForAxisCoordSys, coordSysDimDefs, seriesModel) : null;\n  var createDimensionOptions = {\n    coordDimensions: coordSysDimDefs,\n    generateCoord: opt.generateCoord,\n    encodeDefine: seriesModel.getEncode(),\n    encodeDefaulter: encodeDefaulter,\n    canOmitUnusedDimensions: !isOriginalSource\n  };\n  var schema = prepareSeriesDataSchema(source, createDimensionOptions);\n  var firstCategoryDimIndex = injectOrdinalMeta(schema.dimensions, opt.createInvertedIndices, coordSysInfo);\n  var store = !isOriginalSource ? sourceManager.getSharedDataStore(schema) : null;\n  var stackCalculationInfo = enableDataStack(seriesModel, {\n    schema: schema,\n    store: store\n  });\n  var data = new SeriesData(schema, seriesModel);\n  data.setCalculationInfo(stackCalculationInfo);\n  var dimValueGetter = firstCategoryDimIndex != null && isNeedCompleteOrdinalData(source) ? function (itemOpt, dimName, dataIndex, dimIndex) {\n    // Use dataIndex as ordinal value in categoryAxis\n    return dimIndex === firstCategoryDimIndex ? dataIndex : this.defaultDimValueGetter(itemOpt, dimName, dataIndex, dimIndex);\n  } : null;\n  data.hasItemOption = false;\n  data.initData(\n  // Try to reuse the data store in sourceManager if using dataset.\n  isOriginalSource ? source : store, null, dimValueGetter);\n  return data;\n}\nfunction isNeedCompleteOrdinalData(source) {\n  if (source.sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n    var sampleItem = firstDataNotNull(source.data || []);\n    return !zrUtil.isArray(getDataItemValue(sampleItem));\n  }\n}\nfunction firstDataNotNull(arr) {\n  var i = 0;\n  while (i < arr.length && arr[i] == null) {\n    i++;\n  }\n  return arr[i];\n}\nexport default createSeriesData;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}