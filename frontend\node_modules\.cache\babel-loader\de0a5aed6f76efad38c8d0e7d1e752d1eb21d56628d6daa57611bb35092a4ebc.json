{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\nimport ComponentModel from '../../model/Component.js';\nimport { copyLayoutParams, mergeLayoutParam } from '../../util/layout.js';\n;\n;\n;\nexport function setKeyInfoToNewElOption(resultItem, newElOption) {\n  var existElOption = resultItem.existing;\n  // Set id and type after id assigned.\n  newElOption.id = resultItem.keyInfo.id;\n  !newElOption.type && existElOption && (newElOption.type = existElOption.type);\n  // Set parent id if not specified\n  if (newElOption.parentId == null) {\n    var newElParentOption = newElOption.parentOption;\n    if (newElParentOption) {\n      newElOption.parentId = newElParentOption.id;\n    } else if (existElOption) {\n      newElOption.parentId = existElOption.parentId;\n    }\n  }\n  // Clear\n  newElOption.parentOption = null;\n}\nfunction isSetLoc(obj, props) {\n  var isSet;\n  zrUtil.each(props, function (prop) {\n    obj[prop] != null && obj[prop] !== 'auto' && (isSet = true);\n  });\n  return isSet;\n}\nfunction mergeNewElOptionToExist(existList, index, newElOption) {\n  // Update existing options, for `getOption` feature.\n  var newElOptCopy = zrUtil.extend({}, newElOption);\n  var existElOption = existList[index];\n  var $action = newElOption.$action || 'merge';\n  if ($action === 'merge') {\n    if (existElOption) {\n      if (process.env.NODE_ENV !== 'production') {\n        var newType = newElOption.type;\n        zrUtil.assert(!newType || existElOption.type === newType, 'Please set $action: \"replace\" to change `type`');\n      }\n      // We can ensure that newElOptCopy and existElOption are not\n      // the same object, so `merge` will not change newElOptCopy.\n      zrUtil.merge(existElOption, newElOptCopy, true);\n      // Rigid body, use ignoreSize.\n      mergeLayoutParam(existElOption, newElOptCopy, {\n        ignoreSize: true\n      });\n      // Will be used in render.\n      copyLayoutParams(newElOption, existElOption);\n      // Copy transition info to new option so it can be used in the transition.\n      // DO IT AFTER merge\n      copyTransitionInfo(newElOption, existElOption);\n      copyTransitionInfo(newElOption, existElOption, 'shape');\n      copyTransitionInfo(newElOption, existElOption, 'style');\n      copyTransitionInfo(newElOption, existElOption, 'extra');\n      // Copy clipPath\n      newElOption.clipPath = existElOption.clipPath;\n    } else {\n      existList[index] = newElOptCopy;\n    }\n  } else if ($action === 'replace') {\n    existList[index] = newElOptCopy;\n  } else if ($action === 'remove') {\n    // null will be cleaned later.\n    existElOption && (existList[index] = null);\n  }\n}\nvar TRANSITION_PROPS_TO_COPY = ['transition', 'enterFrom', 'leaveTo'];\nvar ROOT_TRANSITION_PROPS_TO_COPY = TRANSITION_PROPS_TO_COPY.concat(['enterAnimation', 'updateAnimation', 'leaveAnimation']);\nfunction copyTransitionInfo(target, source, targetProp) {\n  if (targetProp) {\n    if (!target[targetProp] && source[targetProp]) {\n      // TODO avoid creating this empty object when there is no transition configuration.\n      target[targetProp] = {};\n    }\n    target = target[targetProp];\n    source = source[targetProp];\n  }\n  if (!target || !source) {\n    return;\n  }\n  var props = targetProp ? TRANSITION_PROPS_TO_COPY : ROOT_TRANSITION_PROPS_TO_COPY;\n  for (var i = 0; i < props.length; i++) {\n    var prop = props[i];\n    if (target[prop] == null && source[prop] != null) {\n      target[prop] = source[prop];\n    }\n  }\n}\nfunction setLayoutInfoToExist(existItem, newElOption) {\n  if (!existItem) {\n    return;\n  }\n  existItem.hv = newElOption.hv = [\n  // Rigid body, don't care about `width`.\n  isSetLoc(newElOption, ['left', 'right']),\n  // Rigid body, don't care about `height`.\n  isSetLoc(newElOption, ['top', 'bottom'])];\n  // Give default group size. Otherwise layout error may occur.\n  if (existItem.type === 'group') {\n    var existingGroupOpt = existItem;\n    var newGroupOpt = newElOption;\n    existingGroupOpt.width == null && (existingGroupOpt.width = newGroupOpt.width = 0);\n    existingGroupOpt.height == null && (existingGroupOpt.height = newGroupOpt.height = 0);\n  }\n}\nvar GraphicComponentModel = /** @class */function (_super) {\n  __extends(GraphicComponentModel, _super);\n  function GraphicComponentModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GraphicComponentModel.type;\n    _this.preventAutoZ = true;\n    return _this;\n  }\n  GraphicComponentModel.prototype.mergeOption = function (option, ecModel) {\n    // Prevent default merge to elements\n    var elements = this.option.elements;\n    this.option.elements = null;\n    _super.prototype.mergeOption.call(this, option, ecModel);\n    this.option.elements = elements;\n  };\n  GraphicComponentModel.prototype.optionUpdated = function (newOption, isInit) {\n    var thisOption = this.option;\n    var newList = (isInit ? thisOption : newOption).elements;\n    var existList = thisOption.elements = isInit ? [] : thisOption.elements;\n    var flattenedList = [];\n    this._flatten(newList, flattenedList, null);\n    var mappingResult = modelUtil.mappingToExists(existList, flattenedList, 'normalMerge');\n    // Clear elOptionsToUpdate\n    var elOptionsToUpdate = this._elOptionsToUpdate = [];\n    zrUtil.each(mappingResult, function (resultItem, index) {\n      var newElOption = resultItem.newOption;\n      if (process.env.NODE_ENV !== 'production') {\n        zrUtil.assert(zrUtil.isObject(newElOption) || resultItem.existing, 'Empty graphic option definition');\n      }\n      if (!newElOption) {\n        return;\n      }\n      elOptionsToUpdate.push(newElOption);\n      setKeyInfoToNewElOption(resultItem, newElOption);\n      mergeNewElOptionToExist(existList, index, newElOption);\n      setLayoutInfoToExist(existList[index], newElOption);\n    }, this);\n    // Clean\n    thisOption.elements = zrUtil.filter(existList, function (item) {\n      // $action should be volatile, otherwise option gotten from\n      // `getOption` will contain unexpected $action.\n      item && delete item.$action;\n      return item != null;\n    });\n  };\n  /**\r\n   * Convert\r\n   * [{\r\n   *  type: 'group',\r\n   *  id: 'xx',\r\n   *  children: [{type: 'circle'}, {type: 'polygon'}]\r\n   * }]\r\n   * to\r\n   * [\r\n   *  {type: 'group', id: 'xx'},\r\n   *  {type: 'circle', parentId: 'xx'},\r\n   *  {type: 'polygon', parentId: 'xx'}\r\n   * ]\r\n   */\n  GraphicComponentModel.prototype._flatten = function (optionList, result, parentOption) {\n    zrUtil.each(optionList, function (option) {\n      if (!option) {\n        return;\n      }\n      if (parentOption) {\n        option.parentOption = parentOption;\n      }\n      result.push(option);\n      var children = option.children;\n      // here we don't judge if option.type is `group`\n      // when new option doesn't provide `type`, it will cause that the children can't be updated.\n      if (children && children.length) {\n        this._flatten(children, result, option);\n      }\n      // Deleting for JSON output, and for not affecting group creation.\n      delete option.children;\n    }, this);\n  };\n  // FIXME\n  // Pass to view using payload? setOption has a payload?\n  GraphicComponentModel.prototype.useElOptionsToUpdate = function () {\n    var els = this._elOptionsToUpdate;\n    // Clear to avoid render duplicately when zooming.\n    this._elOptionsToUpdate = null;\n    return els;\n  };\n  GraphicComponentModel.type = 'graphic';\n  GraphicComponentModel.defaultOption = {\n    elements: []\n    // parentId: null\n  };\n  return GraphicComponentModel;\n}(ComponentModel);\nexport { GraphicComponentModel };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}