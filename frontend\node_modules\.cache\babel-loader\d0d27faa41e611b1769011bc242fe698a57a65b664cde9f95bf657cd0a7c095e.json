{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getLayoutRect } from '../../util/layout.js';\nvar paramsSet = [['left', 'right', 'width'], ['top', 'bottom', 'height']];\n/**\r\n * @param visualMapModel\r\n * @param api\r\n * @param itemSize always [short, long]\r\n * @return {string} 'left' or 'right' or 'top' or 'bottom'\r\n */\nexport function getItemAlign(visualMapModel, api, itemSize) {\n  var modelOption = visualMapModel.option;\n  var itemAlign = modelOption.align;\n  if (itemAlign != null && itemAlign !== 'auto') {\n    return itemAlign;\n  }\n  // Auto decision align.\n  var ecSize = {\n    width: api.getWidth(),\n    height: api.getHeight()\n  };\n  var realIndex = modelOption.orient === 'horizontal' ? 1 : 0;\n  var reals = paramsSet[realIndex];\n  var fakeValue = [0, null, 10];\n  var layoutInput = {};\n  for (var i = 0; i < 3; i++) {\n    layoutInput[paramsSet[1 - realIndex][i]] = fakeValue[i];\n    layoutInput[reals[i]] = i === 2 ? itemSize[0] : modelOption[reals[i]];\n  }\n  var rParam = [['x', 'width', 3], ['y', 'height', 0]][realIndex];\n  var rect = getLayoutRect(layoutInput, ecSize, modelOption.padding);\n  return reals[(rect.margin[rParam[2]] || 0) + rect[rParam[0]] + rect[rParam[1]] * 0.5 < ecSize[rParam[1]] * 0.5 ? 0 : 1];\n}\n/**\r\n * Prepare dataIndex for outside usage, where dataIndex means rawIndex, and\r\n * dataIndexInside means filtered index.\r\n */\n// TODO: TYPE more specified payload types.\nexport function makeHighDownBatch(batch, visualMapModel) {\n  zrUtil.each(batch || [], function (batchItem) {\n    if (batchItem.dataIndex != null) {\n      batchItem.dataIndexInside = batchItem.dataIndex;\n      batchItem.dataIndex = null;\n    }\n    batchItem.highlightKey = 'visualMap' + (visualMapModel ? visualMapModel.componentIndex : '');\n  });\n  return batch;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}