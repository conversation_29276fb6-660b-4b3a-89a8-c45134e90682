{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* Some formulas were originally copied from \"d3.js\" with some\r\n* modifications made for this project.\r\n* (See more details in the comment of the method \"step\" below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\nimport * as vec2 from 'zrender/lib/core/vector.js';\nvar scaleAndAdd = vec2.scaleAndAdd;\n// function adjacentNode(n, e) {\n//     return e.n1 === n ? e.n2 : e.n1;\n// }\nexport function forceLayout(inNodes, inEdges, opts) {\n  var nodes = inNodes;\n  var edges = inEdges;\n  var rect = opts.rect;\n  var width = rect.width;\n  var height = rect.height;\n  var center = [rect.x + width / 2, rect.y + height / 2];\n  // let scale = opts.scale || 1;\n  var gravity = opts.gravity == null ? 0.1 : opts.gravity;\n  // for (let i = 0; i < edges.length; i++) {\n  //     let e = edges[i];\n  //     let n1 = e.n1;\n  //     let n2 = e.n2;\n  //     n1.edges = n1.edges || [];\n  //     n2.edges = n2.edges || [];\n  //     n1.edges.push(e);\n  //     n2.edges.push(e);\n  // }\n  // Init position\n  for (var i = 0; i < nodes.length; i++) {\n    var n = nodes[i];\n    if (!n.p) {\n      n.p = vec2.create(width * (Math.random() - 0.5) + center[0], height * (Math.random() - 0.5) + center[1]);\n    }\n    n.pp = vec2.clone(n.p);\n    n.edges = null;\n  }\n  // Formula in 'Graph Drawing by Force-directed Placement'\n  // let k = scale * Math.sqrt(width * height / nodes.length);\n  // let k2 = k * k;\n  var initialFriction = opts.friction == null ? 0.6 : opts.friction;\n  var friction = initialFriction;\n  var beforeStepCallback;\n  var afterStepCallback;\n  return {\n    warmUp: function () {\n      friction = initialFriction * 0.8;\n    },\n    setFixed: function (idx) {\n      nodes[idx].fixed = true;\n    },\n    setUnfixed: function (idx) {\n      nodes[idx].fixed = false;\n    },\n    /**\r\n     * Before step hook\r\n     */\n    beforeStep: function (cb) {\n      beforeStepCallback = cb;\n    },\n    /**\r\n     * After step hook\r\n     */\n    afterStep: function (cb) {\n      afterStepCallback = cb;\n    },\n    /**\r\n     * Some formulas were originally copied from \"d3.js\"\r\n     * https://github.com/d3/d3/blob/b516d77fb8566b576088e73410437494717ada26/src/layout/force.js\r\n     * with some modifications made for this project.\r\n     * See the license statement at the head of this file.\r\n     */\n    step: function (cb) {\n      beforeStepCallback && beforeStepCallback(nodes, edges);\n      var v12 = [];\n      var nLen = nodes.length;\n      for (var i = 0; i < edges.length; i++) {\n        var e = edges[i];\n        if (e.ignoreForceLayout) {\n          continue;\n        }\n        var n1 = e.n1;\n        var n2 = e.n2;\n        vec2.sub(v12, n2.p, n1.p);\n        var d = vec2.len(v12) - e.d;\n        var w = n2.w / (n1.w + n2.w);\n        if (isNaN(w)) {\n          w = 0;\n        }\n        vec2.normalize(v12, v12);\n        !n1.fixed && scaleAndAdd(n1.p, n1.p, v12, w * d * friction);\n        !n2.fixed && scaleAndAdd(n2.p, n2.p, v12, -(1 - w) * d * friction);\n      }\n      // Gravity\n      for (var i = 0; i < nLen; i++) {\n        var n = nodes[i];\n        if (!n.fixed) {\n          vec2.sub(v12, center, n.p);\n          // let d = vec2.len(v12);\n          // vec2.scale(v12, v12, 1 / d);\n          // let gravityFactor = gravity;\n          scaleAndAdd(n.p, n.p, v12, gravity * friction);\n        }\n      }\n      // Repulsive\n      // PENDING\n      for (var i = 0; i < nLen; i++) {\n        var n1 = nodes[i];\n        for (var j = i + 1; j < nLen; j++) {\n          var n2 = nodes[j];\n          vec2.sub(v12, n2.p, n1.p);\n          var d = vec2.len(v12);\n          if (d === 0) {\n            // Random repulse\n            vec2.set(v12, Math.random() - 0.5, Math.random() - 0.5);\n            d = 1;\n          }\n          var repFact = (n1.rep + n2.rep) / d / d;\n          !n1.fixed && scaleAndAdd(n1.pp, n1.pp, v12, repFact);\n          !n2.fixed && scaleAndAdd(n2.pp, n2.pp, v12, -repFact);\n        }\n      }\n      var v = [];\n      for (var i = 0; i < nLen; i++) {\n        var n = nodes[i];\n        if (!n.fixed) {\n          vec2.sub(v, n.p, n.pp);\n          scaleAndAdd(n.p, n.p, v, friction);\n          vec2.copy(n.pp, n.p);\n        }\n      }\n      friction = friction * 0.992;\n      var finished = friction < 0.01;\n      afterStepCallback && afterStepCallback(nodes, edges, finished);\n      cb && cb(finished);\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}