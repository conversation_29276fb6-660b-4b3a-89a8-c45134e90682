{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\nimport { AxisModelCommonMixin } from '../axisModelCommonMixin.js';\nimport { mixin } from 'zrender/lib/core/util.js';\nvar SingleAxisModel = /** @class */function (_super) {\n  __extends(SingleAxisModel, _super);\n  function SingleAxisModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SingleAxisModel.type;\n    return _this;\n  }\n  SingleAxisModel.prototype.getCoordSysModel = function () {\n    return this;\n  };\n  SingleAxisModel.type = 'singleAxis';\n  SingleAxisModel.layoutMode = 'box';\n  SingleAxisModel.defaultOption = {\n    left: '5%',\n    top: '5%',\n    right: '5%',\n    bottom: '5%',\n    type: 'value',\n    position: 'bottom',\n    orient: 'horizontal',\n    axisLine: {\n      show: true,\n      lineStyle: {\n        width: 1,\n        type: 'solid'\n      }\n    },\n    // Single coordinate system and single axis is the,\n    // which is used as the parent tooltip model.\n    // same model, so we set default tooltip show as true.\n    tooltip: {\n      show: true\n    },\n    axisTick: {\n      show: true,\n      length: 6,\n      lineStyle: {\n        width: 1\n      }\n    },\n    axisLabel: {\n      show: true,\n      interval: 'auto'\n    },\n    splitLine: {\n      show: true,\n      lineStyle: {\n        type: 'dashed',\n        opacity: 0.2\n      }\n    }\n  };\n  return SingleAxisModel;\n}(ComponentModel);\nmixin(SingleAxisModel, AxisModelCommonMixin.prototype);\nexport default SingleAxisModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}