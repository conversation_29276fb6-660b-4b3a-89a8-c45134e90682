"""
数据库优化模块
"""
import asyncio
import asyncpg
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging
from contextlib import asynccontextmanager

from core.cache_manager import get_data_cache, monitor_performance

logger = logging.getLogger(__name__)

class DatabaseOptimizer:
    """数据库优化器"""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.connection_pool = None
        self.data_cache = get_data_cache()
    
    async def initialize(self):
        """初始化连接池"""
        try:
            self.connection_pool = await asyncpg.create_pool(
                self.connection_string,
                min_size=5,
                max_size=20,
                command_timeout=60,
                server_settings={
                    'jit': 'off',  # 关闭JIT以提高小查询性能
                    'shared_preload_libraries': 'pg_stat_statements'
                }
            )
            logger.info("数据库连接池初始化成功")
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            raise
    
    async def close(self):
        """关闭连接池"""
        if self.connection_pool:
            await self.connection_pool.close()
            logger.info("数据库连接池已关闭")
    
    @asynccontextmanager
    async def get_connection(self):
        """获取数据库连接"""
        if not self.connection_pool:
            raise RuntimeError("数据库连接池未初始化")
        
        async with self.connection_pool.acquire() as connection:
            yield connection
    
    @monitor_performance("db_query")
    async def execute_query(self, query: str, params: tuple = None, use_cache: bool = True, cache_ttl: int = 300) -> List[Dict]:
        """执行查询"""
        
        # 生成缓存键
        cache_key = f"query:{hash(query + str(params))}"
        
        # 尝试从缓存获取
        if use_cache:
            cached_result = self.data_cache.cache_manager.get(cache_key)
            if cached_result is not None:
                logger.debug(f"从缓存获取查询结果: {query[:50]}...")
                return cached_result
        
        # 执行查询
        async with self.get_connection() as conn:
            try:
                if params:
                    rows = await conn.fetch(query, *params)
                else:
                    rows = await conn.fetch(query)
                
                # 转换为字典列表
                result = [dict(row) for row in rows]
                
                # 缓存结果
                if use_cache and result:
                    self.data_cache.cache_manager.set(cache_key, result, cache_ttl)
                
                logger.debug(f"查询执行成功，返回 {len(result)} 行")
                return result
                
            except Exception as e:
                logger.error(f"查询执行失败: {e}")
                raise
    
    @monitor_performance("db_batch_insert")
    async def batch_insert(self, table: str, data: List[Dict], batch_size: int = 1000) -> int:
        """批量插入数据"""
        
        if not data:
            return 0
        
        total_inserted = 0
        
        async with self.get_connection() as conn:
            try:
                # 获取表结构
                columns = list(data[0].keys())
                placeholders = ', '.join([f'${i+1}' for i in range(len(columns))])
                
                insert_query = f"""
                    INSERT INTO {table} ({', '.join(columns)})
                    VALUES ({placeholders})
                    ON CONFLICT DO NOTHING
                """
                
                # 分批插入
                for i in range(0, len(data), batch_size):
                    batch = data[i:i + batch_size]
                    
                    # 准备批量数据
                    batch_values = []
                    for row in batch:
                        batch_values.append(tuple(row[col] for col in columns))
                    
                    # 执行批量插入
                    await conn.executemany(insert_query, batch_values)
                    total_inserted += len(batch)
                    
                    logger.debug(f"批量插入 {len(batch)} 行到 {table}")
                
                logger.info(f"批量插入完成，总计 {total_inserted} 行到 {table}")
                return total_inserted
                
            except Exception as e:
                logger.error(f"批量插入失败: {e}")
                raise
    
    async def optimize_table(self, table_name: str):
        """优化表"""
        
        async with self.get_connection() as conn:
            try:
                # 更新表统计信息
                await conn.execute(f"ANALYZE {table_name}")
                
                # 重建索引（如果需要）
                await conn.execute(f"REINDEX TABLE {table_name}")
                
                logger.info(f"表 {table_name} 优化完成")
                
            except Exception as e:
                logger.error(f"优化表 {table_name} 失败: {e}")
                raise
    
    async def get_table_stats(self, table_name: str) -> Dict[str, Any]:
        """获取表统计信息"""
        
        query = """
            SELECT 
                schemaname,
                tablename,
                attname,
                n_distinct,
                correlation
            FROM pg_stats 
            WHERE tablename = $1
        """
        
        stats = await self.execute_query(query, (table_name,), use_cache=False)
        
        # 获取表大小
        size_query = """
            SELECT 
                pg_size_pretty(pg_total_relation_size($1)) as total_size,
                pg_size_pretty(pg_relation_size($1)) as table_size,
                pg_size_pretty(pg_total_relation_size($1) - pg_relation_size($1)) as index_size
        """
        
        size_info = await self.execute_query(size_query, (table_name,), use_cache=False)
        
        return {
            'column_stats': stats,
            'size_info': size_info[0] if size_info else {},
            'last_analyzed': datetime.now().isoformat()
        }
    
    async def create_indexes(self, table_name: str, indexes: List[Dict[str, Any]]):
        """创建索引"""
        
        async with self.get_connection() as conn:
            for index_info in indexes:
                try:
                    index_name = index_info['name']
                    columns = index_info['columns']
                    index_type = index_info.get('type', 'btree')
                    unique = index_info.get('unique', False)
                    
                    # 检查索引是否已存在
                    check_query = """
                        SELECT 1 FROM pg_indexes 
                        WHERE tablename = $1 AND indexname = $2
                    """
                    
                    exists = await conn.fetchval(check_query, table_name, index_name)
                    
                    if not exists:
                        unique_clause = "UNIQUE" if unique else ""
                        create_query = f"""
                            CREATE {unique_clause} INDEX CONCURRENTLY {index_name}
                            ON {table_name} USING {index_type} ({', '.join(columns)})
                        """
                        
                        await conn.execute(create_query)
                        logger.info(f"创建索引 {index_name} 成功")
                    else:
                        logger.info(f"索引 {index_name} 已存在")
                        
                except Exception as e:
                    logger.error(f"创建索引 {index_name} 失败: {e}")
    
    async def cleanup_old_data(self, table_name: str, date_column: str, days_to_keep: int = 90):
        """清理旧数据"""
        
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        async with self.get_connection() as conn:
            try:
                # 删除旧数据
                delete_query = f"""
                    DELETE FROM {table_name} 
                    WHERE {date_column} < $1
                """
                
                result = await conn.execute(delete_query, cutoff_date)
                deleted_count = int(result.split()[-1])
                
                logger.info(f"从 {table_name} 删除了 {deleted_count} 行旧数据")
                
                # 清理后优化表
                await self.optimize_table(table_name)
                
                return deleted_count
                
            except Exception as e:
                logger.error(f"清理 {table_name} 旧数据失败: {e}")
                raise

class QueryOptimizer:
    """查询优化器"""
    
    def __init__(self, db_optimizer: DatabaseOptimizer):
        self.db_optimizer = db_optimizer
    
    def optimize_stock_data_query(self, ts_code: str, start_date: str, end_date: str) -> str:
        """优化股票数据查询"""
        
        # 使用索引友好的查询
        query = """
            SELECT 
                trade_date,
                open,
                high,
                low,
                close,
                pre_close,
                change,
                pct_chg,
                vol,
                amount
            FROM daily_data 
            WHERE ts_code = $1 
                AND trade_date >= $2 
                AND trade_date <= $3
            ORDER BY trade_date
        """
        
        return query
    
    def optimize_indicator_query(self, ts_code: str, indicator_type: str) -> str:
        """优化技术指标查询"""
        
        query = """
            SELECT 
                trade_date,
                indicator_value,
                indicator_params
            FROM technical_indicators 
            WHERE ts_code = $1 
                AND indicator_type = $2
                AND trade_date >= CURRENT_DATE - INTERVAL '60 days'
            ORDER BY trade_date DESC
            LIMIT 60
        """
        
        return query
    
    def get_recommended_indexes(self) -> List[Dict[str, Any]]:
        """获取推荐的索引"""
        
        return [
            {
                'table': 'daily_data',
                'name': 'idx_daily_data_ts_code_date',
                'columns': ['ts_code', 'trade_date'],
                'type': 'btree'
            },
            {
                'table': 'daily_data',
                'name': 'idx_daily_data_date',
                'columns': ['trade_date'],
                'type': 'btree'
            },
            {
                'table': 'technical_indicators',
                'name': 'idx_indicators_ts_code_type_date',
                'columns': ['ts_code', 'indicator_type', 'trade_date'],
                'type': 'btree'
            },
            {
                'table': 'risk_alerts',
                'name': 'idx_risk_alerts_date_level',
                'columns': ['alert_date', 'risk_level'],
                'type': 'btree'
            }
        ]

# 全局数据库优化器实例
db_optimizer = None

async def initialize_database_optimizer(connection_string: str):
    """初始化数据库优化器"""
    global db_optimizer
    db_optimizer = DatabaseOptimizer(connection_string)
    await db_optimizer.initialize()

def get_database_optimizer() -> Optional[DatabaseOptimizer]:
    """获取数据库优化器"""
    return db_optimizer
