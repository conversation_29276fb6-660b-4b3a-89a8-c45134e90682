{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as featureManager from './featureManager.js';\nimport ComponentModel from '../../model/Component.js';\nvar ToolboxModel = /** @class */function (_super) {\n  __extends(ToolboxModel, _super);\n  function ToolboxModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ToolboxModel.type;\n    return _this;\n  }\n  ToolboxModel.prototype.optionUpdated = function () {\n    _super.prototype.optionUpdated.apply(this, arguments);\n    var ecModel = this.ecModel;\n    zrUtil.each(this.option.feature, function (featureOpt, featureName) {\n      var Feature = featureManager.getFeature(featureName);\n      if (Feature) {\n        if (Feature.getDefaultOption) {\n          Feature.defaultOption = Feature.getDefaultOption(ecModel);\n        }\n        zrUtil.merge(featureOpt, Feature.defaultOption);\n      }\n    });\n  };\n  ToolboxModel.type = 'toolbox';\n  ToolboxModel.layoutMode = {\n    type: 'box',\n    ignoreSize: true\n  };\n  ToolboxModel.defaultOption = {\n    show: true,\n    z: 6,\n    // zlevel: 0,\n    orient: 'horizontal',\n    left: 'right',\n    top: 'top',\n    // right\n    // bottom\n    backgroundColor: 'transparent',\n    borderColor: '#ccc',\n    borderRadius: 0,\n    borderWidth: 0,\n    padding: 5,\n    itemSize: 15,\n    itemGap: 8,\n    showTitle: true,\n    iconStyle: {\n      borderColor: '#666',\n      color: 'none'\n    },\n    emphasis: {\n      iconStyle: {\n        borderColor: '#3E98C5'\n      }\n    },\n    // textStyle: {},\n    // feature\n    tooltip: {\n      show: false,\n      position: 'bottom'\n    }\n  };\n  return ToolboxModel;\n}(ComponentModel);\nexport default ToolboxModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}