{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Eventful from 'zrender/lib/core/Eventful.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport * as interactionMutex from './interactionMutex.js';\nimport { isString, bind, defaults, clone } from 'zrender/lib/core/util.js';\n;\nvar RoamController = /** @class */function (_super) {\n  __extends(RoamController, _super);\n  function RoamController(zr) {\n    var _this = _super.call(this) || this;\n    _this._zr = zr;\n    // Avoid two roamController bind the same handler\n    var mousedownHandler = bind(_this._mousedownHandler, _this);\n    var mousemoveHandler = bind(_this._mousemoveHandler, _this);\n    var mouseupHandler = bind(_this._mouseupHandler, _this);\n    var mousewheelHandler = bind(_this._mousewheelHandler, _this);\n    var pinchHandler = bind(_this._pinchHandler, _this);\n    /**\r\n     * Notice: only enable needed types. For example, if 'zoom'\r\n     * is not needed, 'zoom' should not be enabled, otherwise\r\n     * default mousewheel behaviour (scroll page) will be disabled.\r\n     */\n    _this.enable = function (controlType, opt) {\n      // Disable previous first\n      this.disable();\n      this._opt = defaults(clone(opt) || {}, {\n        zoomOnMouseWheel: true,\n        moveOnMouseMove: true,\n        // By default, wheel do not trigger move.\n        moveOnMouseWheel: false,\n        preventDefaultMouseMove: true\n      });\n      if (controlType == null) {\n        controlType = true;\n      }\n      if (controlType === true || controlType === 'move' || controlType === 'pan') {\n        zr.on('mousedown', mousedownHandler);\n        zr.on('mousemove', mousemoveHandler);\n        zr.on('mouseup', mouseupHandler);\n      }\n      if (controlType === true || controlType === 'scale' || controlType === 'zoom') {\n        zr.on('mousewheel', mousewheelHandler);\n        zr.on('pinch', pinchHandler);\n      }\n    };\n    _this.disable = function () {\n      zr.off('mousedown', mousedownHandler);\n      zr.off('mousemove', mousemoveHandler);\n      zr.off('mouseup', mouseupHandler);\n      zr.off('mousewheel', mousewheelHandler);\n      zr.off('pinch', pinchHandler);\n    };\n    return _this;\n  }\n  RoamController.prototype.isDragging = function () {\n    return this._dragging;\n  };\n  RoamController.prototype.isPinching = function () {\n    return this._pinching;\n  };\n  RoamController.prototype.setPointerChecker = function (pointerChecker) {\n    this.pointerChecker = pointerChecker;\n  };\n  RoamController.prototype.dispose = function () {\n    this.disable();\n  };\n  RoamController.prototype._mousedownHandler = function (e) {\n    if (eventTool.isMiddleOrRightButtonOnMouseUpDown(e)) {\n      return;\n    }\n    var el = e.target;\n    while (el) {\n      if (el.draggable) {\n        return;\n      }\n      // check if host is draggable\n      el = el.__hostTarget || el.parent;\n    }\n    var x = e.offsetX;\n    var y = e.offsetY;\n    // Only check on mosedown, but not mousemove.\n    // Mouse can be out of target when mouse moving.\n    if (this.pointerChecker && this.pointerChecker(e, x, y)) {\n      this._x = x;\n      this._y = y;\n      this._dragging = true;\n    }\n  };\n  RoamController.prototype._mousemoveHandler = function (e) {\n    if (!this._dragging || !isAvailableBehavior('moveOnMouseMove', e, this._opt) || e.gestureEvent === 'pinch' || interactionMutex.isTaken(this._zr, 'globalPan')) {\n      return;\n    }\n    var x = e.offsetX;\n    var y = e.offsetY;\n    var oldX = this._x;\n    var oldY = this._y;\n    var dx = x - oldX;\n    var dy = y - oldY;\n    this._x = x;\n    this._y = y;\n    this._opt.preventDefaultMouseMove && eventTool.stop(e.event);\n    trigger(this, 'pan', 'moveOnMouseMove', e, {\n      dx: dx,\n      dy: dy,\n      oldX: oldX,\n      oldY: oldY,\n      newX: x,\n      newY: y,\n      isAvailableBehavior: null\n    });\n  };\n  RoamController.prototype._mouseupHandler = function (e) {\n    if (!eventTool.isMiddleOrRightButtonOnMouseUpDown(e)) {\n      this._dragging = false;\n    }\n  };\n  RoamController.prototype._mousewheelHandler = function (e) {\n    var shouldZoom = isAvailableBehavior('zoomOnMouseWheel', e, this._opt);\n    var shouldMove = isAvailableBehavior('moveOnMouseWheel', e, this._opt);\n    var wheelDelta = e.wheelDelta;\n    var absWheelDeltaDelta = Math.abs(wheelDelta);\n    var originX = e.offsetX;\n    var originY = e.offsetY;\n    // wheelDelta maybe -0 in chrome mac.\n    if (wheelDelta === 0 || !shouldZoom && !shouldMove) {\n      return;\n    }\n    // If both `shouldZoom` and `shouldMove` is true, trigger\n    // their event both, and the final behavior is determined\n    // by event listener themselves.\n    if (shouldZoom) {\n      // Convenience:\n      // Mac and VM Windows on Mac: scroll up: zoom out.\n      // Windows: scroll up: zoom in.\n      // FIXME: Should do more test in different environment.\n      // wheelDelta is too complicated in difference nvironment\n      // (https://developer.mozilla.org/en-US/docs/Web/Events/mousewheel),\n      // although it has been normallized by zrender.\n      // wheelDelta of mouse wheel is bigger than touch pad.\n      var factor = absWheelDeltaDelta > 3 ? 1.4 : absWheelDeltaDelta > 1 ? 1.2 : 1.1;\n      var scale = wheelDelta > 0 ? factor : 1 / factor;\n      checkPointerAndTrigger(this, 'zoom', 'zoomOnMouseWheel', e, {\n        scale: scale,\n        originX: originX,\n        originY: originY,\n        isAvailableBehavior: null\n      });\n    }\n    if (shouldMove) {\n      // FIXME: Should do more test in different environment.\n      var absDelta = Math.abs(wheelDelta);\n      // wheelDelta of mouse wheel is bigger than touch pad.\n      var scrollDelta = (wheelDelta > 0 ? 1 : -1) * (absDelta > 3 ? 0.4 : absDelta > 1 ? 0.15 : 0.05);\n      checkPointerAndTrigger(this, 'scrollMove', 'moveOnMouseWheel', e, {\n        scrollDelta: scrollDelta,\n        originX: originX,\n        originY: originY,\n        isAvailableBehavior: null\n      });\n    }\n  };\n  RoamController.prototype._pinchHandler = function (e) {\n    if (interactionMutex.isTaken(this._zr, 'globalPan')) {\n      return;\n    }\n    var scale = e.pinchScale > 1 ? 1.1 : 1 / 1.1;\n    checkPointerAndTrigger(this, 'zoom', null, e, {\n      scale: scale,\n      originX: e.pinchX,\n      originY: e.pinchY,\n      isAvailableBehavior: null\n    });\n  };\n  return RoamController;\n}(Eventful);\nfunction checkPointerAndTrigger(controller, eventName, behaviorToCheck, e, contollerEvent) {\n  if (controller.pointerChecker && controller.pointerChecker(e, contollerEvent.originX, contollerEvent.originY)) {\n    // When mouse is out of roamController rect,\n    // default befavoius should not be be disabled, otherwise\n    // page sliding is disabled, contrary to expectation.\n    eventTool.stop(e.event);\n    trigger(controller, eventName, behaviorToCheck, e, contollerEvent);\n  }\n}\nfunction trigger(controller, eventName, behaviorToCheck, e, contollerEvent) {\n  // Also provide behavior checker for event listener, for some case that\n  // multiple components share one listener.\n  contollerEvent.isAvailableBehavior = bind(isAvailableBehavior, null, behaviorToCheck, e);\n  // TODO should not have type issue.\n  controller.trigger(eventName, contollerEvent);\n}\n// settings: {\n//     zoomOnMouseWheel\n//     moveOnMouseMove\n//     moveOnMouseWheel\n// }\n// The value can be: true / false / 'shift' / 'ctrl' / 'alt'.\nfunction isAvailableBehavior(behaviorToCheck, e, settings) {\n  var setting = settings[behaviorToCheck];\n  return !behaviorToCheck || setting && (!isString(setting) || e.event[setting + 'Key']);\n}\nexport default RoamController;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}