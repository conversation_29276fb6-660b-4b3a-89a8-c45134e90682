{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parsePercent } from '../util/number.js';\nimport { isDimensionStacked } from '../data/helper/dataStackHelper.js';\nfunction getSeriesStackId(seriesModel) {\n  return seriesModel.get('stack') || '__ec_stack_' + seriesModel.seriesIndex;\n}\nfunction getAxisKey(polar, axis) {\n  return axis.dim + polar.model.componentIndex;\n}\nfunction barLayoutPolar(seriesType, ecModel, api) {\n  var lastStackCoords = {};\n  var barWidthAndOffset = calRadialBar(zrUtil.filter(ecModel.getSeriesByType(seriesType), function (seriesModel) {\n    return !ecModel.isSeriesFiltered(seriesModel) && seriesModel.coordinateSystem && seriesModel.coordinateSystem.type === 'polar';\n  }));\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    // Check series coordinate, do layout for polar only\n    if (seriesModel.coordinateSystem.type !== 'polar') {\n      return;\n    }\n    var data = seriesModel.getData();\n    var polar = seriesModel.coordinateSystem;\n    var baseAxis = polar.getBaseAxis();\n    var axisKey = getAxisKey(polar, baseAxis);\n    var stackId = getSeriesStackId(seriesModel);\n    var columnLayoutInfo = barWidthAndOffset[axisKey][stackId];\n    var columnOffset = columnLayoutInfo.offset;\n    var columnWidth = columnLayoutInfo.width;\n    var valueAxis = polar.getOtherAxis(baseAxis);\n    var cx = seriesModel.coordinateSystem.cx;\n    var cy = seriesModel.coordinateSystem.cy;\n    var barMinHeight = seriesModel.get('barMinHeight') || 0;\n    var barMinAngle = seriesModel.get('barMinAngle') || 0;\n    lastStackCoords[stackId] = lastStackCoords[stackId] || [];\n    var valueDim = data.mapDimension(valueAxis.dim);\n    var baseDim = data.mapDimension(baseAxis.dim);\n    var stacked = isDimensionStacked(data, valueDim /* , baseDim */);\n    var clampLayout = baseAxis.dim !== 'radius' || !seriesModel.get('roundCap', true);\n    var valueAxisModel = valueAxis.model;\n    var startValue = valueAxisModel.get('startValue');\n    var valueAxisStart = valueAxis.dataToCoord(startValue || 0);\n    for (var idx = 0, len = data.count(); idx < len; idx++) {\n      var value = data.get(valueDim, idx);\n      var baseValue = data.get(baseDim, idx);\n      var sign = value >= 0 ? 'p' : 'n';\n      var baseCoord = valueAxisStart;\n      // Because of the barMinHeight, we can not use the value in\n      // stackResultDimension directly.\n      // Only ordinal axis can be stacked.\n      if (stacked) {\n        if (!lastStackCoords[stackId][baseValue]) {\n          lastStackCoords[stackId][baseValue] = {\n            p: valueAxisStart,\n            n: valueAxisStart // Negative stack\n          };\n        }\n        // Should also consider #4243\n        baseCoord = lastStackCoords[stackId][baseValue][sign];\n      }\n      var r0 = void 0;\n      var r = void 0;\n      var startAngle = void 0;\n      var endAngle = void 0;\n      // radial sector\n      if (valueAxis.dim === 'radius') {\n        var radiusSpan = valueAxis.dataToCoord(value) - valueAxisStart;\n        var angle = baseAxis.dataToCoord(baseValue);\n        if (Math.abs(radiusSpan) < barMinHeight) {\n          radiusSpan = (radiusSpan < 0 ? -1 : 1) * barMinHeight;\n        }\n        r0 = baseCoord;\n        r = baseCoord + radiusSpan;\n        startAngle = angle - columnOffset;\n        endAngle = startAngle - columnWidth;\n        stacked && (lastStackCoords[stackId][baseValue][sign] = r);\n      }\n      // tangential sector\n      else {\n        var angleSpan = valueAxis.dataToCoord(value, clampLayout) - valueAxisStart;\n        var radius = baseAxis.dataToCoord(baseValue);\n        if (Math.abs(angleSpan) < barMinAngle) {\n          angleSpan = (angleSpan < 0 ? -1 : 1) * barMinAngle;\n        }\n        r0 = radius + columnOffset;\n        r = r0 + columnWidth;\n        startAngle = baseCoord;\n        endAngle = baseCoord + angleSpan;\n        // if the previous stack is at the end of the ring,\n        // add a round to differentiate it from origin\n        // let extent = angleAxis.getExtent();\n        // let stackCoord = angle;\n        // if (stackCoord === extent[0] && value > 0) {\n        //     stackCoord = extent[1];\n        // }\n        // else if (stackCoord === extent[1] && value < 0) {\n        //     stackCoord = extent[0];\n        // }\n        stacked && (lastStackCoords[stackId][baseValue][sign] = endAngle);\n      }\n      data.setItemLayout(idx, {\n        cx: cx,\n        cy: cy,\n        r0: r0,\n        r: r,\n        // Consider that positive angle is anti-clockwise,\n        // while positive radian of sector is clockwise\n        startAngle: -startAngle * Math.PI / 180,\n        endAngle: -endAngle * Math.PI / 180,\n        /**\r\n         * Keep the same logic with bar in catesion: use end value to\r\n         * control direction. Notice that if clockwise is true (by\r\n         * default), the sector will always draw clockwisely, no matter\r\n         * whether endAngle is greater or less than startAngle.\r\n         */\n        clockwise: startAngle >= endAngle\n      });\n    }\n  });\n}\n/**\r\n * Calculate bar width and offset for radial bar charts\r\n */\nfunction calRadialBar(barSeries) {\n  // Columns info on each category axis. Key is polar name\n  var columnsMap = {};\n  zrUtil.each(barSeries, function (seriesModel, idx) {\n    var data = seriesModel.getData();\n    var polar = seriesModel.coordinateSystem;\n    var baseAxis = polar.getBaseAxis();\n    var axisKey = getAxisKey(polar, baseAxis);\n    var axisExtent = baseAxis.getExtent();\n    var bandWidth = baseAxis.type === 'category' ? baseAxis.getBandWidth() : Math.abs(axisExtent[1] - axisExtent[0]) / data.count();\n    var columnsOnAxis = columnsMap[axisKey] || {\n      bandWidth: bandWidth,\n      remainedWidth: bandWidth,\n      autoWidthCount: 0,\n      categoryGap: '20%',\n      gap: '30%',\n      stacks: {}\n    };\n    var stacks = columnsOnAxis.stacks;\n    columnsMap[axisKey] = columnsOnAxis;\n    var stackId = getSeriesStackId(seriesModel);\n    if (!stacks[stackId]) {\n      columnsOnAxis.autoWidthCount++;\n    }\n    stacks[stackId] = stacks[stackId] || {\n      width: 0,\n      maxWidth: 0\n    };\n    var barWidth = parsePercent(seriesModel.get('barWidth'), bandWidth);\n    var barMaxWidth = parsePercent(seriesModel.get('barMaxWidth'), bandWidth);\n    var barGap = seriesModel.get('barGap');\n    var barCategoryGap = seriesModel.get('barCategoryGap');\n    if (barWidth && !stacks[stackId].width) {\n      barWidth = Math.min(columnsOnAxis.remainedWidth, barWidth);\n      stacks[stackId].width = barWidth;\n      columnsOnAxis.remainedWidth -= barWidth;\n    }\n    barMaxWidth && (stacks[stackId].maxWidth = barMaxWidth);\n    barGap != null && (columnsOnAxis.gap = barGap);\n    barCategoryGap != null && (columnsOnAxis.categoryGap = barCategoryGap);\n  });\n  var result = {};\n  zrUtil.each(columnsMap, function (columnsOnAxis, coordSysName) {\n    result[coordSysName] = {};\n    var stacks = columnsOnAxis.stacks;\n    var bandWidth = columnsOnAxis.bandWidth;\n    var categoryGap = parsePercent(columnsOnAxis.categoryGap, bandWidth);\n    var barGapPercent = parsePercent(columnsOnAxis.gap, 1);\n    var remainedWidth = columnsOnAxis.remainedWidth;\n    var autoWidthCount = columnsOnAxis.autoWidthCount;\n    var autoWidth = (remainedWidth - categoryGap) / (autoWidthCount + (autoWidthCount - 1) * barGapPercent);\n    autoWidth = Math.max(autoWidth, 0);\n    // Find if any auto calculated bar exceeded maxBarWidth\n    zrUtil.each(stacks, function (column, stack) {\n      var maxWidth = column.maxWidth;\n      if (maxWidth && maxWidth < autoWidth) {\n        maxWidth = Math.min(maxWidth, remainedWidth);\n        if (column.width) {\n          maxWidth = Math.min(maxWidth, column.width);\n        }\n        remainedWidth -= maxWidth;\n        column.width = maxWidth;\n        autoWidthCount--;\n      }\n    });\n    // Recalculate width again\n    autoWidth = (remainedWidth - categoryGap) / (autoWidthCount + (autoWidthCount - 1) * barGapPercent);\n    autoWidth = Math.max(autoWidth, 0);\n    var widthSum = 0;\n    var lastColumn;\n    zrUtil.each(stacks, function (column, idx) {\n      if (!column.width) {\n        column.width = autoWidth;\n      }\n      lastColumn = column;\n      widthSum += column.width * (1 + barGapPercent);\n    });\n    if (lastColumn) {\n      widthSum -= lastColumn.width * barGapPercent;\n    }\n    var offset = -widthSum / 2;\n    zrUtil.each(stacks, function (column, stackId) {\n      result[coordSysName][stackId] = result[coordSysName][stackId] || {\n        offset: offset,\n        width: column.width\n      };\n      offset += column.width * (1 + barGapPercent);\n    });\n  });\n  return result;\n}\nexport default barLayoutPolar;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}