{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Path from 'zrender/lib/graphic/Path.js';\nvar PointerShape = /** @class */function () {\n  function PointerShape() {\n    this.angle = 0;\n    this.width = 10;\n    this.r = 10;\n    this.x = 0;\n    this.y = 0;\n  }\n  return PointerShape;\n}();\nvar PointerPath = /** @class */function (_super) {\n  __extends(PointerPath, _super);\n  function PointerPath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'pointer';\n    return _this;\n  }\n  PointerPath.prototype.getDefaultShape = function () {\n    return new PointerShape();\n  };\n  PointerPath.prototype.buildPath = function (ctx, shape) {\n    var mathCos = Math.cos;\n    var mathSin = Math.sin;\n    var r = shape.r;\n    var width = shape.width;\n    var angle = shape.angle;\n    var x = shape.x - mathCos(angle) * width * (width >= r / 3 ? 1 : 2);\n    var y = shape.y - mathSin(angle) * width * (width >= r / 3 ? 1 : 2);\n    angle = shape.angle - Math.PI / 2;\n    ctx.moveTo(x, y);\n    ctx.lineTo(shape.x + mathCos(angle) * width, shape.y + mathSin(angle) * width);\n    ctx.lineTo(shape.x + mathCos(shape.angle) * r, shape.y + mathSin(shape.angle) * r);\n    ctx.lineTo(shape.x - mathCos(angle) * width, shape.y - mathSin(angle) * width);\n    ctx.lineTo(x, y);\n  };\n  return PointerPath;\n}(Path);\nexport default PointerPath;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}