{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { hasOwn, assert, isString, retrieve2, retrieve3, defaults, each, indexOf } from 'zrender/lib/core/util.js';\nimport * as graphicUtil from '../../util/graphic.js';\nimport { setDefaultStateProxy, toggleHoverEmphasis } from '../../util/states.js';\nimport * as labelStyleHelper from '../../label/labelStyle.js';\nimport { getDefaultLabel } from '../helper/labelHelper.js';\nimport { getLayoutOnAxis } from '../../layout/barGrid.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport ChartView from '../../view/Chart.js';\nimport { createClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport prepareCartesian2d from '../../coord/cartesian/prepareCustom.js';\nimport prepareGeo from '../../coord/geo/prepareCustom.js';\nimport prepareSingleAxis from '../../coord/single/prepareCustom.js';\nimport preparePolar from '../../coord/polar/prepareCustom.js';\nimport prepareCalendar from '../../coord/calendar/prepareCustom.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { convertToEC4StyleForCustomSerise, isEC4CompatibleStyle, convertFromEC4CompatibleStyle, warnDeprecated } from '../../util/styleCompat.js';\nimport { throwError } from '../../util/log.js';\nimport { createOrUpdatePatternFromDecal } from '../../util/decal.js';\nimport { STYLE_VISUAL_TYPE, NON_STYLE_VISUAL_PROPS, customInnerStore } from './CustomSeries.js';\nimport { applyLeaveTransition, applyUpdateTransition } from '../../animation/customGraphicTransition.js';\nimport { applyKeyframeAnimation, stopPreviousKeyframeAnimationAndRestore } from '../../animation/customGraphicKeyframeAnimation.js';\nvar EMPHASIS = 'emphasis';\nvar NORMAL = 'normal';\nvar BLUR = 'blur';\nvar SELECT = 'select';\nvar STATES = [NORMAL, EMPHASIS, BLUR, SELECT];\nvar PATH_ITEM_STYLE = {\n  normal: ['itemStyle'],\n  emphasis: [EMPHASIS, 'itemStyle'],\n  blur: [BLUR, 'itemStyle'],\n  select: [SELECT, 'itemStyle']\n};\nvar PATH_LABEL = {\n  normal: ['label'],\n  emphasis: [EMPHASIS, 'label'],\n  blur: [BLUR, 'label'],\n  select: [SELECT, 'label']\n};\nvar DEFAULT_TRANSITION = ['x', 'y'];\n// Use prefix to avoid index to be the same as el.name,\n// which will cause weird update animation.\nvar GROUP_DIFF_PREFIX = 'e\\0\\0';\nvar attachedTxInfoTmp = {\n  normal: {},\n  emphasis: {},\n  blur: {},\n  select: {}\n};\n/**\r\n * To reduce total package size of each coordinate systems, the modules `prepareCustom`\r\n * of each coordinate systems are not required by each coordinate systems directly, but\r\n * required by the module `custom`.\r\n *\r\n * prepareInfoForCustomSeries {Function}: optional\r\n *     @return {Object} {coordSys: {...}, api: {\r\n *         coord: function (data, clamp) {}, // return point in global.\r\n *         size: function (dataSize, dataItem) {} // return size of each axis in coordSys.\r\n *     }}\r\n */\nvar prepareCustoms = {\n  cartesian2d: prepareCartesian2d,\n  geo: prepareGeo,\n  single: prepareSingleAxis,\n  polar: preparePolar,\n  calendar: prepareCalendar\n};\nfunction isPath(el) {\n  return el instanceof graphicUtil.Path;\n}\nfunction isDisplayable(el) {\n  return el instanceof Displayable;\n}\nfunction copyElement(sourceEl, targetEl) {\n  targetEl.copyTransform(sourceEl);\n  if (isDisplayable(targetEl) && isDisplayable(sourceEl)) {\n    targetEl.setStyle(sourceEl.style);\n    targetEl.z = sourceEl.z;\n    targetEl.z2 = sourceEl.z2;\n    targetEl.zlevel = sourceEl.zlevel;\n    targetEl.invisible = sourceEl.invisible;\n    targetEl.ignore = sourceEl.ignore;\n    if (isPath(targetEl) && isPath(sourceEl)) {\n      targetEl.setShape(sourceEl.shape);\n    }\n  }\n}\nvar CustomChartView = /** @class */function (_super) {\n  __extends(CustomChartView, _super);\n  function CustomChartView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CustomChartView.type;\n    return _this;\n  }\n  CustomChartView.prototype.render = function (customSeries, ecModel, api, payload) {\n    // Clear previously rendered progressive elements.\n    this._progressiveEls = null;\n    var oldData = this._data;\n    var data = customSeries.getData();\n    var group = this.group;\n    var renderItem = makeRenderItem(customSeries, data, ecModel, api);\n    if (!oldData) {\n      // Previous render is incremental render or first render.\n      // Needs remove the incremental rendered elements.\n      group.removeAll();\n    }\n    data.diff(oldData).add(function (newIdx) {\n      createOrUpdateItem(api, null, newIdx, renderItem(newIdx, payload), customSeries, group, data);\n    }).remove(function (oldIdx) {\n      var el = oldData.getItemGraphicEl(oldIdx);\n      el && applyLeaveTransition(el, customInnerStore(el).option, customSeries);\n    }).update(function (newIdx, oldIdx) {\n      var oldEl = oldData.getItemGraphicEl(oldIdx);\n      createOrUpdateItem(api, oldEl, newIdx, renderItem(newIdx, payload), customSeries, group, data);\n    }).execute();\n    // Do clipping\n    var clipPath = customSeries.get('clip', true) ? createClipPath(customSeries.coordinateSystem, false, customSeries) : null;\n    if (clipPath) {\n      group.setClipPath(clipPath);\n    } else {\n      group.removeClipPath();\n    }\n    this._data = data;\n  };\n  CustomChartView.prototype.incrementalPrepareRender = function (customSeries, ecModel, api) {\n    this.group.removeAll();\n    this._data = null;\n  };\n  CustomChartView.prototype.incrementalRender = function (params, customSeries, ecModel, api, payload) {\n    var data = customSeries.getData();\n    var renderItem = makeRenderItem(customSeries, data, ecModel, api);\n    var progressiveEls = this._progressiveEls = [];\n    function setIncrementalAndHoverLayer(el) {\n      if (!el.isGroup) {\n        el.incremental = true;\n        el.ensureState('emphasis').hoverLayer = true;\n      }\n    }\n    for (var idx = params.start; idx < params.end; idx++) {\n      var el = createOrUpdateItem(null, null, idx, renderItem(idx, payload), customSeries, this.group, data);\n      if (el) {\n        el.traverse(setIncrementalAndHoverLayer);\n        progressiveEls.push(el);\n      }\n    }\n  };\n  CustomChartView.prototype.eachRendered = function (cb) {\n    graphicUtil.traverseElements(this._progressiveEls || this.group, cb);\n  };\n  CustomChartView.prototype.filterForExposedEvent = function (eventType, query, targetEl, packedEvent) {\n    var elementName = query.element;\n    if (elementName == null || targetEl.name === elementName) {\n      return true;\n    }\n    // Enable to give a name on a group made by `renderItem`, and listen\n    // events that are triggered by its descendents.\n    while ((targetEl = targetEl.__hostTarget || targetEl.parent) && targetEl !== this.group) {\n      if (targetEl.name === elementName) {\n        return true;\n      }\n    }\n    return false;\n  };\n  CustomChartView.type = 'custom';\n  return CustomChartView;\n}(ChartView);\nexport default CustomChartView;\nfunction createEl(elOption) {\n  var graphicType = elOption.type;\n  var el;\n  // Those graphic elements are not shapes. They should not be\n  // overwritten by users, so do them first.\n  if (graphicType === 'path') {\n    var shape = elOption.shape;\n    // Using pathRect brings convenience to users sacle svg path.\n    var pathRect = shape.width != null && shape.height != null ? {\n      x: shape.x || 0,\n      y: shape.y || 0,\n      width: shape.width,\n      height: shape.height\n    } : null;\n    var pathData = getPathData(shape);\n    // Path is also used for icon, so layout 'center' by default.\n    el = graphicUtil.makePath(pathData, null, pathRect, shape.layout || 'center');\n    customInnerStore(el).customPathData = pathData;\n  } else if (graphicType === 'image') {\n    el = new graphicUtil.Image({});\n    customInnerStore(el).customImagePath = elOption.style.image;\n  } else if (graphicType === 'text') {\n    el = new graphicUtil.Text({});\n    // customInnerStore(el).customText = (elOption.style as TextStyleProps).text;\n  } else if (graphicType === 'group') {\n    el = new graphicUtil.Group();\n  } else if (graphicType === 'compoundPath') {\n    throw new Error('\"compoundPath\" is not supported yet.');\n  } else {\n    var Clz = graphicUtil.getShapeClass(graphicType);\n    if (!Clz) {\n      var errMsg = '';\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'graphic type \"' + graphicType + '\" can not be found.';\n      }\n      throwError(errMsg);\n    }\n    el = new Clz();\n  }\n  customInnerStore(el).customGraphicType = graphicType;\n  el.name = elOption.name;\n  // Compat ec4: the default z2 lift is 1. If changing the number,\n  // some cases probably be broken: hierarchy layout along z, like circle packing,\n  // where emphasis only intending to modify color/border rather than lift z2.\n  el.z2EmphasisLift = 1;\n  el.z2SelectLift = 1;\n  return el;\n}\nfunction updateElNormal(\n// Can be null/undefined\napi, el, dataIndex, elOption, attachedTxInfo, seriesModel, isInit) {\n  // Stop and restore before update any other attributes.\n  stopPreviousKeyframeAnimationAndRestore(el);\n  var txCfgOpt = attachedTxInfo && attachedTxInfo.normal.cfg;\n  if (txCfgOpt) {\n    // PENDING: whether use user object directly rather than clone?\n    // TODO:5.0 textConfig transition animation?\n    el.setTextConfig(txCfgOpt);\n  }\n  // Default transition ['x', 'y']\n  if (elOption && elOption.transition == null) {\n    elOption.transition = DEFAULT_TRANSITION;\n  }\n  // Do some normalization on style.\n  var styleOpt = elOption && elOption.style;\n  if (styleOpt) {\n    if (el.type === 'text') {\n      var textOptionStyle = styleOpt;\n      // Compatible with ec4: if `textFill` or `textStroke` exists use them.\n      hasOwn(textOptionStyle, 'textFill') && (textOptionStyle.fill = textOptionStyle.textFill);\n      hasOwn(textOptionStyle, 'textStroke') && (textOptionStyle.stroke = textOptionStyle.textStroke);\n    }\n    var decalPattern = void 0;\n    var decalObj = isPath(el) ? styleOpt.decal : null;\n    if (api && decalObj) {\n      decalObj.dirty = true;\n      decalPattern = createOrUpdatePatternFromDecal(decalObj, api);\n    }\n    // Always overwrite in case user specify this prop.\n    styleOpt.__decalPattern = decalPattern;\n  }\n  if (isDisplayable(el)) {\n    if (styleOpt) {\n      var decalPattern = styleOpt.__decalPattern;\n      if (decalPattern) {\n        styleOpt.decal = decalPattern;\n      }\n    }\n  }\n  applyUpdateTransition(el, elOption, seriesModel, {\n    dataIndex: dataIndex,\n    isInit: isInit,\n    clearStyle: true\n  });\n  applyKeyframeAnimation(el, elOption.keyframeAnimation, seriesModel);\n}\nfunction updateElOnState(state, el, elStateOpt, styleOpt, attachedTxInfo) {\n  var elDisplayable = el.isGroup ? null : el;\n  var txCfgOpt = attachedTxInfo && attachedTxInfo[state].cfg;\n  // PENDING:5.0 support customize scale change and transition animation?\n  if (elDisplayable) {\n    // By default support auto lift color when hover whether `emphasis` specified.\n    var stateObj = elDisplayable.ensureState(state);\n    if (styleOpt === false) {\n      var existingEmphasisState = elDisplayable.getState(state);\n      if (existingEmphasisState) {\n        existingEmphasisState.style = null;\n      }\n    } else {\n      // style is needed to enable default emphasis.\n      stateObj.style = styleOpt || null;\n    }\n    // If `elOption.styleEmphasis` or `elOption.emphasis.style` is `false`,\n    // remove hover style.\n    // If `elOption.textConfig` or `elOption.emphasis.textConfig` is null/undefined, it does not\n    // make sense. So for simplicity, we do not ditinguish `hasOwnProperty` and null/undefined.\n    if (txCfgOpt) {\n      stateObj.textConfig = txCfgOpt;\n    }\n    setDefaultStateProxy(elDisplayable);\n  }\n}\nfunction updateZ(el, elOption, seriesModel) {\n  // Group not support textContent and not support z yet.\n  if (el.isGroup) {\n    return;\n  }\n  var elDisplayable = el;\n  var currentZ = seriesModel.currentZ;\n  var currentZLevel = seriesModel.currentZLevel;\n  // Always erase.\n  elDisplayable.z = currentZ;\n  elDisplayable.zlevel = currentZLevel;\n  // z2 must not be null/undefined, otherwise sort error may occur.\n  var optZ2 = elOption.z2;\n  optZ2 != null && (elDisplayable.z2 = optZ2 || 0);\n  for (var i = 0; i < STATES.length; i++) {\n    updateZForEachState(elDisplayable, elOption, STATES[i]);\n  }\n}\nfunction updateZForEachState(elDisplayable, elOption, state) {\n  var isNormal = state === NORMAL;\n  var elStateOpt = isNormal ? elOption : retrieveStateOption(elOption, state);\n  var optZ2 = elStateOpt ? elStateOpt.z2 : null;\n  var stateObj;\n  if (optZ2 != null) {\n    // Do not `ensureState` until required.\n    stateObj = isNormal ? elDisplayable : elDisplayable.ensureState(state);\n    stateObj.z2 = optZ2 || 0;\n  }\n}\nfunction makeRenderItem(customSeries, data, ecModel, api) {\n  var renderItem = customSeries.get('renderItem');\n  var coordSys = customSeries.coordinateSystem;\n  var prepareResult = {};\n  if (coordSys) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(renderItem, 'series.render is required.');\n      assert(coordSys.prepareCustoms || prepareCustoms[coordSys.type], 'This coordSys does not support custom series.');\n    }\n    // `coordSys.prepareCustoms` is used for external coord sys like bmap.\n    prepareResult = coordSys.prepareCustoms ? coordSys.prepareCustoms(coordSys) : prepareCustoms[coordSys.type](coordSys);\n  }\n  var userAPI = defaults({\n    getWidth: api.getWidth,\n    getHeight: api.getHeight,\n    getZr: api.getZr,\n    getDevicePixelRatio: api.getDevicePixelRatio,\n    value: value,\n    style: style,\n    ordinalRawValue: ordinalRawValue,\n    styleEmphasis: styleEmphasis,\n    visual: visual,\n    barLayout: barLayout,\n    currentSeriesIndices: currentSeriesIndices,\n    font: font\n  }, prepareResult.api || {});\n  var userParams = {\n    // The life cycle of context: current round of rendering.\n    // The global life cycle is probably not necessary, because\n    // user can store global status by themselves.\n    context: {},\n    seriesId: customSeries.id,\n    seriesName: customSeries.name,\n    seriesIndex: customSeries.seriesIndex,\n    coordSys: prepareResult.coordSys,\n    dataInsideLength: data.count(),\n    encode: wrapEncodeDef(customSeries.getData())\n  };\n  // If someday intending to refactor them to a class, should consider do not\n  // break change: currently these attribute member are encapsulated in a closure\n  // so that do not need to force user to call these method with a scope.\n  // Do not support call `api` asynchronously without dataIndexInside input.\n  var currDataIndexInside;\n  var currItemModel;\n  var currItemStyleModels = {};\n  var currLabelModels = {};\n  var seriesItemStyleModels = {};\n  var seriesLabelModels = {};\n  for (var i = 0; i < STATES.length; i++) {\n    var stateName = STATES[i];\n    seriesItemStyleModels[stateName] = customSeries.getModel(PATH_ITEM_STYLE[stateName]);\n    seriesLabelModels[stateName] = customSeries.getModel(PATH_LABEL[stateName]);\n  }\n  function getItemModel(dataIndexInside) {\n    return dataIndexInside === currDataIndexInside ? currItemModel || (currItemModel = data.getItemModel(dataIndexInside)) : data.getItemModel(dataIndexInside);\n  }\n  function getItemStyleModel(dataIndexInside, state) {\n    return !data.hasItemOption ? seriesItemStyleModels[state] : dataIndexInside === currDataIndexInside ? currItemStyleModels[state] || (currItemStyleModels[state] = getItemModel(dataIndexInside).getModel(PATH_ITEM_STYLE[state])) : getItemModel(dataIndexInside).getModel(PATH_ITEM_STYLE[state]);\n  }\n  function getLabelModel(dataIndexInside, state) {\n    return !data.hasItemOption ? seriesLabelModels[state] : dataIndexInside === currDataIndexInside ? currLabelModels[state] || (currLabelModels[state] = getItemModel(dataIndexInside).getModel(PATH_LABEL[state])) : getItemModel(dataIndexInside).getModel(PATH_LABEL[state]);\n  }\n  return function (dataIndexInside, payload) {\n    currDataIndexInside = dataIndexInside;\n    currItemModel = null;\n    currItemStyleModels = {};\n    currLabelModels = {};\n    return renderItem && renderItem(defaults({\n      dataIndexInside: dataIndexInside,\n      dataIndex: data.getRawIndex(dataIndexInside),\n      // Can be used for optimization when zoom or roam.\n      actionType: payload ? payload.type : null\n    }, userParams), userAPI);\n  };\n  /**\r\n   * @public\r\n   * @param dim by default 0.\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function value(dim, dataIndexInside) {\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    return data.getStore().get(data.getDimensionIndex(dim || 0), dataIndexInside);\n  }\n  /**\r\n   * @public\r\n   * @param dim by default 0.\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function ordinalRawValue(dim, dataIndexInside) {\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    dim = dim || 0;\n    var dimInfo = data.getDimensionInfo(dim);\n    if (!dimInfo) {\n      var dimIndex = data.getDimensionIndex(dim);\n      return dimIndex >= 0 ? data.getStore().get(dimIndex, dataIndexInside) : undefined;\n    }\n    var val = data.get(dimInfo.name, dataIndexInside);\n    var ordinalMeta = dimInfo && dimInfo.ordinalMeta;\n    return ordinalMeta ? ordinalMeta.categories[val] : val;\n  }\n  /**\r\n   * @deprecated The original intention of `api.style` is enable to set itemStyle\r\n   * like other series. But it is not necessary and not easy to give a strict definition\r\n   * of what it returns. And since echarts5 it needs to be make compat work. So\r\n   * deprecates it since echarts5.\r\n   *\r\n   * By default, `visual` is applied to style (to support visualMap).\r\n   * `visual.color` is applied at `fill`. If user want apply visual.color on `stroke`,\r\n   * it can be implemented as:\r\n   * `api.style({stroke: api.visual('color'), fill: null})`;\r\n   *\r\n   * [Compat]: since ec5, RectText has been separated from its hosts el.\r\n   * so `api.style()` will only return the style from `itemStyle` but not handle `label`\r\n   * any more. But `series.label` config is never published in doc.\r\n   * We still compat it in `api.style()`. But not encourage to use it and will still not\r\n   * to pulish it to doc.\r\n   * @public\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function style(userProps, dataIndexInside) {\n    if (process.env.NODE_ENV !== 'production') {\n      warnDeprecated('api.style', 'Please write literal style directly instead.');\n    }\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    var style = data.getItemVisual(dataIndexInside, 'style');\n    var visualColor = style && style.fill;\n    var opacity = style && style.opacity;\n    var itemStyle = getItemStyleModel(dataIndexInside, NORMAL).getItemStyle();\n    visualColor != null && (itemStyle.fill = visualColor);\n    opacity != null && (itemStyle.opacity = opacity);\n    var opt = {\n      inheritColor: isString(visualColor) ? visualColor : '#000'\n    };\n    var labelModel = getLabelModel(dataIndexInside, NORMAL);\n    // Now that the feature of \"auto adjust text fill/stroke\" has been migrated to zrender\n    // since ec5, we should set `isAttached` as `false` here and make compat in\n    // `convertToEC4StyleForCustomSerise`.\n    var textStyle = labelStyleHelper.createTextStyle(labelModel, null, opt, false, true);\n    textStyle.text = labelModel.getShallow('show') ? retrieve2(customSeries.getFormattedLabel(dataIndexInside, NORMAL), getDefaultLabel(data, dataIndexInside)) : null;\n    var textConfig = labelStyleHelper.createTextConfig(labelModel, opt, false);\n    preFetchFromExtra(userProps, itemStyle);\n    itemStyle = convertToEC4StyleForCustomSerise(itemStyle, textStyle, textConfig);\n    userProps && applyUserPropsAfter(itemStyle, userProps);\n    itemStyle.legacy = true;\n    return itemStyle;\n  }\n  /**\r\n   * @deprecated The reason see `api.style()`\r\n   * @public\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function styleEmphasis(userProps, dataIndexInside) {\n    if (process.env.NODE_ENV !== 'production') {\n      warnDeprecated('api.styleEmphasis', 'Please write literal style directly instead.');\n    }\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    var itemStyle = getItemStyleModel(dataIndexInside, EMPHASIS).getItemStyle();\n    var labelModel = getLabelModel(dataIndexInside, EMPHASIS);\n    var textStyle = labelStyleHelper.createTextStyle(labelModel, null, null, true, true);\n    textStyle.text = labelModel.getShallow('show') ? retrieve3(customSeries.getFormattedLabel(dataIndexInside, EMPHASIS), customSeries.getFormattedLabel(dataIndexInside, NORMAL), getDefaultLabel(data, dataIndexInside)) : null;\n    var textConfig = labelStyleHelper.createTextConfig(labelModel, null, true);\n    preFetchFromExtra(userProps, itemStyle);\n    itemStyle = convertToEC4StyleForCustomSerise(itemStyle, textStyle, textConfig);\n    userProps && applyUserPropsAfter(itemStyle, userProps);\n    itemStyle.legacy = true;\n    return itemStyle;\n  }\n  function applyUserPropsAfter(itemStyle, extra) {\n    for (var key in extra) {\n      if (hasOwn(extra, key)) {\n        itemStyle[key] = extra[key];\n      }\n    }\n  }\n  function preFetchFromExtra(extra, itemStyle) {\n    // A trick to retrieve those props firstly, which are used to\n    // apply auto inside fill/stroke in `convertToEC4StyleForCustomSerise`.\n    // (It's not reasonable but only for a degree of compat)\n    if (extra) {\n      extra.textFill && (itemStyle.textFill = extra.textFill);\n      extra.textPosition && (itemStyle.textPosition = extra.textPosition);\n    }\n  }\n  /**\r\n   * @public\r\n   * @param dataIndexInside by default `currDataIndexInside`.\r\n   */\n  function visual(visualType, dataIndexInside) {\n    dataIndexInside == null && (dataIndexInside = currDataIndexInside);\n    if (hasOwn(STYLE_VISUAL_TYPE, visualType)) {\n      var style_1 = data.getItemVisual(dataIndexInside, 'style');\n      return style_1 ? style_1[STYLE_VISUAL_TYPE[visualType]] : null;\n    }\n    // Only support these visuals. Other visual might be inner tricky\n    // for performance (like `style`), do not expose to users.\n    if (hasOwn(NON_STYLE_VISUAL_PROPS, visualType)) {\n      return data.getItemVisual(dataIndexInside, visualType);\n    }\n  }\n  /**\r\n   * @public\r\n   * @return If not support, return undefined.\r\n   */\n  function barLayout(opt) {\n    if (coordSys.type === 'cartesian2d') {\n      var baseAxis = coordSys.getBaseAxis();\n      return getLayoutOnAxis(defaults({\n        axis: baseAxis\n      }, opt));\n    }\n  }\n  /**\r\n   * @public\r\n   */\n  function currentSeriesIndices() {\n    return ecModel.getCurrentSeriesIndices();\n  }\n  /**\r\n   * @public\r\n   * @return font string\r\n   */\n  function font(opt) {\n    return labelStyleHelper.getFont(opt, ecModel);\n  }\n}\nfunction wrapEncodeDef(data) {\n  var encodeDef = {};\n  each(data.dimensions, function (dimName) {\n    var dimInfo = data.getDimensionInfo(dimName);\n    if (!dimInfo.isExtraCoord) {\n      var coordDim = dimInfo.coordDim;\n      var dataDims = encodeDef[coordDim] = encodeDef[coordDim] || [];\n      dataDims[dimInfo.coordDimIndex] = data.getDimensionIndex(dimName);\n    }\n  });\n  return encodeDef;\n}\nfunction createOrUpdateItem(api, existsEl, dataIndex, elOption, seriesModel, group, data) {\n  // [Rule]\n  // If `renderItem` returns `null`/`undefined`/`false`, remove the previous el if existing.\n  //     (It seems that violate the \"merge\" principle, but most of users probably intuitively\n  //     regard \"return;\" as \"show nothing element whatever\", so make a exception to meet the\n  //     most cases.)\n  // The rule or \"merge\" see [STRATEGY_MERGE].\n  // If `elOption` is `null`/`undefined`/`false` (when `renderItem` returns nothing).\n  if (!elOption) {\n    group.remove(existsEl);\n    return;\n  }\n  var el = doCreateOrUpdateEl(api, existsEl, dataIndex, elOption, seriesModel, group);\n  el && data.setItemGraphicEl(dataIndex, el);\n  el && toggleHoverEmphasis(el, elOption.focus, elOption.blurScope, elOption.emphasisDisabled);\n  return el;\n}\nfunction doCreateOrUpdateEl(api, existsEl, dataIndex, elOption, seriesModel, group) {\n  if (process.env.NODE_ENV !== 'production') {\n    assert(elOption, 'should not have an null/undefined element setting');\n  }\n  var toBeReplacedIdx = -1;\n  var oldEl = existsEl;\n  if (existsEl && doesElNeedRecreate(existsEl, elOption, seriesModel)\n  // || (\n  //     // PENDING: even in one-to-one mapping case, if el is marked as morph,\n  //     // do not sure whether the el will be mapped to another el with different\n  //     // hierarchy in Group tree. So always recreate el rather than reuse the el.\n  //     morphHelper && morphHelper.isOneToOneFrom(el)\n  // )\n  ) {\n    // Should keep at the original index, otherwise \"merge by index\" will be incorrect.\n    toBeReplacedIdx = indexOf(group.childrenRef(), existsEl);\n    existsEl = null;\n  }\n  var isInit = !existsEl;\n  var el = existsEl;\n  if (!el) {\n    el = createEl(elOption);\n    if (oldEl) {\n      copyElement(oldEl, el);\n    }\n  } else {\n    // FIMXE:NEXT unified clearState?\n    // If in some case the performance issue arised, consider\n    // do not clearState but update cached normal state directly.\n    el.clearStates();\n  }\n  // Need to set morph: false explictly to disable automatically morphing.\n  if (elOption.morph === false) {\n    el.disableMorphing = true;\n  } else if (el.disableMorphing) {\n    el.disableMorphing = false;\n  }\n  attachedTxInfoTmp.normal.cfg = attachedTxInfoTmp.normal.conOpt = attachedTxInfoTmp.emphasis.cfg = attachedTxInfoTmp.emphasis.conOpt = attachedTxInfoTmp.blur.cfg = attachedTxInfoTmp.blur.conOpt = attachedTxInfoTmp.select.cfg = attachedTxInfoTmp.select.conOpt = null;\n  attachedTxInfoTmp.isLegacy = false;\n  doCreateOrUpdateAttachedTx(el, dataIndex, elOption, seriesModel, isInit, attachedTxInfoTmp);\n  doCreateOrUpdateClipPath(el, dataIndex, elOption, seriesModel, isInit);\n  updateElNormal(api, el, dataIndex, elOption, attachedTxInfoTmp, seriesModel, isInit);\n  // `elOption.info` enables user to mount some info on\n  // elements and use them in event handlers.\n  // Update them only when user specified, otherwise, remain.\n  hasOwn(elOption, 'info') && (customInnerStore(el).info = elOption.info);\n  for (var i = 0; i < STATES.length; i++) {\n    var stateName = STATES[i];\n    if (stateName !== NORMAL) {\n      var otherStateOpt = retrieveStateOption(elOption, stateName);\n      var otherStyleOpt = retrieveStyleOptionOnState(elOption, otherStateOpt, stateName);\n      updateElOnState(stateName, el, otherStateOpt, otherStyleOpt, attachedTxInfoTmp);\n    }\n  }\n  updateZ(el, elOption, seriesModel);\n  if (elOption.type === 'group') {\n    mergeChildren(api, el, dataIndex, elOption, seriesModel);\n  }\n  if (toBeReplacedIdx >= 0) {\n    group.replaceAt(el, toBeReplacedIdx);\n  } else {\n    group.add(el);\n  }\n  return el;\n}\n// `el` must not be null/undefined.\nfunction doesElNeedRecreate(el, elOption, seriesModel) {\n  var elInner = customInnerStore(el);\n  var elOptionType = elOption.type;\n  var elOptionShape = elOption.shape;\n  var elOptionStyle = elOption.style;\n  return (\n    // Always create new if universal transition is enabled.\n    // Because we do transition after render. It needs to know what old element is. Replacement will loose it.\n    seriesModel.isUniversalTransitionEnabled()\n    // If `elOptionType` is `null`, follow the merge principle.\n    || elOptionType != null && elOptionType !== elInner.customGraphicType || elOptionType === 'path' && hasOwnPathData(elOptionShape) && getPathData(elOptionShape) !== elInner.customPathData || elOptionType === 'image' && hasOwn(elOptionStyle, 'image') && elOptionStyle.image !== elInner.customImagePath\n    // // FIXME test and remove this restriction?\n    // || (elOptionType === 'text'\n    //     && hasOwn(elOptionStyle, 'text')\n    //     && (elOptionStyle as TextStyleProps).text !== elInner.customText\n    // )\n  );\n}\nfunction doCreateOrUpdateClipPath(el, dataIndex, elOption, seriesModel, isInit) {\n  // Based on the \"merge\" principle, if no clipPath provided,\n  // do nothing. The exists clip will be totally removed only if\n  // `el.clipPath` is `false`. Otherwise it will be merged/replaced.\n  var clipPathOpt = elOption.clipPath;\n  if (clipPathOpt === false) {\n    if (el && el.getClipPath()) {\n      el.removeClipPath();\n    }\n  } else if (clipPathOpt) {\n    var clipPath = el.getClipPath();\n    if (clipPath && doesElNeedRecreate(clipPath, clipPathOpt, seriesModel)) {\n      clipPath = null;\n    }\n    if (!clipPath) {\n      clipPath = createEl(clipPathOpt);\n      if (process.env.NODE_ENV !== 'production') {\n        assert(isPath(clipPath), 'Only any type of `path` can be used in `clipPath`, rather than ' + clipPath.type + '.');\n      }\n      el.setClipPath(clipPath);\n    }\n    updateElNormal(null, clipPath, dataIndex, clipPathOpt, null, seriesModel, isInit);\n  }\n  // If not define `clipPath` in option, do nothing unnecessary.\n}\nfunction doCreateOrUpdateAttachedTx(el, dataIndex, elOption, seriesModel, isInit, attachedTxInfo) {\n  // Group does not support textContent temporarily until necessary.\n  if (el.isGroup) {\n    return;\n  }\n  // Normal must be called before emphasis, for `isLegacy` detection.\n  processTxInfo(elOption, null, attachedTxInfo);\n  processTxInfo(elOption, EMPHASIS, attachedTxInfo);\n  // If `elOption.textConfig` or `elOption.textContent` is null/undefined, it does not make sense.\n  // So for simplicity, if \"elOption hasOwnProperty of them but be null/undefined\", we do not\n  // trade them as set to null to el.\n  // Especially:\n  // `elOption.textContent: false` means remove textContent.\n  // `elOption.textContent.emphasis.style: false` means remove the style from emphasis state.\n  var txConOptNormal = attachedTxInfo.normal.conOpt;\n  var txConOptEmphasis = attachedTxInfo.emphasis.conOpt;\n  var txConOptBlur = attachedTxInfo.blur.conOpt;\n  var txConOptSelect = attachedTxInfo.select.conOpt;\n  if (txConOptNormal != null || txConOptEmphasis != null || txConOptSelect != null || txConOptBlur != null) {\n    var textContent = el.getTextContent();\n    if (txConOptNormal === false) {\n      textContent && el.removeTextContent();\n    } else {\n      txConOptNormal = attachedTxInfo.normal.conOpt = txConOptNormal || {\n        type: 'text'\n      };\n      if (!textContent) {\n        textContent = createEl(txConOptNormal);\n        el.setTextContent(textContent);\n      } else {\n        // If in some case the performance issue arised, consider\n        // do not clearState but update cached normal state directly.\n        textContent.clearStates();\n      }\n      updateElNormal(null, textContent, dataIndex, txConOptNormal, null, seriesModel, isInit);\n      var txConStlOptNormal = txConOptNormal && txConOptNormal.style;\n      for (var i = 0; i < STATES.length; i++) {\n        var stateName = STATES[i];\n        if (stateName !== NORMAL) {\n          var txConOptOtherState = attachedTxInfo[stateName].conOpt;\n          updateElOnState(stateName, textContent, txConOptOtherState, retrieveStyleOptionOnState(txConOptNormal, txConOptOtherState, stateName), null);\n        }\n      }\n      txConStlOptNormal ? textContent.dirty() : textContent.markRedraw();\n    }\n  }\n}\nfunction processTxInfo(elOption, state, attachedTxInfo) {\n  var stateOpt = !state ? elOption : retrieveStateOption(elOption, state);\n  var styleOpt = !state ? elOption.style : retrieveStyleOptionOnState(elOption, stateOpt, EMPHASIS);\n  var elType = elOption.type;\n  var txCfg = stateOpt ? stateOpt.textConfig : null;\n  var txConOptNormal = elOption.textContent;\n  var txConOpt = !txConOptNormal ? null : !state ? txConOptNormal : retrieveStateOption(txConOptNormal, state);\n  if (styleOpt && (\n  // Because emphasis style has little info to detect legacy,\n  // if normal is legacy, emphasis is trade as legacy.\n  attachedTxInfo.isLegacy || isEC4CompatibleStyle(styleOpt, elType, !!txCfg, !!txConOpt))) {\n    attachedTxInfo.isLegacy = true;\n    var convertResult = convertFromEC4CompatibleStyle(styleOpt, elType, !state);\n    // Explicitly specified `textConfig` and `textContent` has higher priority than\n    // the ones generated by legacy style. Otherwise if users use them and `api.style`\n    // at the same time, they not both work and hardly to known why.\n    if (!txCfg && convertResult.textConfig) {\n      txCfg = convertResult.textConfig;\n    }\n    if (!txConOpt && convertResult.textContent) {\n      txConOpt = convertResult.textContent;\n    }\n  }\n  if (!state && txConOpt) {\n    var txConOptNormal_1 = txConOpt;\n    // `textContent: {type: 'text'}`, the \"type\" is easy to be missing. So we tolerate it.\n    !txConOptNormal_1.type && (txConOptNormal_1.type = 'text');\n    if (process.env.NODE_ENV !== 'production') {\n      // Do not tolerate incorrcet type for forward compat.\n      assert(txConOptNormal_1.type === 'text', 'textContent.type must be \"text\"');\n    }\n  }\n  var info = !state ? attachedTxInfo.normal : attachedTxInfo[state];\n  info.cfg = txCfg;\n  info.conOpt = txConOpt;\n}\nfunction retrieveStateOption(elOption, state) {\n  return !state ? elOption : elOption ? elOption[state] : null;\n}\nfunction retrieveStyleOptionOnState(stateOptionNormal, stateOption, state) {\n  var style = stateOption && stateOption.style;\n  if (style == null && state === EMPHASIS && stateOptionNormal) {\n    style = stateOptionNormal.styleEmphasis;\n  }\n  return style;\n}\n// Usage:\n// (1) By default, `elOption.$mergeChildren` is `'byIndex'`, which indicates\n//     that the existing children will not be removed, and enables the feature\n//     that update some of the props of some of the children simply by construct\n//     the returned children of `renderItem` like:\n//     `var children = group.children = []; children[3] = {opacity: 0.5};`\n// (2) If `elOption.$mergeChildren` is `'byName'`, add/update/remove children\n//     by child.name. But that might be lower performance.\n// (3) If `elOption.$mergeChildren` is `false`, the existing children will be\n//     replaced totally.\n// (4) If `!elOption.children`, following the \"merge\" principle, nothing will\n//     happen.\n// (5) If `elOption.$mergeChildren` is not `false` neither `'byName'` and the\n//     `el` is a group, and if any of the new child is null, it means to remove\n//     the element at the same index, if exists. On the other hand, if the new\n//     child is and empty object `{}`, it means to keep the element not changed.\n//\n// For implementation simpleness, do not provide a direct way to remove single\n// child (otherwise the total indices of the children array have to be modified).\n// User can remove a single child by setting its `ignore` to `true`.\nfunction mergeChildren(api, el, dataIndex, elOption, seriesModel) {\n  var newChildren = elOption.children;\n  var newLen = newChildren ? newChildren.length : 0;\n  var mergeChildren = elOption.$mergeChildren;\n  // `diffChildrenByName` has been deprecated.\n  var byName = mergeChildren === 'byName' || elOption.diffChildrenByName;\n  var notMerge = mergeChildren === false;\n  // For better performance on roam update, only enter if necessary.\n  if (!newLen && !byName && !notMerge) {\n    return;\n  }\n  if (byName) {\n    diffGroupChildren({\n      api: api,\n      oldChildren: el.children() || [],\n      newChildren: newChildren || [],\n      dataIndex: dataIndex,\n      seriesModel: seriesModel,\n      group: el\n    });\n    return;\n  }\n  notMerge && el.removeAll();\n  // Mapping children of a group simply by index, which\n  // might be better performance.\n  var index = 0;\n  for (; index < newLen; index++) {\n    var newChild = newChildren[index];\n    var oldChild = el.childAt(index);\n    if (newChild) {\n      if (newChild.ignore == null) {\n        // The old child is set to be ignored if null (see comments\n        // below). So we need to set ignore to be false back.\n        newChild.ignore = false;\n      }\n      doCreateOrUpdateEl(api, oldChild, dataIndex, newChild, seriesModel, el);\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(oldChild, 'renderItem should not return a group containing elements' + ' as null/undefined/{} if they do not exist before.');\n      }\n      // If the new element option is null, it means to remove the old\n      // element. But we cannot really remove the element from the group\n      // directly, because the element order may not be stable when this\n      // element is added back. So we set the element to be ignored.\n      oldChild.ignore = true;\n    }\n  }\n  for (var i = el.childCount() - 1; i >= index; i--) {\n    var child = el.childAt(i);\n    removeChildFromGroup(el, child, seriesModel);\n  }\n}\nfunction removeChildFromGroup(group, child, seriesModel) {\n  // Do not support leave elements that are not mentioned in the latest\n  // `renderItem` return. Otherwise users may not have a clear and simple\n  // concept that how to control all of the elements.\n  child && applyLeaveTransition(child, customInnerStore(group).option, seriesModel);\n}\nfunction diffGroupChildren(context) {\n  new DataDiffer(context.oldChildren, context.newChildren, getKey, getKey, context).add(processAddUpdate).update(processAddUpdate).remove(processRemove).execute();\n}\nfunction getKey(item, idx) {\n  var name = item && item.name;\n  return name != null ? name : GROUP_DIFF_PREFIX + idx;\n}\nfunction processAddUpdate(newIndex, oldIndex) {\n  var context = this.context;\n  var childOption = newIndex != null ? context.newChildren[newIndex] : null;\n  var child = oldIndex != null ? context.oldChildren[oldIndex] : null;\n  doCreateOrUpdateEl(context.api, child, context.dataIndex, childOption, context.seriesModel, context.group);\n}\nfunction processRemove(oldIndex) {\n  var context = this.context;\n  var child = context.oldChildren[oldIndex];\n  child && applyLeaveTransition(child, customInnerStore(child).option, context.seriesModel);\n}\n/**\r\n * @return SVG Path data.\r\n */\nfunction getPathData(shape) {\n  // \"d\" follows the SVG convention.\n  return shape && (shape.pathData || shape.d);\n}\nfunction hasOwnPathData(shape) {\n  return shape && (hasOwn(shape, 'pathData') || hasOwn(shape, 'd'));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}