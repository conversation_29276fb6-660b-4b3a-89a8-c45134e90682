{"ast": null, "code": "var Param = function () {\n  function Param(target, e) {\n    this.target = target;\n    this.topTarget = e && e.topTarget;\n  }\n  return Param;\n}();\nvar Draggable = function () {\n  function Draggable(handler) {\n    this.handler = handler;\n    handler.on('mousedown', this._dragStart, this);\n    handler.on('mousemove', this._drag, this);\n    handler.on('mouseup', this._dragEnd, this);\n  }\n  Draggable.prototype._dragStart = function (e) {\n    var draggingTarget = e.target;\n    while (draggingTarget && !draggingTarget.draggable) {\n      draggingTarget = draggingTarget.parent || draggingTarget.__hostTarget;\n    }\n    if (draggingTarget) {\n      this._draggingTarget = draggingTarget;\n      draggingTarget.dragging = true;\n      this._x = e.offsetX;\n      this._y = e.offsetY;\n      this.handler.dispatchToElement(new Param(draggingTarget, e), 'dragstart', e.event);\n    }\n  };\n  Draggable.prototype._drag = function (e) {\n    var draggingTarget = this._draggingTarget;\n    if (draggingTarget) {\n      var x = e.offsetX;\n      var y = e.offsetY;\n      var dx = x - this._x;\n      var dy = y - this._y;\n      this._x = x;\n      this._y = y;\n      draggingTarget.drift(dx, dy, e);\n      this.handler.dispatchToElement(new Param(draggingTarget, e), 'drag', e.event);\n      var dropTarget = this.handler.findHover(x, y, draggingTarget).target;\n      var lastDropTarget = this._dropTarget;\n      this._dropTarget = dropTarget;\n      if (draggingTarget !== dropTarget) {\n        if (lastDropTarget && dropTarget !== lastDropTarget) {\n          this.handler.dispatchToElement(new Param(lastDropTarget, e), 'dragleave', e.event);\n        }\n        if (dropTarget && dropTarget !== lastDropTarget) {\n          this.handler.dispatchToElement(new Param(dropTarget, e), 'dragenter', e.event);\n        }\n      }\n    }\n  };\n  Draggable.prototype._dragEnd = function (e) {\n    var draggingTarget = this._draggingTarget;\n    if (draggingTarget) {\n      draggingTarget.dragging = false;\n    }\n    this.handler.dispatchToElement(new Param(draggingTarget, e), 'dragend', e.event);\n    if (this._dropTarget) {\n      this.handler.dispatchToElement(new Param(this._dropTarget, e), 'drop', e.event);\n    }\n    this._draggingTarget = null;\n    this._dropTarget = null;\n  };\n  return Draggable;\n}();\nexport default Draggable;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}