{"ast": null, "code": "function getError(option, xhr) {\n  var msg = \"cannot \".concat(option.method, \" \").concat(option.action, \" \").concat(xhr.status, \"'\");\n  var err = new Error(msg);\n  err.status = xhr.status;\n  err.method = option.method;\n  err.url = option.action;\n  return err;\n}\nfunction getBody(xhr) {\n  var text = xhr.responseText || xhr.response;\n  if (!text) {\n    return text;\n  }\n  try {\n    return JSON.parse(text);\n  } catch (e) {\n    return text;\n  }\n}\nexport default function upload(option) {\n  // eslint-disable-next-line no-undef\n  var xhr = new XMLHttpRequest();\n  if (option.onProgress && xhr.upload) {\n    xhr.upload.onprogress = function progress(e) {\n      if (e.total > 0) {\n        e.percent = e.loaded / e.total * 100;\n      }\n      option.onProgress(e);\n    };\n  }\n\n  // eslint-disable-next-line no-undef\n  var formData = new FormData();\n  if (option.data) {\n    Object.keys(option.data).forEach(function (key) {\n      var value = option.data[key];\n      // support key-value array data\n      if (Array.isArray(value)) {\n        value.forEach(function (item) {\n          // { list: [ 11, 22 ] }\n          // formData.append('list[]', 11);\n          formData.append(\"\".concat(key, \"[]\"), item);\n        });\n        return;\n      }\n      formData.append(key, value);\n    });\n  }\n\n  // eslint-disable-next-line no-undef\n  if (option.file instanceof Blob) {\n    formData.append(option.filename, option.file, option.file.name);\n  } else {\n    formData.append(option.filename, option.file);\n  }\n  xhr.onerror = function error(e) {\n    option.onError(e);\n  };\n  xhr.onload = function onload() {\n    // allow success when 2xx status\n    // see https://github.com/react-component/upload/issues/34\n    if (xhr.status < 200 || xhr.status >= 300) {\n      return option.onError(getError(option, xhr), getBody(xhr));\n    }\n    return option.onSuccess(getBody(xhr), xhr);\n  };\n  xhr.open(option.method, option.action, true);\n\n  // Has to be after `.open()`. See https://github.com/enyo/dropzone/issues/179\n  if (option.withCredentials && 'withCredentials' in xhr) {\n    xhr.withCredentials = true;\n  }\n  var headers = option.headers || {};\n\n  // when set headers['X-Requested-With'] = null , can close default XHR header\n  // see https://github.com/react-component/upload/issues/33\n  if (headers['X-Requested-With'] !== null) {\n    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n  }\n  Object.keys(headers).forEach(function (h) {\n    if (headers[h] !== null) {\n      xhr.setRequestHeader(h, headers[h]);\n    }\n  });\n  xhr.send(formData);\n  return {\n    abort: function abort() {\n      xhr.abort();\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}