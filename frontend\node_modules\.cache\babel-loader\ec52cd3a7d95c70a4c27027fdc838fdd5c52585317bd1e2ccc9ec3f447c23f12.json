{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.removeSensor = exports.getSensor = exports.Sensors = void 0;\nvar _id = _interopRequireDefault(require(\"./id\"));\nvar _sensors = require(\"./sensors\");\nvar _constant = require(\"./constant\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\n\n/**\n * all the sensor objects.\n * sensor pool\n */\nvar Sensors = {};\n\n/**\n * When destroy the sensor, remove it from the pool\n */\nexports.Sensors = Sensors;\nfunction clean(sensorId) {\n  // exist, then remove from pool\n  if (sensorId && Sensors[sensorId]) {\n    delete Sensors[sensorId];\n  }\n}\n\n/**\n * get one sensor\n * @param element\n * @returns {*}\n */\nvar getSensor = function getSensor(element) {\n  var sensorId = element.getAttribute(_constant.SizeSensorId);\n\n  // 1. if the sensor exists, then use it\n  if (sensorId && Sensors[sensorId]) {\n    return Sensors[sensorId];\n  }\n\n  // 2. not exist, then create one\n  var newId = (0, _id[\"default\"])();\n  element.setAttribute(_constant.SizeSensorId, newId);\n  var sensor = (0, _sensors.createSensor)(element, function () {\n    return clean(newId);\n  });\n  // add sensor into pool\n  Sensors[newId] = sensor;\n  return sensor;\n};\n\n/**\n * 移除 sensor\n * @param sensor\n */\nexports.getSensor = getSensor;\nvar removeSensor = function removeSensor(sensor) {\n  var sensorId = sensor.element.getAttribute(_constant.SizeSensorId);\n  // remove event, dom of the sensor used\n  sensor.destroy();\n  clean(sensorId);\n};\nexports.removeSensor = removeSensor;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}