import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Typography, 
  Tag, 
  Progress, 
  Timeline,
  Alert,
  Button,
  Input,
  Space,
  Spin,
  Tabs,
  Table,
  Statistic,
  Switch
} from 'antd';
import { 
  RobotOutlined,
  BulbOutlined,
  WarningOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined,
  SendOutlined,
  ReloadOutlined,
  EyeOutlined,
  <PERSON>boltOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';

const { Title, Paragraph, Text } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;

interface AIRecommendation {
  stock_code: string;
  stock_name: string;
  action: 'BUY' | 'SELL' | 'HOLD';
  confidence: number;
  reason: string;
  target_price?: number;
  risk_level: 'LOW' | 'MEDIUM' | 'HIGH';
}

interface MarketPrediction {
  prediction_date: string;
  market_trend: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  confidence: number;
  key_factors: string[];
  risk_factors: string[];
  support_level: number;
  resistance_level: number;
}

interface RealTimeRiskEvent {
  id: string;
  timestamp: string;
  event_type: 'NEWS' | 'MARKET' | 'POLICY' | 'COMPANY';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  description: string;
  affected_stocks: string[];
  ai_analysis: string;
}

const AIAnalysisEnhanced: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [newsInput, setNewsInput] = useState('');
  const [analyzing, setAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  
  // Phase 3 新功能状态
  const [aiRecommendations, setAiRecommendations] = useState<AIRecommendation[]>([]);
  const [marketPrediction, setMarketPrediction] = useState<MarketPrediction | null>(null);
  const [realTimeRiskEvents, setRealTimeRiskEvents] = useState<RealTimeRiskEvent[]>([]);
  const [realTimeMonitoring, setRealTimeMonitoring] = useState(true);
  const [dailyReportGenerated, setDailyReportGenerated] = useState(false);

  // 模拟数据初始化
  useEffect(() => {
    loadMockData();
  }, []);

  const loadMockData = () => {
    // 模拟AI推荐
    const mockRecommendations: AIRecommendation[] = [
      {
        stock_code: '000001.SZ',
        stock_name: '平安银行',
        action: 'BUY',
        confidence: 0.85,
        reason: 'AI模型预测金融板块将受益于政策利好，技术指标显示突破信号',
        target_price: 13.50,
        risk_level: 'LOW'
      },
      {
        stock_code: '600036.SH',
        stock_name: '招商银行',
        action: 'HOLD',
        confidence: 0.72,
        reason: '基本面稳健，但短期技术指标显示震荡整理',
        target_price: 37.80,
        risk_level: 'MEDIUM'
      },
      {
        stock_code: '000858.SZ',
        stock_name: '五粮液',
        action: 'SELL',
        confidence: 0.68,
        reason: '消费板块面临压力，AI情绪分析显示负面情绪增加',
        target_price: 125.00,
        risk_level: 'HIGH'
      }
    ];

    // 模拟市场预测
    const mockPrediction: MarketPrediction = {
      prediction_date: new Date().toISOString().split('T')[0],
      market_trend: 'BULLISH',
      confidence: 0.78,
      key_factors: [
        '央行降准政策释放流动性',
        '科技板块业绩预期改善',
        '外资持续流入A股市场'
      ],
      risk_factors: [
        '地缘政治不确定性',
        '通胀压力仍然存在',
        '部分行业估值偏高'
      ],
      support_level: 3150,
      resistance_level: 3280
    };

    // 模拟实时风险事件
    const mockRiskEvents: RealTimeRiskEvent[] = [
      {
        id: '1',
        timestamp: new Date().toISOString(),
        event_type: 'NEWS',
        severity: 'HIGH',
        title: '某上市公司被证监会立案调查',
        description: '涉嫌信息披露违法违规，可能面临重大处罚',
        affected_stocks: ['000001.SZ'],
        ai_analysis: 'AI分析显示该事件对公司股价将产生负面影响，建议投资者谨慎操作'
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        event_type: 'POLICY',
        severity: 'MEDIUM',
        title: '央行宣布降准0.25个百分点',
        description: '释放长期资金约5000亿元，支持实体经济发展',
        affected_stocks: [],
        ai_analysis: 'AI分析认为降准政策对市场整体利好，特别是银行和地产板块'
      }
    ];

    setAiRecommendations(mockRecommendations);
    setMarketPrediction(mockPrediction);
    setRealTimeRiskEvents(mockRiskEvents);
  };

  // 分析新闻文本
  const analyzeNews = async () => {
    if (!newsInput.trim()) return;
    
    setAnalyzing(true);
    
    // 模拟API调用
    setTimeout(() => {
      const mockResult = {
        sentiment_score: Math.random() * 2 - 1, // -1 到 1
        sentiment_label: Math.random() > 0.5 ? 'POSITIVE' : 'NEGATIVE',
        confidence: 0.7 + Math.random() * 0.3,
        key_points: [
          '市场情绪整体偏向乐观',
          '政策面释放积极信号',
          '资金流向显示增量资金入市'
        ],
        risk_factors: [
          '短期波动性可能增加',
          '部分板块估值偏高'
        ],
        investment_suggestion: '建议适度增加仓位，关注优质成长股'
      };
      
      setAnalysisResult(mockResult);
      setAnalyzing(false);
    }, 2000);
  };

  // 生成每日报告
  const generateDailyReport = async () => {
    setLoading(true);
    
    // 模拟报告生成
    setTimeout(() => {
      setDailyReportGenerated(true);
      setLoading(false);
    }, 3000);
  };

  // 情绪趋势图表配置
  const sentimentChartOption = {
    title: { text: '市场情绪趋势', left: 'center' },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: { type: 'value', min: -1, max: 1 },
    series: [{
      name: '情绪指数',
      type: 'line',
      data: [0.2, 0.5, 0.8, 0.3, -0.2, 0.6, 0.4],
      smooth: true,
      lineStyle: { color: '#1890ff' },
      areaStyle: { opacity: 0.3 }
    }]
  };

  // AI推荐表格列配置
  const recommendationColumns = [
    {
      title: '股票',
      dataIndex: 'stock_name',
      key: 'stock_name',
      render: (text: string, record: AIRecommendation) => (
        <div>
          <div>{text}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>{record.stock_code}</Text>
        </div>
      )
    },
    {
      title: '操作建议',
      dataIndex: 'action',
      key: 'action',
      render: (action: string) => {
        const colors = { BUY: 'green', SELL: 'red', HOLD: 'orange' };
        return <Tag color={colors[action as keyof typeof colors]}>{action}</Tag>;
      }
    },
    {
      title: '置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      render: (confidence: number) => (
        <Progress 
          percent={Math.round(confidence * 100)} 
          size="small" 
          status={confidence > 0.8 ? 'success' : confidence > 0.6 ? 'normal' : 'exception'}
        />
      )
    },
    {
      title: '风险等级',
      dataIndex: 'risk_level',
      key: 'risk_level',
      render: (level: string) => {
        const colors = { LOW: 'green', MEDIUM: 'orange', HIGH: 'red' };
        return <Tag color={colors[level as keyof typeof colors]}>{level}</Tag>;
      }
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <RobotOutlined /> AI智能分析 - Phase 3增强版
      </Title>
      
      <Tabs defaultActiveKey="1">
        {/* 实时风险监控 */}
        <TabPane tab={<span><EyeOutlined />实时风险监控</span>} key="1">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card 
                title="实时风险监控" 
                extra={
                  <Space>
                    <Text>监控状态:</Text>
                    <Switch 
                      checked={realTimeMonitoring} 
                      onChange={setRealTimeMonitoring}
                      checkedChildren="开启"
                      unCheckedChildren="关闭"
                    />
                  </Space>
                }
              >
                <Timeline>
                  {realTimeRiskEvents.map(event => (
                    <Timeline.Item 
                      key={event.id}
                      color={event.severity === 'CRITICAL' ? 'red' : 
                             event.severity === 'HIGH' ? 'orange' : 
                             event.severity === 'MEDIUM' ? 'blue' : 'green'}
                    >
                      <div>
                        <Space>
                          <Tag color={event.severity === 'CRITICAL' ? 'red' : 
                                     event.severity === 'HIGH' ? 'orange' : 
                                     event.severity === 'MEDIUM' ? 'blue' : 'green'}>
                            {event.severity}
                          </Tag>
                          <Tag>{event.event_type}</Tag>
                          <Text type="secondary">
                            {new Date(event.timestamp).toLocaleTimeString()}
                          </Text>
                        </Space>
                        <div style={{ marginTop: '8px' }}>
                          <Text strong>{event.title}</Text>
                        </div>
                        <div style={{ marginTop: '4px' }}>
                          <Text>{event.description}</Text>
                        </div>
                        <div style={{ marginTop: '8px', padding: '8px', backgroundColor: '#f6f6f6', borderRadius: '4px' }}>
                          <Text type="secondary">AI分析: {event.ai_analysis}</Text>
                        </div>
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* 市场情绪分析 */}
        <TabPane tab={<span><TrendingUpOutlined />市场情绪分析</span>} key="2">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="情绪趋势分析">
                <ReactECharts option={sentimentChartOption} style={{ height: '300px' }} />
              </Card>
            </Col>
            <Col span={24}>
              <Card title="新闻情绪分析">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <TextArea
                    rows={4}
                    placeholder="输入新闻文本进行AI情绪分析..."
                    value={newsInput}
                    onChange={(e) => setNewsInput(e.target.value)}
                  />
                  <Button 
                    type="primary" 
                    icon={<SendOutlined />}
                    loading={analyzing}
                    onClick={analyzeNews}
                  >
                    AI分析
                  </Button>
                  
                  {analysisResult && (
                    <Alert
                      message="AI分析结果"
                      description={
                        <div>
                          <p><strong>情绪评分:</strong> {analysisResult.sentiment_score.toFixed(2)} ({analysisResult.sentiment_label})</p>
                          <p><strong>置信度:</strong> {(analysisResult.confidence * 100).toFixed(1)}%</p>
                          <p><strong>投资建议:</strong> {analysisResult.investment_suggestion}</p>
                        </div>
                      }
                      type="info"
                      showIcon
                    />
                  )}
                </Space>
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* AI投资建议 */}
        <TabPane tab={<span><BulbOutlined />AI投资建议</span>} key="3">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card 
                title="AI智能推荐" 
                extra={
                  <Button icon={<ReloadOutlined />} onClick={loadMockData}>
                    刷新推荐
                  </Button>
                }
              >
                <Table 
                  dataSource={aiRecommendations}
                  columns={recommendationColumns}
                  rowKey="stock_code"
                  pagination={false}
                  expandable={{
                    expandedRowRender: (record) => (
                      <div style={{ padding: '16px', backgroundColor: '#fafafa' }}>
                        <p><strong>推荐理由:</strong> {record.reason}</p>
                        {record.target_price && (
                          <p><strong>目标价格:</strong> ¥{record.target_price}</p>
                        )}
                      </div>
                    )
                  }}
                />
              </Card>
            </Col>
            
            {marketPrediction && (
              <Col span={24}>
                <Card title="市场预测分析">
                  <Row gutter={16}>
                    <Col span={8}>
                      <Statistic 
                        title="市场趋势" 
                        value={marketPrediction.market_trend}
                        valueStyle={{ 
                          color: marketPrediction.market_trend === 'BULLISH' ? '#3f8600' : 
                                 marketPrediction.market_trend === 'BEARISH' ? '#cf1322' : '#1890ff'
                        }}
                      />
                    </Col>
                    <Col span={8}>
                      <Statistic 
                        title="预测置信度" 
                        value={marketPrediction.confidence * 100}
                        precision={1}
                        suffix="%"
                      />
                    </Col>
                    <Col span={8}>
                      <Statistic 
                        title="支撑位" 
                        value={marketPrediction.support_level}
                      />
                    </Col>
                  </Row>
                  
                  <Row gutter={16} style={{ marginTop: '16px' }}>
                    <Col span={12}>
                      <Card size="small" title="利好因素">
                        <ul>
                          {marketPrediction.key_factors.map((factor, index) => (
                            <li key={index}>{factor}</li>
                          ))}
                        </ul>
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card size="small" title="风险因素">
                        <ul>
                          {marketPrediction.risk_factors.map((factor, index) => (
                            <li key={index}>{factor}</li>
                          ))}
                        </ul>
                      </Card>
                    </Col>
                  </Row>
                </Card>
              </Col>
            )}
          </Row>
        </TabPane>

        {/* 每日AI报告 */}
        <TabPane tab={<span><FileTextOutlined />每日AI报告</span>} key="4">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card 
                title="AI每日复盘报告"
                extra={
                  <Button 
                    type="primary" 
                    icon={<ThunderboltOutlined />}
                    loading={loading}
                    onClick={generateDailyReport}
                  >
                    生成今日报告
                  </Button>
                }
              >
                {dailyReportGenerated ? (
                  <div>
                    <Title level={4}>📊 {new Date().toLocaleDateString()} 市场复盘</Title>
                    
                    <Paragraph>
                      <Text strong>市场概况：</Text>
                      今日A股市场整体表现平稳，上证指数收于3,200点附近，成交量较昨日略有放大。
                      科技板块表现强劲，金融板块稳步上涨，消费板块有所调整。
                    </Paragraph>
                    
                    <Paragraph>
                      <Text strong>AI分析要点：</Text>
                      <ul>
                        <li>市场情绪指数显示投资者信心有所回升</li>
                        <li>资金流向分析显示增量资金持续入市</li>
                        <li>技术指标显示短期震荡整理格局</li>
                      </ul>
                    </Paragraph>
                    
                    <Paragraph>
                      <Text strong>明日展望：</Text>
                      基于AI模型分析，预计明日市场将延续震荡格局，建议关注政策面变化和外围市场动态。
                      重点关注科技和新能源板块的投资机会。
                    </Paragraph>
                    
                    <Alert
                      message="风险提示"
                      description="以上分析仅供参考，投资有风险，入市需谨慎。"
                      type="warning"
                      showIcon
                    />
                  </div>
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <Text type="secondary">点击上方按钮生成今日AI复盘报告</Text>
                  </div>
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default AIAnalysisEnhanced;
