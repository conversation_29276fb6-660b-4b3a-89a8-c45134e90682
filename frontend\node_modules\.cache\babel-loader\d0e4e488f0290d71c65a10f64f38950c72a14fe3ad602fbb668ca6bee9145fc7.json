{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesModel from '../../model/Series.js';\nimport Tree from '../../data/Tree.js';\nimport Model from '../../model/Model.js';\nimport { wrapTreePathInfo } from '../helper/treeHelper.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nimport enableAriaDecalForTree from '../helper/enableAriaDecalForTree.js';\nvar TreemapSeriesModel = /** @class */function (_super) {\n  __extends(TreemapSeriesModel, _super);\n  function TreemapSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TreemapSeriesModel.type;\n    _this.preventUsingHoverLayer = true;\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  TreemapSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    // Create a virtual root.\n    var root = {\n      name: option.name,\n      children: option.data\n    };\n    completeTreeValue(root);\n    var levels = option.levels || [];\n    // Used in \"visual priority\" in `treemapVisual.js`.\n    // This way is a little tricky, must satisfy the precondition:\n    //   1. There is no `treeNode.getModel('itemStyle.xxx')` used.\n    //   2. The `Model.prototype.getModel()` will not use any clone-like way.\n    var designatedVisualItemStyle = this.designatedVisualItemStyle = {};\n    var designatedVisualModel = new Model({\n      itemStyle: designatedVisualItemStyle\n    }, this, ecModel);\n    levels = option.levels = setDefault(levels, ecModel);\n    var levelModels = zrUtil.map(levels || [], function (levelDefine) {\n      return new Model(levelDefine, designatedVisualModel, ecModel);\n    }, this);\n    // Make sure always a new tree is created when setOption,\n    // in TreemapView, we check whether oldTree === newTree\n    // to choose mappings approach among old shapes and new shapes.\n    var tree = Tree.createTree(root, this, beforeLink);\n    function beforeLink(nodeData) {\n      nodeData.wrapMethod('getItemModel', function (model, idx) {\n        var node = tree.getNodeByDataIndex(idx);\n        var levelModel = node ? levelModels[node.depth] : null;\n        // If no levelModel, we also need `designatedVisualModel`.\n        model.parentModel = levelModel || designatedVisualModel;\n        return model;\n      });\n    }\n    return tree.data;\n  };\n  TreemapSeriesModel.prototype.optionUpdated = function () {\n    this.resetViewRoot();\n  };\n  /**\r\n   * @override\r\n   * @param {number} dataIndex\r\n   * @param {boolean} [mutipleSeries=false]\r\n   */\n  TreemapSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var value = this.getRawValue(dataIndex);\n    var name = data.getName(dataIndex);\n    return createTooltipMarkup('nameValue', {\n      name: name,\n      value: value\n    });\n  };\n  /**\r\n   * Add tree path to tooltip param\r\n   *\r\n   * @override\r\n   * @param {number} dataIndex\r\n   * @return {Object}\r\n   */\n  TreemapSeriesModel.prototype.getDataParams = function (dataIndex) {\n    var params = _super.prototype.getDataParams.apply(this, arguments);\n    var node = this.getData().tree.getNodeByDataIndex(dataIndex);\n    params.treeAncestors = wrapTreePathInfo(node, this);\n    // compatitable the previous code.\n    params.treePathInfo = params.treeAncestors;\n    return params;\n  };\n  /**\r\n   * @public\r\n   * @param {Object} layoutInfo {\r\n   *                                x: containerGroup x\r\n   *                                y: containerGroup y\r\n   *                                width: containerGroup width\r\n   *                                height: containerGroup height\r\n   *                            }\r\n   */\n  TreemapSeriesModel.prototype.setLayoutInfo = function (layoutInfo) {\n    /**\r\n     * @readOnly\r\n     * @type {Object}\r\n     */\n    this.layoutInfo = this.layoutInfo || {};\n    zrUtil.extend(this.layoutInfo, layoutInfo);\n  };\n  /**\r\n   * @param  {string} id\r\n   * @return {number} index\r\n   */\n  TreemapSeriesModel.prototype.mapIdToIndex = function (id) {\n    // A feature is implemented:\n    // index is monotone increasing with the sequence of\n    // input id at the first time.\n    // This feature can make sure that each data item and its\n    // mapped color have the same index between data list and\n    // color list at the beginning, which is useful for user\n    // to adjust data-color mapping.\n    /**\r\n     * @private\r\n     * @type {Object}\r\n     */\n    var idIndexMap = this._idIndexMap;\n    if (!idIndexMap) {\n      idIndexMap = this._idIndexMap = zrUtil.createHashMap();\n      /**\r\n       * @private\r\n       * @type {number}\r\n       */\n      this._idIndexMapCount = 0;\n    }\n    var index = idIndexMap.get(id);\n    if (index == null) {\n      idIndexMap.set(id, index = this._idIndexMapCount++);\n    }\n    return index;\n  };\n  TreemapSeriesModel.prototype.getViewRoot = function () {\n    return this._viewRoot;\n  };\n  TreemapSeriesModel.prototype.resetViewRoot = function (viewRoot) {\n    viewRoot ? this._viewRoot = viewRoot : viewRoot = this._viewRoot;\n    var root = this.getRawData().tree.root;\n    if (!viewRoot || viewRoot !== root && !root.contains(viewRoot)) {\n      this._viewRoot = root;\n    }\n  };\n  TreemapSeriesModel.prototype.enableAriaDecal = function () {\n    enableAriaDecalForTree(this);\n  };\n  TreemapSeriesModel.type = 'series.treemap';\n  TreemapSeriesModel.layoutMode = 'box';\n  TreemapSeriesModel.defaultOption = {\n    // Disable progressive rendering\n    progressive: 0,\n    // size: ['80%', '80%'],            // deprecated, compatible with ec2.\n    left: 'center',\n    top: 'middle',\n    width: '80%',\n    height: '80%',\n    sort: true,\n    clipWindow: 'origin',\n    squareRatio: 0.5 * (1 + Math.sqrt(5)),\n    leafDepth: null,\n    drillDownIcon: '▶',\n    // to align specialized icon. ▷▶❒❐▼✚\n    zoomToNodeRatio: 0.32 * 0.32,\n    scaleLimit: null,\n    roam: true,\n    nodeClick: 'zoomToNode',\n    animation: true,\n    animationDurationUpdate: 900,\n    animationEasing: 'quinticInOut',\n    breadcrumb: {\n      show: true,\n      height: 22,\n      left: 'center',\n      top: 'bottom',\n      // right\n      // bottom\n      emptyItemWidth: 25,\n      itemStyle: {\n        color: 'rgba(0,0,0,0.7)',\n        textStyle: {\n          color: '#fff'\n        }\n      },\n      emphasis: {\n        itemStyle: {\n          color: 'rgba(0,0,0,0.9)' // '#5793f3',\n        }\n      }\n    },\n    label: {\n      show: true,\n      // Do not use textDistance, for ellipsis rect just the same as treemap node rect.\n      distance: 0,\n      padding: 5,\n      position: 'inside',\n      // formatter: null,\n      color: '#fff',\n      overflow: 'truncate'\n      // align\n      // verticalAlign\n    },\n    upperLabel: {\n      show: false,\n      position: [0, '50%'],\n      height: 20,\n      // formatter: null,\n      // color: '#fff',\n      overflow: 'truncate',\n      // align: null,\n      verticalAlign: 'middle'\n    },\n    itemStyle: {\n      color: null,\n      colorAlpha: null,\n      colorSaturation: null,\n      borderWidth: 0,\n      gapWidth: 0,\n      borderColor: '#fff',\n      borderColorSaturation: null // If specified, borderColor will be ineffective, and the\n      // border color is evaluated by color of current node and\n      // borderColorSaturation.\n    },\n    emphasis: {\n      upperLabel: {\n        show: true,\n        position: [0, '50%'],\n        overflow: 'truncate',\n        verticalAlign: 'middle'\n      }\n    },\n    visualDimension: 0,\n    visualMin: null,\n    visualMax: null,\n    color: [],\n    // level[n].color (if necessary).\n    // + Specify color list of each level. level[0].color would be global\n    // color list if not specified. (see method `setDefault`).\n    // + But set as a empty array to forbid fetch color from global palette\n    // when using nodeModel.get('color'), otherwise nodes on deep level\n    // will always has color palette set and are not able to inherit color\n    // from parent node.\n    // + TreemapSeries.color can not be set as 'none', otherwise effect\n    // legend color fetching (see seriesColor.js).\n    colorAlpha: null,\n    colorSaturation: null,\n    colorMappingBy: 'index',\n    visibleMin: 10,\n    // be rendered. Only works when sort is 'asc' or 'desc'.\n    childrenVisibleMin: null,\n    // grandchildren will not show.\n    // Why grandchildren? If not grandchildren but children,\n    // some siblings show children and some not,\n    // the appearance may be mess and not consistent,\n    levels: [] // Each item: {\n    //     visibleMin, itemStyle, visualDimension, label\n    // }\n  };\n  return TreemapSeriesModel;\n}(SeriesModel);\n/**\r\n * @param {Object} dataNode\r\n */\nfunction completeTreeValue(dataNode) {\n  // Postorder travel tree.\n  // If value of none-leaf node is not set,\n  // calculate it by suming up the value of all children.\n  var sum = 0;\n  zrUtil.each(dataNode.children, function (child) {\n    completeTreeValue(child);\n    var childValue = child.value;\n    zrUtil.isArray(childValue) && (childValue = childValue[0]);\n    sum += childValue;\n  });\n  var thisValue = dataNode.value;\n  if (zrUtil.isArray(thisValue)) {\n    thisValue = thisValue[0];\n  }\n  if (thisValue == null || isNaN(thisValue)) {\n    thisValue = sum;\n  }\n  // Value should not less than 0.\n  if (thisValue < 0) {\n    thisValue = 0;\n  }\n  zrUtil.isArray(dataNode.value) ? dataNode.value[0] = thisValue : dataNode.value = thisValue;\n}\n/**\r\n * set default to level configuration\r\n */\nfunction setDefault(levels, ecModel) {\n  var globalColorList = normalizeToArray(ecModel.get('color'));\n  var globalDecalList = normalizeToArray(ecModel.get(['aria', 'decal', 'decals']));\n  if (!globalColorList) {\n    return;\n  }\n  levels = levels || [];\n  var hasColorDefine;\n  var hasDecalDefine;\n  zrUtil.each(levels, function (levelDefine) {\n    var model = new Model(levelDefine);\n    var modelColor = model.get('color');\n    var modelDecal = model.get('decal');\n    if (model.get(['itemStyle', 'color']) || modelColor && modelColor !== 'none') {\n      hasColorDefine = true;\n    }\n    if (model.get(['itemStyle', 'decal']) || modelDecal && modelDecal !== 'none') {\n      hasDecalDefine = true;\n    }\n  });\n  var level0 = levels[0] || (levels[0] = {});\n  if (!hasColorDefine) {\n    level0.color = globalColorList.slice();\n  }\n  if (!hasDecalDefine && globalDecalList) {\n    level0.decal = globalDecalList.slice();\n  }\n  return levels;\n}\nexport default TreemapSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}