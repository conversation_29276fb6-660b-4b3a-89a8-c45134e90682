"""
数据库初始化脚本
"""
from sqlalchemy import create_engine
from app.models.database import Base
from app.models.stock import Stock, StockBasic, MonitoringConfig, Alert
from app.core.config import settings
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_tables():
    """创建所有数据表"""
    try:
        engine = create_engine(settings.SQLALCHEMY_DATABASE_URI)
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        logger.info("✅ PostgreSQL数据表创建成功")
        
        # 打印创建的表
        tables = Base.metadata.tables.keys()
        logger.info(f"创建的表: {list(tables)}")
        
    except Exception as e:
        logger.error(f"❌ 数据表创建失败: {e}")
        raise

def init_sample_data():
    """初始化示例数据"""
    from app.models.database import SessionLocal
    
    db = SessionLocal()
    try:
        # 检查是否已有数据
        existing_stocks = db.query(Stock).count()
        if existing_stocks > 0:
            logger.info("数据库中已有股票数据，跳过初始化")
            return
        
        # 添加一些示例股票
        sample_stocks = [
            Stock(
                ts_code="000001.SZ",
                symbol="000001",
                name="平安银行",
                market="A股",
                industry="银行",
                area="深圳",
                list_date="19910403",
                is_active=True
            ),
            Stock(
                ts_code="000002.SZ",
                symbol="000002",
                name="万科A",
                market="A股",
                industry="房地产",
                area="深圳",
                list_date="19910129",
                is_active=True
            ),
            Stock(
                ts_code="600000.SH",
                symbol="600000",
                name="浦发银行",
                market="A股",
                industry="银行",
                area="上海",
                list_date="19991110",
                is_active=True
            ),
            Stock(
                ts_code="00700.HK",
                symbol="00700",
                name="腾讯控股",
                market="港股",
                industry="互联网",
                area="香港",
                list_date="20040616",
                is_active=False  # 暂时不监控港股
            )
        ]
        
        for stock in sample_stocks:
            db.add(stock)
        
        # 添加监控配置
        for stock in sample_stocks:
            if stock.is_active:  # 只为激活的股票添加监控配置
                config = MonitoringConfig(
                    ts_code=stock.ts_code,
                    ma_periods="5,10,20,60",
                    rsi_period=14,
                    macd_config="12,26,9",
                    max_position_ratio=0.1,
                    stop_loss_ratio=0.05,
                    max_drawdown=0.1,
                    price_change_threshold=0.05,
                    volume_change_threshold=2.0,
                    is_active=True
                )
                db.add(config)
        
        db.commit()
        logger.info(f"✅ 初始化了 {len(sample_stocks)} 只示例股票")
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ 示例数据初始化失败: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    logger.info("开始初始化数据库...")
    create_tables()
    init_sample_data()
    logger.info("数据库初始化完成！")
