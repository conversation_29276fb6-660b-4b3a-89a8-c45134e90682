{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport createSeriesDataSimply from '../helper/createSeriesDataSimply.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport LegendVisualProvider from '../../visual/LegendVisualProvider.js';\nimport { createTooltipMarkup, retrieveVisualColorForTooltipMarker } from '../../component/tooltip/tooltipMarkup.js';\nvar RadarSeriesModel = /** @class */function (_super) {\n  __extends(RadarSeriesModel, _super);\n  function RadarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = RadarSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    return _this;\n  }\n  // Overwrite\n  RadarSeriesModel.prototype.init = function (option) {\n    _super.prototype.init.apply(this, arguments);\n    // Enable legend selection for each data item\n    // Use a function instead of direct access because data reference may changed\n    this.legendVisualProvider = new LegendVisualProvider(zrUtil.bind(this.getData, this), zrUtil.bind(this.getRawData, this));\n  };\n  RadarSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesDataSimply(this, {\n      generateCoord: 'indicator_',\n      generateCoordCount: Infinity\n    });\n  };\n  RadarSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var coordSys = this.coordinateSystem;\n    var indicatorAxes = coordSys.getIndicatorAxes();\n    var name = this.getData().getName(dataIndex);\n    var nameToDisplay = name === '' ? this.name : name;\n    var markerColor = retrieveVisualColorForTooltipMarker(this, dataIndex);\n    return createTooltipMarkup('section', {\n      header: nameToDisplay,\n      sortBlocks: true,\n      blocks: zrUtil.map(indicatorAxes, function (axis) {\n        var val = data.get(data.mapDimension(axis.dim), dataIndex);\n        return createTooltipMarkup('nameValue', {\n          markerType: 'subItem',\n          markerColor: markerColor,\n          name: axis.name,\n          value: val,\n          sortParam: val\n        });\n      })\n    });\n  };\n  RadarSeriesModel.prototype.getTooltipPosition = function (dataIndex) {\n    if (dataIndex != null) {\n      var data_1 = this.getData();\n      var coordSys = this.coordinateSystem;\n      var values = data_1.getValues(zrUtil.map(coordSys.dimensions, function (dim) {\n        return data_1.mapDimension(dim);\n      }), dataIndex);\n      for (var i = 0, len = values.length; i < len; i++) {\n        if (!isNaN(values[i])) {\n          var indicatorAxes = coordSys.getIndicatorAxes();\n          return coordSys.coordToPoint(indicatorAxes[i].dataToCoord(values[i]), i);\n        }\n      }\n    }\n  };\n  RadarSeriesModel.type = 'series.radar';\n  RadarSeriesModel.dependencies = ['radar'];\n  RadarSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    colorBy: 'data',\n    coordinateSystem: 'radar',\n    legendHoverLink: true,\n    radarIndex: 0,\n    lineStyle: {\n      width: 2,\n      type: 'solid',\n      join: 'round'\n    },\n    label: {\n      position: 'top'\n    },\n    // areaStyle: {\n    // },\n    // itemStyle: {}\n    symbolSize: 8\n    // symbolRotate: null\n  };\n  return RadarSeriesModel;\n}(SeriesModel);\nexport default RadarSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}