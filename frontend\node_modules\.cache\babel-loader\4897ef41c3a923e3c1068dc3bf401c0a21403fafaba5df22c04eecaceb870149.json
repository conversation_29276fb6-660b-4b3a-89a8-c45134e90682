{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport Model from '../../model/Model.js';\nimport AxisView from './AxisView.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport { getECData } from '../../util/innerStore.js';\nvar elementList = ['axisLine', 'axisLabel', 'axisTick', 'minorTick', 'splitLine', 'minorSplitLine', 'splitArea'];\nfunction getAxisLineShape(polar, rExtent, angle) {\n  rExtent[1] > rExtent[0] && (rExtent = rExtent.slice().reverse());\n  var start = polar.coordToPoint([rExtent[0], angle]);\n  var end = polar.coordToPoint([rExtent[1], angle]);\n  return {\n    x1: start[0],\n    y1: start[1],\n    x2: end[0],\n    y2: end[1]\n  };\n}\nfunction getRadiusIdx(polar) {\n  var radiusAxis = polar.getRadiusAxis();\n  return radiusAxis.inverse ? 0 : 1;\n}\n// Remove the last tick which will overlap the first tick\nfunction fixAngleOverlap(list) {\n  var firstItem = list[0];\n  var lastItem = list[list.length - 1];\n  if (firstItem && lastItem && Math.abs(Math.abs(firstItem.coord - lastItem.coord) - 360) < 1e-4) {\n    list.pop();\n  }\n}\nvar AngleAxisView = /** @class */function (_super) {\n  __extends(AngleAxisView, _super);\n  function AngleAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AngleAxisView.type;\n    _this.axisPointerClass = 'PolarAxisPointer';\n    return _this;\n  }\n  AngleAxisView.prototype.render = function (angleAxisModel, ecModel) {\n    this.group.removeAll();\n    if (!angleAxisModel.get('show')) {\n      return;\n    }\n    var angleAxis = angleAxisModel.axis;\n    var polar = angleAxis.polar;\n    var radiusExtent = polar.getRadiusAxis().getExtent();\n    var ticksAngles = angleAxis.getTicksCoords();\n    var minorTickAngles = angleAxis.getMinorTicksCoords();\n    var labels = zrUtil.map(angleAxis.getViewLabels(), function (labelItem) {\n      labelItem = zrUtil.clone(labelItem);\n      var scale = angleAxis.scale;\n      var tickValue = scale.type === 'ordinal' ? scale.getRawOrdinalNumber(labelItem.tickValue) : labelItem.tickValue;\n      labelItem.coord = angleAxis.dataToCoord(tickValue);\n      return labelItem;\n    });\n    fixAngleOverlap(labels);\n    fixAngleOverlap(ticksAngles);\n    zrUtil.each(elementList, function (name) {\n      if (angleAxisModel.get([name, 'show']) && (!angleAxis.scale.isBlank() || name === 'axisLine')) {\n        angelAxisElementsBuilders[name](this.group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent, labels);\n      }\n    }, this);\n  };\n  AngleAxisView.type = 'angleAxis';\n  return AngleAxisView;\n}(AxisView);\nvar angelAxisElementsBuilders = {\n  axisLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var lineStyleModel = angleAxisModel.getModel(['axisLine', 'lineStyle']);\n    var angleAxis = polar.getAngleAxis();\n    var RADIAN = Math.PI / 180;\n    var angleExtent = angleAxis.getExtent();\n    // extent id of the axis radius (r0 and r)\n    var rId = getRadiusIdx(polar);\n    var r0Id = rId ? 0 : 1;\n    var shape;\n    var shapeType = Math.abs(angleExtent[1] - angleExtent[0]) === 360 ? 'Circle' : 'Arc';\n    if (radiusExtent[r0Id] === 0) {\n      shape = new graphic[shapeType]({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r: radiusExtent[rId],\n          startAngle: -angleExtent[0] * RADIAN,\n          endAngle: -angleExtent[1] * RADIAN,\n          clockwise: angleAxis.inverse\n        },\n        style: lineStyleModel.getLineStyle(),\n        z2: 1,\n        silent: true\n      });\n    } else {\n      shape = new graphic.Ring({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r: radiusExtent[rId],\n          r0: radiusExtent[r0Id]\n        },\n        style: lineStyleModel.getLineStyle(),\n        z2: 1,\n        silent: true\n      });\n    }\n    shape.style.fill = null;\n    group.add(shape);\n  },\n  axisTick: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var tickModel = angleAxisModel.getModel('axisTick');\n    var tickLen = (tickModel.get('inside') ? -1 : 1) * tickModel.get('length');\n    var radius = radiusExtent[getRadiusIdx(polar)];\n    var lines = zrUtil.map(ticksAngles, function (tickAngleItem) {\n      return new graphic.Line({\n        shape: getAxisLineShape(polar, [radius, radius + tickLen], tickAngleItem.coord)\n      });\n    });\n    group.add(graphic.mergePath(lines, {\n      style: zrUtil.defaults(tickModel.getModel('lineStyle').getLineStyle(), {\n        stroke: angleAxisModel.get(['axisLine', 'lineStyle', 'color'])\n      })\n    }));\n  },\n  minorTick: function (group, angleAxisModel, polar, tickAngles, minorTickAngles, radiusExtent) {\n    if (!minorTickAngles.length) {\n      return;\n    }\n    var tickModel = angleAxisModel.getModel('axisTick');\n    var minorTickModel = angleAxisModel.getModel('minorTick');\n    var tickLen = (tickModel.get('inside') ? -1 : 1) * minorTickModel.get('length');\n    var radius = radiusExtent[getRadiusIdx(polar)];\n    var lines = [];\n    for (var i = 0; i < minorTickAngles.length; i++) {\n      for (var k = 0; k < minorTickAngles[i].length; k++) {\n        lines.push(new graphic.Line({\n          shape: getAxisLineShape(polar, [radius, radius + tickLen], minorTickAngles[i][k].coord)\n        }));\n      }\n    }\n    group.add(graphic.mergePath(lines, {\n      style: zrUtil.defaults(minorTickModel.getModel('lineStyle').getLineStyle(), zrUtil.defaults(tickModel.getLineStyle(), {\n        stroke: angleAxisModel.get(['axisLine', 'lineStyle', 'color'])\n      }))\n    }));\n  },\n  axisLabel: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent, labels) {\n    var rawCategoryData = angleAxisModel.getCategories(true);\n    var commonLabelModel = angleAxisModel.getModel('axisLabel');\n    var labelMargin = commonLabelModel.get('margin');\n    var triggerEvent = angleAxisModel.get('triggerEvent');\n    // Use length of ticksAngles because it may remove the last tick to avoid overlapping\n    zrUtil.each(labels, function (labelItem, idx) {\n      var labelModel = commonLabelModel;\n      var tickValue = labelItem.tickValue;\n      var r = radiusExtent[getRadiusIdx(polar)];\n      var p = polar.coordToPoint([r + labelMargin, labelItem.coord]);\n      var cx = polar.cx;\n      var cy = polar.cy;\n      var labelTextAlign = Math.abs(p[0] - cx) / r < 0.3 ? 'center' : p[0] > cx ? 'left' : 'right';\n      var labelTextVerticalAlign = Math.abs(p[1] - cy) / r < 0.3 ? 'middle' : p[1] > cy ? 'top' : 'bottom';\n      if (rawCategoryData && rawCategoryData[tickValue]) {\n        var rawCategoryItem = rawCategoryData[tickValue];\n        if (zrUtil.isObject(rawCategoryItem) && rawCategoryItem.textStyle) {\n          labelModel = new Model(rawCategoryItem.textStyle, commonLabelModel, commonLabelModel.ecModel);\n        }\n      }\n      var textEl = new graphic.Text({\n        silent: AxisBuilder.isLabelSilent(angleAxisModel),\n        style: createTextStyle(labelModel, {\n          x: p[0],\n          y: p[1],\n          fill: labelModel.getTextColor() || angleAxisModel.get(['axisLine', 'lineStyle', 'color']),\n          text: labelItem.formattedLabel,\n          align: labelTextAlign,\n          verticalAlign: labelTextVerticalAlign\n        })\n      });\n      group.add(textEl);\n      // Pack data for mouse event\n      if (triggerEvent) {\n        var eventData = AxisBuilder.makeAxisEventDataBase(angleAxisModel);\n        eventData.targetType = 'axisLabel';\n        eventData.value = labelItem.rawLabel;\n        getECData(textEl).eventData = eventData;\n      }\n    }, this);\n  },\n  splitLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var splitLineModel = angleAxisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    var lineCount = 0;\n    lineColors = lineColors instanceof Array ? lineColors : [lineColors];\n    var splitLines = [];\n    for (var i = 0; i < ticksAngles.length; i++) {\n      var colorIndex = lineCount++ % lineColors.length;\n      splitLines[colorIndex] = splitLines[colorIndex] || [];\n      splitLines[colorIndex].push(new graphic.Line({\n        shape: getAxisLineShape(polar, radiusExtent, ticksAngles[i].coord)\n      }));\n    }\n    // Simple optimization\n    // Batching the lines if color are the same\n    for (var i = 0; i < splitLines.length; i++) {\n      group.add(graphic.mergePath(splitLines[i], {\n        style: zrUtil.defaults({\n          stroke: lineColors[i % lineColors.length]\n        }, lineStyleModel.getLineStyle()),\n        silent: true,\n        z: angleAxisModel.get('z')\n      }));\n    }\n  },\n  minorSplitLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    if (!minorTickAngles.length) {\n      return;\n    }\n    var minorSplitLineModel = angleAxisModel.getModel('minorSplitLine');\n    var lineStyleModel = minorSplitLineModel.getModel('lineStyle');\n    var lines = [];\n    for (var i = 0; i < minorTickAngles.length; i++) {\n      for (var k = 0; k < minorTickAngles[i].length; k++) {\n        lines.push(new graphic.Line({\n          shape: getAxisLineShape(polar, radiusExtent, minorTickAngles[i][k].coord)\n        }));\n      }\n    }\n    group.add(graphic.mergePath(lines, {\n      style: lineStyleModel.getLineStyle(),\n      silent: true,\n      z: angleAxisModel.get('z')\n    }));\n  },\n  splitArea: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    if (!ticksAngles.length) {\n      return;\n    }\n    var splitAreaModel = angleAxisModel.getModel('splitArea');\n    var areaStyleModel = splitAreaModel.getModel('areaStyle');\n    var areaColors = areaStyleModel.get('color');\n    var lineCount = 0;\n    areaColors = areaColors instanceof Array ? areaColors : [areaColors];\n    var splitAreas = [];\n    var RADIAN = Math.PI / 180;\n    var prevAngle = -ticksAngles[0].coord * RADIAN;\n    var r0 = Math.min(radiusExtent[0], radiusExtent[1]);\n    var r1 = Math.max(radiusExtent[0], radiusExtent[1]);\n    var clockwise = angleAxisModel.get('clockwise');\n    for (var i = 1, len = ticksAngles.length; i <= len; i++) {\n      var coord = i === len ? ticksAngles[0].coord : ticksAngles[i].coord;\n      var colorIndex = lineCount++ % areaColors.length;\n      splitAreas[colorIndex] = splitAreas[colorIndex] || [];\n      splitAreas[colorIndex].push(new graphic.Sector({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r0: r0,\n          r: r1,\n          startAngle: prevAngle,\n          endAngle: -coord * RADIAN,\n          clockwise: clockwise\n        },\n        silent: true\n      }));\n      prevAngle = -coord * RADIAN;\n    }\n    // Simple optimization\n    // Batching the lines if color are the same\n    for (var i = 0; i < splitAreas.length; i++) {\n      group.add(graphic.mergePath(splitAreas[i], {\n        style: zrUtil.defaults({\n          fill: areaColors[i % areaColors.length]\n        }, areaStyleModel.getAreaStyle()),\n        silent: true\n      }));\n    }\n  }\n};\nexport default AngleAxisView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}