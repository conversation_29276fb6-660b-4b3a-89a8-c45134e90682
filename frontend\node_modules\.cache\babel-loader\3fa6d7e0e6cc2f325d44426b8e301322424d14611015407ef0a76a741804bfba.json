{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isString, indexOf, each, bind, isFunction, isArray, isDom, retrieve2 } from 'zrender/lib/core/util.js';\nimport { normalizeEvent } from 'zrender/lib/core/event.js';\nimport { transformLocalCoord } from 'zrender/lib/core/dom.js';\nimport env from 'zrender/lib/core/env.js';\nimport { convertToColorString, toCamelCase, normalizeCssArray } from '../../util/format.js';\nimport { shouldTooltipConfine, toCSSVendorPrefix, getComputedStyle, TRANSFORM_VENDOR, TRANSITION_VENDOR } from './helper.js';\nimport { getPaddingFromTooltipModel } from './tooltipMarkup.js';\n/* global document, window */\nvar CSS_TRANSITION_VENDOR = toCSSVendorPrefix(TRANSITION_VENDOR, 'transition');\nvar CSS_TRANSFORM_VENDOR = toCSSVendorPrefix(TRANSFORM_VENDOR, 'transform');\n// eslint-disable-next-line\nvar gCssText = \"position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;\" + (env.transform3dSupported ? 'will-change:transform;' : '');\nfunction mirrorPos(pos) {\n  pos = pos === 'left' ? 'right' : pos === 'right' ? 'left' : pos === 'top' ? 'bottom' : 'top';\n  return pos;\n}\nfunction assembleArrow(tooltipModel, borderColor, arrowPosition) {\n  if (!isString(arrowPosition) || arrowPosition === 'inside') {\n    return '';\n  }\n  var backgroundColor = tooltipModel.get('backgroundColor');\n  var borderWidth = tooltipModel.get('borderWidth');\n  borderColor = convertToColorString(borderColor);\n  var arrowPos = mirrorPos(arrowPosition);\n  var arrowSize = Math.max(Math.round(borderWidth) * 1.5, 6);\n  var positionStyle = '';\n  var transformStyle = CSS_TRANSFORM_VENDOR + ':';\n  var rotateDeg;\n  if (indexOf(['left', 'right'], arrowPos) > -1) {\n    positionStyle += 'top:50%';\n    transformStyle += \"translateY(-50%) rotate(\" + (rotateDeg = arrowPos === 'left' ? -225 : -45) + \"deg)\";\n  } else {\n    positionStyle += 'left:50%';\n    transformStyle += \"translateX(-50%) rotate(\" + (rotateDeg = arrowPos === 'top' ? 225 : 45) + \"deg)\";\n  }\n  var rotateRadian = rotateDeg * Math.PI / 180;\n  var arrowWH = arrowSize + borderWidth;\n  var rotatedWH = arrowWH * Math.abs(Math.cos(rotateRadian)) + arrowWH * Math.abs(Math.sin(rotateRadian));\n  var arrowOffset = Math.round(((rotatedWH - Math.SQRT2 * borderWidth) / 2 + Math.SQRT2 * borderWidth - (rotatedWH - arrowWH) / 2) * 100) / 100;\n  positionStyle += \";\" + arrowPos + \":-\" + arrowOffset + \"px\";\n  var borderStyle = borderColor + \" solid \" + borderWidth + \"px;\";\n  var styleCss = [\"position:absolute;width:\" + arrowSize + \"px;height:\" + arrowSize + \"px;z-index:-1;\", positionStyle + \";\" + transformStyle + \";\", \"border-bottom:\" + borderStyle, \"border-right:\" + borderStyle, \"background-color:\" + backgroundColor + \";\"];\n  return \"<div style=\\\"\" + styleCss.join('') + \"\\\"></div>\";\n}\nfunction assembleTransition(duration, onlyFade) {\n  var transitionCurve = 'cubic-bezier(0.23,1,0.32,1)';\n  var transitionOption = \" \" + duration / 2 + \"s \" + transitionCurve;\n  var transitionText = \"opacity\" + transitionOption + \",visibility\" + transitionOption;\n  if (!onlyFade) {\n    transitionOption = \" \" + duration + \"s \" + transitionCurve;\n    transitionText += env.transformSupported ? \",\" + CSS_TRANSFORM_VENDOR + transitionOption : \",left\" + transitionOption + \",top\" + transitionOption;\n  }\n  return CSS_TRANSITION_VENDOR + ':' + transitionText;\n}\nfunction assembleTransform(x, y, toString) {\n  // If using float on style, the final width of the dom might\n  // keep changing slightly while mouse move. So `toFixed(0)` them.\n  var x0 = x.toFixed(0) + 'px';\n  var y0 = y.toFixed(0) + 'px';\n  // not support transform, use `left` and `top` instead.\n  if (!env.transformSupported) {\n    return toString ? \"top:\" + y0 + \";left:\" + x0 + \";\" : [['top', y0], ['left', x0]];\n  }\n  // support transform\n  var is3d = env.transform3dSupported;\n  var translate = \"translate\" + (is3d ? '3d' : '') + \"(\" + x0 + \",\" + y0 + (is3d ? ',0' : '') + \")\";\n  return toString ? 'top:0;left:0;' + CSS_TRANSFORM_VENDOR + ':' + translate + ';' : [['top', 0], ['left', 0], [TRANSFORM_VENDOR, translate]];\n}\n/**\r\n * @param {Object} textStyle\r\n * @return {string}\r\n * @inner\r\n */\nfunction assembleFont(textStyleModel) {\n  var cssText = [];\n  var fontSize = textStyleModel.get('fontSize');\n  var color = textStyleModel.getTextColor();\n  color && cssText.push('color:' + color);\n  cssText.push('font:' + textStyleModel.getFont());\n  // @ts-ignore, leave it to the tooltip refactor.\n  var lineHeight = retrieve2(textStyleModel.get('lineHeight'), Math.round(fontSize * 3 / 2));\n  fontSize && cssText.push('line-height:' + lineHeight + 'px');\n  var shadowColor = textStyleModel.get('textShadowColor');\n  var shadowBlur = textStyleModel.get('textShadowBlur') || 0;\n  var shadowOffsetX = textStyleModel.get('textShadowOffsetX') || 0;\n  var shadowOffsetY = textStyleModel.get('textShadowOffsetY') || 0;\n  shadowColor && shadowBlur && cssText.push('text-shadow:' + shadowOffsetX + 'px ' + shadowOffsetY + 'px ' + shadowBlur + 'px ' + shadowColor);\n  each(['decoration', 'align'], function (name) {\n    var val = textStyleModel.get(name);\n    val && cssText.push('text-' + name + ':' + val);\n  });\n  return cssText.join(';');\n}\nfunction assembleCssText(tooltipModel, enableTransition, onlyFade) {\n  var cssText = [];\n  var transitionDuration = tooltipModel.get('transitionDuration');\n  var backgroundColor = tooltipModel.get('backgroundColor');\n  var shadowBlur = tooltipModel.get('shadowBlur');\n  var shadowColor = tooltipModel.get('shadowColor');\n  var shadowOffsetX = tooltipModel.get('shadowOffsetX');\n  var shadowOffsetY = tooltipModel.get('shadowOffsetY');\n  var textStyleModel = tooltipModel.getModel('textStyle');\n  var padding = getPaddingFromTooltipModel(tooltipModel, 'html');\n  var boxShadow = shadowOffsetX + \"px \" + shadowOffsetY + \"px \" + shadowBlur + \"px \" + shadowColor;\n  cssText.push('box-shadow:' + boxShadow);\n  // Animation transition. Do not animate when transitionDuration is 0.\n  enableTransition && transitionDuration && cssText.push(assembleTransition(transitionDuration, onlyFade));\n  if (backgroundColor) {\n    cssText.push('background-color:' + backgroundColor);\n  }\n  // Border style\n  each(['width', 'color', 'radius'], function (name) {\n    var borderName = 'border-' + name;\n    var camelCase = toCamelCase(borderName);\n    var val = tooltipModel.get(camelCase);\n    val != null && cssText.push(borderName + ':' + val + (name === 'color' ? '' : 'px'));\n  });\n  // Text style\n  cssText.push(assembleFont(textStyleModel));\n  // Padding\n  if (padding != null) {\n    cssText.push('padding:' + normalizeCssArray(padding).join('px ') + 'px');\n  }\n  return cssText.join(';') + ';';\n}\n// If not able to make, do not modify the input `out`.\nfunction makeStyleCoord(out, zr, container, zrX, zrY) {\n  var zrPainter = zr && zr.painter;\n  if (container) {\n    var zrViewportRoot = zrPainter && zrPainter.getViewportRoot();\n    if (zrViewportRoot) {\n      // Some APPs might use scale on body, so we support CSS transform here.\n      transformLocalCoord(out, zrViewportRoot, container, zrX, zrY);\n    }\n  } else {\n    out[0] = zrX;\n    out[1] = zrY;\n    // xy should be based on canvas root. But tooltipContent is\n    // the sibling of canvas root. So padding of ec container\n    // should be considered here.\n    var viewportRootOffset = zrPainter && zrPainter.getViewportRootOffset();\n    if (viewportRootOffset) {\n      out[0] += viewportRootOffset.offsetLeft;\n      out[1] += viewportRootOffset.offsetTop;\n    }\n  }\n  out[2] = out[0] / zr.getWidth();\n  out[3] = out[1] / zr.getHeight();\n}\nvar TooltipHTMLContent = /** @class */function () {\n  function TooltipHTMLContent(api, opt) {\n    this._show = false;\n    this._styleCoord = [0, 0, 0, 0];\n    this._enterable = true;\n    this._alwaysShowContent = false;\n    this._firstShow = true;\n    this._longHide = true;\n    if (env.wxa) {\n      return null;\n    }\n    var el = document.createElement('div');\n    // TODO: TYPE\n    el.domBelongToZr = true;\n    this.el = el;\n    var zr = this._zr = api.getZr();\n    var appendTo = opt.appendTo;\n    var container = appendTo && (isString(appendTo) ? document.querySelector(appendTo) : isDom(appendTo) ? appendTo : isFunction(appendTo) && appendTo(api.getDom()));\n    makeStyleCoord(this._styleCoord, zr, container, api.getWidth() / 2, api.getHeight() / 2);\n    (container || api.getDom()).appendChild(el);\n    this._api = api;\n    this._container = container;\n    // FIXME\n    // Is it needed to trigger zr event manually if\n    // the browser do not support `pointer-events: none`.\n    var self = this;\n    el.onmouseenter = function () {\n      // clear the timeout in hideLater and keep showing tooltip\n      if (self._enterable) {\n        clearTimeout(self._hideTimeout);\n        self._show = true;\n      }\n      self._inContent = true;\n    };\n    el.onmousemove = function (e) {\n      e = e || window.event;\n      if (!self._enterable) {\n        // `pointer-events: none` is set to tooltip content div\n        // if `enterable` is set as `false`, and `el.onmousemove`\n        // can not be triggered. But in browser that do not\n        // support `pointer-events`, we need to do this:\n        // Try trigger zrender event to avoid mouse\n        // in and out shape too frequently\n        var handler = zr.handler;\n        var zrViewportRoot = zr.painter.getViewportRoot();\n        normalizeEvent(zrViewportRoot, e, true);\n        handler.dispatch('mousemove', e);\n      }\n    };\n    el.onmouseleave = function () {\n      // set `_inContent` to `false` before `hideLater`\n      self._inContent = false;\n      if (self._enterable) {\n        if (self._show) {\n          self.hideLater(self._hideDelay);\n        }\n      }\n    };\n  }\n  /**\r\n   * Update when tooltip is rendered\r\n   */\n  TooltipHTMLContent.prototype.update = function (tooltipModel) {\n    // FIXME\n    // Move this logic to ec main?\n    if (!this._container) {\n      var container = this._api.getDom();\n      var position = getComputedStyle(container, 'position');\n      var domStyle = container.style;\n      if (domStyle.position !== 'absolute' && position !== 'absolute') {\n        domStyle.position = 'relative';\n      }\n    }\n    // move tooltip if chart resized\n    var alwaysShowContent = tooltipModel.get('alwaysShowContent');\n    alwaysShowContent && this._moveIfResized();\n    // update alwaysShowContent\n    this._alwaysShowContent = alwaysShowContent;\n    // update className\n    this.el.className = tooltipModel.get('className') || '';\n    // Hide the tooltip\n    // PENDING\n    // this.hide();\n  };\n  TooltipHTMLContent.prototype.show = function (tooltipModel, nearPointColor) {\n    clearTimeout(this._hideTimeout);\n    clearTimeout(this._longHideTimeout);\n    var el = this.el;\n    var style = el.style;\n    var styleCoord = this._styleCoord;\n    if (!el.innerHTML) {\n      style.display = 'none';\n    } else {\n      style.cssText = gCssText + assembleCssText(tooltipModel, !this._firstShow, this._longHide)\n      // initial transform\n      + assembleTransform(styleCoord[0], styleCoord[1], true) + (\"border-color:\" + convertToColorString(nearPointColor) + \";\") + (tooltipModel.get('extraCssText') || '')\n      // If mouse occasionally move over the tooltip, a mouseout event will be\n      // triggered by canvas, and cause some unexpectable result like dragging\n      // stop, \"unfocusAdjacency\". Here `pointer-events: none` is used to solve\n      // it. Although it is not supported by IE8~IE10, fortunately it is a rare\n      // scenario.\n      + (\";pointer-events:\" + (this._enterable ? 'auto' : 'none'));\n    }\n    this._show = true;\n    this._firstShow = false;\n    this._longHide = false;\n  };\n  TooltipHTMLContent.prototype.setContent = function (content, markers, tooltipModel, borderColor, arrowPosition) {\n    var el = this.el;\n    if (content == null) {\n      el.innerHTML = '';\n      return;\n    }\n    var arrow = '';\n    if (isString(arrowPosition) && tooltipModel.get('trigger') === 'item' && !shouldTooltipConfine(tooltipModel)) {\n      arrow = assembleArrow(tooltipModel, borderColor, arrowPosition);\n    }\n    if (isString(content)) {\n      el.innerHTML = content + arrow;\n    } else if (content) {\n      // Clear previous\n      el.innerHTML = '';\n      if (!isArray(content)) {\n        content = [content];\n      }\n      for (var i = 0; i < content.length; i++) {\n        if (isDom(content[i]) && content[i].parentNode !== el) {\n          el.appendChild(content[i]);\n        }\n      }\n      // no arrow if empty\n      if (arrow && el.childNodes.length) {\n        // no need to create a new parent element, but it's not supported by IE 10 and older.\n        // const arrowEl = document.createRange().createContextualFragment(arrow);\n        var arrowEl = document.createElement('div');\n        arrowEl.innerHTML = arrow;\n        el.appendChild(arrowEl);\n      }\n    }\n  };\n  TooltipHTMLContent.prototype.setEnterable = function (enterable) {\n    this._enterable = enterable;\n  };\n  TooltipHTMLContent.prototype.getSize = function () {\n    var el = this.el;\n    return el ? [el.offsetWidth, el.offsetHeight] : [0, 0];\n  };\n  TooltipHTMLContent.prototype.moveTo = function (zrX, zrY) {\n    if (!this.el) {\n      return;\n    }\n    var styleCoord = this._styleCoord;\n    makeStyleCoord(styleCoord, this._zr, this._container, zrX, zrY);\n    if (styleCoord[0] != null && styleCoord[1] != null) {\n      var style_1 = this.el.style;\n      var transforms = assembleTransform(styleCoord[0], styleCoord[1]);\n      each(transforms, function (transform) {\n        style_1[transform[0]] = transform[1];\n      });\n    }\n  };\n  /**\r\n   * when `alwaysShowContent` is true,\r\n   * move the tooltip after chart resized\r\n   */\n  TooltipHTMLContent.prototype._moveIfResized = function () {\n    // The ratio of left to width\n    var ratioX = this._styleCoord[2];\n    // The ratio of top to height\n    var ratioY = this._styleCoord[3];\n    this.moveTo(ratioX * this._zr.getWidth(), ratioY * this._zr.getHeight());\n  };\n  TooltipHTMLContent.prototype.hide = function () {\n    var _this = this;\n    var style = this.el.style;\n    style.visibility = 'hidden';\n    style.opacity = '0';\n    env.transform3dSupported && (style.willChange = '');\n    this._show = false;\n    this._longHideTimeout = setTimeout(function () {\n      return _this._longHide = true;\n    }, 500);\n  };\n  TooltipHTMLContent.prototype.hideLater = function (time) {\n    if (this._show && !(this._inContent && this._enterable) && !this._alwaysShowContent) {\n      if (time) {\n        this._hideDelay = time;\n        // Set show false to avoid invoke hideLater multiple times\n        this._show = false;\n        this._hideTimeout = setTimeout(bind(this.hide, this), time);\n      } else {\n        this.hide();\n      }\n    }\n  };\n  TooltipHTMLContent.prototype.isShow = function () {\n    return this._show;\n  };\n  TooltipHTMLContent.prototype.dispose = function () {\n    clearTimeout(this._hideTimeout);\n    clearTimeout(this._longHideTimeout);\n    var parentNode = this.el.parentNode;\n    parentNode && parentNode.removeChild(this.el);\n    this.el = this._container = null;\n  };\n  return TooltipHTMLContent;\n}();\nexport default TooltipHTMLContent;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}