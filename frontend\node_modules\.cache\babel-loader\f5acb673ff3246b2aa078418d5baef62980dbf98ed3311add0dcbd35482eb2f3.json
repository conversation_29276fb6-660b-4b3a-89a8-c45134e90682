{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { parsePercent, linearMap } from '../../util/number.js';\nimport * as layout from '../../util/layout.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { normalizeArcAngles } from 'zrender/lib/core/PathProxy.js';\nimport { makeInner } from '../../util/model.js';\nvar PI2 = Math.PI * 2;\nvar RADIAN = Math.PI / 180;\nfunction getViewRect(seriesModel, api) {\n  return layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\nexport function getBasicPieLayout(seriesModel, api) {\n  var viewRect = getViewRect(seriesModel, api);\n  // center can be string or number when coordinateSystem is specified\n  var center = seriesModel.get('center');\n  var radius = seriesModel.get('radius');\n  if (!zrUtil.isArray(radius)) {\n    radius = [0, radius];\n  }\n  var width = parsePercent(viewRect.width, api.getWidth());\n  var height = parsePercent(viewRect.height, api.getHeight());\n  var size = Math.min(width, height);\n  var r0 = parsePercent(radius[0], size / 2);\n  var r = parsePercent(radius[1], size / 2);\n  var cx;\n  var cy;\n  var coordSys = seriesModel.coordinateSystem;\n  if (coordSys) {\n    // percentage is not allowed when coordinate system is specified\n    var point = coordSys.dataToPoint(center);\n    cx = point[0] || 0;\n    cy = point[1] || 0;\n  } else {\n    if (!zrUtil.isArray(center)) {\n      center = [center, center];\n    }\n    cx = parsePercent(center[0], width) + viewRect.x;\n    cy = parsePercent(center[1], height) + viewRect.y;\n  }\n  return {\n    cx: cx,\n    cy: cy,\n    r0: r0,\n    r: r\n  };\n}\nexport default function pieLayout(seriesType, ecModel, api) {\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    var data = seriesModel.getData();\n    var valueDim = data.mapDimension('value');\n    var viewRect = getViewRect(seriesModel, api);\n    var _a = getBasicPieLayout(seriesModel, api),\n      cx = _a.cx,\n      cy = _a.cy,\n      r = _a.r,\n      r0 = _a.r0;\n    var startAngle = -seriesModel.get('startAngle') * RADIAN;\n    var endAngle = seriesModel.get('endAngle');\n    var padAngle = seriesModel.get('padAngle') * RADIAN;\n    endAngle = endAngle === 'auto' ? startAngle - PI2 : -endAngle * RADIAN;\n    var minAngle = seriesModel.get('minAngle') * RADIAN;\n    var minAndPadAngle = minAngle + padAngle;\n    var validDataCount = 0;\n    data.each(valueDim, function (value) {\n      !isNaN(value) && validDataCount++;\n    });\n    var sum = data.getSum(valueDim);\n    // Sum may be 0\n    var unitRadian = Math.PI / (sum || validDataCount) * 2;\n    var clockwise = seriesModel.get('clockwise');\n    var roseType = seriesModel.get('roseType');\n    var stillShowZeroSum = seriesModel.get('stillShowZeroSum');\n    // [0...max]\n    var extent = data.getDataExtent(valueDim);\n    extent[0] = 0;\n    var dir = clockwise ? 1 : -1;\n    var angles = [startAngle, endAngle];\n    var halfPadAngle = dir * padAngle / 2;\n    normalizeArcAngles(angles, !clockwise);\n    startAngle = angles[0], endAngle = angles[1];\n    var layoutData = getSeriesLayoutData(seriesModel);\n    layoutData.startAngle = startAngle;\n    layoutData.endAngle = endAngle;\n    layoutData.clockwise = clockwise;\n    var angleRange = Math.abs(endAngle - startAngle);\n    // In the case some sector angle is smaller than minAngle\n    var restAngle = angleRange;\n    var valueSumLargerThanMinAngle = 0;\n    var currentAngle = startAngle;\n    data.setLayout({\n      viewRect: viewRect,\n      r: r\n    });\n    data.each(valueDim, function (value, idx) {\n      var angle;\n      if (isNaN(value)) {\n        data.setItemLayout(idx, {\n          angle: NaN,\n          startAngle: NaN,\n          endAngle: NaN,\n          clockwise: clockwise,\n          cx: cx,\n          cy: cy,\n          r0: r0,\n          r: roseType ? NaN : r\n        });\n        return;\n      }\n      // FIXME 兼容 2.0 但是 roseType 是 area 的时候才是这样？\n      if (roseType !== 'area') {\n        angle = sum === 0 && stillShowZeroSum ? unitRadian : value * unitRadian;\n      } else {\n        angle = angleRange / validDataCount;\n      }\n      if (angle < minAndPadAngle) {\n        angle = minAndPadAngle;\n        restAngle -= minAndPadAngle;\n      } else {\n        valueSumLargerThanMinAngle += value;\n      }\n      var endAngle = currentAngle + dir * angle;\n      // calculate display angle\n      var actualStartAngle = 0;\n      var actualEndAngle = 0;\n      if (padAngle > angle) {\n        actualStartAngle = currentAngle + dir * angle / 2;\n        actualEndAngle = actualStartAngle;\n      } else {\n        actualStartAngle = currentAngle + halfPadAngle;\n        actualEndAngle = endAngle - halfPadAngle;\n      }\n      data.setItemLayout(idx, {\n        angle: angle,\n        startAngle: actualStartAngle,\n        endAngle: actualEndAngle,\n        clockwise: clockwise,\n        cx: cx,\n        cy: cy,\n        r0: r0,\n        r: roseType ? linearMap(value, extent, [r0, r]) : r\n      });\n      currentAngle = endAngle;\n    });\n    // Some sector is constrained by minAngle and padAngle\n    // Rest sectors needs recalculate angle\n    if (restAngle < PI2 && validDataCount) {\n      // Average the angle if rest angle is not enough after all angles is\n      // Constrained by minAngle and padAngle\n      if (restAngle <= 1e-3) {\n        var angle_1 = angleRange / validDataCount;\n        data.each(valueDim, function (value, idx) {\n          if (!isNaN(value)) {\n            var layout_1 = data.getItemLayout(idx);\n            layout_1.angle = angle_1;\n            var actualStartAngle = 0;\n            var actualEndAngle = 0;\n            if (angle_1 < padAngle) {\n              actualStartAngle = startAngle + dir * (idx + 1 / 2) * angle_1;\n              actualEndAngle = actualStartAngle;\n            } else {\n              actualStartAngle = startAngle + dir * idx * angle_1 + halfPadAngle;\n              actualEndAngle = startAngle + dir * (idx + 1) * angle_1 - halfPadAngle;\n            }\n            layout_1.startAngle = actualStartAngle;\n            layout_1.endAngle = actualEndAngle;\n          }\n        });\n      } else {\n        unitRadian = restAngle / valueSumLargerThanMinAngle;\n        currentAngle = startAngle;\n        data.each(valueDim, function (value, idx) {\n          if (!isNaN(value)) {\n            var layout_2 = data.getItemLayout(idx);\n            var angle = layout_2.angle === minAndPadAngle ? minAndPadAngle : value * unitRadian;\n            var actualStartAngle = 0;\n            var actualEndAngle = 0;\n            if (angle < padAngle) {\n              actualStartAngle = currentAngle + dir * angle / 2;\n              actualEndAngle = actualStartAngle;\n            } else {\n              actualStartAngle = currentAngle + halfPadAngle;\n              actualEndAngle = currentAngle + dir * angle - halfPadAngle;\n            }\n            layout_2.startAngle = actualStartAngle;\n            layout_2.endAngle = actualEndAngle;\n            currentAngle += dir * angle;\n          }\n        });\n      }\n    }\n  });\n}\nexport var getSeriesLayoutData = makeInner();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}