{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport AxisView from './AxisView.js';\nvar axisBuilderAttrs = ['axisLine', 'axisTickLabel', 'axisName'];\nvar selfBuilderAttrs = ['splitLine', 'splitArea', 'minorSplitLine'];\nvar RadiusAxisView = /** @class */function (_super) {\n  __extends(RadiusAxisView, _super);\n  function RadiusAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = RadiusAxisView.type;\n    _this.axisPointerClass = 'PolarAxisPointer';\n    return _this;\n  }\n  RadiusAxisView.prototype.render = function (radiusAxisModel, ecModel) {\n    this.group.removeAll();\n    if (!radiusAxisModel.get('show')) {\n      return;\n    }\n    var oldAxisGroup = this._axisGroup;\n    var newAxisGroup = this._axisGroup = new graphic.Group();\n    this.group.add(newAxisGroup);\n    var radiusAxis = radiusAxisModel.axis;\n    var polar = radiusAxis.polar;\n    var angleAxis = polar.getAngleAxis();\n    var ticksCoords = radiusAxis.getTicksCoords();\n    var minorTicksCoords = radiusAxis.getMinorTicksCoords();\n    var axisAngle = angleAxis.getExtent()[0];\n    var radiusExtent = radiusAxis.getExtent();\n    var layout = layoutAxis(polar, radiusAxisModel, axisAngle);\n    var axisBuilder = new AxisBuilder(radiusAxisModel, layout);\n    zrUtil.each(axisBuilderAttrs, axisBuilder.add, axisBuilder);\n    newAxisGroup.add(axisBuilder.getGroup());\n    graphic.groupTransition(oldAxisGroup, newAxisGroup, radiusAxisModel);\n    zrUtil.each(selfBuilderAttrs, function (name) {\n      if (radiusAxisModel.get([name, 'show']) && !radiusAxis.scale.isBlank()) {\n        axisElementBuilders[name](this.group, radiusAxisModel, polar, axisAngle, radiusExtent, ticksCoords, minorTicksCoords);\n      }\n    }, this);\n  };\n  RadiusAxisView.type = 'radiusAxis';\n  return RadiusAxisView;\n}(AxisView);\nvar axisElementBuilders = {\n  splitLine: function (group, radiusAxisModel, polar, axisAngle, radiusExtent, ticksCoords) {\n    var splitLineModel = radiusAxisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    var lineCount = 0;\n    var angleAxis = polar.getAngleAxis();\n    var RADIAN = Math.PI / 180;\n    var angleExtent = angleAxis.getExtent();\n    var shapeType = Math.abs(angleExtent[1] - angleExtent[0]) === 360 ? 'Circle' : 'Arc';\n    lineColors = lineColors instanceof Array ? lineColors : [lineColors];\n    var splitLines = [];\n    for (var i = 0; i < ticksCoords.length; i++) {\n      var colorIndex = lineCount++ % lineColors.length;\n      splitLines[colorIndex] = splitLines[colorIndex] || [];\n      splitLines[colorIndex].push(new graphic[shapeType]({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          // ensure circle radius >= 0\n          r: Math.max(ticksCoords[i].coord, 0),\n          startAngle: -angleExtent[0] * RADIAN,\n          endAngle: -angleExtent[1] * RADIAN,\n          clockwise: angleAxis.inverse\n        }\n      }));\n    }\n    // Simple optimization\n    // Batching the lines if color are the same\n    for (var i = 0; i < splitLines.length; i++) {\n      group.add(graphic.mergePath(splitLines[i], {\n        style: zrUtil.defaults({\n          stroke: lineColors[i % lineColors.length],\n          fill: null\n        }, lineStyleModel.getLineStyle()),\n        silent: true\n      }));\n    }\n  },\n  minorSplitLine: function (group, radiusAxisModel, polar, axisAngle, radiusExtent, ticksCoords, minorTicksCoords) {\n    if (!minorTicksCoords.length) {\n      return;\n    }\n    var minorSplitLineModel = radiusAxisModel.getModel('minorSplitLine');\n    var lineStyleModel = minorSplitLineModel.getModel('lineStyle');\n    var lines = [];\n    for (var i = 0; i < minorTicksCoords.length; i++) {\n      for (var k = 0; k < minorTicksCoords[i].length; k++) {\n        lines.push(new graphic.Circle({\n          shape: {\n            cx: polar.cx,\n            cy: polar.cy,\n            r: minorTicksCoords[i][k].coord\n          }\n        }));\n      }\n    }\n    group.add(graphic.mergePath(lines, {\n      style: zrUtil.defaults({\n        fill: null\n      }, lineStyleModel.getLineStyle()),\n      silent: true\n    }));\n  },\n  splitArea: function (group, radiusAxisModel, polar, axisAngle, radiusExtent, ticksCoords) {\n    if (!ticksCoords.length) {\n      return;\n    }\n    var splitAreaModel = radiusAxisModel.getModel('splitArea');\n    var areaStyleModel = splitAreaModel.getModel('areaStyle');\n    var areaColors = areaStyleModel.get('color');\n    var lineCount = 0;\n    areaColors = areaColors instanceof Array ? areaColors : [areaColors];\n    var splitAreas = [];\n    var prevRadius = ticksCoords[0].coord;\n    for (var i = 1; i < ticksCoords.length; i++) {\n      var colorIndex = lineCount++ % areaColors.length;\n      splitAreas[colorIndex] = splitAreas[colorIndex] || [];\n      splitAreas[colorIndex].push(new graphic.Sector({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r0: prevRadius,\n          r: ticksCoords[i].coord,\n          startAngle: 0,\n          endAngle: Math.PI * 2\n        },\n        silent: true\n      }));\n      prevRadius = ticksCoords[i].coord;\n    }\n    // Simple optimization\n    // Batching the lines if color are the same\n    for (var i = 0; i < splitAreas.length; i++) {\n      group.add(graphic.mergePath(splitAreas[i], {\n        style: zrUtil.defaults({\n          fill: areaColors[i % areaColors.length]\n        }, areaStyleModel.getAreaStyle()),\n        silent: true\n      }));\n    }\n  }\n};\n/**\r\n * @inner\r\n */\nfunction layoutAxis(polar, radiusAxisModel, axisAngle) {\n  return {\n    position: [polar.cx, polar.cy],\n    rotation: axisAngle / 180 * Math.PI,\n    labelDirection: -1,\n    tickDirection: -1,\n    nameDirection: 1,\n    labelRotate: radiusAxisModel.getModel('axisLabel').get('rotate'),\n    // Over splitLine and splitArea\n    z2: 1\n  };\n}\nexport default RadiusAxisView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}