{"ast": null, "code": "import rules from \"../rule\";\nimport { isEmptyValue } from \"../util\";\nvar number = function number(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (value === '') {\n      // eslint-disable-next-line no-param-reassign\n      value = undefined;\n    }\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nexport default number;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}