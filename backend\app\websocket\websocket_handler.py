"""
WebSocket处理器
"""
from fastapi import WebSocket, WebSocketDisconnect
import json
import logging
from typing import Dict, Any

from app.websocket.connection_manager import get_connection_manager
from app.services.indicator_service import get_indicator_service
from data.data_service import get_data_service

logger = logging.getLogger(__name__)

class WebSocketHandler:
    """WebSocket消息处理器"""
    
    def __init__(self):
        self.manager = get_connection_manager()
        self.indicator_service = get_indicator_service()
        self.data_service = get_data_service()
    
    async def handle_connection(self, websocket: WebSocket, client_id: str = None):
        """处理WebSocket连接"""
        await self.manager.connect(websocket, client_id)
        
        try:
            while True:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理消息
                await self.handle_message(websocket, message)
                
        except WebSocketDisconnect:
            self.manager.disconnect(websocket)
        except Exception as e:
            logger.error(f"WebSocket处理异常: {e}")
            self.manager.disconnect(websocket)
    
    async def handle_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """处理客户端消息"""
        try:
            message_type = message.get('type')
            
            if message_type == 'subscribe':
                # 订阅数据
                data_types = message.get('data_types', [])
                await self.manager.subscribe(websocket, data_types)
                
            elif message_type == 'unsubscribe':
                # 取消订阅
                data_types = message.get('data_types', [])
                await self.manager.unsubscribe(websocket, data_types)
                
            elif message_type == 'get_stock_data':
                # 获取股票数据
                ts_code = message.get('ts_code')
                if ts_code:
                    await self.send_stock_data(websocket, ts_code)
                    
            elif message_type == 'get_indicators':
                # 获取技术指标
                ts_code = message.get('ts_code')
                if ts_code:
                    await self.send_indicators(websocket, ts_code)
                    
            elif message_type == 'ping':
                # 心跳检测
                await self.manager.send_personal_message({
                    'type': 'pong',
                    'timestamp': message.get('timestamp')
                }, websocket)
                
            else:
                await self.manager.send_personal_message({
                    'type': 'error',
                    'message': f'未知消息类型: {message_type}'
                }, websocket)
                
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            await self.manager.send_personal_message({
                'type': 'error',
                'message': f'处理消息失败: {str(e)}'
            }, websocket)
    
    async def send_stock_data(self, websocket: WebSocket, ts_code: str):
        """发送股票数据"""
        try:
            # 获取最新价格
            latest_price = self.data_service.get_latest_price(ts_code)
            
            response = {
                'type': 'stock_data',
                'ts_code': ts_code,
                'data': {
                    'latest_price': latest_price,
                    'update_time': 'real-time'
                }
            }
            
            await self.manager.send_personal_message(response, websocket)
            
        except Exception as e:
            logger.error(f"发送股票数据失败: {e}")
    
    async def send_indicators(self, websocket: WebSocket, ts_code: str):
        """发送技术指标数据"""
        try:
            # 获取技术指标
            indicators = self.indicator_service.calculate_stock_indicators(ts_code)
            
            response = {
                'type': 'indicators',
                'ts_code': ts_code,
                'data': indicators
            }
            
            await self.manager.send_personal_message(response, websocket)
            
        except Exception as e:
            logger.error(f"发送技术指标失败: {e}")
    
    async def broadcast_market_update(self, market_data: Dict[str, Any]):
        """广播市场更新"""
        message = {
            'type': 'market_update',
            'data': market_data
        }
        await self.manager.broadcast(message, 'market_data')
    
    async def broadcast_price_update(self, ts_code: str, price_data: Dict[str, Any]):
        """广播价格更新"""
        message = {
            'type': 'price_update',
            'ts_code': ts_code,
            'data': price_data
        }
        await self.manager.broadcast(message, 'price_data')
    
    async def broadcast_signal_update(self, ts_code: str, signals: Dict[str, Any]):
        """广播交易信号更新"""
        message = {
            'type': 'signal_update',
            'ts_code': ts_code,
            'data': signals
        }
        await self.manager.broadcast(message, 'signals')
    
    async def broadcast_risk_alert(self, alert_data: Dict[str, Any]):
        """广播风险告警"""
        message = {
            'type': 'risk_alert',
            'data': alert_data
        }
        await self.manager.broadcast(message, 'risk_alerts')

# 全局WebSocket处理器实例
websocket_handler = WebSocketHandler()

def get_websocket_handler() -> WebSocketHandler:
    """获取WebSocket处理器实例"""
    return websocket_handler
