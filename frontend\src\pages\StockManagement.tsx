import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Switch, 
  Space, 
  message,
  Tag,
  Popconfirm
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  SyncOutlined,
  StockOutlined
} from '@ant-design/icons';

const { Option } = Select;

interface Stock {
  id: number;
  ts_code: string;
  symbol: string;
  name: string;
  market: string;
  industry: string;
  is_active: boolean;
  created_at: string;
}

const StockManagement: React.FC = () => {
  const [stocks, setStocks] = useState<Stock[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingStock, setEditingStock] = useState<Stock | null>(null);
  const [form] = Form.useForm();

  // 模拟数据
  useEffect(() => {
    const mockStocks: Stock[] = [
      {
        id: 1,
        ts_code: '000001.SZ',
        symbol: '000001',
        name: '平安银行',
        market: 'A股',
        industry: '银行',
        is_active: true,
        created_at: '2024-01-15'
      },
      {
        id: 2,
        ts_code: '000002.SZ',
        symbol: '000002',
        name: '万科A',
        market: 'A股',
        industry: '房地产',
        is_active: true,
        created_at: '2024-01-15'
      },
      {
        id: 3,
        ts_code: '600000.SH',
        symbol: '600000',
        name: '浦发银行',
        market: 'A股',
        industry: '银行',
        is_active: false,
        created_at: '2024-01-16'
      },
      {
        id: 4,
        ts_code: '00700.HK',
        symbol: '00700',
        name: '腾讯控股',
        market: '港股',
        industry: '互联网',
        is_active: false,
        created_at: '2024-01-16'
      }
    ];

    setStocks(mockStocks);
  }, []);

  const columns = [
    {
      title: '股票代码',
      dataIndex: 'ts_code',
      key: 'ts_code',
      render: (text: string) => <Tag color="blue">{text}</Tag>
    },
    {
      title: '股票名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '市场',
      dataIndex: 'market',
      key: 'market',
      render: (market: string) => (
        <Tag color={market === 'A股' ? 'green' : 'orange'}>{market}</Tag>
      )
    },
    {
      title: '行业',
      dataIndex: 'industry',
      key: 'industry',
    },
    {
      title: '监控状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean, record: Stock) => (
        <Switch
          checked={isActive}
          onChange={(checked) => handleToggleMonitoring(record.id, checked)}
          checkedChildren="监控中"
          unCheckedChildren="已停止"
        />
      )
    },
    {
      title: '添加时间',
      dataIndex: 'created_at',
      key: 'created_at',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: Stock) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            icon={<SyncOutlined />}
            onClick={() => handleSyncData(record.ts_code)}
          >
            同步数据
          </Button>
          <Popconfirm
            title="确定要删除这只股票吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingStock(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (stock: Stock) => {
    setEditingStock(stock);
    form.setFieldsValue(stock);
    setModalVisible(true);
  };

  const handleDelete = (id: number) => {
    setStocks(stocks.filter(stock => stock.id !== id));
    message.success('删除成功');
  };

  const handleToggleMonitoring = (id: number, isActive: boolean) => {
    setStocks(stocks.map(stock => 
      stock.id === id ? { ...stock, is_active: isActive } : stock
    ));
    message.success(isActive ? '已开启监控' : '已停止监控');
  };

  const handleSyncData = (tsCode: string) => {
    setLoading(true);
    // 模拟同步数据
    setTimeout(() => {
      setLoading(false);
      message.success(`${tsCode} 数据同步完成`);
    }, 2000);
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingStock) {
        // 编辑
        setStocks(stocks.map(stock => 
          stock.id === editingStock.id ? { ...stock, ...values } : stock
        ));
        message.success('更新成功');
      } else {
        // 新增
        const newStock: Stock = {
          id: Date.now(),
          ...values,
          created_at: new Date().toISOString().split('T')[0]
        };
        setStocks([...stocks, newStock]);
        message.success('添加成功');
      }
      
      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleModalCancel = () => {
    setModalVisible(false);
  };

  const handleBatchSync = () => {
    const activeStocks = stocks.filter(stock => stock.is_active);
    if (activeStocks.length === 0) {
      message.warning('没有需要同步的股票');
      return;
    }

    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      message.success(`批量同步完成，共同步 ${activeStocks.length} 只股票`);
    }, 3000);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <StockOutlined />
            股票管理
          </Space>
        }
        extra={
          <Space>
            <Button
              type="primary"
              icon={<SyncOutlined />}
              onClick={handleBatchSync}
              loading={loading}
            >
              批量同步
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              添加股票
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={stocks}
          rowKey="id"
          loading={loading}
          pagination={{
            total: stocks.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingStock ? '编辑股票' : '添加股票'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            market: 'A股',
            is_active: true
          }}
        >
          <Form.Item
            name="ts_code"
            label="股票代码"
            rules={[
              { required: true, message: '请输入股票代码' },
              { pattern: /^[0-9]{6}\.(SZ|SH|HK)$/, message: '请输入正确的股票代码格式' }
            ]}
          >
            <Input placeholder="例如：000001.SZ" />
          </Form.Item>

          <Form.Item
            name="name"
            label="股票名称"
            rules={[{ required: true, message: '请输入股票名称' }]}
          >
            <Input placeholder="例如：平安银行" />
          </Form.Item>

          <Form.Item
            name="market"
            label="市场"
            rules={[{ required: true, message: '请选择市场' }]}
          >
            <Select>
              <Option value="A股">A股</Option>
              <Option value="港股">港股</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="industry"
            label="行业"
          >
            <Input placeholder="例如：银行" />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="是否监控"
            valuePropName="checked"
          >
            <Switch checkedChildren="是" unCheckedChildren="否" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default StockManagement;
