{"ast": null, "code": "import { adjustTextY, getIdURL, getMatrixStr, getPathPrecision, getShadow<PERSON>ey, getSRTTransformString, hasShadow, isAroundZero, isGradient, isImagePattern, isLinearGradient, isPattern, isRadialGradient, normalizeColor, round4, TEXT_ALIGN_TO_ANCHOR } from './helper.js';\nimport Path from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport { getLineHeight } from '../contain/text.js';\nimport TSpan from '../graphic/TSpan.js';\nimport SVGPathRebuilder from './SVGPathRebuilder.js';\nimport mapStyleToAttrs from './mapStyleToAttrs.js';\nimport { createVNode, vNodeToString, META_DATA_PREFIX } from './core.js';\nimport { assert, clone, isFunction, isString, logError, map, retrieve2 } from '../core/util.js';\nimport { createOrUpdateImage } from '../graphic/helper/image.js';\nimport { createCSSAnimation } from './cssAnimation.js';\nimport { hasSeparateFont, parseFontSize } from '../graphic/Text.js';\nimport { DEFAULT_FONT, DEFAULT_FONT_FAMILY } from '../core/platform.js';\nimport { createCSSEmphasis } from './cssEmphasis.js';\nimport { getElementSSRData } from '../zrender.js';\nvar round = Math.round;\nfunction isImageLike(val) {\n  return val && isString(val.src);\n}\nfunction isCanvasLike(val) {\n  return val && isFunction(val.toDataURL);\n}\nfunction setStyleAttrs(attrs, style, el, scope) {\n  mapStyleToAttrs(function (key, val) {\n    var isFillStroke = key === 'fill' || key === 'stroke';\n    if (isFillStroke && isGradient(val)) {\n      setGradient(style, attrs, key, scope);\n    } else if (isFillStroke && isPattern(val)) {\n      setPattern(el, attrs, key, scope);\n    } else {\n      attrs[key] = val;\n    }\n    if (isFillStroke && scope.ssr && val === 'none') {\n      attrs['pointer-events'] = 'visible';\n    }\n  }, style, el, false);\n  setShadow(el, attrs, scope);\n}\nfunction setMetaData(attrs, el) {\n  var metaData = getElementSSRData(el);\n  if (metaData) {\n    metaData.each(function (val, key) {\n      val != null && (attrs[(META_DATA_PREFIX + key).toLowerCase()] = val + '');\n    });\n    if (el.isSilent()) {\n      attrs[META_DATA_PREFIX + 'silent'] = 'true';\n    }\n  }\n}\nfunction noRotateScale(m) {\n  return isAroundZero(m[0] - 1) && isAroundZero(m[1]) && isAroundZero(m[2]) && isAroundZero(m[3] - 1);\n}\nfunction noTranslate(m) {\n  return isAroundZero(m[4]) && isAroundZero(m[5]);\n}\nfunction setTransform(attrs, m, compress) {\n  if (m && !(noTranslate(m) && noRotateScale(m))) {\n    var mul = compress ? 10 : 1e4;\n    attrs.transform = noRotateScale(m) ? \"translate(\" + round(m[4] * mul) / mul + \" \" + round(m[5] * mul) / mul + \")\" : getMatrixStr(m);\n  }\n}\nfunction convertPolyShape(shape, attrs, mul) {\n  var points = shape.points;\n  var strArr = [];\n  for (var i = 0; i < points.length; i++) {\n    strArr.push(round(points[i][0] * mul) / mul);\n    strArr.push(round(points[i][1] * mul) / mul);\n  }\n  attrs.points = strArr.join(' ');\n}\nfunction validatePolyShape(shape) {\n  return !shape.smooth;\n}\nfunction createAttrsConvert(desc) {\n  var normalizedDesc = map(desc, function (item) {\n    return typeof item === 'string' ? [item, item] : item;\n  });\n  return function (shape, attrs, mul) {\n    for (var i = 0; i < normalizedDesc.length; i++) {\n      var item = normalizedDesc[i];\n      var val = shape[item[0]];\n      if (val != null) {\n        attrs[item[1]] = round(val * mul) / mul;\n      }\n    }\n  };\n}\nvar builtinShapesDef = {\n  circle: [createAttrsConvert(['cx', 'cy', 'r'])],\n  polyline: [convertPolyShape, validatePolyShape],\n  polygon: [convertPolyShape, validatePolyShape]\n};\nfunction hasShapeAnimation(el) {\n  var animators = el.animators;\n  for (var i = 0; i < animators.length; i++) {\n    if (animators[i].targetName === 'shape') {\n      return true;\n    }\n  }\n  return false;\n}\nexport function brushSVGPath(el, scope) {\n  var style = el.style;\n  var shape = el.shape;\n  var builtinShpDef = builtinShapesDef[el.type];\n  var attrs = {};\n  var needsAnimate = scope.animation;\n  var svgElType = 'path';\n  var strokePercent = el.style.strokePercent;\n  var precision = scope.compress && getPathPrecision(el) || 4;\n  if (builtinShpDef && !scope.willUpdate && !(builtinShpDef[1] && !builtinShpDef[1](shape)) && !(needsAnimate && hasShapeAnimation(el)) && !(strokePercent < 1)) {\n    svgElType = el.type;\n    var mul = Math.pow(10, precision);\n    builtinShpDef[0](shape, attrs, mul);\n  } else {\n    var needBuildPath = !el.path || el.shapeChanged();\n    if (!el.path) {\n      el.createPathProxy();\n    }\n    var path = el.path;\n    if (needBuildPath) {\n      path.beginPath();\n      el.buildPath(path, el.shape);\n      el.pathUpdated();\n    }\n    var pathVersion = path.getVersion();\n    var elExt = el;\n    var svgPathBuilder = elExt.__svgPathBuilder;\n    if (elExt.__svgPathVersion !== pathVersion || !svgPathBuilder || strokePercent !== elExt.__svgPathStrokePercent) {\n      if (!svgPathBuilder) {\n        svgPathBuilder = elExt.__svgPathBuilder = new SVGPathRebuilder();\n      }\n      svgPathBuilder.reset(precision);\n      path.rebuildPath(svgPathBuilder, strokePercent);\n      svgPathBuilder.generateStr();\n      elExt.__svgPathVersion = pathVersion;\n      elExt.__svgPathStrokePercent = strokePercent;\n    }\n    attrs.d = svgPathBuilder.getStr();\n  }\n  setTransform(attrs, el.transform);\n  setStyleAttrs(attrs, style, el, scope);\n  setMetaData(attrs, el);\n  scope.animation && createCSSAnimation(el, attrs, scope);\n  scope.emphasis && createCSSEmphasis(el, attrs, scope);\n  return createVNode(svgElType, el.id + '', attrs);\n}\nexport function brushSVGImage(el, scope) {\n  var style = el.style;\n  var image = style.image;\n  if (image && !isString(image)) {\n    if (isImageLike(image)) {\n      image = image.src;\n    } else if (isCanvasLike(image)) {\n      image = image.toDataURL();\n    }\n  }\n  if (!image) {\n    return;\n  }\n  var x = style.x || 0;\n  var y = style.y || 0;\n  var dw = style.width;\n  var dh = style.height;\n  var attrs = {\n    href: image,\n    width: dw,\n    height: dh\n  };\n  if (x) {\n    attrs.x = x;\n  }\n  if (y) {\n    attrs.y = y;\n  }\n  setTransform(attrs, el.transform);\n  setStyleAttrs(attrs, style, el, scope);\n  setMetaData(attrs, el);\n  scope.animation && createCSSAnimation(el, attrs, scope);\n  return createVNode('image', el.id + '', attrs);\n}\n;\nexport function brushSVGTSpan(el, scope) {\n  var style = el.style;\n  var text = style.text;\n  text != null && (text += '');\n  if (!text || isNaN(style.x) || isNaN(style.y)) {\n    return;\n  }\n  var font = style.font || DEFAULT_FONT;\n  var x = style.x || 0;\n  var y = adjustTextY(style.y || 0, getLineHeight(font), style.textBaseline);\n  var textAlign = TEXT_ALIGN_TO_ANCHOR[style.textAlign] || style.textAlign;\n  var attrs = {\n    'dominant-baseline': 'central',\n    'text-anchor': textAlign\n  };\n  if (hasSeparateFont(style)) {\n    var separatedFontStr = '';\n    var fontStyle = style.fontStyle;\n    var fontSize = parseFontSize(style.fontSize);\n    if (!parseFloat(fontSize)) {\n      return;\n    }\n    var fontFamily = style.fontFamily || DEFAULT_FONT_FAMILY;\n    var fontWeight = style.fontWeight;\n    separatedFontStr += \"font-size:\" + fontSize + \";font-family:\" + fontFamily + \";\";\n    if (fontStyle && fontStyle !== 'normal') {\n      separatedFontStr += \"font-style:\" + fontStyle + \";\";\n    }\n    if (fontWeight && fontWeight !== 'normal') {\n      separatedFontStr += \"font-weight:\" + fontWeight + \";\";\n    }\n    attrs.style = separatedFontStr;\n  } else {\n    attrs.style = \"font: \" + font;\n  }\n  if (text.match(/\\s/)) {\n    attrs['xml:space'] = 'preserve';\n  }\n  if (x) {\n    attrs.x = x;\n  }\n  if (y) {\n    attrs.y = y;\n  }\n  setTransform(attrs, el.transform);\n  setStyleAttrs(attrs, style, el, scope);\n  setMetaData(attrs, el);\n  scope.animation && createCSSAnimation(el, attrs, scope);\n  return createVNode('text', el.id + '', attrs, undefined, text);\n}\nexport function brush(el, scope) {\n  if (el instanceof Path) {\n    return brushSVGPath(el, scope);\n  } else if (el instanceof ZRImage) {\n    return brushSVGImage(el, scope);\n  } else if (el instanceof TSpan) {\n    return brushSVGTSpan(el, scope);\n  }\n}\nfunction setShadow(el, attrs, scope) {\n  var style = el.style;\n  if (hasShadow(style)) {\n    var shadowKey = getShadowKey(el);\n    var shadowCache = scope.shadowCache;\n    var shadowId = shadowCache[shadowKey];\n    if (!shadowId) {\n      var globalScale = el.getGlobalScale();\n      var scaleX = globalScale[0];\n      var scaleY = globalScale[1];\n      if (!scaleX || !scaleY) {\n        return;\n      }\n      var offsetX = style.shadowOffsetX || 0;\n      var offsetY = style.shadowOffsetY || 0;\n      var blur_1 = style.shadowBlur;\n      var _a = normalizeColor(style.shadowColor),\n        opacity = _a.opacity,\n        color = _a.color;\n      var stdDx = blur_1 / 2 / scaleX;\n      var stdDy = blur_1 / 2 / scaleY;\n      var stdDeviation = stdDx + ' ' + stdDy;\n      shadowId = scope.zrId + '-s' + scope.shadowIdx++;\n      scope.defs[shadowId] = createVNode('filter', shadowId, {\n        'id': shadowId,\n        'x': '-100%',\n        'y': '-100%',\n        'width': '300%',\n        'height': '300%'\n      }, [createVNode('feDropShadow', '', {\n        'dx': offsetX / scaleX,\n        'dy': offsetY / scaleY,\n        'stdDeviation': stdDeviation,\n        'flood-color': color,\n        'flood-opacity': opacity\n      })]);\n      shadowCache[shadowKey] = shadowId;\n    }\n    attrs.filter = getIdURL(shadowId);\n  }\n}\nexport function setGradient(style, attrs, target, scope) {\n  var val = style[target];\n  var gradientTag;\n  var gradientAttrs = {\n    'gradientUnits': val.global ? 'userSpaceOnUse' : 'objectBoundingBox'\n  };\n  if (isLinearGradient(val)) {\n    gradientTag = 'linearGradient';\n    gradientAttrs.x1 = val.x;\n    gradientAttrs.y1 = val.y;\n    gradientAttrs.x2 = val.x2;\n    gradientAttrs.y2 = val.y2;\n  } else if (isRadialGradient(val)) {\n    gradientTag = 'radialGradient';\n    gradientAttrs.cx = retrieve2(val.x, 0.5);\n    gradientAttrs.cy = retrieve2(val.y, 0.5);\n    gradientAttrs.r = retrieve2(val.r, 0.5);\n  } else {\n    if (process.env.NODE_ENV !== 'production') {\n      logError('Illegal gradient type.');\n    }\n    return;\n  }\n  var colors = val.colorStops;\n  var colorStops = [];\n  for (var i = 0, len = colors.length; i < len; ++i) {\n    var offset = round4(colors[i].offset) * 100 + '%';\n    var stopColor = colors[i].color;\n    var _a = normalizeColor(stopColor),\n      color = _a.color,\n      opacity = _a.opacity;\n    var stopsAttrs = {\n      'offset': offset\n    };\n    stopsAttrs['stop-color'] = color;\n    if (opacity < 1) {\n      stopsAttrs['stop-opacity'] = opacity;\n    }\n    colorStops.push(createVNode('stop', i + '', stopsAttrs));\n  }\n  var gradientVNode = createVNode(gradientTag, '', gradientAttrs, colorStops);\n  var gradientKey = vNodeToString(gradientVNode);\n  var gradientCache = scope.gradientCache;\n  var gradientId = gradientCache[gradientKey];\n  if (!gradientId) {\n    gradientId = scope.zrId + '-g' + scope.gradientIdx++;\n    gradientCache[gradientKey] = gradientId;\n    gradientAttrs.id = gradientId;\n    scope.defs[gradientId] = createVNode(gradientTag, gradientId, gradientAttrs, colorStops);\n  }\n  attrs[target] = getIdURL(gradientId);\n}\nexport function setPattern(el, attrs, target, scope) {\n  var val = el.style[target];\n  var boundingRect = el.getBoundingRect();\n  var patternAttrs = {};\n  var repeat = val.repeat;\n  var noRepeat = repeat === 'no-repeat';\n  var repeatX = repeat === 'repeat-x';\n  var repeatY = repeat === 'repeat-y';\n  var child;\n  if (isImagePattern(val)) {\n    var imageWidth_1 = val.imageWidth;\n    var imageHeight_1 = val.imageHeight;\n    var imageSrc = void 0;\n    var patternImage = val.image;\n    if (isString(patternImage)) {\n      imageSrc = patternImage;\n    } else if (isImageLike(patternImage)) {\n      imageSrc = patternImage.src;\n    } else if (isCanvasLike(patternImage)) {\n      imageSrc = patternImage.toDataURL();\n    }\n    if (typeof Image === 'undefined') {\n      var errMsg = 'Image width/height must been given explictly in svg-ssr renderer.';\n      assert(imageWidth_1, errMsg);\n      assert(imageHeight_1, errMsg);\n    } else if (imageWidth_1 == null || imageHeight_1 == null) {\n      var setSizeToVNode_1 = function (vNode, img) {\n        if (vNode) {\n          var svgEl = vNode.elm;\n          var width = imageWidth_1 || img.width;\n          var height = imageHeight_1 || img.height;\n          if (vNode.tag === 'pattern') {\n            if (repeatX) {\n              height = 1;\n              width /= boundingRect.width;\n            } else if (repeatY) {\n              width = 1;\n              height /= boundingRect.height;\n            }\n          }\n          vNode.attrs.width = width;\n          vNode.attrs.height = height;\n          if (svgEl) {\n            svgEl.setAttribute('width', width);\n            svgEl.setAttribute('height', height);\n          }\n        }\n      };\n      var createdImage = createOrUpdateImage(imageSrc, null, el, function (img) {\n        noRepeat || setSizeToVNode_1(patternVNode, img);\n        setSizeToVNode_1(child, img);\n      });\n      if (createdImage && createdImage.width && createdImage.height) {\n        imageWidth_1 = imageWidth_1 || createdImage.width;\n        imageHeight_1 = imageHeight_1 || createdImage.height;\n      }\n    }\n    child = createVNode('image', 'img', {\n      href: imageSrc,\n      width: imageWidth_1,\n      height: imageHeight_1\n    });\n    patternAttrs.width = imageWidth_1;\n    patternAttrs.height = imageHeight_1;\n  } else if (val.svgElement) {\n    child = clone(val.svgElement);\n    patternAttrs.width = val.svgWidth;\n    patternAttrs.height = val.svgHeight;\n  }\n  if (!child) {\n    return;\n  }\n  var patternWidth;\n  var patternHeight;\n  if (noRepeat) {\n    patternWidth = patternHeight = 1;\n  } else if (repeatX) {\n    patternHeight = 1;\n    patternWidth = patternAttrs.width / boundingRect.width;\n  } else if (repeatY) {\n    patternWidth = 1;\n    patternHeight = patternAttrs.height / boundingRect.height;\n  } else {\n    patternAttrs.patternUnits = 'userSpaceOnUse';\n  }\n  if (patternWidth != null && !isNaN(patternWidth)) {\n    patternAttrs.width = patternWidth;\n  }\n  if (patternHeight != null && !isNaN(patternHeight)) {\n    patternAttrs.height = patternHeight;\n  }\n  var patternTransform = getSRTTransformString(val);\n  patternTransform && (patternAttrs.patternTransform = patternTransform);\n  var patternVNode = createVNode('pattern', '', patternAttrs, [child]);\n  var patternKey = vNodeToString(patternVNode);\n  var patternCache = scope.patternCache;\n  var patternId = patternCache[patternKey];\n  if (!patternId) {\n    patternId = scope.zrId + '-p' + scope.patternIdx++;\n    patternCache[patternKey] = patternId;\n    patternAttrs.id = patternId;\n    patternVNode = scope.defs[patternId] = createVNode('pattern', patternId, patternAttrs, [child]);\n  }\n  attrs[target] = getIdURL(patternId);\n}\nexport function setClipPath(clipPath, attrs, scope) {\n  var clipPathCache = scope.clipPathCache,\n    defs = scope.defs;\n  var clipPathId = clipPathCache[clipPath.id];\n  if (!clipPathId) {\n    clipPathId = scope.zrId + '-c' + scope.clipPathIdx++;\n    var clipPathAttrs = {\n      id: clipPathId\n    };\n    clipPathCache[clipPath.id] = clipPathId;\n    defs[clipPathId] = createVNode('clipPath', clipPathId, clipPathAttrs, [brushSVGPath(clipPath, scope)]);\n  }\n  attrs['clip-path'] = getIdURL(clipPathId);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}