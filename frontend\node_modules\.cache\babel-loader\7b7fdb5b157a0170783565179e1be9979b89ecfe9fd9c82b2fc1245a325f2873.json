{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nvar ToolboxFeature = /** @class */function () {\n  function ToolboxFeature() {}\n  return ToolboxFeature;\n}();\nexport { ToolboxFeature };\nvar features = {};\nexport function registerFeature(name, ctor) {\n  features[name] = ctor;\n}\nexport function getFeature(name) {\n  return features[name];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}