"""
策略回测引擎
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

from strategy.indicators import get_technical_indicators

logger = logging.getLogger(__name__)

@dataclass
class Position:
    """持仓信息"""
    ts_code: str
    shares: int
    entry_price: float
    entry_date: str
    entry_signal: str

@dataclass
class Trade:
    """交易记录"""
    ts_code: str
    action: str  # BUY/SELL
    shares: int
    price: float
    date: str
    signal: str
    commission: float = 0.0

@dataclass
class BacktestResult:
    """回测结果"""
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    total_trades: int
    profit_trades: int
    loss_trades: int
    avg_profit: float
    avg_loss: float
    profit_factor: float
    trades: List[Trade]
    portfolio_values: pd.Series
    positions: List[Position]

class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, initial_capital: float = 1000000, commission_rate: float = 0.0003):
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.technical_indicators = get_technical_indicators()
        
        # 回测状态
        self.current_capital = initial_capital
        self.positions: Dict[str, Position] = {}
        self.trades: List[Trade] = []
        self.portfolio_values: List[float] = []
        self.dates: List[str] = []
    
    def reset(self):
        """重置回测状态"""
        self.current_capital = self.initial_capital
        self.positions = {}
        self.trades = []
        self.portfolio_values = []
        self.dates = []
    
    def run_backtest(self, 
                    data: Dict[str, pd.DataFrame], 
                    strategy_func: callable,
                    start_date: str = None,
                    end_date: str = None,
                    max_position_size: float = 0.2) -> BacktestResult:
        """运行回测"""
        
        self.reset()
        
        # 数据预处理
        processed_data = self._prepare_data(data, start_date, end_date)
        
        if not processed_data:
            raise ValueError("没有可用的回测数据")
        
        # 获取所有交易日期
        all_dates = set()
        for df in processed_data.values():
            all_dates.update(df['trade_date'].tolist())
        
        all_dates = sorted(list(all_dates))
        
        logger.info(f"开始回测，时间范围: {all_dates[0]} - {all_dates[-1]}")
        
        # 逐日回测
        for date in all_dates:
            self._process_trading_day(date, processed_data, strategy_func, max_position_size)
        
        # 计算回测结果
        result = self._calculate_results()
        
        logger.info(f"回测完成，总收益率: {result.total_return:.2%}")
        return result
    
    def _prepare_data(self, data: Dict[str, pd.DataFrame], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """准备回测数据"""
        processed_data = {}
        
        for ts_code, df in data.items():
            if df.empty:
                continue
            
            # 过滤日期范围
            if start_date:
                df = df[df['trade_date'] >= start_date]
            if end_date:
                df = df[df['trade_date'] <= end_date]
            
            if df.empty:
                continue
            
            # 计算技术指标
            df_with_indicators = self.technical_indicators.calculate_all_indicators(df)
            
            # 确保数据按日期排序
            df_with_indicators = df_with_indicators.sort_values('trade_date')
            
            processed_data[ts_code] = df_with_indicators
        
        return processed_data
    
    def _process_trading_day(self, date: str, data: Dict[str, pd.DataFrame], strategy_func: callable, max_position_size: float):
        """处理单个交易日"""
        
        # 获取当日数据
        day_data = {}
        for ts_code, df in data.items():
            day_row = df[df['trade_date'] == date]
            if not day_row.empty:
                day_data[ts_code] = day_row.iloc[0]
        
        if not day_data:
            return
        
        # 执行策略
        signals = strategy_func(day_data, self.positions)
        
        # 处理交易信号
        for ts_code, signal in signals.items():
            if signal in ['BUY', 'SELL']:
                self._execute_trade(ts_code, signal, day_data[ts_code], max_position_size)
        
        # 记录组合价值
        portfolio_value = self._calculate_portfolio_value(date, day_data)
        self.portfolio_values.append(portfolio_value)
        self.dates.append(date)
    
    def _execute_trade(self, ts_code: str, signal: str, day_data: pd.Series, max_position_size: float):
        """执行交易"""
        
        price = day_data['close']
        date = day_data['trade_date']
        
        if signal == 'BUY' and ts_code not in self.positions:
            # 买入
            max_investment = self.current_capital * max_position_size
            shares = int(max_investment / price)
            
            if shares > 0:
                cost = shares * price
                commission = cost * self.commission_rate
                total_cost = cost + commission
                
                if total_cost <= self.current_capital:
                    # 执行买入
                    self.current_capital -= total_cost
                    self.positions[ts_code] = Position(
                        ts_code=ts_code,
                        shares=shares,
                        entry_price=price,
                        entry_date=date,
                        entry_signal=signal
                    )
                    
                    trade = Trade(
                        ts_code=ts_code,
                        action='BUY',
                        shares=shares,
                        price=price,
                        date=date,
                        signal=signal,
                        commission=commission
                    )
                    self.trades.append(trade)
                    
                    logger.debug(f"买入 {ts_code}: {shares}股 @ {price:.2f}")
        
        elif signal == 'SELL' and ts_code in self.positions:
            # 卖出
            position = self.positions[ts_code]
            shares = position.shares
            
            revenue = shares * price
            commission = revenue * self.commission_rate
            net_revenue = revenue - commission
            
            self.current_capital += net_revenue
            del self.positions[ts_code]
            
            trade = Trade(
                ts_code=ts_code,
                action='SELL',
                shares=shares,
                price=price,
                date=date,
                signal=signal,
                commission=commission
            )
            self.trades.append(trade)
            
            logger.debug(f"卖出 {ts_code}: {shares}股 @ {price:.2f}")
    
    def _calculate_portfolio_value(self, date: str, day_data: Dict[str, pd.Series]) -> float:
        """计算组合价值"""
        total_value = self.current_capital
        
        for ts_code, position in self.positions.items():
            if ts_code in day_data:
                current_price = day_data[ts_code]['close']
                position_value = position.shares * current_price
                total_value += position_value
        
        return total_value
    
    def _calculate_results(self) -> BacktestResult:
        """计算回测结果"""
        
        if not self.portfolio_values:
            raise ValueError("没有组合价值数据")
        
        portfolio_series = pd.Series(self.portfolio_values, index=self.dates)
        
        # 基本收益指标
        total_return = (self.portfolio_values[-1] - self.initial_capital) / self.initial_capital
        
        # 计算年化收益率
        days = len(self.portfolio_values)
        annual_return = (1 + total_return) ** (252 / days) - 1 if days > 0 else 0
        
        # 计算最大回撤
        cumulative_max = portfolio_series.expanding().max()
        drawdown = (portfolio_series - cumulative_max) / cumulative_max
        max_drawdown = drawdown.min()
        
        # 计算夏普比率
        returns = portfolio_series.pct_change().dropna()
        sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
        
        # 交易统计
        buy_trades = [t for t in self.trades if t.action == 'BUY']
        sell_trades = [t for t in self.trades if t.action == 'SELL']
        
        # 计算盈亏交易
        profit_trades = 0
        loss_trades = 0
        profits = []
        losses = []
        
        for sell_trade in sell_trades:
            # 找到对应的买入交易
            buy_trade = None
            for bt in buy_trades:
                if bt.ts_code == sell_trade.ts_code and bt.date <= sell_trade.date:
                    buy_trade = bt
                    break
            
            if buy_trade:
                profit = (sell_trade.price - buy_trade.price) * sell_trade.shares - sell_trade.commission - buy_trade.commission
                if profit > 0:
                    profit_trades += 1
                    profits.append(profit)
                else:
                    loss_trades += 1
                    losses.append(abs(profit))
        
        # 胜率和盈亏比
        total_trades = profit_trades + loss_trades
        win_rate = profit_trades / total_trades if total_trades > 0 else 0
        avg_profit = np.mean(profits) if profits else 0
        avg_loss = np.mean(losses) if losses else 0
        profit_factor = sum(profits) / sum(losses) if losses else float('inf')
        
        return BacktestResult(
            total_return=total_return,
            annual_return=annual_return,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            win_rate=win_rate,
            total_trades=total_trades,
            profit_trades=profit_trades,
            loss_trades=loss_trades,
            avg_profit=avg_profit,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            trades=self.trades,
            portfolio_values=portfolio_series,
            positions=list(self.positions.values())
        )

# 全局回测引擎实例
backtest_engine = BacktestEngine()

def get_backtest_engine() -> BacktestEngine:
    """获取回测引擎实例"""
    return backtest_engine
