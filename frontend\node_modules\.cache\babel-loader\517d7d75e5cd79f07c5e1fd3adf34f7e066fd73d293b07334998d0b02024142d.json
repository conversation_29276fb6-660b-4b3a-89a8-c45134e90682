{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\n// import AngleAxis from './AngleAxis.js';\nfunction dataToCoordSize(dataSize, dataItem) {\n  // dataItem is necessary in log axis.\n  dataItem = dataItem || [0, 0];\n  return zrUtil.map(['Radius', 'Angle'], function (dim, dimIdx) {\n    var getterName = 'get' + dim + 'Axis';\n    // TODO: TYPE Check Angle Axis\n    var axis = this[getterName]();\n    var val = dataItem[dimIdx];\n    var halfSize = dataSize[dimIdx] / 2;\n    var result = axis.type === 'category' ? axis.getBandWidth() : Math.abs(axis.dataToCoord(val - halfSize) - axis.dataToCoord(val + halfSize));\n    if (dim === 'Angle') {\n      result = result * Math.PI / 180;\n    }\n    return result;\n  }, this);\n}\nexport default function polarPrepareCustom(coordSys) {\n  var radiusAxis = coordSys.getRadiusAxis();\n  var angleAxis = coordSys.getAngleAxis();\n  var radius = radiusAxis.getExtent();\n  radius[0] > radius[1] && radius.reverse();\n  return {\n    coordSys: {\n      type: 'polar',\n      cx: coordSys.cx,\n      cy: coordSys.cy,\n      r: radius[1],\n      r0: radius[0]\n    },\n    api: {\n      coord: function (data) {\n        var radius = radiusAxis.dataToRadius(data[0]);\n        var angle = angleAxis.dataToAngle(data[1]);\n        var coord = coordSys.coordToPoint([radius, angle]);\n        coord.push(radius, angle * Math.PI / 180);\n        return coord;\n      },\n      size: zrUtil.bind(dataToCoordSize, coordSys)\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}