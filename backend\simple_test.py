"""
简化的Tushare测试
"""
import tushare as ts

# 配置token
TUSHARE_TOKEN = "772e043e246ef24189447f73f0c276e7fc98f18a0cb7cce72bd0f28d"
print(f"使用Token: {TUSHARE_TOKEN[:20]}...")

try:
    ts.set_token(TUSHARE_TOKEN)
    pro = ts.pro_api()
    print("✅ Token设置成功")
    
    # 简单测试
    print("正在测试基础连接...")
    cal = pro.trade_cal(exchange='SSE', start_date='20241201', end_date='20241205')
    print(f"✅ 基础连接成功，获取到 {len(cal)} 条交易日历数据")
    
    print("正在测试A股数据...")
    daily_data = pro.daily(ts_code='000001.SZ', start_date='20241201', end_date='20241205')
    print(f"✅ A股数据获取成功，获取到 {len(daily_data)} 条数据")
    
    print("🎉 Tushare API测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    print("可能的原因：")
    print("1. 网络连接问题")
    print("2. Token权限不足")
    print("3. API调用频率限制")
