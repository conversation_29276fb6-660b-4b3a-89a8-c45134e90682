{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as graphic from '../../util/graphic.js';\nimport { round } from '../../util/number.js';\nimport { isFunction } from 'zrender/lib/core/util.js';\nfunction createGridClipPath(cartesian, hasAnimation, seriesModel, done, during) {\n  var rect = cartesian.getArea();\n  var x = rect.x;\n  var y = rect.y;\n  var width = rect.width;\n  var height = rect.height;\n  var lineWidth = seriesModel.get(['lineStyle', 'width']) || 0;\n  // Expand the clip path a bit to avoid the border is clipped and looks thinner\n  x -= lineWidth / 2;\n  y -= lineWidth / 2;\n  width += lineWidth;\n  height += lineWidth;\n  // fix: https://github.com/apache/incubator-echarts/issues/11369\n  width = Math.ceil(width);\n  if (x !== Math.floor(x)) {\n    x = Math.floor(x);\n    // if no extra 1px on `width`, it will still be clipped since `x` is floored\n    width++;\n  }\n  var clipPath = new graphic.Rect({\n    shape: {\n      x: x,\n      y: y,\n      width: width,\n      height: height\n    }\n  });\n  if (hasAnimation) {\n    var baseAxis = cartesian.getBaseAxis();\n    var isHorizontal = baseAxis.isHorizontal();\n    var isAxisInversed = baseAxis.inverse;\n    if (isHorizontal) {\n      if (isAxisInversed) {\n        clipPath.shape.x += width;\n      }\n      clipPath.shape.width = 0;\n    } else {\n      if (!isAxisInversed) {\n        clipPath.shape.y += height;\n      }\n      clipPath.shape.height = 0;\n    }\n    var duringCb = isFunction(during) ? function (percent) {\n      during(percent, clipPath);\n    } : null;\n    graphic.initProps(clipPath, {\n      shape: {\n        width: width,\n        height: height,\n        x: x,\n        y: y\n      }\n    }, seriesModel, null, done, duringCb);\n  }\n  return clipPath;\n}\nfunction createPolarClipPath(polar, hasAnimation, seriesModel) {\n  var sectorArea = polar.getArea();\n  // Avoid float number rounding error for symbol on the edge of axis extent.\n  var r0 = round(sectorArea.r0, 1);\n  var r = round(sectorArea.r, 1);\n  var clipPath = new graphic.Sector({\n    shape: {\n      cx: round(polar.cx, 1),\n      cy: round(polar.cy, 1),\n      r0: r0,\n      r: r,\n      startAngle: sectorArea.startAngle,\n      endAngle: sectorArea.endAngle,\n      clockwise: sectorArea.clockwise\n    }\n  });\n  if (hasAnimation) {\n    var isRadial = polar.getBaseAxis().dim === 'angle';\n    if (isRadial) {\n      clipPath.shape.endAngle = sectorArea.startAngle;\n    } else {\n      clipPath.shape.r = r0;\n    }\n    graphic.initProps(clipPath, {\n      shape: {\n        endAngle: sectorArea.endAngle,\n        r: r\n      }\n    }, seriesModel);\n  }\n  return clipPath;\n}\nfunction createClipPath(coordSys, hasAnimation, seriesModel, done, during) {\n  if (!coordSys) {\n    return null;\n  } else if (coordSys.type === 'polar') {\n    return createPolarClipPath(coordSys, hasAnimation, seriesModel);\n  } else if (coordSys.type === 'cartesian2d') {\n    return createGridClipPath(coordSys, hasAnimation, seriesModel, done, during);\n  }\n  return null;\n}\nexport { createGridClipPath, createPolarClipPath, createClipPath };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}