import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Table, Tag, Progress, Alert } from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  DollarOutlined,
  StockOutlined,
  WarningOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';

interface MarketData {
  ts_code: string;
  name: string;
  price: number;
  change: number;
  change_pct: number;
  volume: number;
}

interface RiskAlert {
  id: string;
  type: string;
  level: string;
  message: string;
  time: string;
}

const Dashboard: React.FC = () => {
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [riskAlerts, setRiskAlerts] = useState<RiskAlert[]>([]);
  const [loading, setLoading] = useState(true);

  // 模拟数据
  useEffect(() => {
    const mockMarketData: MarketData[] = [
      { ts_code: '000001.SZ', name: '平安银行', price: 12.45, change: 0.15, change_pct: 1.22, volume: 1234567 },
      { ts_code: '000002.SZ', name: '万科A', price: 18.76, change: -0.23, change_pct: -1.21, volume: 987654 },
      { ts_code: '600000.SH', name: '浦发银行', price: 9.87, change: 0.08, change_pct: 0.82, volume: 2345678 },
      { ts_code: '600036.SH', name: '招商银行', price: 35.42, change: 0.67, change_pct: 1.93, volume: 1876543 },
      { ts_code: '000858.SZ', name: '五粮液', price: 128.90, change: -2.10, change_pct: -1.60, volume: 654321 },
    ];

    const mockRiskAlerts: RiskAlert[] = [
      { id: '1', type: '持仓风险', level: 'HIGH', message: '平安银行持仓比例过高 (15.2%)', time: '10:30' },
      { id: '2', type: '波动率', level: 'MEDIUM', message: '组合波动率超过阈值 (28.5%)', time: '09:45' },
      { id: '3', type: '回撤', level: 'LOW', message: '当前回撤 3.2%，接近预警线', time: '09:15' },
    ];

    setTimeout(() => {
      setMarketData(mockMarketData);
      setRiskAlerts(mockRiskAlerts);
      setLoading(false);
    }, 1000);
  }, []);

  // 表格列定义
  const columns = [
    {
      title: '股票代码',
      dataIndex: 'ts_code',
      key: 'ts_code',
    },
    {
      title: '股票名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '当前价格',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => `¥${price.toFixed(2)}`,
    },
    {
      title: '涨跌额',
      dataIndex: 'change',
      key: 'change',
      render: (change: number) => (
        <span style={{ color: change >= 0 ? '#3f8600' : '#cf1322' }}>
          {change >= 0 ? '+' : ''}{change.toFixed(2)}
        </span>
      ),
    },
    {
      title: '涨跌幅',
      dataIndex: 'change_pct',
      key: 'change_pct',
      render: (pct: number) => (
        <span style={{ color: pct >= 0 ? '#3f8600' : '#cf1322' }}>
          {pct >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
          {Math.abs(pct).toFixed(2)}%
        </span>
      ),
    },
    {
      title: '成交量',
      dataIndex: 'volume',
      key: 'volume',
      render: (volume: number) => (volume / 10000).toFixed(1) + '万',
    },
  ];

  // 风险告警列定义
  const alertColumns = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '等级',
      dataIndex: 'level',
      key: 'level',
      render: (level: string) => {
        const color = level === 'HIGH' ? 'red' : level === 'MEDIUM' ? 'orange' : 'blue';
        return <Tag color={color}>{level}</Tag>;
      },
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
    },
  ];

  // 图表配置
  const chartOption = {
    title: {
      text: '组合净值走势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['09:30', '10:00', '10:30', '11:00', '11:30', '13:00', '13:30', '14:00', '14:30', '15:00']
    },
    yAxis: {
      type: 'value',
      scale: true
    },
    series: [{
      name: '净值',
      type: 'line',
      data: [1.000, 1.002, 0.998, 1.005, 1.008, 1.012, 1.015, 1.018, 1.020, 1.025],
      smooth: true,
      lineStyle: {
        color: '#1890ff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(24, 144, 255, 0.3)'
          }, {
            offset: 1, color: 'rgba(24, 144, 255, 0.1)'
          }]
        }
      }
    }]
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总资产"
              value={1250000}
              precision={2}
              valueStyle={{ color: '#3f8600' }}
              prefix={<DollarOutlined />}
              suffix="元"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日收益"
              value={12500}
              precision={2}
              valueStyle={{ color: '#3f8600' }}
              prefix={<ArrowUpOutlined />}
              suffix="元"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="持仓股票"
              value={5}
              valueStyle={{ color: '#1890ff' }}
              prefix={<StockOutlined />}
              suffix="只"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="风险评分"
              value={35}
              valueStyle={{ color: '#faad14' }}
              prefix={<WarningOutlined />}
              suffix="/100"
            />
            <Progress percent={35} showInfo={false} strokeColor="#faad14" />
          </Card>
        </Col>
      </Row>

      {/* 图表和告警 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={16}>
          <Card title="组合净值走势" extra={<ArrowUpOutlined />}>
            <ReactECharts option={chartOption} style={{ height: '300px' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="风险告警" extra={<WarningOutlined />}>
            <div style={{ height: '300px', overflowY: 'auto' }}>
              {riskAlerts.map(alert => (
                <Alert
                  key={alert.id}
                  message={alert.type}
                  description={alert.message}
                  type={alert.level === 'HIGH' ? 'error' : alert.level === 'MEDIUM' ? 'warning' : 'info'}
                  showIcon
                  style={{ marginBottom: '8px' }}
                />
              ))}
            </div>
          </Card>
        </Col>
      </Row>

      {/* 股票列表 */}
      <Row>
        <Col span={24}>
          <Card title="持仓股票" extra="实时更新">
            <Table
              columns={columns}
              dataSource={marketData}
              rowKey="ts_code"
              loading={loading}
              pagination={false}
              size="middle"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
