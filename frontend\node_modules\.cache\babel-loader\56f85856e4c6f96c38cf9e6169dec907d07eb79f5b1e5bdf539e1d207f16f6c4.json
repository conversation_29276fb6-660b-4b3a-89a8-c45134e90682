{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { assert, clone, createHashMap, isFunction, keys, map, reduce } from 'zrender/lib/core/util.js';\nimport { parseDataValue } from './helper/dataValueHelper.js';\nimport { shouldRetrieveDataByName } from './Source.js';\nvar UNDEFINED = 'undefined';\n/* global Float64Array, Int32Array, Uint32Array, Uint16Array */\n// Caution: MUST not use `new CtorUint32Array(arr, 0, len)`, because the Ctor of array is\n// different from the Ctor of typed array.\nexport var CtorUint32Array = typeof Uint32Array === UNDEFINED ? Array : Uint32Array;\nexport var CtorUint16Array = typeof Uint16Array === UNDEFINED ? Array : Uint16Array;\nexport var CtorInt32Array = typeof Int32Array === UNDEFINED ? Array : Int32Array;\nexport var CtorFloat64Array = typeof Float64Array === UNDEFINED ? Array : Float64Array;\n/**\r\n * Multi dimensional data store\r\n */\nvar dataCtors = {\n  'float': CtorFloat64Array,\n  'int': CtorInt32Array,\n  // Ordinal data type can be string or int\n  'ordinal': Array,\n  'number': Array,\n  'time': CtorFloat64Array\n};\nvar defaultDimValueGetters;\nfunction getIndicesCtor(rawCount) {\n  // The possible max value in this._indicies is always this._rawCount despite of filtering.\n  return rawCount > 65535 ? CtorUint32Array : CtorUint16Array;\n}\n;\nfunction getInitialExtent() {\n  return [Infinity, -Infinity];\n}\n;\nfunction cloneChunk(originalChunk) {\n  var Ctor = originalChunk.constructor;\n  // Only shallow clone is enough when Array.\n  return Ctor === Array ? originalChunk.slice() : new Ctor(originalChunk);\n}\nfunction prepareStore(store, dimIdx, dimType, end, append) {\n  var DataCtor = dataCtors[dimType || 'float'];\n  if (append) {\n    var oldStore = store[dimIdx];\n    var oldLen = oldStore && oldStore.length;\n    if (!(oldLen === end)) {\n      var newStore = new DataCtor(end);\n      // The cost of the copy is probably inconsiderable\n      // within the initial chunkSize.\n      for (var j = 0; j < oldLen; j++) {\n        newStore[j] = oldStore[j];\n      }\n      store[dimIdx] = newStore;\n    }\n  } else {\n    store[dimIdx] = new DataCtor(end);\n  }\n}\n;\n/**\r\n * Basically, DataStore API keep immutable.\r\n */\nvar DataStore = /** @class */function () {\n  function DataStore() {\n    this._chunks = [];\n    // It will not be calculated until needed.\n    this._rawExtent = [];\n    this._extent = [];\n    this._count = 0;\n    this._rawCount = 0;\n    this._calcDimNameToIdx = createHashMap();\n  }\n  /**\r\n   * Initialize from data\r\n   */\n  DataStore.prototype.initData = function (provider, inputDimensions, dimValueGetter) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(isFunction(provider.getItem) && isFunction(provider.count), 'Invalid data provider.');\n    }\n    this._provider = provider;\n    // Clear\n    this._chunks = [];\n    this._indices = null;\n    this.getRawIndex = this._getRawIdxIdentity;\n    var source = provider.getSource();\n    var defaultGetter = this.defaultDimValueGetter = defaultDimValueGetters[source.sourceFormat];\n    // Default dim value getter\n    this._dimValueGetter = dimValueGetter || defaultGetter;\n    // Reset raw extent.\n    this._rawExtent = [];\n    var willRetrieveDataByName = shouldRetrieveDataByName(source);\n    this._dimensions = map(inputDimensions, function (dim) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (willRetrieveDataByName) {\n          assert(dim.property != null);\n        }\n      }\n      return {\n        // Only pick these two props. Not leak other properties like orderMeta.\n        type: dim.type,\n        property: dim.property\n      };\n    });\n    this._initDataFromProvider(0, provider.count());\n  };\n  DataStore.prototype.getProvider = function () {\n    return this._provider;\n  };\n  /**\r\n   * Caution: even when a `source` instance owned by a series, the created data store\r\n   * may still be shared by different sereis (the source hash does not use all `source`\r\n   * props, see `sourceManager`). In this case, the `source` props that are not used in\r\n   * hash (like `source.dimensionDefine`) probably only belongs to a certain series and\r\n   * thus should not be fetch here.\r\n   */\n  DataStore.prototype.getSource = function () {\n    return this._provider.getSource();\n  };\n  /**\r\n   * @caution Only used in dataStack.\r\n   */\n  DataStore.prototype.ensureCalculationDimension = function (dimName, type) {\n    var calcDimNameToIdx = this._calcDimNameToIdx;\n    var dimensions = this._dimensions;\n    var calcDimIdx = calcDimNameToIdx.get(dimName);\n    if (calcDimIdx != null) {\n      if (dimensions[calcDimIdx].type === type) {\n        return calcDimIdx;\n      }\n    } else {\n      calcDimIdx = dimensions.length;\n    }\n    dimensions[calcDimIdx] = {\n      type: type\n    };\n    calcDimNameToIdx.set(dimName, calcDimIdx);\n    this._chunks[calcDimIdx] = new dataCtors[type || 'float'](this._rawCount);\n    this._rawExtent[calcDimIdx] = getInitialExtent();\n    return calcDimIdx;\n  };\n  DataStore.prototype.collectOrdinalMeta = function (dimIdx, ordinalMeta) {\n    var chunk = this._chunks[dimIdx];\n    var dim = this._dimensions[dimIdx];\n    var rawExtents = this._rawExtent;\n    var offset = dim.ordinalOffset || 0;\n    var len = chunk.length;\n    if (offset === 0) {\n      // We need to reset the rawExtent if collect is from start.\n      // Because this dimension may be guessed as number and calcuating a wrong extent.\n      rawExtents[dimIdx] = getInitialExtent();\n    }\n    var dimRawExtent = rawExtents[dimIdx];\n    // Parse from previous data offset. len may be changed after appendData\n    for (var i = offset; i < len; i++) {\n      var val = chunk[i] = ordinalMeta.parseAndCollect(chunk[i]);\n      if (!isNaN(val)) {\n        dimRawExtent[0] = Math.min(val, dimRawExtent[0]);\n        dimRawExtent[1] = Math.max(val, dimRawExtent[1]);\n      }\n    }\n    dim.ordinalMeta = ordinalMeta;\n    dim.ordinalOffset = len;\n    dim.type = 'ordinal'; // Force to be ordinal\n  };\n  DataStore.prototype.getOrdinalMeta = function (dimIdx) {\n    var dimInfo = this._dimensions[dimIdx];\n    var ordinalMeta = dimInfo.ordinalMeta;\n    return ordinalMeta;\n  };\n  DataStore.prototype.getDimensionProperty = function (dimIndex) {\n    var item = this._dimensions[dimIndex];\n    return item && item.property;\n  };\n  /**\r\n   * Caution: Can be only called on raw data (before `this._indices` created).\r\n   */\n  DataStore.prototype.appendData = function (data) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(!this._indices, 'appendData can only be called on raw data.');\n    }\n    var provider = this._provider;\n    var start = this.count();\n    provider.appendData(data);\n    var end = provider.count();\n    if (!provider.persistent) {\n      end += start;\n    }\n    if (start < end) {\n      this._initDataFromProvider(start, end, true);\n    }\n    return [start, end];\n  };\n  DataStore.prototype.appendValues = function (values, minFillLen) {\n    var chunks = this._chunks;\n    var dimensions = this._dimensions;\n    var dimLen = dimensions.length;\n    var rawExtent = this._rawExtent;\n    var start = this.count();\n    var end = start + Math.max(values.length, minFillLen || 0);\n    for (var i = 0; i < dimLen; i++) {\n      var dim = dimensions[i];\n      prepareStore(chunks, i, dim.type, end, true);\n    }\n    var emptyDataItem = [];\n    for (var idx = start; idx < end; idx++) {\n      var sourceIdx = idx - start;\n      // Store the data by dimensions\n      for (var dimIdx = 0; dimIdx < dimLen; dimIdx++) {\n        var dim = dimensions[dimIdx];\n        var val = defaultDimValueGetters.arrayRows.call(this, values[sourceIdx] || emptyDataItem, dim.property, sourceIdx, dimIdx);\n        chunks[dimIdx][idx] = val;\n        var dimRawExtent = rawExtent[dimIdx];\n        val < dimRawExtent[0] && (dimRawExtent[0] = val);\n        val > dimRawExtent[1] && (dimRawExtent[1] = val);\n      }\n    }\n    this._rawCount = this._count = end;\n    return {\n      start: start,\n      end: end\n    };\n  };\n  DataStore.prototype._initDataFromProvider = function (start, end, append) {\n    var provider = this._provider;\n    var chunks = this._chunks;\n    var dimensions = this._dimensions;\n    var dimLen = dimensions.length;\n    var rawExtent = this._rawExtent;\n    var dimNames = map(dimensions, function (dim) {\n      return dim.property;\n    });\n    for (var i = 0; i < dimLen; i++) {\n      var dim = dimensions[i];\n      if (!rawExtent[i]) {\n        rawExtent[i] = getInitialExtent();\n      }\n      prepareStore(chunks, i, dim.type, end, append);\n    }\n    if (provider.fillStorage) {\n      provider.fillStorage(start, end, chunks, rawExtent);\n    } else {\n      var dataItem = [];\n      for (var idx = start; idx < end; idx++) {\n        // NOTICE: Try not to write things into dataItem\n        dataItem = provider.getItem(idx, dataItem);\n        // Each data item is value\n        // [1, 2]\n        // 2\n        // Bar chart, line chart which uses category axis\n        // only gives the 'y' value. 'x' value is the indices of category\n        // Use a tempValue to normalize the value to be a (x, y) value\n        // Store the data by dimensions\n        for (var dimIdx = 0; dimIdx < dimLen; dimIdx++) {\n          var dimStorage = chunks[dimIdx];\n          // PENDING NULL is empty or zero\n          var val = this._dimValueGetter(dataItem, dimNames[dimIdx], idx, dimIdx);\n          dimStorage[idx] = val;\n          var dimRawExtent = rawExtent[dimIdx];\n          val < dimRawExtent[0] && (dimRawExtent[0] = val);\n          val > dimRawExtent[1] && (dimRawExtent[1] = val);\n        }\n      }\n    }\n    if (!provider.persistent && provider.clean) {\n      // Clean unused data if data source is typed array.\n      provider.clean();\n    }\n    this._rawCount = this._count = end;\n    // Reset data extent\n    this._extent = [];\n  };\n  DataStore.prototype.count = function () {\n    return this._count;\n  };\n  /**\r\n   * Get value. Return NaN if idx is out of range.\r\n   */\n  DataStore.prototype.get = function (dim, idx) {\n    if (!(idx >= 0 && idx < this._count)) {\n      return NaN;\n    }\n    var dimStore = this._chunks[dim];\n    return dimStore ? dimStore[this.getRawIndex(idx)] : NaN;\n  };\n  DataStore.prototype.getValues = function (dimensions, idx) {\n    var values = [];\n    var dimArr = [];\n    if (idx == null) {\n      idx = dimensions;\n      // TODO get all from store?\n      dimensions = [];\n      // All dimensions\n      for (var i = 0; i < this._dimensions.length; i++) {\n        dimArr.push(i);\n      }\n    } else {\n      dimArr = dimensions;\n    }\n    for (var i = 0, len = dimArr.length; i < len; i++) {\n      values.push(this.get(dimArr[i], idx));\n    }\n    return values;\n  };\n  /**\r\n   * @param dim concrete dim\r\n   */\n  DataStore.prototype.getByRawIndex = function (dim, rawIdx) {\n    if (!(rawIdx >= 0 && rawIdx < this._rawCount)) {\n      return NaN;\n    }\n    var dimStore = this._chunks[dim];\n    return dimStore ? dimStore[rawIdx] : NaN;\n  };\n  /**\r\n   * Get sum of data in one dimension\r\n   */\n  DataStore.prototype.getSum = function (dim) {\n    var dimData = this._chunks[dim];\n    var sum = 0;\n    if (dimData) {\n      for (var i = 0, len = this.count(); i < len; i++) {\n        var value = this.get(dim, i);\n        if (!isNaN(value)) {\n          sum += value;\n        }\n      }\n    }\n    return sum;\n  };\n  /**\r\n   * Get median of data in one dimension\r\n   */\n  DataStore.prototype.getMedian = function (dim) {\n    var dimDataArray = [];\n    // map all data of one dimension\n    this.each([dim], function (val) {\n      if (!isNaN(val)) {\n        dimDataArray.push(val);\n      }\n    });\n    // TODO\n    // Use quick select?\n    var sortedDimDataArray = dimDataArray.sort(function (a, b) {\n      return a - b;\n    });\n    var len = this.count();\n    // calculate median\n    return len === 0 ? 0 : len % 2 === 1 ? sortedDimDataArray[(len - 1) / 2] : (sortedDimDataArray[len / 2] + sortedDimDataArray[len / 2 - 1]) / 2;\n  };\n  /**\r\n   * Retrieve the index with given raw data index.\r\n   */\n  DataStore.prototype.indexOfRawIndex = function (rawIndex) {\n    if (rawIndex >= this._rawCount || rawIndex < 0) {\n      return -1;\n    }\n    if (!this._indices) {\n      return rawIndex;\n    }\n    // Indices are ascending\n    var indices = this._indices;\n    // If rawIndex === dataIndex\n    var rawDataIndex = indices[rawIndex];\n    if (rawDataIndex != null && rawDataIndex < this._count && rawDataIndex === rawIndex) {\n      return rawIndex;\n    }\n    var left = 0;\n    var right = this._count - 1;\n    while (left <= right) {\n      var mid = (left + right) / 2 | 0;\n      if (indices[mid] < rawIndex) {\n        left = mid + 1;\n      } else if (indices[mid] > rawIndex) {\n        right = mid - 1;\n      } else {\n        return mid;\n      }\n    }\n    return -1;\n  };\n  /**\r\n   * Retrieve the index of nearest value.\r\n   * @param dim\r\n   * @param value\r\n   * @param [maxDistance=Infinity]\r\n   * @return If and only if multiple indices have\r\n   *         the same value, they are put to the result.\r\n   */\n  DataStore.prototype.indicesOfNearest = function (dim, value, maxDistance) {\n    var chunks = this._chunks;\n    var dimData = chunks[dim];\n    var nearestIndices = [];\n    if (!dimData) {\n      return nearestIndices;\n    }\n    if (maxDistance == null) {\n      maxDistance = Infinity;\n    }\n    var minDist = Infinity;\n    var minDiff = -1;\n    var nearestIndicesLen = 0;\n    // Check the test case of `test/ut/spec/data/SeriesData.js`.\n    for (var i = 0, len = this.count(); i < len; i++) {\n      var dataIndex = this.getRawIndex(i);\n      var diff = value - dimData[dataIndex];\n      var dist = Math.abs(diff);\n      if (dist <= maxDistance) {\n        // When the `value` is at the middle of `this.get(dim, i)` and `this.get(dim, i+1)`,\n        // we'd better not push both of them to `nearestIndices`, otherwise it is easy to\n        // get more than one item in `nearestIndices` (more specifically, in `tooltip`).\n        // So we choose the one that `diff >= 0` in this case.\n        // But if `this.get(dim, i)` and `this.get(dim, j)` get the same value, both of them\n        // should be push to `nearestIndices`.\n        if (dist < minDist || dist === minDist && diff >= 0 && minDiff < 0) {\n          minDist = dist;\n          minDiff = diff;\n          nearestIndicesLen = 0;\n        }\n        if (diff === minDiff) {\n          nearestIndices[nearestIndicesLen++] = i;\n        }\n      }\n    }\n    nearestIndices.length = nearestIndicesLen;\n    return nearestIndices;\n  };\n  DataStore.prototype.getIndices = function () {\n    var newIndices;\n    var indices = this._indices;\n    if (indices) {\n      var Ctor = indices.constructor;\n      var thisCount = this._count;\n      // `new Array(a, b, c)` is different from `new Uint32Array(a, b, c)`.\n      if (Ctor === Array) {\n        newIndices = new Ctor(thisCount);\n        for (var i = 0; i < thisCount; i++) {\n          newIndices[i] = indices[i];\n        }\n      } else {\n        newIndices = new Ctor(indices.buffer, 0, thisCount);\n      }\n    } else {\n      var Ctor = getIndicesCtor(this._rawCount);\n      newIndices = new Ctor(this.count());\n      for (var i = 0; i < newIndices.length; i++) {\n        newIndices[i] = i;\n      }\n    }\n    return newIndices;\n  };\n  /**\r\n   * Data filter.\r\n   */\n  DataStore.prototype.filter = function (dims, cb) {\n    if (!this._count) {\n      return this;\n    }\n    var newStore = this.clone();\n    var count = newStore.count();\n    var Ctor = getIndicesCtor(newStore._rawCount);\n    var newIndices = new Ctor(count);\n    var value = [];\n    var dimSize = dims.length;\n    var offset = 0;\n    var dim0 = dims[0];\n    var chunks = newStore._chunks;\n    for (var i = 0; i < count; i++) {\n      var keep = void 0;\n      var rawIdx = newStore.getRawIndex(i);\n      // Simple optimization\n      if (dimSize === 0) {\n        keep = cb(i);\n      } else if (dimSize === 1) {\n        var val = chunks[dim0][rawIdx];\n        keep = cb(val, i);\n      } else {\n        var k = 0;\n        for (; k < dimSize; k++) {\n          value[k] = chunks[dims[k]][rawIdx];\n        }\n        value[k] = i;\n        keep = cb.apply(null, value);\n      }\n      if (keep) {\n        newIndices[offset++] = rawIdx;\n      }\n    }\n    // Set indices after filtered.\n    if (offset < count) {\n      newStore._indices = newIndices;\n    }\n    newStore._count = offset;\n    // Reset data extent\n    newStore._extent = [];\n    newStore._updateGetRawIdx();\n    return newStore;\n  };\n  /**\r\n   * Select data in range. (For optimization of filter)\r\n   * (Manually inline code, support 5 million data filtering in data zoom.)\r\n   */\n  DataStore.prototype.selectRange = function (range) {\n    var newStore = this.clone();\n    var len = newStore._count;\n    if (!len) {\n      return this;\n    }\n    var dims = keys(range);\n    var dimSize = dims.length;\n    if (!dimSize) {\n      return this;\n    }\n    var originalCount = newStore.count();\n    var Ctor = getIndicesCtor(newStore._rawCount);\n    var newIndices = new Ctor(originalCount);\n    var offset = 0;\n    var dim0 = dims[0];\n    var min = range[dim0][0];\n    var max = range[dim0][1];\n    var storeArr = newStore._chunks;\n    var quickFinished = false;\n    if (!newStore._indices) {\n      // Extreme optimization for common case. About 2x faster in chrome.\n      var idx = 0;\n      if (dimSize === 1) {\n        var dimStorage = storeArr[dims[0]];\n        for (var i = 0; i < len; i++) {\n          var val = dimStorage[i];\n          // NaN will not be filtered. Consider the case, in line chart, empty\n          // value indicates the line should be broken. But for the case like\n          // scatter plot, a data item with empty value will not be rendered,\n          // but the axis extent may be effected if some other dim of the data\n          // item has value. Fortunately it is not a significant negative effect.\n          if (val >= min && val <= max || isNaN(val)) {\n            newIndices[offset++] = idx;\n          }\n          idx++;\n        }\n        quickFinished = true;\n      } else if (dimSize === 2) {\n        var dimStorage = storeArr[dims[0]];\n        var dimStorage2 = storeArr[dims[1]];\n        var min2 = range[dims[1]][0];\n        var max2 = range[dims[1]][1];\n        for (var i = 0; i < len; i++) {\n          var val = dimStorage[i];\n          var val2 = dimStorage2[i];\n          // Do not filter NaN, see comment above.\n          if ((val >= min && val <= max || isNaN(val)) && (val2 >= min2 && val2 <= max2 || isNaN(val2))) {\n            newIndices[offset++] = idx;\n          }\n          idx++;\n        }\n        quickFinished = true;\n      }\n    }\n    if (!quickFinished) {\n      if (dimSize === 1) {\n        for (var i = 0; i < originalCount; i++) {\n          var rawIndex = newStore.getRawIndex(i);\n          var val = storeArr[dims[0]][rawIndex];\n          // Do not filter NaN, see comment above.\n          if (val >= min && val <= max || isNaN(val)) {\n            newIndices[offset++] = rawIndex;\n          }\n        }\n      } else {\n        for (var i = 0; i < originalCount; i++) {\n          var keep = true;\n          var rawIndex = newStore.getRawIndex(i);\n          for (var k = 0; k < dimSize; k++) {\n            var dimk = dims[k];\n            var val = storeArr[dimk][rawIndex];\n            // Do not filter NaN, see comment above.\n            if (val < range[dimk][0] || val > range[dimk][1]) {\n              keep = false;\n            }\n          }\n          if (keep) {\n            newIndices[offset++] = newStore.getRawIndex(i);\n          }\n        }\n      }\n    }\n    // Set indices after filtered.\n    if (offset < originalCount) {\n      newStore._indices = newIndices;\n    }\n    newStore._count = offset;\n    // Reset data extent\n    newStore._extent = [];\n    newStore._updateGetRawIdx();\n    return newStore;\n  };\n  // /**\n  //  * Data mapping to a plain array\n  //  */\n  // mapArray(dims: DimensionIndex[], cb: MapArrayCb): any[] {\n  //     const result: any[] = [];\n  //     this.each(dims, function () {\n  //         result.push(cb && (cb as MapArrayCb).apply(null, arguments));\n  //     });\n  //     return result;\n  // }\n  /**\r\n   * Data mapping to a new List with given dimensions\r\n   */\n  DataStore.prototype.map = function (dims, cb) {\n    // TODO only clone picked chunks.\n    var target = this.clone(dims);\n    this._updateDims(target, dims, cb);\n    return target;\n  };\n  /**\r\n   * @caution Danger!! Only used in dataStack.\r\n   */\n  DataStore.prototype.modify = function (dims, cb) {\n    this._updateDims(this, dims, cb);\n  };\n  DataStore.prototype._updateDims = function (target, dims, cb) {\n    var targetChunks = target._chunks;\n    var tmpRetValue = [];\n    var dimSize = dims.length;\n    var dataCount = target.count();\n    var values = [];\n    var rawExtent = target._rawExtent;\n    for (var i = 0; i < dims.length; i++) {\n      rawExtent[dims[i]] = getInitialExtent();\n    }\n    for (var dataIndex = 0; dataIndex < dataCount; dataIndex++) {\n      var rawIndex = target.getRawIndex(dataIndex);\n      for (var k = 0; k < dimSize; k++) {\n        values[k] = targetChunks[dims[k]][rawIndex];\n      }\n      values[dimSize] = dataIndex;\n      var retValue = cb && cb.apply(null, values);\n      if (retValue != null) {\n        // a number or string (in oridinal dimension)?\n        if (typeof retValue !== 'object') {\n          tmpRetValue[0] = retValue;\n          retValue = tmpRetValue;\n        }\n        for (var i = 0; i < retValue.length; i++) {\n          var dim = dims[i];\n          var val = retValue[i];\n          var rawExtentOnDim = rawExtent[dim];\n          var dimStore = targetChunks[dim];\n          if (dimStore) {\n            dimStore[rawIndex] = val;\n          }\n          if (val < rawExtentOnDim[0]) {\n            rawExtentOnDim[0] = val;\n          }\n          if (val > rawExtentOnDim[1]) {\n            rawExtentOnDim[1] = val;\n          }\n        }\n      }\n    }\n  };\n  /**\r\n   * Large data down sampling using largest-triangle-three-buckets\r\n   * @param {string} valueDimension\r\n   * @param {number} targetCount\r\n   */\n  DataStore.prototype.lttbDownSample = function (valueDimension, rate) {\n    var target = this.clone([valueDimension], true);\n    var targetStorage = target._chunks;\n    var dimStore = targetStorage[valueDimension];\n    var len = this.count();\n    var sampledIndex = 0;\n    var frameSize = Math.floor(1 / rate);\n    var currentRawIndex = this.getRawIndex(0);\n    var maxArea;\n    var area;\n    var nextRawIndex;\n    var newIndices = new (getIndicesCtor(this._rawCount))(Math.min((Math.ceil(len / frameSize) + 2) * 2, len));\n    // First frame use the first data.\n    newIndices[sampledIndex++] = currentRawIndex;\n    for (var i = 1; i < len - 1; i += frameSize) {\n      var nextFrameStart = Math.min(i + frameSize, len - 1);\n      var nextFrameEnd = Math.min(i + frameSize * 2, len);\n      var avgX = (nextFrameEnd + nextFrameStart) / 2;\n      var avgY = 0;\n      for (var idx = nextFrameStart; idx < nextFrameEnd; idx++) {\n        var rawIndex = this.getRawIndex(idx);\n        var y = dimStore[rawIndex];\n        if (isNaN(y)) {\n          continue;\n        }\n        avgY += y;\n      }\n      avgY /= nextFrameEnd - nextFrameStart;\n      var frameStart = i;\n      var frameEnd = Math.min(i + frameSize, len);\n      var pointAX = i - 1;\n      var pointAY = dimStore[currentRawIndex];\n      maxArea = -1;\n      nextRawIndex = frameStart;\n      var firstNaNIndex = -1;\n      var countNaN = 0;\n      // Find a point from current frame that construct a triangle with largest area with previous selected point\n      // And the average of next frame.\n      for (var idx = frameStart; idx < frameEnd; idx++) {\n        var rawIndex = this.getRawIndex(idx);\n        var y = dimStore[rawIndex];\n        if (isNaN(y)) {\n          countNaN++;\n          if (firstNaNIndex < 0) {\n            firstNaNIndex = rawIndex;\n          }\n          continue;\n        }\n        // Calculate triangle area over three buckets\n        area = Math.abs((pointAX - avgX) * (y - pointAY) - (pointAX - idx) * (avgY - pointAY));\n        if (area > maxArea) {\n          maxArea = area;\n          nextRawIndex = rawIndex; // Next a is this b\n        }\n      }\n      if (countNaN > 0 && countNaN < frameEnd - frameStart) {\n        // Append first NaN point in every bucket.\n        // It is necessary to ensure the correct order of indices.\n        newIndices[sampledIndex++] = Math.min(firstNaNIndex, nextRawIndex);\n        nextRawIndex = Math.max(firstNaNIndex, nextRawIndex);\n      }\n      newIndices[sampledIndex++] = nextRawIndex;\n      currentRawIndex = nextRawIndex; // This a is the next a (chosen b)\n    }\n    // First frame use the last data.\n    newIndices[sampledIndex++] = this.getRawIndex(len - 1);\n    target._count = sampledIndex;\n    target._indices = newIndices;\n    target.getRawIndex = this._getRawIdx;\n    return target;\n  };\n  /**\r\n   * Large data down sampling using min-max\r\n   * @param {string} valueDimension\r\n   * @param {number} rate\r\n   */\n  DataStore.prototype.minmaxDownSample = function (valueDimension, rate) {\n    var target = this.clone([valueDimension], true);\n    var targetStorage = target._chunks;\n    var frameSize = Math.floor(1 / rate);\n    var dimStore = targetStorage[valueDimension];\n    var len = this.count();\n    // Each frame results in 2 data points, one for min and one for max\n    var newIndices = new (getIndicesCtor(this._rawCount))(Math.ceil(len / frameSize) * 2);\n    var offset = 0;\n    for (var i = 0; i < len; i += frameSize) {\n      var minIndex = i;\n      var minValue = dimStore[this.getRawIndex(minIndex)];\n      var maxIndex = i;\n      var maxValue = dimStore[this.getRawIndex(maxIndex)];\n      var thisFrameSize = frameSize;\n      // Handle final smaller frame\n      if (i + frameSize > len) {\n        thisFrameSize = len - i;\n      }\n      // Determine min and max within the current frame\n      for (var k = 0; k < thisFrameSize; k++) {\n        var rawIndex = this.getRawIndex(i + k);\n        var value = dimStore[rawIndex];\n        if (value < minValue) {\n          minValue = value;\n          minIndex = i + k;\n        }\n        if (value > maxValue) {\n          maxValue = value;\n          maxIndex = i + k;\n        }\n      }\n      var rawMinIndex = this.getRawIndex(minIndex);\n      var rawMaxIndex = this.getRawIndex(maxIndex);\n      // Set the order of the min and max values, based on their ordering in the frame\n      if (minIndex < maxIndex) {\n        newIndices[offset++] = rawMinIndex;\n        newIndices[offset++] = rawMaxIndex;\n      } else {\n        newIndices[offset++] = rawMaxIndex;\n        newIndices[offset++] = rawMinIndex;\n      }\n    }\n    target._count = offset;\n    target._indices = newIndices;\n    target._updateGetRawIdx();\n    return target;\n  };\n  /**\r\n   * Large data down sampling on given dimension\r\n   * @param sampleIndex Sample index for name and id\r\n   */\n  DataStore.prototype.downSample = function (dimension, rate, sampleValue, sampleIndex) {\n    var target = this.clone([dimension], true);\n    var targetStorage = target._chunks;\n    var frameValues = [];\n    var frameSize = Math.floor(1 / rate);\n    var dimStore = targetStorage[dimension];\n    var len = this.count();\n    var rawExtentOnDim = target._rawExtent[dimension] = getInitialExtent();\n    var newIndices = new (getIndicesCtor(this._rawCount))(Math.ceil(len / frameSize));\n    var offset = 0;\n    for (var i = 0; i < len; i += frameSize) {\n      // Last frame\n      if (frameSize > len - i) {\n        frameSize = len - i;\n        frameValues.length = frameSize;\n      }\n      for (var k = 0; k < frameSize; k++) {\n        var dataIdx = this.getRawIndex(i + k);\n        frameValues[k] = dimStore[dataIdx];\n      }\n      var value = sampleValue(frameValues);\n      var sampleFrameIdx = this.getRawIndex(Math.min(i + sampleIndex(frameValues, value) || 0, len - 1));\n      // Only write value on the filtered data\n      dimStore[sampleFrameIdx] = value;\n      if (value < rawExtentOnDim[0]) {\n        rawExtentOnDim[0] = value;\n      }\n      if (value > rawExtentOnDim[1]) {\n        rawExtentOnDim[1] = value;\n      }\n      newIndices[offset++] = sampleFrameIdx;\n    }\n    target._count = offset;\n    target._indices = newIndices;\n    target._updateGetRawIdx();\n    return target;\n  };\n  /**\r\n   * Data iteration\r\n   * @param ctx default this\r\n   * @example\r\n   *  list.each('x', function (x, idx) {});\r\n   *  list.each(['x', 'y'], function (x, y, idx) {});\r\n   *  list.each(function (idx) {})\r\n   */\n  DataStore.prototype.each = function (dims, cb) {\n    if (!this._count) {\n      return;\n    }\n    var dimSize = dims.length;\n    var chunks = this._chunks;\n    for (var i = 0, len = this.count(); i < len; i++) {\n      var rawIdx = this.getRawIndex(i);\n      // Simple optimization\n      switch (dimSize) {\n        case 0:\n          cb(i);\n          break;\n        case 1:\n          cb(chunks[dims[0]][rawIdx], i);\n          break;\n        case 2:\n          cb(chunks[dims[0]][rawIdx], chunks[dims[1]][rawIdx], i);\n          break;\n        default:\n          var k = 0;\n          var value = [];\n          for (; k < dimSize; k++) {\n            value[k] = chunks[dims[k]][rawIdx];\n          }\n          // Index\n          value[k] = i;\n          cb.apply(null, value);\n      }\n    }\n  };\n  /**\r\n   * Get extent of data in one dimension\r\n   */\n  DataStore.prototype.getDataExtent = function (dim) {\n    // Make sure use concrete dim as cache name.\n    var dimData = this._chunks[dim];\n    var initialExtent = getInitialExtent();\n    if (!dimData) {\n      return initialExtent;\n    }\n    // Make more strict checkings to ensure hitting cache.\n    var currEnd = this.count();\n    // Consider the most cases when using data zoom, `getDataExtent`\n    // happened before filtering. We cache raw extent, which is not\n    // necessary to be cleared and recalculated when restore data.\n    var useRaw = !this._indices;\n    var dimExtent;\n    if (useRaw) {\n      return this._rawExtent[dim].slice();\n    }\n    dimExtent = this._extent[dim];\n    if (dimExtent) {\n      return dimExtent.slice();\n    }\n    dimExtent = initialExtent;\n    var min = dimExtent[0];\n    var max = dimExtent[1];\n    for (var i = 0; i < currEnd; i++) {\n      var rawIdx = this.getRawIndex(i);\n      var value = dimData[rawIdx];\n      value < min && (min = value);\n      value > max && (max = value);\n    }\n    dimExtent = [min, max];\n    this._extent[dim] = dimExtent;\n    return dimExtent;\n  };\n  /**\r\n   * Get raw data item\r\n   */\n  DataStore.prototype.getRawDataItem = function (idx) {\n    var rawIdx = this.getRawIndex(idx);\n    if (!this._provider.persistent) {\n      var val = [];\n      var chunks = this._chunks;\n      for (var i = 0; i < chunks.length; i++) {\n        val.push(chunks[i][rawIdx]);\n      }\n      return val;\n    } else {\n      return this._provider.getItem(rawIdx);\n    }\n  };\n  /**\r\n   * Clone shallow.\r\n   *\r\n   * @param clonedDims Determine which dims to clone. Will share the data if not specified.\r\n   */\n  DataStore.prototype.clone = function (clonedDims, ignoreIndices) {\n    var target = new DataStore();\n    var chunks = this._chunks;\n    var clonedDimsMap = clonedDims && reduce(clonedDims, function (obj, dimIdx) {\n      obj[dimIdx] = true;\n      return obj;\n    }, {});\n    if (clonedDimsMap) {\n      for (var i = 0; i < chunks.length; i++) {\n        // Not clone if dim is not picked.\n        target._chunks[i] = !clonedDimsMap[i] ? chunks[i] : cloneChunk(chunks[i]);\n      }\n    } else {\n      target._chunks = chunks;\n    }\n    this._copyCommonProps(target);\n    if (!ignoreIndices) {\n      target._indices = this._cloneIndices();\n    }\n    target._updateGetRawIdx();\n    return target;\n  };\n  DataStore.prototype._copyCommonProps = function (target) {\n    target._count = this._count;\n    target._rawCount = this._rawCount;\n    target._provider = this._provider;\n    target._dimensions = this._dimensions;\n    target._extent = clone(this._extent);\n    target._rawExtent = clone(this._rawExtent);\n  };\n  DataStore.prototype._cloneIndices = function () {\n    if (this._indices) {\n      var Ctor = this._indices.constructor;\n      var indices = void 0;\n      if (Ctor === Array) {\n        var thisCount = this._indices.length;\n        indices = new Ctor(thisCount);\n        for (var i = 0; i < thisCount; i++) {\n          indices[i] = this._indices[i];\n        }\n      } else {\n        indices = new Ctor(this._indices);\n      }\n      return indices;\n    }\n    return null;\n  };\n  DataStore.prototype._getRawIdxIdentity = function (idx) {\n    return idx;\n  };\n  DataStore.prototype._getRawIdx = function (idx) {\n    if (idx < this._count && idx >= 0) {\n      return this._indices[idx];\n    }\n    return -1;\n  };\n  DataStore.prototype._updateGetRawIdx = function () {\n    this.getRawIndex = this._indices ? this._getRawIdx : this._getRawIdxIdentity;\n  };\n  DataStore.internalField = function () {\n    function getDimValueSimply(dataItem, property, dataIndex, dimIndex) {\n      return parseDataValue(dataItem[dimIndex], this._dimensions[dimIndex]);\n    }\n    defaultDimValueGetters = {\n      arrayRows: getDimValueSimply,\n      objectRows: function (dataItem, property, dataIndex, dimIndex) {\n        return parseDataValue(dataItem[property], this._dimensions[dimIndex]);\n      },\n      keyedColumns: getDimValueSimply,\n      original: function (dataItem, property, dataIndex, dimIndex) {\n        // Performance sensitive, do not use modelUtil.getDataItemValue.\n        // If dataItem is an plain object with no value field, the let `value`\n        // will be assigned with the object, but it will be tread correctly\n        // in the `convertValue`.\n        var value = dataItem && (dataItem.value == null ? dataItem : dataItem.value);\n        return parseDataValue(value instanceof Array ? value[dimIndex]\n        // If value is a single number or something else not array.\n        : value, this._dimensions[dimIndex]);\n      },\n      typedArray: function (dataItem, property, dataIndex, dimIndex) {\n        return dataItem[dimIndex];\n      }\n    };\n  }();\n  return DataStore;\n}();\nexport default DataStore;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}