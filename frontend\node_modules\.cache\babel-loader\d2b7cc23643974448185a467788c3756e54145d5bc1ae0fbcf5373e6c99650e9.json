{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nimport { subPixelOptimizeLine } from '../helper/subPixelOptimize.js';\nvar subPixelOptimizeOutputShape = {};\nvar LineShape = function () {\n  function LineShape() {\n    this.x1 = 0;\n    this.y1 = 0;\n    this.x2 = 0;\n    this.y2 = 0;\n    this.percent = 1;\n  }\n  return LineShape;\n}();\nexport { LineShape };\nvar Line = function (_super) {\n  __extends(Line, _super);\n  function Line(opts) {\n    return _super.call(this, opts) || this;\n  }\n  Line.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  Line.prototype.getDefaultShape = function () {\n    return new LineShape();\n  };\n  Line.prototype.buildPath = function (ctx, shape) {\n    var x1;\n    var y1;\n    var x2;\n    var y2;\n    if (this.subPixelOptimize) {\n      var optimizedShape = subPixelOptimizeLine(subPixelOptimizeOutputShape, shape, this.style);\n      x1 = optimizedShape.x1;\n      y1 = optimizedShape.y1;\n      x2 = optimizedShape.x2;\n      y2 = optimizedShape.y2;\n    } else {\n      x1 = shape.x1;\n      y1 = shape.y1;\n      x2 = shape.x2;\n      y2 = shape.y2;\n    }\n    var percent = shape.percent;\n    if (percent === 0) {\n      return;\n    }\n    ctx.moveTo(x1, y1);\n    if (percent < 1) {\n      x2 = x1 * (1 - percent) + x2 * percent;\n      y2 = y1 * (1 - percent) + y2 * percent;\n    }\n    ctx.lineTo(x2, y2);\n  };\n  Line.prototype.pointAt = function (p) {\n    var shape = this.shape;\n    return [shape.x1 * (1 - p) + shape.x2 * p, shape.y1 * (1 - p) + shape.y2 * p];\n  };\n  return Line;\n}(Path);\nLine.prototype.type = 'line';\nexport default Line;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}