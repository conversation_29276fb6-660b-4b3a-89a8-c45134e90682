"""
AI服务
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging

from ai.stock_selector import get_stock_selector, StockScore
from ai.market_predictor import get_market_predictor, PredictionResult, MarketTrend
from ai.deepseek_client import get_deepseek_client
from data.tushare_client import get_tushare_client

logger = logging.getLogger(__name__)

class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.stock_selector = get_stock_selector()
        self.market_predictor = get_market_predictor()
        self.deepseek_client = get_deepseek_client()
        self.tushare_client = get_tushare_client()
    
    def intelligent_stock_selection(self, 
                                  stock_pool: List[str] = None,
                                  top_n: int = 10,
                                  min_score: float = 60,
                                  sector_filter: str = None) -> Dict[str, Any]:
        """智能选股"""
        
        try:
            logger.info("开始智能选股")
            
            # 如果没有提供股票池，使用默认股票池
            if not stock_pool:
                stock_pool = self._get_default_stock_pool()
            
            # 行业过滤
            if sector_filter:
                stock_pool = self._filter_by_sector(stock_pool, sector_filter)
            
            # 执行选股
            selected_stocks = self.stock_selector.select_stocks(stock_pool, top_n, min_score)
            
            # 格式化结果
            result = {
                'success': True,
                'selection_time': datetime.now().isoformat(),
                'total_analyzed': len(stock_pool),
                'selected_count': len(selected_stocks),
                'selection_criteria': {
                    'min_score': min_score,
                    'top_n': top_n,
                    'sector_filter': sector_filter
                },
                'selected_stocks': [
                    {
                        'ts_code': stock.ts_code,
                        'name': stock.name,
                        'total_score': round(stock.total_score, 2),
                        'technical_score': round(stock.technical_score, 2),
                        'fundamental_score': round(stock.fundamental_score, 2),
                        'momentum_score': round(stock.momentum_score, 2),
                        'risk_score': round(stock.risk_score, 2),
                        'recommendation': stock.recommendation,
                        'reasons': stock.reasons
                    }
                    for stock in selected_stocks
                ],
                'score_distribution': self._analyze_score_distribution(selected_stocks)
            }
            
            logger.info(f"智能选股完成，选出 {len(selected_stocks)} 只股票")
            return result
            
        except Exception as e:
            logger.error(f"智能选股失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def market_prediction_analysis(self, stock_codes: List[str], days_ahead: int = 5) -> Dict[str, Any]:
        """市场预测分析"""
        
        try:
            logger.info(f"开始市场预测分析，股票数量: {len(stock_codes)}")
            
            # 获取历史数据
            market_data = {}
            for ts_code in stock_codes:
                try:
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=60)
                    
                    df = self.tushare_client.get_daily_data(
                        ts_code,
                        start_date.strftime('%Y%m%d'),
                        end_date.strftime('%Y%m%d')
                    )
                    
                    if not df.empty:
                        market_data[ts_code] = df
                        
                except Exception as e:
                    logger.error(f"获取 {ts_code} 数据失败: {e}")
            
            if not market_data:
                return {
                    'success': False,
                    'error': '无法获取市场数据'
                }
            
            # 个股价格预测
            stock_predictions = []
            for ts_code, data in market_data.items():
                prediction = self.market_predictor.predict_stock_price(ts_code, data, days_ahead)
                if prediction:
                    stock_predictions.append({
                        'ts_code': prediction.ts_code,
                        'current_price': round(prediction.current_price, 2),
                        'predicted_price': round(prediction.predicted_price, 2),
                        'prediction_change': round(prediction.prediction_change * 100, 2),
                        'confidence': round(prediction.confidence, 2),
                        'prediction_date': prediction.prediction_date,
                        'model_accuracy': round(prediction.model_accuracy, 2)
                    })
            
            # 市场趋势分析
            market_trend = self.market_predictor.analyze_market_trend(market_data)
            
            # 市场波动率预测
            volatility_prediction = self.market_predictor.predict_market_volatility(market_data, days_ahead)
            
            # 生成AI解读
            ai_insights = self._generate_ai_insights(stock_predictions, market_trend, volatility_prediction)
            
            result = {
                'success': True,
                'analysis_time': datetime.now().isoformat(),
                'prediction_period': f"{days_ahead} days",
                'stock_predictions': stock_predictions,
                'market_trend': {
                    'direction': market_trend.trend_direction,
                    'strength': round(market_trend.trend_strength, 2),
                    'support_level': round(market_trend.support_level, 2),
                    'resistance_level': round(market_trend.resistance_level, 2),
                    'key_factors': market_trend.key_factors,
                    'confidence': round(market_trend.confidence, 2)
                },
                'volatility_prediction': volatility_prediction,
                'ai_insights': ai_insights,
                'summary': self._generate_prediction_summary(stock_predictions, market_trend)
            }
            
            logger.info("市场预测分析完成")
            return result
            
        except Exception as e:
            logger.error(f"市场预测分析失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def ai_news_analysis(self, news_text: str) -> Dict[str, Any]:
        """AI新闻分析"""
        
        try:
            if not self.deepseek_client:
                return {
                    'success': False,
                    'error': 'DeepSeek API未配置'
                }
            
            # 情绪分析
            sentiment_result = self.deepseek_client.analyze_market_sentiment(news_text)
            
            # 风险识别
            risk_result = self.deepseek_client.identify_risk_events(news_text)
            
            # 综合分析
            analysis_result = {
                'success': True,
                'analysis_time': datetime.now().isoformat(),
                'news_length': len(news_text),
                'sentiment_analysis': sentiment_result,
                'risk_analysis': risk_result,
                'investment_suggestion': self._generate_investment_suggestion(sentiment_result, risk_result)
            }
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"AI新闻分析失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_daily_ai_report(self, stock_codes: List[str]) -> Dict[str, Any]:
        """生成每日AI报告"""
        
        try:
            logger.info("开始生成每日AI报告")
            
            # 获取市场数据
            market_data = self._get_market_data_for_report(stock_codes)
            
            # 智能选股
            selection_result = self.intelligent_stock_selection(stock_codes, top_n=5)
            
            # 市场预测
            prediction_result = self.market_prediction_analysis(stock_codes[:10])
            
            # 生成报告内容
            report_content = {
                'report_date': datetime.now().strftime('%Y-%m-%d'),
                'market_overview': self._generate_market_overview(market_data),
                'top_picks': selection_result.get('selected_stocks', [])[:3],
                'market_prediction': prediction_result.get('summary', {}),
                'risk_alerts': self._generate_risk_alerts(market_data),
                'investment_recommendations': self._generate_investment_recommendations(selection_result, prediction_result)
            }
            
            # 使用DeepSeek生成报告摘要
            if self.deepseek_client:
                try:
                    ai_summary = self.deepseek_client.generate_daily_report(report_content)
                    report_content['ai_summary'] = ai_summary
                except Exception as e:
                    logger.error(f"生成AI摘要失败: {e}")
                    report_content['ai_summary'] = "AI摘要生成失败"
            
            result = {
                'success': True,
                'report': report_content,
                'generation_time': datetime.now().isoformat()
            }
            
            logger.info("每日AI报告生成完成")
            return result
            
        except Exception as e:
            logger.error(f"生成每日AI报告失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_default_stock_pool(self) -> List[str]:
        """获取默认股票池"""
        # 返回一些常见的股票代码
        return [
            '000001.SZ', '000002.SZ', '000858.SZ', '000876.SZ',
            '600000.SH', '600036.SH', '600519.SH', '600887.SH',
            '000725.SZ', '002415.SZ', '300059.SZ', '300750.SZ'
        ]
    
    def _filter_by_sector(self, stock_pool: List[str], sector: str) -> List[str]:
        """按行业过滤股票"""
        # 简化实现，实际应该从数据库查询
        return stock_pool  # 暂时返回原股票池
    
    def _analyze_score_distribution(self, stocks: List[StockScore]) -> Dict[str, Any]:
        """分析评分分布"""
        if not stocks:
            return {}
        
        scores = [stock.total_score for stock in stocks]
        
        return {
            'avg_score': round(np.mean(scores), 2),
            'max_score': round(max(scores), 2),
            'min_score': round(min(scores), 2),
            'score_std': round(np.std(scores), 2)
        }
    
    def _generate_ai_insights(self, predictions: List[Dict], trend: MarketTrend, volatility: Dict) -> List[str]:
        """生成AI洞察"""
        insights = []
        
        if predictions:
            positive_predictions = [p for p in predictions if p['prediction_change'] > 0]
            if len(positive_predictions) > len(predictions) * 0.6:
                insights.append("多数股票预期上涨，市场情绪偏乐观")
            
            high_confidence = [p for p in predictions if p['confidence'] > 0.7]
            if high_confidence:
                insights.append(f"有 {len(high_confidence)} 只股票预测置信度较高")
        
        if trend.trend_direction == "UP":
            insights.append("市场整体呈上升趋势")
        elif trend.trend_direction == "DOWN":
            insights.append("市场整体呈下降趋势")
        
        if volatility.get('volatility_level') == 'HIGH':
            insights.append("预期市场波动率较高，注意风险控制")
        
        return insights
    
    def _generate_prediction_summary(self, predictions: List[Dict], trend: MarketTrend) -> Dict[str, Any]:
        """生成预测摘要"""
        if not predictions:
            return {}
        
        positive_count = len([p for p in predictions if p['prediction_change'] > 0])
        avg_change = np.mean([p['prediction_change'] for p in predictions])
        
        return {
            'total_stocks': len(predictions),
            'positive_predictions': positive_count,
            'negative_predictions': len(predictions) - positive_count,
            'avg_predicted_change': round(avg_change, 2),
            'market_sentiment': 'BULLISH' if avg_change > 1 else 'BEARISH' if avg_change < -1 else 'NEUTRAL'
        }
    
    def _generate_investment_suggestion(self, sentiment: Dict, risk: Dict) -> str:
        """生成投资建议"""
        sentiment_score = sentiment.get('sentiment_score', 0)
        risk_level = risk.get('risk_level', 'LOW')
        
        if sentiment_score > 0.5 and risk_level == 'LOW':
            return "积极投资，市场情绪乐观且风险可控"
        elif sentiment_score > 0 and risk_level in ['LOW', 'MEDIUM']:
            return "谨慎乐观，可适度增加仓位"
        elif sentiment_score < -0.5 or risk_level == 'HIGH':
            return "保守投资，建议降低仓位或观望"
        else:
            return "中性观点，维持现有仓位"
    
    def _get_market_data_for_report(self, stock_codes: List[str]) -> Dict[str, Any]:
        """获取报告用市场数据"""
        # 简化实现
        return {
            'total_stocks': len(stock_codes),
            'analysis_date': datetime.now().strftime('%Y-%m-%d')
        }
    
    def _generate_market_overview(self, market_data: Dict) -> str:
        """生成市场概览"""
        return f"今日分析了 {market_data.get('total_stocks', 0)} 只股票的市场表现"
    
    def _generate_risk_alerts(self, market_data: Dict) -> List[str]:
        """生成风险提醒"""
        return ["市场波动加剧，注意风险控制"]
    
    def _generate_investment_recommendations(self, selection: Dict, prediction: Dict) -> List[str]:
        """生成投资建议"""
        recommendations = []
        
        if selection.get('success') and selection.get('selected_stocks'):
            top_stock = selection['selected_stocks'][0]
            recommendations.append(f"推荐关注 {top_stock['name']}，综合评分 {top_stock['total_score']}")
        
        if prediction.get('success'):
            market_sentiment = prediction.get('summary', {}).get('market_sentiment', 'NEUTRAL')
            if market_sentiment == 'BULLISH':
                recommendations.append("市场情绪偏乐观，可适度增加仓位")
            elif market_sentiment == 'BEARISH':
                recommendations.append("市场情绪偏悲观，建议控制仓位")
        
        return recommendations

# 全局AI服务实例
ai_service = AIService()

def get_ai_service() -> AIService:
    """获取AI服务实例"""
    return ai_service
