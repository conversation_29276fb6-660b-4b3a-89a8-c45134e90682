{"ast": null, "code": "// This icon file is generated automatically.\nvar TwitchFilled = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"fill-rule\": \"evenodd\",\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"defs\",\n      \"attrs\": {},\n      \"children\": [{\n        \"tag\": \"filter\",\n        \"attrs\": {\n          \"filterUnits\": \"objectBoundingBox\",\n          \"height\": \"102.3%\",\n          \"id\": \"a\",\n          \"width\": \"102.3%\",\n          \"x\": \"-1.2%\",\n          \"y\": \"-1.2%\"\n        },\n        \"children\": [{\n          \"tag\": \"feOffset\",\n          \"attrs\": {\n            \"dy\": \"2\",\n            \"in\": \"SourceAlpha\",\n            \"result\": \"shadowOffsetOuter1\"\n          }\n        }, {\n          \"tag\": \"feGaussianBlur\",\n          \"attrs\": {\n            \"in\": \"shadowOffsetOuter1\",\n            \"result\": \"shadowBlurOuter1\",\n            \"stdDeviation\": \"2\"\n          }\n        }, {\n          \"tag\": \"feColorMatrix\",\n          \"attrs\": {\n            \"in\": \"shadowBlurOuter1\",\n            \"result\": \"shadowMatrixOuter1\",\n            \"values\": \"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0\"\n          }\n        }, {\n          \"tag\": \"feMerge\",\n          \"attrs\": {},\n          \"children\": [{\n            \"tag\": \"feMergeNode\",\n            \"attrs\": {\n              \"in\": \"shadowMatrixOuter1\"\n            }\n          }, {\n            \"tag\": \"feMergeNode\",\n            \"attrs\": {\n              \"in\": \"SourceGraphic\"\n            }\n          }]\n        }]\n      }]\n    }, {\n      \"tag\": \"g\",\n      \"attrs\": {\n        \"filter\": \"url(#a)\",\n        \"transform\": \"translate(9 9)\"\n      },\n      \"children\": [{\n        \"tag\": \"path\",\n        \"attrs\": {\n          \"d\": \"M185.14 112L128 254.86V797.7h171.43V912H413.7L528 797.71h142.86l200-200V112zm314.29 428.57H413.7V310.21h85.72zm200 0H613.7V310.21h85.72z\"\n        }\n      }]\n    }]\n  },\n  \"name\": \"twitch\",\n  \"theme\": \"filled\"\n};\nexport default TwitchFilled;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}