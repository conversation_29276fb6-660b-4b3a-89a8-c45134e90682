import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Table, 
  Tag, 
  Progress, 
  Statistic, 
  Alert,
  Space,
  Button,
  Select,
  DatePicker
} from 'antd';
import { 
  MonitorOutlined,
  WarningOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';

const { Option } = Select;
const { RangePicker } = DatePicker;

interface MonitoringData {
  ts_code: string;
  name: string;
  price: number;
  change_pct: number;
  ma5: number;
  ma20: number;
  rsi: number;
  macd_signal: string;
  risk_score: number;
  volume_ratio: number;
}

interface TechnicalSignal {
  ts_code: string;
  name: string;
  indicator: string;
  signal: string;
  value: number;
  time: string;
}

const Monitoring: React.FC = () => {
  const [monitoringData, setMonitoringData] = useState<MonitoringData[]>([]);
  const [signals, setSignals] = useState<TechnicalSignal[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedStock, setSelectedStock] = useState<string>('000001.SZ');

  // 模拟数据
  useEffect(() => {
    const mockData: MonitoringData[] = [
      {
        ts_code: '000001.SZ',
        name: '平安银行',
        price: 12.45,
        change_pct: 1.22,
        ma5: 12.30,
        ma20: 12.10,
        rsi: 65.5,
        macd_signal: 'BUY',
        risk_score: 25,
        volume_ratio: 1.8
      },
      {
        ts_code: '000002.SZ',
        name: '万科A',
        price: 18.76,
        change_pct: -1.21,
        ma5: 18.90,
        ma20: 19.20,
        rsi: 45.2,
        macd_signal: 'SELL',
        risk_score: 60,
        volume_ratio: 0.9
      },
      {
        ts_code: '600000.SH',
        name: '浦发银行',
        price: 9.87,
        change_pct: 0.82,
        ma5: 9.75,
        ma20: 9.60,
        rsi: 58.3,
        macd_signal: 'HOLD',
        risk_score: 35,
        volume_ratio: 1.2
      }
    ];

    const mockSignals: TechnicalSignal[] = [
      { ts_code: '000001.SZ', name: '平安银行', indicator: 'MACD', signal: 'BUY', value: 0.15, time: '10:30' },
      { ts_code: '000002.SZ', name: '万科A', indicator: 'RSI', signal: 'SELL', value: 75.2, time: '10:25' },
      { ts_code: '600000.SH', name: '浦发银行', indicator: 'MA', signal: 'BUY', value: 9.87, time: '10:20' },
      { ts_code: '000001.SZ', name: '平安银行', indicator: 'KDJ', signal: 'BUY', value: 85.6, time: '10:15' },
    ];

    setMonitoringData(mockData);
    setSignals(mockSignals);
  }, []);

  const columns = [
    {
      title: '股票',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: MonitoringData) => (
        <div>
          <div>{name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.ts_code}</div>
        </div>
      )
    },
    {
      title: '当前价格',
      dataIndex: 'price',
      key: 'price',
      render: (price: number, record: MonitoringData) => (
        <div>
          <div>¥{price.toFixed(2)}</div>
          <div style={{ 
            fontSize: '12px', 
            color: record.change_pct >= 0 ? '#3f8600' : '#cf1322' 
          }}>
            {record.change_pct >= 0 ? '+' : ''}{record.change_pct.toFixed(2)}%
          </div>
        </div>
      )
    },
    {
      title: '技术指标',
      key: 'indicators',
      render: (_, record: MonitoringData) => (
        <div>
          <div>MA5: {record.ma5.toFixed(2)}</div>
          <div>MA20: {record.ma20.toFixed(2)}</div>
          <div>RSI: {record.rsi.toFixed(1)}</div>
        </div>
      )
    },
    {
      title: '信号',
      dataIndex: 'macd_signal',
      key: 'macd_signal',
      render: (signal: string) => {
        const color = signal === 'BUY' ? 'green' : signal === 'SELL' ? 'red' : 'blue';
        const icon = signal === 'BUY' ? <TrendingUpOutlined /> : 
                    signal === 'SELL' ? <TrendingDownOutlined /> : null;
        return (
          <Tag color={color} icon={icon}>
            {signal}
          </Tag>
        );
      }
    },
    {
      title: '风险评分',
      dataIndex: 'risk_score',
      key: 'risk_score',
      render: (score: number) => (
        <div>
          <Progress 
            percent={score} 
            size="small" 
            strokeColor={score > 60 ? '#ff4d4f' : score > 30 ? '#faad14' : '#52c41a'}
            showInfo={false}
          />
          <div style={{ fontSize: '12px' }}>{score}/100</div>
        </div>
      )
    },
    {
      title: '量比',
      dataIndex: 'volume_ratio',
      key: 'volume_ratio',
      render: (ratio: number) => (
        <span style={{ 
          color: ratio > 1.5 ? '#3f8600' : ratio < 0.8 ? '#cf1322' : '#666' 
        }}>
          {ratio.toFixed(1)}
        </span>
      )
    }
  ];

  const signalColumns = [
    {
      title: '股票',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '指标',
      dataIndex: 'indicator',
      key: 'indicator',
    },
    {
      title: '信号',
      dataIndex: 'signal',
      key: 'signal',
      render: (signal: string) => {
        const color = signal === 'BUY' ? 'green' : signal === 'SELL' ? 'red' : 'blue';
        return <Tag color={color}>{signal}</Tag>;
      }
    },
    {
      title: '数值',
      dataIndex: 'value',
      key: 'value',
      render: (value: number) => value.toFixed(2)
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
    }
  ];

  // 实时价格图表配置
  const priceChartOption = {
    title: {
      text: '实时价格监控',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['价格', 'MA5', 'MA20'],
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['09:30', '10:00', '10:30', '11:00', '11:30', '13:00', '13:30', '14:00', '14:30', '15:00']
    },
    yAxis: {
      type: 'value',
      scale: true
    },
    series: [
      {
        name: '价格',
        type: 'line',
        data: [12.30, 12.35, 12.40, 12.38, 12.42, 12.45, 12.48, 12.46, 12.44, 12.45],
        lineStyle: { color: '#1890ff' }
      },
      {
        name: 'MA5',
        type: 'line',
        data: [12.25, 12.28, 12.30, 12.32, 12.33, 12.34, 12.35, 12.34, 12.33, 12.30],
        lineStyle: { color: '#52c41a' }
      },
      {
        name: 'MA20',
        type: 'line',
        data: [12.10, 12.11, 12.12, 12.13, 12.14, 12.15, 12.16, 12.15, 12.14, 12.10],
        lineStyle: { color: '#faad14' }
      }
    ]
  };

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      // 这里可以重新获取数据
    }, 1000);
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 控制面板 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col>
            <Space>
              <span>选择股票:</span>
              <Select 
                value={selectedStock} 
                onChange={setSelectedStock}
                style={{ width: 150 }}
              >
                <Option value="000001.SZ">平安银行</Option>
                <Option value="000002.SZ">万科A</Option>
                <Option value="600000.SH">浦发银行</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <span>时间范围:</span>
              <RangePicker />
            </Space>
          </Col>
          <Col>
            <Button 
              type="primary" 
              icon={<ReloadOutlined />} 
              onClick={handleRefresh}
              loading={loading}
            >
              刷新数据
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 统计概览 */}
      <Row gutter={16} style={{ marginBottom: '16px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="监控股票"
              value={monitoringData.length}
              prefix={<MonitorOutlined />}
              suffix="只"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="买入信号"
              value={signals.filter(s => s.signal === 'BUY').length}
              valueStyle={{ color: '#3f8600' }}
              prefix={<TrendingUpOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="卖出信号"
              value={signals.filter(s => s.signal === 'SELL').length}
              valueStyle={{ color: '#cf1322' }}
              prefix={<TrendingDownOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="风险告警"
              value={monitoringData.filter(d => d.risk_score > 50).length}
              valueStyle={{ color: '#faad14' }}
              prefix={<WarningOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Row gutter={16}>
        <Col span={16}>
          <Card title="实时监控" style={{ marginBottom: '16px' }}>
            <Table
              columns={columns}
              dataSource={monitoringData}
              rowKey="ts_code"
              pagination={false}
              size="middle"
            />
          </Card>
          
          <Card title="价格走势">
            <ReactECharts option={priceChartOption} style={{ height: '300px' }} />
          </Card>
        </Col>
        
        <Col span={8}>
          <Card title="交易信号" style={{ marginBottom: '16px' }}>
            <Table
              columns={signalColumns}
              dataSource={signals}
              rowKey={(record) => `${record.ts_code}-${record.indicator}-${record.time}`}
              pagination={false}
              size="small"
            />
          </Card>
          
          <Card title="系统状态">
            <Alert
              message="系统运行正常"
              description="数据更新时间: 2024-01-20 10:30:15"
              type="success"
              showIcon
              style={{ marginBottom: '16px' }}
            />
            <Alert
              message="注意"
              description="万科A风险评分较高，请关注"
              type="warning"
              showIcon
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Monitoring;
