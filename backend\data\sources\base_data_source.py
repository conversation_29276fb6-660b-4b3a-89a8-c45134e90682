"""
数据源基类 - 定义标准的数据源接口
"""
from abc import ABC, abstractmethod
import pandas as pd
from typing import List, Dict, Optional, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class BaseDataSource(ABC):
    """数据源基类，定义标准接口"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """
        初始化数据源
        
        Args:
            name: 数据源名称
            config: 配置参数
        """
        self.name = name
        self.config = config
        self.is_connected = False
        
    @abstractmethod
    def connect(self) -> bool:
        """
        连接数据源
        
        Returns:
            bool: 连接是否成功
        """
        pass
    
    @abstractmethod
    def disconnect(self):
        """断开数据源连接"""
        pass
    
    @abstractmethod
    def get_stock_basic(self, market: str = "A股") -> pd.DataFrame:
        """
        获取股票基本信息
        
        Args:
            market: 市场类型 ("A股", "港股", "美股")
            
        Returns:
            pd.DataFrame: 股票基本信息
        """
        pass
    
    @abstractmethod
    def get_daily_data(self, ts_code: str, start_date: str, end_date: str = None) -> pd.DataFrame:
        """
        获取日线数据
        
        Args:
            ts_code: 股票代码
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            
        Returns:
            pd.DataFrame: 日线数据
        """
        pass
    
    @abstractmethod
    def get_realtime_data(self, ts_codes: List[str]) -> pd.DataFrame:
        """
        获取实时数据
        
        Args:
            ts_codes: 股票代码列表
            
        Returns:
            pd.DataFrame: 实时数据
        """
        pass
    
    @abstractmethod
    def get_fundamental_data(self, ts_code: str, fields: List[str] = None) -> pd.DataFrame:
        """
        获取基本面数据
        
        Args:
            ts_code: 股票代码
            fields: 需要的字段列表
            
        Returns:
            pd.DataFrame: 基本面数据
        """
        pass
    
    def validate_connection(self) -> bool:
        """
        验证连接状态
        
        Returns:
            bool: 连接是否有效
        """
        try:
            # 尝试获取少量数据来验证连接
            test_data = self.get_stock_basic()
            return not test_data.empty
        except Exception as e:
            logger.error(f"{self.name} 连接验证失败: {e}")
            return False
    
    def get_supported_markets(self) -> List[str]:
        """
        获取支持的市场列表
        
        Returns:
            List[str]: 支持的市场列表
        """
        return ["A股"]  # 默认支持A股，子类可以重写
    
    def get_data_source_info(self) -> Dict[str, Any]:
        """
        获取数据源信息
        
        Returns:
            Dict[str, Any]: 数据源信息
        """
        return {
            "name": self.name,
            "connected": self.is_connected,
            "supported_markets": self.get_supported_markets(),
            "config": {k: "***" if "password" in k.lower() or "token" in k.lower() or "key" in k.lower() 
                      else v for k, v in self.config.items()}
        }

class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self):
        self.data_sources: Dict[str, BaseDataSource] = {}
        self.primary_source: Optional[str] = None
        
    def register_data_source(self, source: BaseDataSource, is_primary: bool = False):
        """
        注册数据源
        
        Args:
            source: 数据源实例
            is_primary: 是否为主数据源
        """
        self.data_sources[source.name] = source
        if is_primary or self.primary_source is None:
            self.primary_source = source.name
        logger.info(f"注册数据源: {source.name}")
    
    def get_data_source(self, name: str = None) -> Optional[BaseDataSource]:
        """
        获取数据源
        
        Args:
            name: 数据源名称，为None时返回主数据源
            
        Returns:
            Optional[BaseDataSource]: 数据源实例
        """
        if name is None:
            name = self.primary_source
        return self.data_sources.get(name)
    
    def get_available_sources(self) -> List[str]:
        """
        获取可用的数据源列表
        
        Returns:
            List[str]: 数据源名称列表
        """
        return [name for name, source in self.data_sources.items() if source.is_connected]
    
    def connect_all(self) -> Dict[str, bool]:
        """
        连接所有数据源
        
        Returns:
            Dict[str, bool]: 各数据源连接结果
        """
        results = {}
        for name, source in self.data_sources.items():
            try:
                results[name] = source.connect()
                logger.info(f"数据源 {name} 连接{'成功' if results[name] else '失败'}")
            except Exception as e:
                results[name] = False
                logger.error(f"数据源 {name} 连接异常: {e}")
        return results
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取所有数据源状态
        
        Returns:
            Dict[str, Any]: 数据源状态信息
        """
        return {
            "primary_source": self.primary_source,
            "sources": {name: source.get_data_source_info() 
                       for name, source in self.data_sources.items()}
        }

# 全局数据源管理器实例
data_source_manager = DataSourceManager()
