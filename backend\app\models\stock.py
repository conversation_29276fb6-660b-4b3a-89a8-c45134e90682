"""
股票相关数据模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Float, Text, Index
from sqlalchemy.sql import func
from app.models.database import Base

class Stock(Base):
    """股票基本信息表"""
    __tablename__ = "stocks"
    
    id = Column(Integer, primary_key=True, index=True)
    ts_code = Column(String(20), unique=True, index=True, comment="Tushare代码")
    symbol = Column(String(10), index=True, comment="股票代码")
    name = Column(String(50), comment="股票名称")
    market = Column(String(10), comment="市场类型：A股/港股")
    industry = Column(String(50), comment="行业")
    area = Column(String(20), comment="地区")
    list_date = Column(String(10), comment="上市日期")
    is_active = Column(Boolean, default=True, comment="是否监控中")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 创建索引
    __table_args__ = (
        Index('idx_stock_market_active', 'market', 'is_active'),
    )

class StockBasic(Base):
    """股票基本面数据表"""
    __tablename__ = "stock_basic"
    
    id = Column(Integer, primary_key=True, index=True)
    ts_code = Column(String(20), index=True, comment="股票代码")
    trade_date = Column(String(10), index=True, comment="交易日期")
    
    # 估值指标
    pe = Column(Float, comment="市盈率")
    pe_ttm = Column(Float, comment="市盈率TTM")
    pb = Column(Float, comment="市净率")
    ps = Column(Float, comment="市销率")
    ps_ttm = Column(Float, comment="市销率TTM")
    
    # 市值数据
    total_mv = Column(Float, comment="总市值(万元)")
    circ_mv = Column(Float, comment="流通市值(万元)")
    
    # 财务指标
    turnover_rate = Column(Float, comment="换手率")
    turnover_rate_f = Column(Float, comment="换手率(自由流通股)")
    volume_ratio = Column(Float, comment="量比")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 创建复合索引
    __table_args__ = (
        Index('idx_basic_code_date', 'ts_code', 'trade_date'),
    )

class MonitoringConfig(Base):
    """监控配置表"""
    __tablename__ = "monitoring_config"
    
    id = Column(Integer, primary_key=True, index=True)
    ts_code = Column(String(20), index=True, comment="股票代码")
    
    # 技术指标配置
    ma_periods = Column(String(50), default="5,10,20,60", comment="均线周期")
    rsi_period = Column(Integer, default=14, comment="RSI周期")
    macd_config = Column(String(20), default="12,26,9", comment="MACD参数")
    
    # 风险控制配置
    max_position_ratio = Column(Float, default=0.1, comment="最大持仓比例")
    stop_loss_ratio = Column(Float, default=0.05, comment="止损比例")
    max_drawdown = Column(Float, default=0.1, comment="最大回撤")
    
    # 告警配置
    price_change_threshold = Column(Float, default=0.05, comment="价格变动告警阈值")
    volume_change_threshold = Column(Float, default=2.0, comment="成交量变动告警阈值")
    
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class Alert(Base):
    """告警记录表"""
    __tablename__ = "alerts"
    
    id = Column(Integer, primary_key=True, index=True)
    ts_code = Column(String(20), index=True, comment="股票代码")
    alert_type = Column(String(20), comment="告警类型")
    severity = Column(String(10), comment="严重程度：LOW/MEDIUM/HIGH")
    title = Column(String(100), comment="告警标题")
    message = Column(Text, comment="告警内容")
    
    # 告警数据
    trigger_value = Column(Float, comment="触发值")
    threshold_value = Column(Float, comment="阈值")
    
    # 状态管理
    status = Column(String(10), default="ACTIVE", comment="状态：ACTIVE/RESOLVED/IGNORED")
    resolved_at = Column(DateTime(timezone=True), comment="解决时间")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 创建索引
    __table_args__ = (
        Index('idx_alert_code_status', 'ts_code', 'status'),
        Index('idx_alert_type_severity', 'alert_type', 'severity'),
    )
