{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nvar each = zrUtil.each;\n/**\r\n * @param {string} key\r\n * @param {module:echarts/ExtensionAPI} api\r\n * @param {Function} handler\r\n *      param: {string} currTrigger\r\n *      param: {Array.<number>} point\r\n */\nexport function register(key, api, handler) {\n  if (env.node) {\n    return;\n  }\n  var zr = api.getZr();\n  inner(zr).records || (inner(zr).records = {});\n  initGlobalListeners(zr, api);\n  var record = inner(zr).records[key] || (inner(zr).records[key] = {});\n  record.handler = handler;\n}\nfunction initGlobalListeners(zr, api) {\n  if (inner(zr).initialized) {\n    return;\n  }\n  inner(zr).initialized = true;\n  useHandler('click', zrUtil.curry(doEnter, 'click'));\n  useHandler('mousemove', zrUtil.curry(doEnter, 'mousemove'));\n  // useHandler('mouseout', onLeave);\n  useHandler('globalout', onLeave);\n  function useHandler(eventType, cb) {\n    zr.on(eventType, function (e) {\n      var dis = makeDispatchAction(api);\n      each(inner(zr).records, function (record) {\n        record && cb(record, e, dis.dispatchAction);\n      });\n      dispatchTooltipFinally(dis.pendings, api);\n    });\n  }\n}\nfunction dispatchTooltipFinally(pendings, api) {\n  var showLen = pendings.showTip.length;\n  var hideLen = pendings.hideTip.length;\n  var actuallyPayload;\n  if (showLen) {\n    actuallyPayload = pendings.showTip[showLen - 1];\n  } else if (hideLen) {\n    actuallyPayload = pendings.hideTip[hideLen - 1];\n  }\n  if (actuallyPayload) {\n    actuallyPayload.dispatchAction = null;\n    api.dispatchAction(actuallyPayload);\n  }\n}\nfunction onLeave(record, e, dispatchAction) {\n  record.handler('leave', null, dispatchAction);\n}\nfunction doEnter(currTrigger, record, e, dispatchAction) {\n  record.handler(currTrigger, e, dispatchAction);\n}\nfunction makeDispatchAction(api) {\n  var pendings = {\n    showTip: [],\n    hideTip: []\n  };\n  // FIXME\n  // better approach?\n  // 'showTip' and 'hideTip' can be triggered by axisPointer and tooltip,\n  // which may be conflict, (axisPointer call showTip but tooltip call hideTip);\n  // So we have to add \"final stage\" to merge those dispatched actions.\n  var dispatchAction = function (payload) {\n    var pendingList = pendings[payload.type];\n    if (pendingList) {\n      pendingList.push(payload);\n    } else {\n      payload.dispatchAction = dispatchAction;\n      api.dispatchAction(payload);\n    }\n  };\n  return {\n    dispatchAction: dispatchAction,\n    pendings: pendings\n  };\n}\nexport function unregister(key, api) {\n  if (env.node) {\n    return;\n  }\n  var zr = api.getZr();\n  var record = (inner(zr).records || {})[key];\n  if (record) {\n    inner(zr).records[key] = null;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}