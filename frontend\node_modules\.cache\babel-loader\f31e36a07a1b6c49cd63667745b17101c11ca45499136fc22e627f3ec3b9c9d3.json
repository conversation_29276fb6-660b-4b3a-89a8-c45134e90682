{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { registerPreprocessor, registerProcessor, registerPostInit, registerPostUpdate, registerAction, registerCoordinateSystem, registerLayout, registerVisual, registerTransform, registerLoading, registerMap, registerUpdateLifecycle, PRIORITY } from './core/echarts.js';\nimport ComponentView from './view/Component.js';\nimport ChartView from './view/Chart.js';\nimport ComponentModel from './model/Component.js';\nimport SeriesModel from './model/Series.js';\nimport { isFunction, indexOf, isArray, each } from 'zrender/lib/core/util.js';\nimport { registerImpl } from './core/impl.js';\nimport { registerPainter } from 'zrender/lib/zrender.js';\nvar extensions = [];\nvar extensionRegisters = {\n  registerPreprocessor: registerPreprocessor,\n  registerProcessor: registerProcessor,\n  registerPostInit: registerPostInit,\n  registerPostUpdate: registerPostUpdate,\n  registerUpdateLifecycle: registerUpdateLifecycle,\n  registerAction: registerAction,\n  registerCoordinateSystem: registerCoordinateSystem,\n  registerLayout: registerLayout,\n  registerVisual: registerVisual,\n  registerTransform: registerTransform,\n  registerLoading: registerLoading,\n  registerMap: registerMap,\n  registerImpl: registerImpl,\n  PRIORITY: PRIORITY,\n  ComponentModel: ComponentModel,\n  ComponentView: ComponentView,\n  SeriesModel: SeriesModel,\n  ChartView: ChartView,\n  // TODO Use ComponentModel and SeriesModel instead of Constructor\n  registerComponentModel: function (ComponentModelClass) {\n    ComponentModel.registerClass(ComponentModelClass);\n  },\n  registerComponentView: function (ComponentViewClass) {\n    ComponentView.registerClass(ComponentViewClass);\n  },\n  registerSeriesModel: function (SeriesModelClass) {\n    SeriesModel.registerClass(SeriesModelClass);\n  },\n  registerChartView: function (ChartViewClass) {\n    ChartView.registerClass(ChartViewClass);\n  },\n  registerSubTypeDefaulter: function (componentType, defaulter) {\n    ComponentModel.registerSubTypeDefaulter(componentType, defaulter);\n  },\n  registerPainter: function (painterType, PainterCtor) {\n    registerPainter(painterType, PainterCtor);\n  }\n};\nexport function use(ext) {\n  if (isArray(ext)) {\n    // use([ChartLine, ChartBar]);\n    each(ext, function (singleExt) {\n      use(singleExt);\n    });\n    return;\n  }\n  if (indexOf(extensions, ext) >= 0) {\n    return;\n  }\n  extensions.push(ext);\n  if (isFunction(ext)) {\n    ext = {\n      install: ext\n    };\n  }\n  ext.install(extensionRegisters);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}