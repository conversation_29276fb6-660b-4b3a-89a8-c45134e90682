{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport Graph from '../../data/Graph.js';\nimport linkSeriesData from '../../data/helper/linkSeriesData.js';\nimport prepareSeriesDataSchema from '../../data/helper/createDimensions.js';\nimport CoordinateSystem from '../../core/CoordinateSystem.js';\nimport createSeriesData from './createSeriesData.js';\nimport { convertOptionIdName } from '../../util/model.js';\nexport default function createGraphFromNodeEdge(nodes, edges, seriesModel, directed, beforeLink) {\n  // ??? TODO\n  // support dataset?\n  var graph = new Graph(directed);\n  for (var i = 0; i < nodes.length; i++) {\n    graph.addNode(zrUtil.retrieve(\n    // Id, name, dataIndex\n    nodes[i].id, nodes[i].name, i), i);\n  }\n  var linkNameList = [];\n  var validEdges = [];\n  var linkCount = 0;\n  for (var i = 0; i < edges.length; i++) {\n    var link = edges[i];\n    var source = link.source;\n    var target = link.target;\n    // addEdge may fail when source or target not exists\n    if (graph.addEdge(source, target, linkCount)) {\n      validEdges.push(link);\n      linkNameList.push(zrUtil.retrieve(convertOptionIdName(link.id, null), source + ' > ' + target));\n      linkCount++;\n    }\n  }\n  var coordSys = seriesModel.get('coordinateSystem');\n  var nodeData;\n  if (coordSys === 'cartesian2d' || coordSys === 'polar') {\n    nodeData = createSeriesData(nodes, seriesModel);\n  } else {\n    var coordSysCtor = CoordinateSystem.get(coordSys);\n    var coordDimensions = coordSysCtor ? coordSysCtor.dimensions || [] : [];\n    // FIXME: Some geo do not need `value` dimenson, whereas `calendar` needs\n    // `value` dimension, but graph need `value` dimension. It's better to\n    // uniform this behavior.\n    if (zrUtil.indexOf(coordDimensions, 'value') < 0) {\n      coordDimensions.concat(['value']);\n    }\n    var dimensions = prepareSeriesDataSchema(nodes, {\n      coordDimensions: coordDimensions,\n      encodeDefine: seriesModel.getEncode()\n    }).dimensions;\n    nodeData = new SeriesData(dimensions, seriesModel);\n    nodeData.initData(nodes);\n  }\n  var edgeData = new SeriesData(['value'], seriesModel);\n  edgeData.initData(validEdges, linkNameList);\n  beforeLink && beforeLink(nodeData, edgeData);\n  linkSeriesData({\n    mainData: nodeData,\n    struct: graph,\n    structAttr: 'graph',\n    datas: {\n      node: nodeData,\n      edge: edgeData\n    },\n    datasAttr: {\n      node: 'data',\n      edge: 'edgeData'\n    }\n  });\n  // Update dataIndex of nodes and edges because invalid edge may be removed\n  graph.update();\n  return graph;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}