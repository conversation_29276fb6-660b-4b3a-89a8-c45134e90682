{"ast": null, "code": "var _jsxFileName = \"C:\\\\Owen_Zhang\\\\\\u91CF\\u5316\\u4EA4\\u6613\\u7CFB\\u7EDF\\\\frontend\\\\src\\\\pages\\\\AIAnalysis.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Typography, Tag, Progress, Timeline, Button, Input, Space, Spin } from 'antd';\nimport { RobotOutlined, BulbOutlined, WarningOutlined, TrendingUpOutlined, TrendingDownOutlined, SendOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst AIAnalysis = () => {\n  _s();\n  const [sentimentData, setSentimentData] = useState([]);\n  const [riskEvents, setRiskEvents] = useState([]);\n  const [dailyReport, setDailyReport] = useState(null);\n  const [newsInput, setNewsInput] = useState('');\n  const [analyzing, setAnalyzing] = useState(false);\n  const [analysisResult, setAnalysisResult] = useState(null);\n\n  // 模拟数据\n  useEffect(() => {\n    const mockSentiment = [{\n      ts_code: '000001.SZ',\n      name: '平安银行',\n      sentiment_score: 0.65,\n      sentiment_label: 'POSITIVE',\n      confidence: 0.85,\n      news_count: 12\n    }, {\n      ts_code: '000002.SZ',\n      name: '万科A',\n      sentiment_score: -0.32,\n      sentiment_label: 'NEGATIVE',\n      confidence: 0.78,\n      news_count: 8\n    }, {\n      ts_code: '600000.SH',\n      name: '浦发银行',\n      sentiment_score: 0.15,\n      sentiment_label: 'NEUTRAL',\n      confidence: 0.72,\n      news_count: 5\n    }];\n    const mockRiskEvents = [{\n      id: '1',\n      ts_code: '000002.SZ',\n      name: '万科A',\n      event_type: '监管风险',\n      risk_level: 'HIGH',\n      description: '房地产调控政策收紧，可能影响公司业务',\n      impact: '预计对股价产生负面影响',\n      time: '10:30'\n    }, {\n      id: '2',\n      ts_code: '000001.SZ',\n      name: '平安银行',\n      event_type: '业绩预告',\n      risk_level: 'LOW',\n      description: '三季度业绩超预期，净利润同比增长15%',\n      impact: '利好消息，预期股价上涨',\n      time: '09:45'\n    }];\n    const mockReport = {\n      date: '2024-01-20',\n      market_summary: '今日A股市场整体表现平稳，上证指数微涨0.3%，深证成指下跌0.1%。银行板块表现较好，房地产板块承压。',\n      key_events: ['央行宣布降准0.25个百分点', '房地产调控政策进一步收紧', '科技股集体回调', '银行股普遍上涨'],\n      performance_analysis: '组合今日收益率为+0.8%，跑赢大盘0.5个百分点。主要贡献来自银行股的强势表现，平安银行涨幅达到2.1%。',\n      recommendations: ['继续持有银行股，关注政策面变化', '适当减持房地产相关标的', '关注科技股回调后的买入机会', '控制整体仓位，保持适度现金比例']\n    };\n    setSentimentData(mockSentiment);\n    setRiskEvents(mockRiskEvents);\n    setDailyReport(mockReport);\n  }, []);\n  const handleAnalyzeNews = async () => {\n    if (!newsInput.trim()) return;\n    setAnalyzing(true);\n\n    // 模拟AI分析\n    setTimeout(() => {\n      const mockResult = {\n        sentiment_score: Math.random() * 2 - 1,\n        // -1 到 1\n        sentiment_label: Math.random() > 0.5 ? 'POSITIVE' : 'NEGATIVE',\n        confidence: 0.7 + Math.random() * 0.3,\n        key_points: ['检测到政策相关关键词', '情绪倾向偏向积极', '可能对银行板块产生影响'],\n        risk_factors: ['政策不确定性', '市场波动风险']\n      };\n      setAnalysisResult(mockResult);\n      setAnalyzing(false);\n    }, 2000);\n  };\n  const getSentimentColor = score => {\n    if (score > 0.3) return '#52c41a';\n    if (score < -0.3) return '#ff4d4f';\n    return '#faad14';\n  };\n  const getSentimentIcon = label => {\n    if (label === 'POSITIVE') return /*#__PURE__*/_jsxDEV(TrendingUpOutlined, {\n      style: {\n        color: '#52c41a'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 38\n    }, this);\n    if (label === 'NEGATIVE') return /*#__PURE__*/_jsxDEV(TrendingDownOutlined, {\n      style: {\n        color: '#ff4d4f'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 38\n    }, this);\n    return /*#__PURE__*/_jsxDEV(BulbOutlined, {\n      style: {\n        color: '#faad14'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 12\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), \" AI\\u667A\\u80FD\\u5206\\u6790\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5E02\\u573A\\u60C5\\u7EEA\\u5206\\u6790\",\n          extra: /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: \"\\u5B9E\\u65F6\\u66F4\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 39\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: sentimentData.map(item => /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: '8px'\n                  },\n                  children: [getSentimentIcon(item.sentiment_label), /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    style: {\n                      marginLeft: '8px'\n                    },\n                    children: item.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                  type: \"circle\",\n                  percent: Math.abs(item.sentiment_score) * 100,\n                  strokeColor: getSentimentColor(item.sentiment_score),\n                  format: () => item.sentiment_score.toFixed(2),\n                  size: 80\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Tag, {\n                    color: getSentimentColor(item.sentiment_score),\n                    children: item.sentiment_label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '12px',\n                      color: '#666'\n                    },\n                    children: [\"\\u7F6E\\u4FE1\\u5EA6: \", (item.confidence * 100).toFixed(1), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '12px',\n                      color: '#666'\n                    },\n                    children: [\"\\u65B0\\u95FB\\u6570\\u91CF: \", item.news_count]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)\n            }, item.ts_code, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u98CE\\u9669\\u4E8B\\u4EF6\\u76D1\\u63A7\",\n          extra: /*#__PURE__*/_jsxDEV(WarningOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 39\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Timeline, {\n            children: riskEvents.map(event => /*#__PURE__*/_jsxDEV(Timeline.Item, {\n              color: event.risk_level === 'HIGH' ? 'red' : event.risk_level === 'MEDIUM' ? 'orange' : 'green',\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: event.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: event.risk_level === 'HIGH' ? 'red' : 'orange',\n                  style: {\n                    marginLeft: '8px'\n                  },\n                  children: event.risk_level\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: event.event_type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    float: 'right',\n                    color: '#666'\n                  },\n                  children: event.time\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                style: {\n                  marginTop: '8px',\n                  marginBottom: '4px'\n                },\n                children: event.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: '12px'\n                },\n                children: [\"\\u5F71\\u54CD\\u8BC4\\u4F30: \", event.impact]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this)]\n            }, event.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u65B0\\u95FB\\u5206\\u6790\\u5DE5\\u5177\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TextArea, {\n              rows: 6,\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u8981\\u5206\\u6790\\u7684\\u65B0\\u95FB\\u5185\\u5BB9...\",\n              value: newsInput,\n              onChange: e => setNewsInput(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(SendOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 23\n              }, this),\n              onClick: handleAnalyzeNews,\n              loading: analyzing,\n              disabled: !newsInput.trim(),\n              children: \"AI\\u5206\\u6790\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this), analyzing && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Spin, {\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: '16px'\n                },\n                children: \"AI\\u6B63\\u5728\\u5206\\u6790\\u4E2D...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), analysisResult && !analyzing && /*#__PURE__*/_jsxDEV(Card, {\n              size: \"small\",\n              title: \"\\u5206\\u6790\\u7ED3\\u679C\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '12px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u60C5\\u7EEA\\u8BC4\\u5206: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                  color: getSentimentColor(analysisResult.sentiment_score),\n                  children: analysisResult.sentiment_score.toFixed(2)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  style: {\n                    marginLeft: '16px'\n                  },\n                  children: \"\\u7F6E\\u4FE1\\u5EA6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [(analysisResult.confidence * 100).toFixed(1), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '12px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u5173\\u952E\\u8981\\u70B9:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  style: {\n                    marginTop: '8px'\n                  },\n                  children: analysisResult.key_points.map((point, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: point\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u98CE\\u9669\\u56E0\\u7D20:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  style: {\n                    marginTop: '8px'\n                  },\n                  children: analysisResult.risk_factors.map((factor, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: factor\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this), dailyReport && /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: `每日AI复盘报告 - ${dailyReport.date}`,\n          extra: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 67\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: [/*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                title: \"\\u5E02\\u573A\\u6982\\u51B5\",\n                children: /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: dailyReport.market_summary\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                title: \"\\u5173\\u952E\\u4E8B\\u4EF6\",\n                style: {\n                  marginTop: '16px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                  children: dailyReport.key_events.map((event, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: event\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: [/*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                title: \"\\u7EC4\\u5408\\u8868\\u73B0\",\n                children: /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: dailyReport.performance_analysis\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                title: \"AI\\u5EFA\\u8BAE\",\n                style: {\n                  marginTop: '16px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                  children: dailyReport.recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: rec\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n};\n_s(AIAnalysis, \"QzdA5jGhDBqrDbuW+B6fC20ivcE=\");\n_c = AIAnalysis;\nexport default AIAnalysis;\nvar _c;\n$RefreshReg$(_c, \"AIAnalysis\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Typography", "Tag", "Progress", "Timeline", "<PERSON><PERSON>", "Input", "Space", "Spin", "RobotOutlined", "BulbOutlined", "WarningOutlined", "TrendingUpOutlined", "TrendingDownOutlined", "SendOutlined", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "Text", "TextArea", "AIAnalysis", "_s", "sentimentData", "setSentimentData", "riskEvents", "setRiskEvents", "dailyReport", "setDailyReport", "newsInput", "setNewsInput", "analyzing", "setAnalyzing", "analysisResult", "setAnalysisResult", "mockSentiment", "ts_code", "name", "sentiment_score", "sentiment_label", "confidence", "news_count", "mockRiskEvents", "id", "event_type", "risk_level", "description", "impact", "time", "mockReport", "date", "market_summary", "key_events", "performance_analysis", "recommendations", "handleAnalyzeNews", "trim", "setTimeout", "mockResult", "Math", "random", "key_points", "risk_factors", "getSentimentColor", "score", "getSentimentIcon", "label", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "padding", "children", "level", "gutter", "marginBottom", "span", "title", "extra", "map", "item", "size", "textAlign", "strong", "marginLeft", "type", "percent", "abs", "strokeColor", "format", "toFixed", "marginTop", "fontSize", "event", "<PERSON><PERSON>", "float", "direction", "width", "rows", "placeholder", "value", "onChange", "e", "target", "icon", "onClick", "loading", "disabled", "point", "index", "factor", "rec", "_c", "$RefreshReg$"], "sources": ["C:/<PERSON>_<PERSON>/量化交易系统/frontend/src/pages/AIAnalysis.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  Row, \n  Col, \n  Typography, \n  Tag, \n  Progress, \n  Timeline,\n  Alert,\n  Button,\n  Input,\n  Space,\n  Spin\n} from 'antd';\nimport { \n  RobotOutlined,\n  BulbOutlined,\n  WarningOutlined,\n  TrendingUpOutlined,\n  TrendingDownOutlined,\n  SendOutlined\n} from '@ant-design/icons';\n\nconst { Title, Paragraph, Text } = Typography;\nconst { TextArea } = Input;\n\ninterface SentimentData {\n  ts_code: string;\n  name: string;\n  sentiment_score: number;\n  sentiment_label: string;\n  confidence: number;\n  news_count: number;\n}\n\ninterface RiskEvent {\n  id: string;\n  ts_code: string;\n  name: string;\n  event_type: string;\n  risk_level: string;\n  description: string;\n  impact: string;\n  time: string;\n}\n\ninterface DailyReport {\n  date: string;\n  market_summary: string;\n  key_events: string[];\n  performance_analysis: string;\n  recommendations: string[];\n}\n\nconst AIAnalysis: React.FC = () => {\n  const [sentimentData, setSentimentData] = useState<SentimentData[]>([]);\n  const [riskEvents, setRiskEvents] = useState<RiskEvent[]>([]);\n  const [dailyReport, setDailyReport] = useState<DailyReport | null>(null);\n  const [newsInput, setNewsInput] = useState('');\n  const [analyzing, setAnalyzing] = useState(false);\n  const [analysisResult, setAnalysisResult] = useState<any>(null);\n\n  // 模拟数据\n  useEffect(() => {\n    const mockSentiment: SentimentData[] = [\n      {\n        ts_code: '000001.SZ',\n        name: '平安银行',\n        sentiment_score: 0.65,\n        sentiment_label: 'POSITIVE',\n        confidence: 0.85,\n        news_count: 12\n      },\n      {\n        ts_code: '000002.SZ',\n        name: '万科A',\n        sentiment_score: -0.32,\n        sentiment_label: 'NEGATIVE',\n        confidence: 0.78,\n        news_count: 8\n      },\n      {\n        ts_code: '600000.SH',\n        name: '浦发银行',\n        sentiment_score: 0.15,\n        sentiment_label: 'NEUTRAL',\n        confidence: 0.72,\n        news_count: 5\n      }\n    ];\n\n    const mockRiskEvents: RiskEvent[] = [\n      {\n        id: '1',\n        ts_code: '000002.SZ',\n        name: '万科A',\n        event_type: '监管风险',\n        risk_level: 'HIGH',\n        description: '房地产调控政策收紧，可能影响公司业务',\n        impact: '预计对股价产生负面影响',\n        time: '10:30'\n      },\n      {\n        id: '2',\n        ts_code: '000001.SZ',\n        name: '平安银行',\n        event_type: '业绩预告',\n        risk_level: 'LOW',\n        description: '三季度业绩超预期，净利润同比增长15%',\n        impact: '利好消息，预期股价上涨',\n        time: '09:45'\n      }\n    ];\n\n    const mockReport: DailyReport = {\n      date: '2024-01-20',\n      market_summary: '今日A股市场整体表现平稳，上证指数微涨0.3%，深证成指下跌0.1%。银行板块表现较好，房地产板块承压。',\n      key_events: [\n        '央行宣布降准0.25个百分点',\n        '房地产调控政策进一步收紧',\n        '科技股集体回调',\n        '银行股普遍上涨'\n      ],\n      performance_analysis: '组合今日收益率为+0.8%，跑赢大盘0.5个百分点。主要贡献来自银行股的强势表现，平安银行涨幅达到2.1%。',\n      recommendations: [\n        '继续持有银行股，关注政策面变化',\n        '适当减持房地产相关标的',\n        '关注科技股回调后的买入机会',\n        '控制整体仓位，保持适度现金比例'\n      ]\n    };\n\n    setSentimentData(mockSentiment);\n    setRiskEvents(mockRiskEvents);\n    setDailyReport(mockReport);\n  }, []);\n\n  const handleAnalyzeNews = async () => {\n    if (!newsInput.trim()) return;\n\n    setAnalyzing(true);\n    \n    // 模拟AI分析\n    setTimeout(() => {\n      const mockResult = {\n        sentiment_score: Math.random() * 2 - 1, // -1 到 1\n        sentiment_label: Math.random() > 0.5 ? 'POSITIVE' : 'NEGATIVE',\n        confidence: 0.7 + Math.random() * 0.3,\n        key_points: [\n          '检测到政策相关关键词',\n          '情绪倾向偏向积极',\n          '可能对银行板块产生影响'\n        ],\n        risk_factors: [\n          '政策不确定性',\n          '市场波动风险'\n        ]\n      };\n      \n      setAnalysisResult(mockResult);\n      setAnalyzing(false);\n    }, 2000);\n  };\n\n  const getSentimentColor = (score: number) => {\n    if (score > 0.3) return '#52c41a';\n    if (score < -0.3) return '#ff4d4f';\n    return '#faad14';\n  };\n\n  const getSentimentIcon = (label: string) => {\n    if (label === 'POSITIVE') return <TrendingUpOutlined style={{ color: '#52c41a' }} />;\n    if (label === 'NEGATIVE') return <TrendingDownOutlined style={{ color: '#ff4d4f' }} />;\n    return <BulbOutlined style={{ color: '#faad14' }} />;\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Title level={2}>\n        <RobotOutlined /> AI智能分析\n      </Title>\n\n      {/* 市场情绪分析 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={24}>\n          <Card title=\"市场情绪分析\" extra={<Tag color=\"blue\">实时更新</Tag>}>\n            <Row gutter={16}>\n              {sentimentData.map(item => (\n                <Col span={8} key={item.ts_code}>\n                  <Card size=\"small\" style={{ textAlign: 'center' }}>\n                    <div style={{ marginBottom: '8px' }}>\n                      {getSentimentIcon(item.sentiment_label)}\n                      <Text strong style={{ marginLeft: '8px' }}>{item.name}</Text>\n                    </div>\n                    <Progress\n                      type=\"circle\"\n                      percent={Math.abs(item.sentiment_score) * 100}\n                      strokeColor={getSentimentColor(item.sentiment_score)}\n                      format={() => item.sentiment_score.toFixed(2)}\n                      size={80}\n                    />\n                    <div style={{ marginTop: '8px' }}>\n                      <Tag color={getSentimentColor(item.sentiment_score)}>\n                        {item.sentiment_label}\n                      </Tag>\n                      <div style={{ fontSize: '12px', color: '#666' }}>\n                        置信度: {(item.confidence * 100).toFixed(1)}%\n                      </div>\n                      <div style={{ fontSize: '12px', color: '#666' }}>\n                        新闻数量: {item.news_count}\n                      </div>\n                    </div>\n                  </Card>\n                </Col>\n              ))}\n            </Row>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 风险事件监控 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={12}>\n          <Card title=\"风险事件监控\" extra={<WarningOutlined />}>\n            <Timeline>\n              {riskEvents.map(event => (\n                <Timeline.Item\n                  key={event.id}\n                  color={event.risk_level === 'HIGH' ? 'red' : event.risk_level === 'MEDIUM' ? 'orange' : 'green'}\n                >\n                  <div>\n                    <Text strong>{event.name}</Text>\n                    <Tag color={event.risk_level === 'HIGH' ? 'red' : 'orange'} style={{ marginLeft: '8px' }}>\n                      {event.risk_level}\n                    </Tag>\n                  </div>\n                  <div style={{ marginTop: '4px' }}>\n                    <Text type=\"secondary\">{event.event_type}</Text>\n                    <span style={{ float: 'right', color: '#666' }}>{event.time}</span>\n                  </div>\n                  <Paragraph style={{ marginTop: '8px', marginBottom: '4px' }}>\n                    {event.description}\n                  </Paragraph>\n                  <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n                    影响评估: {event.impact}\n                  </Text>\n                </Timeline.Item>\n              ))}\n            </Timeline>\n          </Card>\n        </Col>\n\n        <Col span={12}>\n          <Card title=\"新闻分析工具\">\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <TextArea\n                rows={6}\n                placeholder=\"请输入要分析的新闻内容...\"\n                value={newsInput}\n                onChange={(e) => setNewsInput(e.target.value)}\n              />\n              <Button\n                type=\"primary\"\n                icon={<SendOutlined />}\n                onClick={handleAnalyzeNews}\n                loading={analyzing}\n                disabled={!newsInput.trim()}\n              >\n                AI分析\n              </Button>\n              \n              {analyzing && (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <Spin size=\"large\" />\n                  <div style={{ marginTop: '16px' }}>AI正在分析中...</div>\n                </div>\n              )}\n              \n              {analysisResult && !analyzing && (\n                <Card size=\"small\" title=\"分析结果\">\n                  <div style={{ marginBottom: '12px' }}>\n                    <Text strong>情绪评分: </Text>\n                    <Tag color={getSentimentColor(analysisResult.sentiment_score)}>\n                      {analysisResult.sentiment_score.toFixed(2)}\n                    </Tag>\n                    <Text strong style={{ marginLeft: '16px' }}>置信度: </Text>\n                    <span>{(analysisResult.confidence * 100).toFixed(1)}%</span>\n                  </div>\n                  \n                  <div style={{ marginBottom: '12px' }}>\n                    <Text strong>关键要点:</Text>\n                    <ul style={{ marginTop: '8px' }}>\n                      {analysisResult.key_points.map((point: string, index: number) => (\n                        <li key={index}>{point}</li>\n                      ))}\n                    </ul>\n                  </div>\n                  \n                  <div>\n                    <Text strong>风险因素:</Text>\n                    <ul style={{ marginTop: '8px' }}>\n                      {analysisResult.risk_factors.map((factor: string, index: number) => (\n                        <li key={index}>{factor}</li>\n                      ))}\n                    </ul>\n                  </div>\n                </Card>\n              )}\n            </Space>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 每日AI报告 */}\n      {dailyReport && (\n        <Row>\n          <Col span={24}>\n            <Card title={`每日AI复盘报告 - ${dailyReport.date}`} extra={<RobotOutlined />}>\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Card size=\"small\" title=\"市场概况\">\n                    <Paragraph>{dailyReport.market_summary}</Paragraph>\n                  </Card>\n                  \n                  <Card size=\"small\" title=\"关键事件\" style={{ marginTop: '16px' }}>\n                    <ul>\n                      {dailyReport.key_events.map((event, index) => (\n                        <li key={index}>{event}</li>\n                      ))}\n                    </ul>\n                  </Card>\n                </Col>\n                \n                <Col span={12}>\n                  <Card size=\"small\" title=\"组合表现\">\n                    <Paragraph>{dailyReport.performance_analysis}</Paragraph>\n                  </Card>\n                  \n                  <Card size=\"small\" title=\"AI建议\" style={{ marginTop: '16px' }}>\n                    <ul>\n                      {dailyReport.recommendations.map((rec, index) => (\n                        <li key={index}>{rec}</li>\n                      ))}\n                    </ul>\n                  </Card>\n                </Col>\n              </Row>\n            </Card>\n          </Col>\n        </Row>\n      )}\n    </div>\n  );\n};\n\nexport default AIAnalysis;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,QAAQ,EACRC,QAAQ,EAERC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,IAAI,QACC,MAAM;AACb,SACEC,aAAa,EACbC,YAAY,EACZC,eAAe,EACfC,kBAAkB,EAClBC,oBAAoB,EACpBC,YAAY,QACP,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC,SAAS;EAAEC;AAAK,CAAC,GAAGlB,UAAU;AAC7C,MAAM;EAAEmB;AAAS,CAAC,GAAGd,KAAK;AA8B1B,MAAMe,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAkB,EAAE,CAAC;EACvE,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAqB,IAAI,CAAC;EACxE,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAM,IAAI,CAAC;;EAE/D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMsC,aAA8B,GAAG,CACrC;MACEC,OAAO,EAAE,WAAW;MACpBC,IAAI,EAAE,MAAM;MACZC,eAAe,EAAE,IAAI;MACrBC,eAAe,EAAE,UAAU;MAC3BC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;IACd,CAAC,EACD;MACEL,OAAO,EAAE,WAAW;MACpBC,IAAI,EAAE,KAAK;MACXC,eAAe,EAAE,CAAC,IAAI;MACtBC,eAAe,EAAE,UAAU;MAC3BC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;IACd,CAAC,EACD;MACEL,OAAO,EAAE,WAAW;MACpBC,IAAI,EAAE,MAAM;MACZC,eAAe,EAAE,IAAI;MACrBC,eAAe,EAAE,SAAS;MAC1BC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;IACd,CAAC,CACF;IAED,MAAMC,cAA2B,GAAG,CAClC;MACEC,EAAE,EAAE,GAAG;MACPP,OAAO,EAAE,WAAW;MACpBC,IAAI,EAAE,KAAK;MACXO,UAAU,EAAE,MAAM;MAClBC,UAAU,EAAE,MAAM;MAClBC,WAAW,EAAE,oBAAoB;MACjCC,MAAM,EAAE,aAAa;MACrBC,IAAI,EAAE;IACR,CAAC,EACD;MACEL,EAAE,EAAE,GAAG;MACPP,OAAO,EAAE,WAAW;MACpBC,IAAI,EAAE,MAAM;MACZO,UAAU,EAAE,MAAM;MAClBC,UAAU,EAAE,KAAK;MACjBC,WAAW,EAAE,qBAAqB;MAClCC,MAAM,EAAE,aAAa;MACrBC,IAAI,EAAE;IACR,CAAC,CACF;IAED,MAAMC,UAAuB,GAAG;MAC9BC,IAAI,EAAE,YAAY;MAClBC,cAAc,EAAE,sDAAsD;MACtEC,UAAU,EAAE,CACV,gBAAgB,EAChB,cAAc,EACd,SAAS,EACT,SAAS,CACV;MACDC,oBAAoB,EAAE,wDAAwD;MAC9EC,eAAe,EAAE,CACf,iBAAiB,EACjB,aAAa,EACb,eAAe,EACf,iBAAiB;IAErB,CAAC;IAED9B,gBAAgB,CAACW,aAAa,CAAC;IAC/BT,aAAa,CAACgB,cAAc,CAAC;IAC7Bd,cAAc,CAACqB,UAAU,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC1B,SAAS,CAAC2B,IAAI,CAAC,CAAC,EAAE;IAEvBxB,YAAY,CAAC,IAAI,CAAC;;IAElB;IACAyB,UAAU,CAAC,MAAM;MACf,MAAMC,UAAU,GAAG;QACjBpB,eAAe,EAAEqB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QAAE;QACxCrB,eAAe,EAAEoB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,UAAU,GAAG,UAAU;QAC9DpB,UAAU,EAAE,GAAG,GAAGmB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;QACrCC,UAAU,EAAE,CACV,YAAY,EACZ,UAAU,EACV,aAAa,CACd;QACDC,YAAY,EAAE,CACZ,QAAQ,EACR,QAAQ;MAEZ,CAAC;MAED5B,iBAAiB,CAACwB,UAAU,CAAC;MAC7B1B,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAM+B,iBAAiB,GAAIC,KAAa,IAAK;IAC3C,IAAIA,KAAK,GAAG,GAAG,EAAE,OAAO,SAAS;IACjC,IAAIA,KAAK,GAAG,CAAC,GAAG,EAAE,OAAO,SAAS;IAClC,OAAO,SAAS;EAClB,CAAC;EAED,MAAMC,gBAAgB,GAAIC,KAAa,IAAK;IAC1C,IAAIA,KAAK,KAAK,UAAU,EAAE,oBAAOlD,OAAA,CAACJ,kBAAkB;MAACuD,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAU;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpF,IAAIN,KAAK,KAAK,UAAU,EAAE,oBAAOlD,OAAA,CAACH,oBAAoB;MAACsD,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAU;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtF,oBAAOxD,OAAA,CAACN,YAAY;MAACyD,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAU;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACtD,CAAC;EAED,oBACExD,OAAA;IAAKmD,KAAK,EAAE;MAAEM,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9B1D,OAAA,CAACC,KAAK;MAAC0D,KAAK,EAAE,CAAE;MAAAD,QAAA,gBACd1D,OAAA,CAACP,aAAa;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,+BACnB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAGRxD,OAAA,CAACjB,GAAG;MAAC6E,MAAM,EAAE,EAAG;MAACT,KAAK,EAAE;QAAEU,YAAY,EAAE;MAAO,CAAE;MAAAH,QAAA,eAC/C1D,OAAA,CAAChB,GAAG;QAAC8E,IAAI,EAAE,EAAG;QAAAJ,QAAA,eACZ1D,OAAA,CAAClB,IAAI;UAACiF,KAAK,EAAC,sCAAQ;UAACC,KAAK,eAAEhE,OAAA,CAACd,GAAG;YAACkE,KAAK,EAAC,MAAM;YAAAM,QAAA,EAAC;UAAI;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAE;UAAAE,QAAA,eACvD1D,OAAA,CAACjB,GAAG;YAAC6E,MAAM,EAAE,EAAG;YAAAF,QAAA,EACbnD,aAAa,CAAC0D,GAAG,CAACC,IAAI,iBACrBlE,OAAA,CAAChB,GAAG;cAAC8E,IAAI,EAAE,CAAE;cAAAJ,QAAA,eACX1D,OAAA,CAAClB,IAAI;gBAACqF,IAAI,EAAC,OAAO;gBAAChB,KAAK,EAAE;kBAAEiB,SAAS,EAAE;gBAAS,CAAE;gBAAAV,QAAA,gBAChD1D,OAAA;kBAAKmD,KAAK,EAAE;oBAAEU,YAAY,EAAE;kBAAM,CAAE;kBAAAH,QAAA,GACjCT,gBAAgB,CAACiB,IAAI,CAAC3C,eAAe,CAAC,eACvCvB,OAAA,CAACG,IAAI;oBAACkE,MAAM;oBAAClB,KAAK,EAAE;sBAAEmB,UAAU,EAAE;oBAAM,CAAE;oBAAAZ,QAAA,EAAEQ,IAAI,CAAC7C;kBAAI;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACNxD,OAAA,CAACb,QAAQ;kBACPoF,IAAI,EAAC,QAAQ;kBACbC,OAAO,EAAE7B,IAAI,CAAC8B,GAAG,CAACP,IAAI,CAAC5C,eAAe,CAAC,GAAG,GAAI;kBAC9CoD,WAAW,EAAE3B,iBAAiB,CAACmB,IAAI,CAAC5C,eAAe,CAAE;kBACrDqD,MAAM,EAAEA,CAAA,KAAMT,IAAI,CAAC5C,eAAe,CAACsD,OAAO,CAAC,CAAC,CAAE;kBAC9CT,IAAI,EAAE;gBAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACFxD,OAAA;kBAAKmD,KAAK,EAAE;oBAAE0B,SAAS,EAAE;kBAAM,CAAE;kBAAAnB,QAAA,gBAC/B1D,OAAA,CAACd,GAAG;oBAACkE,KAAK,EAAEL,iBAAiB,CAACmB,IAAI,CAAC5C,eAAe,CAAE;oBAAAoC,QAAA,EACjDQ,IAAI,CAAC3C;kBAAe;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACNxD,OAAA;oBAAKmD,KAAK,EAAE;sBAAE2B,QAAQ,EAAE,MAAM;sBAAE1B,KAAK,EAAE;oBAAO,CAAE;oBAAAM,QAAA,GAAC,sBAC1C,EAAC,CAACQ,IAAI,CAAC1C,UAAU,GAAG,GAAG,EAAEoD,OAAO,CAAC,CAAC,CAAC,EAAC,GAC3C;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNxD,OAAA;oBAAKmD,KAAK,EAAE;sBAAE2B,QAAQ,EAAE,MAAM;sBAAE1B,KAAK,EAAE;oBAAO,CAAE;oBAAAM,QAAA,GAAC,4BACzC,EAACQ,IAAI,CAACzC,UAAU;kBAAA;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC,GAxBUU,IAAI,CAAC9C,OAAO;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyB1B,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxD,OAAA,CAACjB,GAAG;MAAC6E,MAAM,EAAE,EAAG;MAACT,KAAK,EAAE;QAAEU,YAAY,EAAE;MAAO,CAAE;MAAAH,QAAA,gBAC/C1D,OAAA,CAAChB,GAAG;QAAC8E,IAAI,EAAE,EAAG;QAAAJ,QAAA,eACZ1D,OAAA,CAAClB,IAAI;UAACiF,KAAK,EAAC,sCAAQ;UAACC,KAAK,eAAEhE,OAAA,CAACL,eAAe;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAE,QAAA,eAC9C1D,OAAA,CAACZ,QAAQ;YAAAsE,QAAA,EACNjD,UAAU,CAACwD,GAAG,CAACc,KAAK,iBACnB/E,OAAA,CAACZ,QAAQ,CAAC4F,IAAI;cAEZ5B,KAAK,EAAE2B,KAAK,CAAClD,UAAU,KAAK,MAAM,GAAG,KAAK,GAAGkD,KAAK,CAAClD,UAAU,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAQ;cAAA6B,QAAA,gBAEhG1D,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA,CAACG,IAAI;kBAACkE,MAAM;kBAAAX,QAAA,EAAEqB,KAAK,CAAC1D;gBAAI;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChCxD,OAAA,CAACd,GAAG;kBAACkE,KAAK,EAAE2B,KAAK,CAAClD,UAAU,KAAK,MAAM,GAAG,KAAK,GAAG,QAAS;kBAACsB,KAAK,EAAE;oBAAEmB,UAAU,EAAE;kBAAM,CAAE;kBAAAZ,QAAA,EACtFqB,KAAK,CAAClD;gBAAU;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxD,OAAA;gBAAKmD,KAAK,EAAE;kBAAE0B,SAAS,EAAE;gBAAM,CAAE;gBAAAnB,QAAA,gBAC/B1D,OAAA,CAACG,IAAI;kBAACoE,IAAI,EAAC,WAAW;kBAAAb,QAAA,EAAEqB,KAAK,CAACnD;gBAAU;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChDxD,OAAA;kBAAMmD,KAAK,EAAE;oBAAE8B,KAAK,EAAE,OAAO;oBAAE7B,KAAK,EAAE;kBAAO,CAAE;kBAAAM,QAAA,EAAEqB,KAAK,CAAC/C;gBAAI;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACNxD,OAAA,CAACE,SAAS;gBAACiD,KAAK,EAAE;kBAAE0B,SAAS,EAAE,KAAK;kBAAEhB,YAAY,EAAE;gBAAM,CAAE;gBAAAH,QAAA,EACzDqB,KAAK,CAACjD;cAAW;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACZxD,OAAA,CAACG,IAAI;gBAACoE,IAAI,EAAC,WAAW;gBAACpB,KAAK,EAAE;kBAAE2B,QAAQ,EAAE;gBAAO,CAAE;gBAAApB,QAAA,GAAC,4BAC5C,EAACqB,KAAK,CAAChD,MAAM;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA,GAlBFuB,KAAK,CAACpD,EAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBA,CAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENxD,OAAA,CAAChB,GAAG;QAAC8E,IAAI,EAAE,EAAG;QAAAJ,QAAA,eACZ1D,OAAA,CAAClB,IAAI;UAACiF,KAAK,EAAC,sCAAQ;UAAAL,QAAA,eAClB1D,OAAA,CAACT,KAAK;YAAC2F,SAAS,EAAC,UAAU;YAAC/B,KAAK,EAAE;cAAEgC,KAAK,EAAE;YAAO,CAAE;YAAAzB,QAAA,gBACnD1D,OAAA,CAACI,QAAQ;cACPgF,IAAI,EAAE,CAAE;cACRC,WAAW,EAAC,uEAAgB;cAC5BC,KAAK,EAAEzE,SAAU;cACjB0E,QAAQ,EAAGC,CAAC,IAAK1E,YAAY,CAAC0E,CAAC,CAACC,MAAM,CAACH,KAAK;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACFxD,OAAA,CAACX,MAAM;cACLkF,IAAI,EAAC,SAAS;cACdmB,IAAI,eAAE1F,OAAA,CAACF,YAAY;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBmC,OAAO,EAAEpD,iBAAkB;cAC3BqD,OAAO,EAAE7E,SAAU;cACnB8E,QAAQ,EAAE,CAAChF,SAAS,CAAC2B,IAAI,CAAC,CAAE;cAAAkB,QAAA,EAC7B;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAERzC,SAAS,iBACRf,OAAA;cAAKmD,KAAK,EAAE;gBAAEiB,SAAS,EAAE,QAAQ;gBAAEX,OAAO,EAAE;cAAO,CAAE;cAAAC,QAAA,gBACnD1D,OAAA,CAACR,IAAI;gBAAC2E,IAAI,EAAC;cAAO;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrBxD,OAAA;gBAAKmD,KAAK,EAAE;kBAAE0B,SAAS,EAAE;gBAAO,CAAE;gBAAAnB,QAAA,EAAC;cAAU;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CACN,EAEAvC,cAAc,IAAI,CAACF,SAAS,iBAC3Bf,OAAA,CAAClB,IAAI;cAACqF,IAAI,EAAC,OAAO;cAACJ,KAAK,EAAC,0BAAM;cAAAL,QAAA,gBAC7B1D,OAAA;gBAAKmD,KAAK,EAAE;kBAAEU,YAAY,EAAE;gBAAO,CAAE;gBAAAH,QAAA,gBACnC1D,OAAA,CAACG,IAAI;kBAACkE,MAAM;kBAAAX,QAAA,EAAC;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1BxD,OAAA,CAACd,GAAG;kBAACkE,KAAK,EAAEL,iBAAiB,CAAC9B,cAAc,CAACK,eAAe,CAAE;kBAAAoC,QAAA,EAC3DzC,cAAc,CAACK,eAAe,CAACsD,OAAO,CAAC,CAAC;gBAAC;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACNxD,OAAA,CAACG,IAAI;kBAACkE,MAAM;kBAAClB,KAAK,EAAE;oBAAEmB,UAAU,EAAE;kBAAO,CAAE;kBAAAZ,QAAA,EAAC;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxDxD,OAAA;kBAAA0D,QAAA,GAAO,CAACzC,cAAc,CAACO,UAAU,GAAG,GAAG,EAAEoD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eAENxD,OAAA;gBAAKmD,KAAK,EAAE;kBAAEU,YAAY,EAAE;gBAAO,CAAE;gBAAAH,QAAA,gBACnC1D,OAAA,CAACG,IAAI;kBAACkE,MAAM;kBAAAX,QAAA,EAAC;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBxD,OAAA;kBAAImD,KAAK,EAAE;oBAAE0B,SAAS,EAAE;kBAAM,CAAE;kBAAAnB,QAAA,EAC7BzC,cAAc,CAAC4B,UAAU,CAACoB,GAAG,CAAC,CAAC6B,KAAa,EAAEC,KAAa,kBAC1D/F,OAAA;oBAAA0D,QAAA,EAAiBoC;kBAAK,GAAbC,KAAK;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAENxD,OAAA;gBAAA0D,QAAA,gBACE1D,OAAA,CAACG,IAAI;kBAACkE,MAAM;kBAAAX,QAAA,EAAC;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBxD,OAAA;kBAAImD,KAAK,EAAE;oBAAE0B,SAAS,EAAE;kBAAM,CAAE;kBAAAnB,QAAA,EAC7BzC,cAAc,CAAC6B,YAAY,CAACmB,GAAG,CAAC,CAAC+B,MAAc,EAAED,KAAa,kBAC7D/F,OAAA;oBAAA0D,QAAA,EAAiBsC;kBAAM,GAAdD,KAAK;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAc,CAC7B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL7C,WAAW,iBACVX,OAAA,CAACjB,GAAG;MAAA2E,QAAA,eACF1D,OAAA,CAAChB,GAAG;QAAC8E,IAAI,EAAE,EAAG;QAAAJ,QAAA,eACZ1D,OAAA,CAAClB,IAAI;UAACiF,KAAK,EAAE,cAAcpD,WAAW,CAACuB,IAAI,EAAG;UAAC8B,KAAK,eAAEhE,OAAA,CAACP,aAAa;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAE,QAAA,eACtE1D,OAAA,CAACjB,GAAG;YAAC6E,MAAM,EAAE,EAAG;YAAAF,QAAA,gBACd1D,OAAA,CAAChB,GAAG;cAAC8E,IAAI,EAAE,EAAG;cAAAJ,QAAA,gBACZ1D,OAAA,CAAClB,IAAI;gBAACqF,IAAI,EAAC,OAAO;gBAACJ,KAAK,EAAC,0BAAM;gBAAAL,QAAA,eAC7B1D,OAAA,CAACE,SAAS;kBAAAwD,QAAA,EAAE/C,WAAW,CAACwB;gBAAc;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eAEPxD,OAAA,CAAClB,IAAI;gBAACqF,IAAI,EAAC,OAAO;gBAACJ,KAAK,EAAC,0BAAM;gBAACZ,KAAK,EAAE;kBAAE0B,SAAS,EAAE;gBAAO,CAAE;gBAAAnB,QAAA,eAC3D1D,OAAA;kBAAA0D,QAAA,EACG/C,WAAW,CAACyB,UAAU,CAAC6B,GAAG,CAAC,CAACc,KAAK,EAAEgB,KAAK,kBACvC/F,OAAA;oBAAA0D,QAAA,EAAiBqB;kBAAK,GAAbgB,KAAK;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENxD,OAAA,CAAChB,GAAG;cAAC8E,IAAI,EAAE,EAAG;cAAAJ,QAAA,gBACZ1D,OAAA,CAAClB,IAAI;gBAACqF,IAAI,EAAC,OAAO;gBAACJ,KAAK,EAAC,0BAAM;gBAAAL,QAAA,eAC7B1D,OAAA,CAACE,SAAS;kBAAAwD,QAAA,EAAE/C,WAAW,CAAC0B;gBAAoB;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eAEPxD,OAAA,CAAClB,IAAI;gBAACqF,IAAI,EAAC,OAAO;gBAACJ,KAAK,EAAC,gBAAM;gBAACZ,KAAK,EAAE;kBAAE0B,SAAS,EAAE;gBAAO,CAAE;gBAAAnB,QAAA,eAC3D1D,OAAA;kBAAA0D,QAAA,EACG/C,WAAW,CAAC2B,eAAe,CAAC2B,GAAG,CAAC,CAACgC,GAAG,EAAEF,KAAK,kBAC1C/F,OAAA;oBAAA0D,QAAA,EAAiBuC;kBAAG,GAAXF,KAAK;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClD,EAAA,CA3SID,UAAoB;AAAA6F,EAAA,GAApB7F,UAAoB;AA6S1B,eAAeA,UAAU;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}