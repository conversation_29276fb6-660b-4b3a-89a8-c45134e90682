{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isFunction, isString } from 'zrender/lib/core/util.js';\nvar samplers = {\n  average: function (frame) {\n    var sum = 0;\n    var count = 0;\n    for (var i = 0; i < frame.length; i++) {\n      if (!isNaN(frame[i])) {\n        sum += frame[i];\n        count++;\n      }\n    }\n    // Return NaN if count is 0\n    return count === 0 ? NaN : sum / count;\n  },\n  sum: function (frame) {\n    var sum = 0;\n    for (var i = 0; i < frame.length; i++) {\n      // Ignore NaN\n      sum += frame[i] || 0;\n    }\n    return sum;\n  },\n  max: function (frame) {\n    var max = -Infinity;\n    for (var i = 0; i < frame.length; i++) {\n      frame[i] > max && (max = frame[i]);\n    }\n    // NaN will cause illegal axis extent.\n    return isFinite(max) ? max : NaN;\n  },\n  min: function (frame) {\n    var min = Infinity;\n    for (var i = 0; i < frame.length; i++) {\n      frame[i] < min && (min = frame[i]);\n    }\n    // NaN will cause illegal axis extent.\n    return isFinite(min) ? min : NaN;\n  },\n  // TODO\n  // Median\n  nearest: function (frame) {\n    return frame[0];\n  }\n};\nvar indexSampler = function (frame) {\n  return Math.round(frame.length / 2);\n};\nexport default function dataSample(seriesType) {\n  return {\n    seriesType: seriesType,\n    // FIXME:TS never used, so comment it\n    // modifyOutputEnd: true,\n    reset: function (seriesModel, ecModel, api) {\n      var data = seriesModel.getData();\n      var sampling = seriesModel.get('sampling');\n      var coordSys = seriesModel.coordinateSystem;\n      var count = data.count();\n      // Only cartesian2d support down sampling. Disable it when there is few data.\n      if (count > 10 && coordSys.type === 'cartesian2d' && sampling) {\n        var baseAxis = coordSys.getBaseAxis();\n        var valueAxis = coordSys.getOtherAxis(baseAxis);\n        var extent = baseAxis.getExtent();\n        var dpr = api.getDevicePixelRatio();\n        // Coordinste system has been resized\n        var size = Math.abs(extent[1] - extent[0]) * (dpr || 1);\n        var rate = Math.round(count / size);\n        if (isFinite(rate) && rate > 1) {\n          if (sampling === 'lttb') {\n            seriesModel.setData(data.lttbDownSample(data.mapDimension(valueAxis.dim), 1 / rate));\n          } else if (sampling === 'minmax') {\n            seriesModel.setData(data.minmaxDownSample(data.mapDimension(valueAxis.dim), 1 / rate));\n          }\n          var sampler = void 0;\n          if (isString(sampling)) {\n            sampler = samplers[sampling];\n          } else if (isFunction(sampling)) {\n            sampler = sampling;\n          }\n          if (sampler) {\n            // Only support sample the first dim mapped from value axis.\n            seriesModel.setData(data.downSample(data.mapDimension(valueAxis.dim), 1 / rate, sampler, indexSampler));\n          }\n        }\n      }\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}