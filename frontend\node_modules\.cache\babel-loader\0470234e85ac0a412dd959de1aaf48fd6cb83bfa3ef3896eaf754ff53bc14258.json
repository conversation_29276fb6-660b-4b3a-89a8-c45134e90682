{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/* global window, Uint8Array, document */\nimport env from 'zrender/lib/core/env.js';\nimport { ToolboxFeature } from '../featureManager.js';\nvar SaveAsImage = /** @class */function (_super) {\n  __extends(SaveAsImage, _super);\n  function SaveAsImage() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  SaveAsImage.prototype.onclick = function (ecModel, api) {\n    var model = this.model;\n    var title = model.get('name') || ecModel.get('title.0.text') || 'echarts';\n    var isSvg = api.getZr().painter.getType() === 'svg';\n    var type = isSvg ? 'svg' : model.get('type', true) || 'png';\n    var url = api.getConnectedDataURL({\n      type: type,\n      backgroundColor: model.get('backgroundColor', true) || ecModel.get('backgroundColor') || '#fff',\n      connectedBackgroundColor: model.get('connectedBackgroundColor'),\n      excludeComponents: model.get('excludeComponents'),\n      pixelRatio: model.get('pixelRatio')\n    });\n    var browser = env.browser;\n    // Chrome, Firefox, New Edge\n    if (typeof MouseEvent === 'function' && (browser.newEdge || !browser.ie && !browser.edge)) {\n      var $a = document.createElement('a');\n      $a.download = title + '.' + type;\n      $a.target = '_blank';\n      $a.href = url;\n      var evt = new MouseEvent('click', {\n        // some micro front-end framework， window maybe is a Proxy\n        view: document.defaultView,\n        bubbles: true,\n        cancelable: false\n      });\n      $a.dispatchEvent(evt);\n    }\n    // IE or old Edge\n    else {\n      // @ts-ignore\n      if (window.navigator.msSaveOrOpenBlob || isSvg) {\n        var parts = url.split(',');\n        // data:[<mime type>][;charset=<charset>][;base64],<encoded data>\n        var base64Encoded = parts[0].indexOf('base64') > -1;\n        var bstr = isSvg\n        // should decode the svg data uri first\n        ? decodeURIComponent(parts[1]) : parts[1];\n        // only `atob` when the data uri is encoded with base64\n        // otherwise, like `svg` data uri exported by zrender,\n        // there will be an error, for it's not encoded with base64.\n        // (just a url-encoded string through `encodeURIComponent`)\n        base64Encoded && (bstr = window.atob(bstr));\n        var filename = title + '.' + type;\n        // @ts-ignore\n        if (window.navigator.msSaveOrOpenBlob) {\n          var n = bstr.length;\n          var u8arr = new Uint8Array(n);\n          while (n--) {\n            u8arr[n] = bstr.charCodeAt(n);\n          }\n          var blob = new Blob([u8arr]); // @ts-ignore\n          window.navigator.msSaveOrOpenBlob(blob, filename);\n        } else {\n          var frame = document.createElement('iframe');\n          document.body.appendChild(frame);\n          var cw = frame.contentWindow;\n          var doc = cw.document;\n          doc.open('image/svg+xml', 'replace');\n          doc.write(bstr);\n          doc.close();\n          cw.focus();\n          doc.execCommand('SaveAs', true, filename);\n          document.body.removeChild(frame);\n        }\n      } else {\n        var lang = model.get('lang');\n        var html = '' + '<body style=\"margin:0;\">' + '<img src=\"' + url + '\" style=\"max-width:100%;\" title=\"' + (lang && lang[0] || '') + '\" />' + '</body>';\n        var tab = window.open();\n        tab.document.write(html);\n        tab.document.title = title;\n      }\n    }\n  };\n  SaveAsImage.getDefaultOption = function (ecModel) {\n    var defaultOption = {\n      show: true,\n      icon: 'M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0',\n      title: ecModel.getLocaleModel().get(['toolbox', 'saveAsImage', 'title']),\n      type: 'png',\n      // Default use option.backgroundColor\n      // backgroundColor: '#fff',\n      connectedBackgroundColor: '#fff',\n      name: '',\n      excludeComponents: ['toolbox'],\n      // use current pixel ratio of device by default\n      // pixelRatio: 1,\n      lang: ecModel.getLocaleModel().get(['toolbox', 'saveAsImage', 'lang'])\n    };\n    return defaultOption;\n  };\n  return SaveAsImage;\n}(ToolboxFeature);\nexport default SaveAsImage;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}