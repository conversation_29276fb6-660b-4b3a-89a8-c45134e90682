{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* The tree layoutHelper implementation was originally copied from\r\n* \"d3.js\"(https://github.com/d3/d3-hierarchy) with\r\n* some modifications made for this project.\r\n* (see more details in the comment of the specific method below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the licence of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\n/**\r\n * @file The layout algorithm of node-link tree diagrams. Here we using Reingold-Tilford algorithm to drawing\r\n *       the tree.\r\n */\nimport * as layout from '../../util/layout.js';\n/**\r\n * Initialize all computational message for following algorithm.\r\n */\nexport function init(inRoot) {\n  var root = inRoot;\n  root.hierNode = {\n    defaultAncestor: null,\n    ancestor: root,\n    prelim: 0,\n    modifier: 0,\n    change: 0,\n    shift: 0,\n    i: 0,\n    thread: null\n  };\n  var nodes = [root];\n  var node;\n  var children;\n  while (node = nodes.pop()) {\n    // jshint ignore:line\n    children = node.children;\n    if (node.isExpand && children.length) {\n      var n = children.length;\n      for (var i = n - 1; i >= 0; i--) {\n        var child = children[i];\n        child.hierNode = {\n          defaultAncestor: null,\n          ancestor: child,\n          prelim: 0,\n          modifier: 0,\n          change: 0,\n          shift: 0,\n          i: i,\n          thread: null\n        };\n        nodes.push(child);\n      }\n    }\n  }\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * Computes a preliminary x coordinate for node. Before that, this function is\r\n * applied recursively to the children of node, as well as the function\r\n * apportion(). After spacing out the children by calling executeShifts(), the\r\n * node is placed to the midpoint of its outermost children.\r\n */\nexport function firstWalk(node, separation) {\n  var children = node.isExpand ? node.children : [];\n  var siblings = node.parentNode.children;\n  var subtreeW = node.hierNode.i ? siblings[node.hierNode.i - 1] : null;\n  if (children.length) {\n    executeShifts(node);\n    var midPoint = (children[0].hierNode.prelim + children[children.length - 1].hierNode.prelim) / 2;\n    if (subtreeW) {\n      node.hierNode.prelim = subtreeW.hierNode.prelim + separation(node, subtreeW);\n      node.hierNode.modifier = node.hierNode.prelim - midPoint;\n    } else {\n      node.hierNode.prelim = midPoint;\n    }\n  } else if (subtreeW) {\n    node.hierNode.prelim = subtreeW.hierNode.prelim + separation(node, subtreeW);\n  }\n  node.parentNode.hierNode.defaultAncestor = apportion(node, subtreeW, node.parentNode.hierNode.defaultAncestor || siblings[0], separation);\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * Computes all real x-coordinates by summing up the modifiers recursively.\r\n */\nexport function secondWalk(node) {\n  var nodeX = node.hierNode.prelim + node.parentNode.hierNode.modifier;\n  node.setLayout({\n    x: nodeX\n  }, true);\n  node.hierNode.modifier += node.parentNode.hierNode.modifier;\n}\nexport function separation(cb) {\n  return arguments.length ? cb : defaultSeparation;\n}\n/**\r\n * Transform the common coordinate to radial coordinate.\r\n */\nexport function radialCoordinate(rad, r) {\n  rad -= Math.PI / 2;\n  return {\n    x: r * Math.cos(rad),\n    y: r * Math.sin(rad)\n  };\n}\n/**\r\n * Get the layout position of the whole view.\r\n */\nexport function getViewRect(seriesModel, api) {\n  return layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\n/**\r\n * All other shifts, applied to the smaller subtrees between w- and w+, are\r\n * performed by this function.\r\n *\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n */\nfunction executeShifts(node) {\n  var children = node.children;\n  var n = children.length;\n  var shift = 0;\n  var change = 0;\n  while (--n >= 0) {\n    var child = children[n];\n    child.hierNode.prelim += shift;\n    child.hierNode.modifier += shift;\n    change += child.hierNode.change;\n    shift += child.hierNode.shift + change;\n  }\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * The core of the algorithm. Here, a new subtree is combined with the\r\n * previous subtrees. Threads are used to traverse the inside and outside\r\n * contours of the left and right subtree up to the highest common level.\r\n * Whenever two nodes of the inside contours conflict, we compute the left\r\n * one of the greatest uncommon ancestors using the function nextAncestor()\r\n * and call moveSubtree() to shift the subtree and prepare the shifts of\r\n * smaller subtrees. Finally, we add a new thread (if necessary).\r\n */\nfunction apportion(subtreeV, subtreeW, ancestor, separation) {\n  if (subtreeW) {\n    var nodeOutRight = subtreeV;\n    var nodeInRight = subtreeV;\n    var nodeOutLeft = nodeInRight.parentNode.children[0];\n    var nodeInLeft = subtreeW;\n    var sumOutRight = nodeOutRight.hierNode.modifier;\n    var sumInRight = nodeInRight.hierNode.modifier;\n    var sumOutLeft = nodeOutLeft.hierNode.modifier;\n    var sumInLeft = nodeInLeft.hierNode.modifier;\n    while (nodeInLeft = nextRight(nodeInLeft), nodeInRight = nextLeft(nodeInRight), nodeInLeft && nodeInRight) {\n      nodeOutRight = nextRight(nodeOutRight);\n      nodeOutLeft = nextLeft(nodeOutLeft);\n      nodeOutRight.hierNode.ancestor = subtreeV;\n      var shift = nodeInLeft.hierNode.prelim + sumInLeft - nodeInRight.hierNode.prelim - sumInRight + separation(nodeInLeft, nodeInRight);\n      if (shift > 0) {\n        moveSubtree(nextAncestor(nodeInLeft, subtreeV, ancestor), subtreeV, shift);\n        sumInRight += shift;\n        sumOutRight += shift;\n      }\n      sumInLeft += nodeInLeft.hierNode.modifier;\n      sumInRight += nodeInRight.hierNode.modifier;\n      sumOutRight += nodeOutRight.hierNode.modifier;\n      sumOutLeft += nodeOutLeft.hierNode.modifier;\n    }\n    if (nodeInLeft && !nextRight(nodeOutRight)) {\n      nodeOutRight.hierNode.thread = nodeInLeft;\n      nodeOutRight.hierNode.modifier += sumInLeft - sumOutRight;\n    }\n    if (nodeInRight && !nextLeft(nodeOutLeft)) {\n      nodeOutLeft.hierNode.thread = nodeInRight;\n      nodeOutLeft.hierNode.modifier += sumInRight - sumOutLeft;\n      ancestor = subtreeV;\n    }\n  }\n  return ancestor;\n}\n/**\r\n * This function is used to traverse the right contour of a subtree.\r\n * It returns the rightmost child of node or the thread of node. The function\r\n * returns null if and only if node is on the highest depth of its subtree.\r\n */\nfunction nextRight(node) {\n  var children = node.children;\n  return children.length && node.isExpand ? children[children.length - 1] : node.hierNode.thread;\n}\n/**\r\n * This function is used to traverse the left contour of a subtree (or a subforest).\r\n * It returns the leftmost child of node or the thread of node. The function\r\n * returns null if and only if node is on the highest depth of its subtree.\r\n */\nfunction nextLeft(node) {\n  var children = node.children;\n  return children.length && node.isExpand ? children[0] : node.hierNode.thread;\n}\n/**\r\n * If nodeInLeft’s ancestor is a sibling of node, returns nodeInLeft’s ancestor.\r\n * Otherwise, returns the specified ancestor.\r\n */\nfunction nextAncestor(nodeInLeft, node, ancestor) {\n  return nodeInLeft.hierNode.ancestor.parentNode === node.parentNode ? nodeInLeft.hierNode.ancestor : ancestor;\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * Shifts the current subtree rooted at wr.\r\n * This is done by increasing prelim(w+) and modifier(w+) by shift.\r\n */\nfunction moveSubtree(wl, wr, shift) {\n  var change = shift / (wr.hierNode.i - wl.hierNode.i);\n  wr.hierNode.change -= change;\n  wr.hierNode.shift += shift;\n  wr.hierNode.modifier += shift;\n  wr.hierNode.prelim += shift;\n  wl.hierNode.change += change;\n}\n/**\r\n * The implementation of this function was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3-hierarchy/blob/4c1f038f2725d6eae2e49b61d01456400694bac4/src/tree.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n */\nfunction defaultSeparation(node1, node2) {\n  return node1.parentNode === node2.parentNode ? 1 : 2;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}