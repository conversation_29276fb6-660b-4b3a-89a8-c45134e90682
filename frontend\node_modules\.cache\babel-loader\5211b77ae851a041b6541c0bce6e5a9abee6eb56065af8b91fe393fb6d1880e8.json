{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SymbolDraw from '../helper/SymbolDraw.js';\nimport EffectSymbol from '../helper/EffectSymbol.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport pointsLayout from '../../layout/points.js';\nimport ChartView from '../../view/Chart.js';\nvar EffectScatterView = /** @class */function (_super) {\n  __extends(EffectScatterView, _super);\n  function EffectScatterView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = EffectScatterView.type;\n    return _this;\n  }\n  EffectScatterView.prototype.init = function () {\n    this._symbolDraw = new SymbolDraw(EffectSymbol);\n  };\n  EffectScatterView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var effectSymbolDraw = this._symbolDraw;\n    effectSymbolDraw.updateData(data, {\n      clipShape: this._getClipShape(seriesModel)\n    });\n    this.group.add(effectSymbolDraw.group);\n  };\n  EffectScatterView.prototype._getClipShape = function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    var clipArea = coordSys && coordSys.getArea && coordSys.getArea();\n    return seriesModel.get('clip', true) ? clipArea : null;\n  };\n  EffectScatterView.prototype.updateTransform = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    this.group.dirty();\n    var res = pointsLayout('').reset(seriesModel, ecModel, api);\n    if (res.progress) {\n      res.progress({\n        start: 0,\n        end: data.count(),\n        count: data.count()\n      }, data);\n    }\n    this._symbolDraw.updateLayout();\n  };\n  EffectScatterView.prototype._updateGroupTransform = function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys && coordSys.getRoamTransform) {\n      this.group.transform = matrix.clone(coordSys.getRoamTransform());\n      this.group.decomposeTransform();\n    }\n  };\n  EffectScatterView.prototype.remove = function (ecModel, api) {\n    this._symbolDraw && this._symbolDraw.remove(true);\n  };\n  EffectScatterView.type = 'effectScatter';\n  return EffectScatterView;\n}(ChartView);\nexport default EffectScatterView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}