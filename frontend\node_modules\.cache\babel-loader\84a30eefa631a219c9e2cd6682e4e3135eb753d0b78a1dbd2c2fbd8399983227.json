{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { parsePercent } from '../../util/number.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\n// let PI2 = Math.PI * 2;\nvar RADIAN = Math.PI / 180;\nexport default function sunburstLayout(seriesType, ecModel, api) {\n  ecModel.eachSeriesByType(seriesType, function (seriesModel) {\n    var center = seriesModel.get('center');\n    var radius = seriesModel.get('radius');\n    if (!zrUtil.isArray(radius)) {\n      radius = [0, radius];\n    }\n    if (!zrUtil.isArray(center)) {\n      center = [center, center];\n    }\n    var width = api.getWidth();\n    var height = api.getHeight();\n    var size = Math.min(width, height);\n    var cx = parsePercent(center[0], width);\n    var cy = parsePercent(center[1], height);\n    var r0 = parsePercent(radius[0], size / 2);\n    var r = parsePercent(radius[1], size / 2);\n    var startAngle = -seriesModel.get('startAngle') * RADIAN;\n    var minAngle = seriesModel.get('minAngle') * RADIAN;\n    var virtualRoot = seriesModel.getData().tree.root;\n    var treeRoot = seriesModel.getViewRoot();\n    var rootDepth = treeRoot.depth;\n    var sort = seriesModel.get('sort');\n    if (sort != null) {\n      initChildren(treeRoot, sort);\n    }\n    var validDataCount = 0;\n    zrUtil.each(treeRoot.children, function (child) {\n      !isNaN(child.getValue()) && validDataCount++;\n    });\n    var sum = treeRoot.getValue();\n    // Sum may be 0\n    var unitRadian = Math.PI / (sum || validDataCount) * 2;\n    var renderRollupNode = treeRoot.depth > 0;\n    var levels = treeRoot.height - (renderRollupNode ? -1 : 1);\n    var rPerLevel = (r - r0) / (levels || 1);\n    var clockwise = seriesModel.get('clockwise');\n    var stillShowZeroSum = seriesModel.get('stillShowZeroSum');\n    // In the case some sector angle is smaller than minAngle\n    // let restAngle = PI2;\n    // let valueSumLargerThanMinAngle = 0;\n    var dir = clockwise ? 1 : -1;\n    /**\r\n     * Render a tree\r\n     * @return increased angle\r\n     */\n    var renderNode = function (node, startAngle) {\n      if (!node) {\n        return;\n      }\n      var endAngle = startAngle;\n      // Render self\n      if (node !== virtualRoot) {\n        // Tree node is virtual, so it doesn't need to be drawn\n        var value = node.getValue();\n        var angle = sum === 0 && stillShowZeroSum ? unitRadian : value * unitRadian;\n        if (angle < minAngle) {\n          angle = minAngle;\n          // restAngle -= minAngle;\n        }\n        // else {\n        //     valueSumLargerThanMinAngle += value;\n        // }\n        endAngle = startAngle + dir * angle;\n        var depth = node.depth - rootDepth - (renderRollupNode ? -1 : 1);\n        var rStart = r0 + rPerLevel * depth;\n        var rEnd = r0 + rPerLevel * (depth + 1);\n        var levelModel = seriesModel.getLevelModel(node);\n        if (levelModel) {\n          var r0_1 = levelModel.get('r0', true);\n          var r_1 = levelModel.get('r', true);\n          var radius_1 = levelModel.get('radius', true);\n          if (radius_1 != null) {\n            r0_1 = radius_1[0];\n            r_1 = radius_1[1];\n          }\n          r0_1 != null && (rStart = parsePercent(r0_1, size / 2));\n          r_1 != null && (rEnd = parsePercent(r_1, size / 2));\n        }\n        node.setLayout({\n          angle: angle,\n          startAngle: startAngle,\n          endAngle: endAngle,\n          clockwise: clockwise,\n          cx: cx,\n          cy: cy,\n          r0: rStart,\n          r: rEnd\n        });\n      }\n      // Render children\n      if (node.children && node.children.length) {\n        // currentAngle = startAngle;\n        var siblingAngle_1 = 0;\n        zrUtil.each(node.children, function (node) {\n          siblingAngle_1 += renderNode(node, startAngle + siblingAngle_1);\n        });\n      }\n      return endAngle - startAngle;\n    };\n    // Virtual root node for roll up\n    if (renderRollupNode) {\n      var rStart = r0;\n      var rEnd = r0 + rPerLevel;\n      var angle = Math.PI * 2;\n      virtualRoot.setLayout({\n        angle: angle,\n        startAngle: startAngle,\n        endAngle: startAngle + angle,\n        clockwise: clockwise,\n        cx: cx,\n        cy: cy,\n        r0: rStart,\n        r: rEnd\n      });\n    }\n    renderNode(treeRoot, startAngle);\n  });\n}\n/**\r\n * Init node children by order and update visual\r\n */\nfunction initChildren(node, sortOrder) {\n  var children = node.children || [];\n  node.children = sort(children, sortOrder);\n  // Init children recursively\n  if (children.length) {\n    zrUtil.each(node.children, function (child) {\n      initChildren(child, sortOrder);\n    });\n  }\n}\n/**\r\n * Sort children nodes\r\n *\r\n * @param {TreeNode[]}               children children of node to be sorted\r\n * @param {string | function | null} sort sort method\r\n *                                   See SunburstSeries.js for details.\r\n */\nfunction sort(children, sortOrder) {\n  if (zrUtil.isFunction(sortOrder)) {\n    var sortTargets = zrUtil.map(children, function (child, idx) {\n      var value = child.getValue();\n      return {\n        params: {\n          depth: child.depth,\n          height: child.height,\n          dataIndex: child.dataIndex,\n          getValue: function () {\n            return value;\n          }\n        },\n        index: idx\n      };\n    });\n    sortTargets.sort(function (a, b) {\n      return sortOrder(a.params, b.params);\n    });\n    return zrUtil.map(sortTargets, function (target) {\n      return children[target.index];\n    });\n  } else {\n    var isAsc_1 = sortOrder === 'asc';\n    return children.sort(function (a, b) {\n      var diff = (a.getValue() - b.getValue()) * (isAsc_1 ? 1 : -1);\n      return diff === 0 ? (a.dataIndex - b.dataIndex) * (isAsc_1 ? -1 : 1) : diff;\n    });\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}