{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Geo, { geo2DDimensions } from './Geo.js';\nimport * as layout from '../../util/layout.js';\nimport * as numberUtil from '../../util/number.js';\nimport geoSourceManager from './geoSourceManager.js';\nimport * as vector from 'zrender/lib/core/vector.js';\n/**\r\n * Resize method bound to the geo\r\n */\nfunction resizeGeo(geoModel, api) {\n  var boundingCoords = geoModel.get('boundingCoords');\n  if (boundingCoords != null) {\n    var leftTop_1 = boundingCoords[0];\n    var rightBottom_1 = boundingCoords[1];\n    if (!(isFinite(leftTop_1[0]) && isFinite(leftTop_1[1]) && isFinite(rightBottom_1[0]) && isFinite(rightBottom_1[1]))) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error('Invalid boundingCoords');\n      }\n    } else {\n      // Sample around the lng/lat rect and use projection to calculate actual bounding rect.\n      var projection_1 = this.projection;\n      if (projection_1) {\n        var xMin = leftTop_1[0];\n        var yMin = leftTop_1[1];\n        var xMax = rightBottom_1[0];\n        var yMax = rightBottom_1[1];\n        leftTop_1 = [Infinity, Infinity];\n        rightBottom_1 = [-Infinity, -Infinity];\n        // TODO better way?\n        var sampleLine = function (x0, y0, x1, y1) {\n          var dx = x1 - x0;\n          var dy = y1 - y0;\n          for (var i = 0; i <= 100; i++) {\n            var p = i / 100;\n            var pt = projection_1.project([x0 + dx * p, y0 + dy * p]);\n            vector.min(leftTop_1, leftTop_1, pt);\n            vector.max(rightBottom_1, rightBottom_1, pt);\n          }\n        };\n        // Top\n        sampleLine(xMin, yMin, xMax, yMin);\n        // Right\n        sampleLine(xMax, yMin, xMax, yMax);\n        // Bottom\n        sampleLine(xMax, yMax, xMin, yMax);\n        // Left\n        sampleLine(xMin, yMax, xMax, yMin);\n      }\n      this.setBoundingRect(leftTop_1[0], leftTop_1[1], rightBottom_1[0] - leftTop_1[0], rightBottom_1[1] - leftTop_1[1]);\n    }\n  }\n  var rect = this.getBoundingRect();\n  var centerOption = geoModel.get('layoutCenter');\n  var sizeOption = geoModel.get('layoutSize');\n  var viewWidth = api.getWidth();\n  var viewHeight = api.getHeight();\n  var aspect = rect.width / rect.height * this.aspectScale;\n  var useCenterAndSize = false;\n  var center;\n  var size;\n  if (centerOption && sizeOption) {\n    center = [numberUtil.parsePercent(centerOption[0], viewWidth), numberUtil.parsePercent(centerOption[1], viewHeight)];\n    size = numberUtil.parsePercent(sizeOption, Math.min(viewWidth, viewHeight));\n    if (!isNaN(center[0]) && !isNaN(center[1]) && !isNaN(size)) {\n      useCenterAndSize = true;\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn('Given layoutCenter or layoutSize data are invalid. Use left/top/width/height instead.');\n      }\n    }\n  }\n  var viewRect;\n  if (useCenterAndSize) {\n    viewRect = {};\n    if (aspect > 1) {\n      // Width is same with size\n      viewRect.width = size;\n      viewRect.height = size / aspect;\n    } else {\n      viewRect.height = size;\n      viewRect.width = size * aspect;\n    }\n    viewRect.y = center[1] - viewRect.height / 2;\n    viewRect.x = center[0] - viewRect.width / 2;\n  } else {\n    // Use left/top/width/height\n    var boxLayoutOption = geoModel.getBoxLayoutParams();\n    boxLayoutOption.aspect = aspect;\n    viewRect = layout.getLayoutRect(boxLayoutOption, {\n      width: viewWidth,\n      height: viewHeight\n    });\n  }\n  this.setViewRect(viewRect.x, viewRect.y, viewRect.width, viewRect.height);\n  this.setCenter(geoModel.get('center'), api);\n  this.setZoom(geoModel.get('zoom'));\n}\n// Back compat for ECharts2, where the coord map is set on map series:\n// {type: 'map', geoCoord: {'cityA': [116.46,39.92], 'cityA': [119.12,24.61]}},\nfunction setGeoCoords(geo, model) {\n  zrUtil.each(model.get('geoCoord'), function (geoCoord, name) {\n    geo.addGeoCoord(name, geoCoord);\n  });\n}\nvar GeoCreator = /** @class */function () {\n  function GeoCreator() {\n    // For deciding which dimensions to use when creating list data\n    this.dimensions = geo2DDimensions;\n  }\n  GeoCreator.prototype.create = function (ecModel, api) {\n    var geoList = [];\n    function getCommonGeoProperties(model) {\n      return {\n        nameProperty: model.get('nameProperty'),\n        aspectScale: model.get('aspectScale'),\n        projection: model.get('projection')\n      };\n    }\n    // FIXME Create each time may be slow\n    ecModel.eachComponent('geo', function (geoModel, idx) {\n      var mapName = geoModel.get('map');\n      var geo = new Geo(mapName + idx, mapName, zrUtil.extend({\n        nameMap: geoModel.get('nameMap')\n      }, getCommonGeoProperties(geoModel)));\n      geo.zoomLimit = geoModel.get('scaleLimit');\n      geoList.push(geo);\n      // setGeoCoords(geo, geoModel);\n      geoModel.coordinateSystem = geo;\n      geo.model = geoModel;\n      // Inject resize method\n      geo.resize = resizeGeo;\n      geo.resize(geoModel, api);\n    });\n    ecModel.eachSeries(function (seriesModel) {\n      var coordSys = seriesModel.get('coordinateSystem');\n      if (coordSys === 'geo') {\n        var geoIndex = seriesModel.get('geoIndex') || 0;\n        seriesModel.coordinateSystem = geoList[geoIndex];\n      }\n    });\n    // If has map series\n    var mapModelGroupBySeries = {};\n    ecModel.eachSeriesByType('map', function (seriesModel) {\n      if (!seriesModel.getHostGeoModel()) {\n        var mapType = seriesModel.getMapType();\n        mapModelGroupBySeries[mapType] = mapModelGroupBySeries[mapType] || [];\n        mapModelGroupBySeries[mapType].push(seriesModel);\n      }\n    });\n    zrUtil.each(mapModelGroupBySeries, function (mapSeries, mapType) {\n      var nameMapList = zrUtil.map(mapSeries, function (singleMapSeries) {\n        return singleMapSeries.get('nameMap');\n      });\n      var geo = new Geo(mapType, mapType, zrUtil.extend({\n        nameMap: zrUtil.mergeAll(nameMapList)\n      }, getCommonGeoProperties(mapSeries[0])));\n      geo.zoomLimit = zrUtil.retrieve.apply(null, zrUtil.map(mapSeries, function (singleMapSeries) {\n        return singleMapSeries.get('scaleLimit');\n      }));\n      geoList.push(geo);\n      // Inject resize method\n      geo.resize = resizeGeo;\n      geo.resize(mapSeries[0], api);\n      zrUtil.each(mapSeries, function (singleMapSeries) {\n        singleMapSeries.coordinateSystem = geo;\n        setGeoCoords(geo, singleMapSeries);\n      });\n    });\n    return geoList;\n  };\n  /**\r\n   * Fill given regions array\r\n   */\n  GeoCreator.prototype.getFilledRegions = function (originRegionArr, mapName, nameMap, nameProperty) {\n    // Not use the original\n    var regionsArr = (originRegionArr || []).slice();\n    var dataNameMap = zrUtil.createHashMap();\n    for (var i = 0; i < regionsArr.length; i++) {\n      dataNameMap.set(regionsArr[i].name, regionsArr[i]);\n    }\n    var source = geoSourceManager.load(mapName, nameMap, nameProperty);\n    zrUtil.each(source.regions, function (region) {\n      var name = region.name;\n      var regionOption = dataNameMap.get(name);\n      // apply specified echarts style in GeoJSON data\n      var specifiedGeoJSONRegionStyle = region.properties && region.properties.echartsStyle;\n      if (!regionOption) {\n        regionOption = {\n          name: name\n        };\n        regionsArr.push(regionOption);\n      }\n      specifiedGeoJSONRegionStyle && zrUtil.merge(regionOption, specifiedGeoJSONRegionStyle);\n    });\n    return regionsArr;\n  };\n  return GeoCreator;\n}();\nvar geoCreator = new GeoCreator();\nexport default geoCreator;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}