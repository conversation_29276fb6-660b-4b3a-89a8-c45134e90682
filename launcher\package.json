{"name": "quant-trading-launcher", "version": "1.0.0", "description": "量化交易监控系统启动器", "main": "main.js", "author": "量化交易系统", "license": "MIT", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "pack": "electron-builder --dir", "dist": "npm run build-backend && npm run build-frontend && npm run build", "build-backend": "node scripts/build-backend.js", "build-frontend": "node scripts/build-frontend.js", "postinstall": "electron-builder install-app-deps"}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"express": "^4.18.2", "axios": "^1.5.0", "node-pty": "^1.0.0", "find-free-port": "^2.0.0", "ps-tree": "^1.2.0"}, "build": {"appId": "com.quanttrading.app", "productName": "量化交易监控系统", "directories": {"output": "dist", "buildResources": "assets"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraResources": [{"from": "resources/backend/", "to": "backend/"}, {"from": "resources/frontend/", "to": "frontend/"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "量化交易监控系统", "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "deleteAppDataOnUninstall": false}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}