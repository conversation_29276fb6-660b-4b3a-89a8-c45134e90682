{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../util/graphic.js';\nvar PI = Math.PI;\n/**\r\n * @param {module:echarts/ExtensionAPI} api\r\n * @param {Object} [opts]\r\n * @param {string} [opts.text]\r\n * @param {string} [opts.color]\r\n * @param {string} [opts.textColor]\r\n * @return {module:zrender/Element}\r\n */\nexport default function defaultLoading(api, opts) {\n  opts = opts || {};\n  zrUtil.defaults(opts, {\n    text: 'loading',\n    textColor: '#000',\n    fontSize: 12,\n    fontWeight: 'normal',\n    fontStyle: 'normal',\n    fontFamily: 'sans-serif',\n    maskColor: 'rgba(255, 255, 255, 0.8)',\n    showSpinner: true,\n    color: '#5470c6',\n    spinnerRadius: 10,\n    lineWidth: 5,\n    zlevel: 0\n  });\n  var group = new graphic.Group();\n  var mask = new graphic.Rect({\n    style: {\n      fill: opts.maskColor\n    },\n    zlevel: opts.zlevel,\n    z: 10000\n  });\n  group.add(mask);\n  var textContent = new graphic.Text({\n    style: {\n      text: opts.text,\n      fill: opts.textColor,\n      fontSize: opts.fontSize,\n      fontWeight: opts.fontWeight,\n      fontStyle: opts.fontStyle,\n      fontFamily: opts.fontFamily\n    },\n    zlevel: opts.zlevel,\n    z: 10001\n  });\n  var labelRect = new graphic.Rect({\n    style: {\n      fill: 'none'\n    },\n    textContent: textContent,\n    textConfig: {\n      position: 'right',\n      distance: 10\n    },\n    zlevel: opts.zlevel,\n    z: 10001\n  });\n  group.add(labelRect);\n  var arc;\n  if (opts.showSpinner) {\n    arc = new graphic.Arc({\n      shape: {\n        startAngle: -PI / 2,\n        endAngle: -PI / 2 + 0.1,\n        r: opts.spinnerRadius\n      },\n      style: {\n        stroke: opts.color,\n        lineCap: 'round',\n        lineWidth: opts.lineWidth\n      },\n      zlevel: opts.zlevel,\n      z: 10001\n    });\n    arc.animateShape(true).when(1000, {\n      endAngle: PI * 3 / 2\n    }).start('circularInOut');\n    arc.animateShape(true).when(1000, {\n      startAngle: PI * 3 / 2\n    }).delay(300).start('circularInOut');\n    group.add(arc);\n  }\n  // Inject resize\n  group.resize = function () {\n    var textWidth = textContent.getBoundingRect().width;\n    var r = opts.showSpinner ? opts.spinnerRadius : 0;\n    // cx = (containerWidth - arcDiameter - textDistance - textWidth) / 2\n    // textDistance needs to be calculated when both animation and text exist\n    var cx = (api.getWidth() - r * 2 - (opts.showSpinner && textWidth ? 10 : 0) - textWidth) / 2 - (opts.showSpinner && textWidth ? 0 : 5 + textWidth / 2)\n    // only show the text\n    + (opts.showSpinner ? 0 : textWidth / 2)\n    // only show the spinner\n    + (textWidth ? 0 : r);\n    var cy = api.getHeight() / 2;\n    opts.showSpinner && arc.setShape({\n      cx: cx,\n      cy: cy\n    });\n    labelRect.setShape({\n      x: cx - r,\n      y: cy - r,\n      width: r * 2,\n      height: r * 2\n    });\n    mask.setShape({\n      x: 0,\n      y: 0,\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n  };\n  group.resize();\n  return group;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}