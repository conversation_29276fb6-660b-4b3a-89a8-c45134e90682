{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Single coordinates system.\r\n */\nimport SingleAxis from './SingleAxis.js';\nimport * as axisHelper from '../axisHelper.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport { each } from 'zrender/lib/core/util.js';\nexport var singleDimensions = ['single'];\n/**\r\n * Create a single coordinates system.\r\n */\nvar Single = /** @class */function () {\n  function Single(axisModel, ecModel, api) {\n    this.type = 'single';\n    this.dimension = 'single';\n    /**\r\n     * Add it just for draw tooltip.\r\n     */\n    this.dimensions = singleDimensions;\n    this.axisPointerEnabled = true;\n    this.model = axisModel;\n    this._init(axisModel, ecModel, api);\n  }\n  /**\r\n   * Initialize single coordinate system.\r\n   */\n  Single.prototype._init = function (axisModel, ecModel, api) {\n    var dim = this.dimension;\n    var axis = new SingleAxis(dim, axisHelper.createScaleByModel(axisModel), [0, 0], axisModel.get('type'), axisModel.get('position'));\n    var isCategory = axis.type === 'category';\n    axis.onBand = isCategory && axisModel.get('boundaryGap');\n    axis.inverse = axisModel.get('inverse');\n    axis.orient = axisModel.get('orient');\n    axisModel.axis = axis;\n    axis.model = axisModel;\n    axis.coordinateSystem = this;\n    this._axis = axis;\n  };\n  /**\r\n   * Update axis scale after data processed\r\n   */\n  Single.prototype.update = function (ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      if (seriesModel.coordinateSystem === this) {\n        var data_1 = seriesModel.getData();\n        each(data_1.mapDimensionsAll(this.dimension), function (dim) {\n          this._axis.scale.unionExtentFromData(data_1, dim);\n        }, this);\n        axisHelper.niceScaleExtent(this._axis.scale, this._axis.model);\n      }\n    }, this);\n  };\n  /**\r\n   * Resize the single coordinate system.\r\n   */\n  Single.prototype.resize = function (axisModel, api) {\n    this._rect = getLayoutRect({\n      left: axisModel.get('left'),\n      top: axisModel.get('top'),\n      right: axisModel.get('right'),\n      bottom: axisModel.get('bottom'),\n      width: axisModel.get('width'),\n      height: axisModel.get('height')\n    }, {\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n    this._adjustAxis();\n  };\n  Single.prototype.getRect = function () {\n    return this._rect;\n  };\n  Single.prototype._adjustAxis = function () {\n    var rect = this._rect;\n    var axis = this._axis;\n    var isHorizontal = axis.isHorizontal();\n    var extent = isHorizontal ? [0, rect.width] : [0, rect.height];\n    var idx = axis.inverse ? 1 : 0;\n    axis.setExtent(extent[idx], extent[1 - idx]);\n    this._updateAxisTransform(axis, isHorizontal ? rect.x : rect.y);\n  };\n  Single.prototype._updateAxisTransform = function (axis, coordBase) {\n    var axisExtent = axis.getExtent();\n    var extentSum = axisExtent[0] + axisExtent[1];\n    var isHorizontal = axis.isHorizontal();\n    axis.toGlobalCoord = isHorizontal ? function (coord) {\n      return coord + coordBase;\n    } : function (coord) {\n      return extentSum - coord + coordBase;\n    };\n    axis.toLocalCoord = isHorizontal ? function (coord) {\n      return coord - coordBase;\n    } : function (coord) {\n      return extentSum - coord + coordBase;\n    };\n  };\n  /**\r\n   * Get axis.\r\n   */\n  Single.prototype.getAxis = function () {\n    return this._axis;\n  };\n  /**\r\n   * Get axis, add it just for draw tooltip.\r\n   */\n  Single.prototype.getBaseAxis = function () {\n    return this._axis;\n  };\n  Single.prototype.getAxes = function () {\n    return [this._axis];\n  };\n  Single.prototype.getTooltipAxes = function () {\n    return {\n      baseAxes: [this.getAxis()],\n      // Empty otherAxes\n      otherAxes: []\n    };\n  };\n  /**\r\n   * If contain point.\r\n   */\n  Single.prototype.containPoint = function (point) {\n    var rect = this.getRect();\n    var axis = this.getAxis();\n    var orient = axis.orient;\n    if (orient === 'horizontal') {\n      return axis.contain(axis.toLocalCoord(point[0])) && point[1] >= rect.y && point[1] <= rect.y + rect.height;\n    } else {\n      return axis.contain(axis.toLocalCoord(point[1])) && point[0] >= rect.y && point[0] <= rect.y + rect.height;\n    }\n  };\n  Single.prototype.pointToData = function (point) {\n    var axis = this.getAxis();\n    return [axis.coordToData(axis.toLocalCoord(point[axis.orient === 'horizontal' ? 0 : 1]))];\n  };\n  /**\r\n   * Convert the series data to concrete point.\r\n   * Can be [val] | val\r\n   */\n  Single.prototype.dataToPoint = function (val) {\n    var axis = this.getAxis();\n    var rect = this.getRect();\n    var pt = [];\n    var idx = axis.orient === 'horizontal' ? 0 : 1;\n    if (val instanceof Array) {\n      val = val[0];\n    }\n    pt[idx] = axis.toGlobalCoord(axis.dataToCoord(+val));\n    pt[1 - idx] = idx === 0 ? rect.y + rect.height / 2 : rect.x + rect.width / 2;\n    return pt;\n  };\n  Single.prototype.convertToPixel = function (ecModel, finder, value) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? this.dataToPoint(value) : null;\n  };\n  Single.prototype.convertFromPixel = function (ecModel, finder, pixel) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? this.pointToData(pixel) : null;\n  };\n  return Single;\n}();\nfunction getCoordSys(finder) {\n  var seriesModel = finder.seriesModel;\n  var singleModel = finder.singleAxisModel;\n  return singleModel && singleModel.coordinateSystem || seriesModel && seriesModel.coordinateSystem;\n}\nexport default Single;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}