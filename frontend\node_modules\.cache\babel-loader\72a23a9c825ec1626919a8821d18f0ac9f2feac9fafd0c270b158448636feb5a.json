{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Axis from '../Axis.js';\nvar SingleAxis = /** @class */function (_super) {\n  __extends(SingleAxis, _super);\n  function SingleAxis(dim, scale, coordExtent, axisType, position) {\n    var _this = _super.call(this, dim, scale, coordExtent) || this;\n    _this.type = axisType || 'value';\n    _this.position = position || 'bottom';\n    return _this;\n  }\n  /**\r\n   * Judge the orient of the axis.\r\n   */\n  SingleAxis.prototype.isHorizontal = function () {\n    var position = this.position;\n    return position === 'top' || position === 'bottom';\n  };\n  SingleAxis.prototype.pointToData = function (point, clamp) {\n    return this.coordinateSystem.pointToData(point)[0];\n  };\n  return SingleAxis;\n}(Axis);\nexport default SingleAxis;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}