const axios = require('axios');
const { BrowserWindow } = require('electron');

class HealthChecker {
  constructor(serviceManager) {
    this.serviceManager = serviceManager;
    this.checkInterval = null;
    this.intervalTime = 10000; // 10秒检查一次
    this.healthStatus = {
      backend: {
        healthy: false,
        lastCheck: null,
        error: null,
        responseTime: null
      },
      frontend: {
        healthy: false,
        lastCheck: null,
        error: null,
        responseTime: null
      },
      database: {
        healthy: false,
        lastCheck: null,
        error: null
      }
    };
    this.consecutiveFailures = {
      backend: 0,
      frontend: 0
    };
    this.maxFailures = 3; // 连续失败3次后触发重启
  }

  startMonitoring() {
    if (this.checkInterval) {
      console.log('健康监控已在运行');
      return;
    }

    console.log('开始健康监控...');
    
    // 立即执行一次检查
    this.performHealthCheck();
    
    // 设置定时检查
    this.checkInterval = setInterval(() => {
      this.performHealthCheck();
    }, this.intervalTime);
  }

  stopMonitoring() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      console.log('健康监控已停止');
    }
  }

  async performHealthCheck() {
    try {
      const timestamp = new Date().toISOString();
      
      // 检查后端健康状态
      await this.checkBackendHealth(timestamp);
      
      // 检查前端健康状态
      await this.checkFrontendHealth(timestamp);
      
      // 检查数据库健康状态
      await this.checkDatabaseHealth(timestamp);
      
      // 发送健康状态更新到渲染进程
      this.notifyHealthStatus();
      
      // 处理连续失败
      this.handleConsecutiveFailures();
      
    } catch (error) {
      console.error('健康检查执行失败:', error);
    }
  }

  async checkBackendHealth(timestamp) {
    try {
      const serviceStatus = this.serviceManager.getServiceStatus();
      
      if (serviceStatus.backend.status !== 'running') {
        throw new Error('后端服务未运行');
      }

      const startTime = Date.now();
      const response = await axios.get(`${serviceStatus.backend.url}/health`, {
        timeout: 5000
      });
      const responseTime = Date.now() - startTime;

      if (response.status === 200) {
        this.healthStatus.backend = {
          healthy: true,
          lastCheck: timestamp,
          error: null,
          responseTime: responseTime
        };
        this.consecutiveFailures.backend = 0;
      } else {
        throw new Error(`后端健康检查失败: HTTP ${response.status}`);
      }

    } catch (error) {
      this.healthStatus.backend = {
        healthy: false,
        lastCheck: timestamp,
        error: error.message,
        responseTime: null
      };
      this.consecutiveFailures.backend++;
      console.error('后端健康检查失败:', error.message);
    }
  }

  async checkFrontendHealth(timestamp) {
    try {
      const serviceStatus = this.serviceManager.getServiceStatus();
      
      if (serviceStatus.frontend.status !== 'running') {
        throw new Error('前端服务未运行');
      }

      const startTime = Date.now();
      const response = await axios.get(serviceStatus.frontend.url, {
        timeout: 5000
      });
      const responseTime = Date.now() - startTime;

      if (response.status === 200) {
        this.healthStatus.frontend = {
          healthy: true,
          lastCheck: timestamp,
          error: null,
          responseTime: responseTime
        };
        this.consecutiveFailures.frontend = 0;
      } else {
        throw new Error(`前端健康检查失败: HTTP ${response.status}`);
      }

    } catch (error) {
      this.healthStatus.frontend = {
        healthy: false,
        lastCheck: timestamp,
        error: error.message,
        responseTime: null
      };
      this.consecutiveFailures.frontend++;
      console.error('前端健康检查失败:', error.message);
    }
  }

  async checkDatabaseHealth(timestamp) {
    try {
      // 通过后端API检查数据库状态
      const serviceStatus = this.serviceManager.getServiceStatus();
      
      if (!this.healthStatus.backend.healthy) {
        throw new Error('后端服务不健康，无法检查数据库');
      }

      const response = await axios.get(`${serviceStatus.backend.url}/api/v1/health/database`, {
        timeout: 5000
      });

      if (response.status === 200 && response.data.healthy) {
        this.healthStatus.database = {
          healthy: true,
          lastCheck: timestamp,
          error: null
        };
      } else {
        throw new Error('数据库连接失败');
      }

    } catch (error) {
      this.healthStatus.database = {
        healthy: false,
        lastCheck: timestamp,
        error: error.message
      };
      console.error('数据库健康检查失败:', error.message);
    }
  }

  handleConsecutiveFailures() {
    // 检查后端连续失败
    if (this.consecutiveFailures.backend >= this.maxFailures) {
      console.warn(`后端服务连续失败${this.consecutiveFailures.backend}次，尝试重启...`);
      this.restartService('backend');
      this.consecutiveFailures.backend = 0;
    }

    // 检查前端连续失败
    if (this.consecutiveFailures.frontend >= this.maxFailures) {
      console.warn(`前端服务连续失败${this.consecutiveFailures.frontend}次，尝试重启...`);
      this.restartService('frontend');
      this.consecutiveFailures.frontend = 0;
    }
  }

  async restartService(serviceName) {
    try {
      console.log(`重启${serviceName}服务...`);
      
      // 停止服务
      await this.serviceManager.stopService(serviceName);
      
      // 等待一段时间
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 重新启动服务
      if (serviceName === 'backend') {
        await this.serviceManager.startBackend();
      } else if (serviceName === 'frontend') {
        await this.serviceManager.startFrontend();
      }
      
      console.log(`${serviceName}服务重启完成`);
      
      // 通知用户
      this.notifyServiceRestart(serviceName);
      
    } catch (error) {
      console.error(`重启${serviceName}服务失败:`, error);
      this.notifyServiceRestartFailed(serviceName, error.message);
    }
  }

  notifyHealthStatus() {
    // 发送健康状态到所有渲染进程
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      if (window && !window.isDestroyed()) {
        window.webContents.send('health-check', {
          status: this.healthStatus,
          timestamp: new Date().toISOString()
        });
      }
    });
  }

  notifyServiceRestart(serviceName) {
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      if (window && !window.isDestroyed()) {
        window.webContents.send('service-restarted', {
          service: serviceName,
          timestamp: new Date().toISOString()
        });
      }
    });
  }

  notifyServiceRestartFailed(serviceName, error) {
    const windows = BrowserWindow.getAllWindows();
    windows.forEach(window => {
      if (window && !window.isDestroyed()) {
        window.webContents.send('service-restart-failed', {
          service: serviceName,
          error: error,
          timestamp: new Date().toISOString()
        });
      }
    });
  }

  getHealthStatus() {
    return {
      ...this.healthStatus,
      overall: this.getOverallHealth(),
      monitoring: this.checkInterval !== null
    };
  }

  getOverallHealth() {
    const backendHealthy = this.healthStatus.backend.healthy;
    const frontendHealthy = this.healthStatus.frontend.healthy;
    const databaseHealthy = this.healthStatus.database.healthy;

    if (backendHealthy && frontendHealthy && databaseHealthy) {
      return 'healthy';
    } else if (backendHealthy && frontendHealthy) {
      return 'warning'; // 数据库问题但核心服务正常
    } else {
      return 'unhealthy';
    }
  }

  setCheckInterval(intervalTime) {
    this.intervalTime = intervalTime;
    
    if (this.checkInterval) {
      this.stopMonitoring();
      this.startMonitoring();
    }
  }

  setMaxFailures(maxFailures) {
    this.maxFailures = maxFailures;
  }

  // 手动触发健康检查
  async triggerHealthCheck() {
    await this.performHealthCheck();
    return this.getHealthStatus();
  }

  // 重置失败计数
  resetFailureCount(serviceName = null) {
    if (serviceName) {
      this.consecutiveFailures[serviceName] = 0;
    } else {
      this.consecutiveFailures.backend = 0;
      this.consecutiveFailures.frontend = 0;
    }
  }
}

module.exports = HealthChecker;
