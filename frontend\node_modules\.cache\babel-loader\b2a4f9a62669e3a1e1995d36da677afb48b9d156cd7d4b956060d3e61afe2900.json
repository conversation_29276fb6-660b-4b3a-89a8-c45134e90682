{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport default function timelinePreprocessor(option) {\n  var timelineOpt = option && option.timeline;\n  if (!zrUtil.isArray(timelineOpt)) {\n    timelineOpt = timelineOpt ? [timelineOpt] : [];\n  }\n  zrUtil.each(timelineOpt, function (opt) {\n    if (!opt) {\n      return;\n    }\n    compatibleEC2(opt);\n  });\n}\nfunction compatibleEC2(opt) {\n  var type = opt.type;\n  var ec2Types = {\n    'number': 'value',\n    'time': 'time'\n  };\n  // Compatible with ec2\n  if (ec2Types[type]) {\n    opt.axisType = ec2Types[type];\n    delete opt.type;\n  }\n  transferItem(opt);\n  if (has(opt, 'controlPosition')) {\n    var controlStyle = opt.controlStyle || (opt.controlStyle = {});\n    if (!has(controlStyle, 'position')) {\n      controlStyle.position = opt.controlPosition;\n    }\n    if (controlStyle.position === 'none' && !has(controlStyle, 'show')) {\n      controlStyle.show = false;\n      delete controlStyle.position;\n    }\n    delete opt.controlPosition;\n  }\n  zrUtil.each(opt.data || [], function (dataItem) {\n    if (zrUtil.isObject(dataItem) && !zrUtil.isArray(dataItem)) {\n      if (!has(dataItem, 'value') && has(dataItem, 'name')) {\n        // In ec2, using name as value.\n        dataItem.value = dataItem.name;\n      }\n      transferItem(dataItem);\n    }\n  });\n}\nfunction transferItem(opt) {\n  var itemStyle = opt.itemStyle || (opt.itemStyle = {});\n  var itemStyleEmphasis = itemStyle.emphasis || (itemStyle.emphasis = {});\n  // Transfer label out\n  var label = opt.label || opt.label || {};\n  var labelNormal = label.normal || (label.normal = {});\n  var excludeLabelAttr = {\n    normal: 1,\n    emphasis: 1\n  };\n  zrUtil.each(label, function (value, name) {\n    if (!excludeLabelAttr[name] && !has(labelNormal, name)) {\n      labelNormal[name] = value;\n    }\n  });\n  if (itemStyleEmphasis.label && !has(label, 'emphasis')) {\n    label.emphasis = itemStyleEmphasis.label;\n    delete itemStyleEmphasis.label;\n  }\n}\nfunction has(obj, attr) {\n  return obj.hasOwnProperty(attr);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}