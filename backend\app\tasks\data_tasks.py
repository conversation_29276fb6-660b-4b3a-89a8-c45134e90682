"""
数据相关定时任务
"""
import asyncio
import logging
from datetime import datetime
from typing import List

from app.tasks.scheduler import get_scheduler
from data.data_service import get_data_service
from app.services.indicator_service import get_indicator_service
from app.services.risk_service import get_risk_service
from app.websocket.websocket_handler import get_websocket_handler
from app.models.database import get_db
from app.models.stock import Stock

logger = logging.getLogger(__name__)

class DataTasks:
    """数据相关任务"""
    
    def __init__(self):
        self.data_service = get_data_service()
        self.indicator_service = get_indicator_service()
        self.risk_service = get_risk_service()
        self.websocket_handler = get_websocket_handler()
        self.scheduler = get_scheduler()
    
    def register_tasks(self):
        """注册所有数据任务"""
        # 实时数据更新任务 (1分钟)
        self.scheduler.add_task(
            name="realtime_data_update",
            func=self.update_realtime_data,
            interval=60,
            enabled=True
        )
        
        # 技术指标计算任务 (5分钟)
        self.scheduler.add_task(
            name="calculate_indicators",
            func=self.calculate_indicators,
            interval=300,
            enabled=True
        )
        
        # 风险评估任务 (10分钟)
        self.scheduler.add_task(
            name="risk_assessment",
            func=self.perform_risk_assessment,
            interval=600,
            enabled=True
        )
        
        # 数据同步任务 (30分钟)
        self.scheduler.add_task(
            name="sync_market_data",
            func=self.sync_market_data,
            interval=1800,
            enabled=True
        )
        
        # 系统健康检查 (5分钟)
        self.scheduler.add_task(
            name="health_check",
            func=self.system_health_check,
            interval=300,
            enabled=True
        )
        
        logger.info("数据任务注册完成")
    
    async def update_realtime_data(self):
        """更新实时数据"""
        try:
            logger.info("开始更新实时数据")
            
            # 获取活跃股票列表
            active_stocks = self.data_service.get_active_stocks()
            
            if not active_stocks:
                logger.warning("没有活跃的监控股票")
                return
            
            # 限制数量避免API调用过多
            stocks_to_update = active_stocks[:10]
            
            # 模拟获取实时数据
            for ts_code in stocks_to_update:
                try:
                    # 获取最新价格
                    latest_price = self.data_service.get_latest_price(ts_code)
                    
                    if latest_price:
                        # 构造价格数据
                        price_data = {
                            'price': latest_price,
                            'change': 0.0,  # 这里应该计算涨跌额
                            'change_pct': 0.0,  # 这里应该计算涨跌幅
                            'volume': 0,  # 这里应该获取成交量
                            'update_time': datetime.now().isoformat()
                        }
                        
                        # 通过WebSocket广播价格更新
                        await self.websocket_handler.broadcast_price_update(ts_code, price_data)
                
                except Exception as e:
                    logger.error(f"更新 {ts_code} 实时数据失败: {e}")
            
            logger.info(f"实时数据更新完成，处理 {len(stocks_to_update)} 只股票")
            
        except Exception as e:
            logger.error(f"更新实时数据失败: {e}")
    
    async def calculate_indicators(self):
        """计算技术指标"""
        try:
            logger.info("开始计算技术指标")
            
            # 获取活跃股票列表
            active_stocks = self.data_service.get_active_stocks()
            
            if not active_stocks:
                return
            
            # 限制数量
            stocks_to_calculate = active_stocks[:5]
            
            for ts_code in stocks_to_calculate:
                try:
                    # 获取交易信号
                    signals = self.indicator_service.get_stock_signals(ts_code)
                    
                    if signals and signals.get('signals'):
                        # 通过WebSocket广播信号更新
                        await self.websocket_handler.broadcast_signal_update(ts_code, signals)
                
                except Exception as e:
                    logger.error(f"计算 {ts_code} 技术指标失败: {e}")
            
            logger.info(f"技术指标计算完成，处理 {len(stocks_to_calculate)} 只股票")
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
    
    async def perform_risk_assessment(self):
        """执行风险评估"""
        try:
            logger.info("开始风险评估")
            
            # 获取活跃股票列表
            active_stocks = self.data_service.get_active_stocks()
            
            if not active_stocks:
                return
            
            # 执行风险评估
            risk_result = self.risk_service.perform_risk_assessment(active_stocks[:10])
            
            # 检查是否有新的风险告警
            if risk_result.get('alerts'):
                high_risk_alerts = [
                    alert for alert in risk_result['alerts'] 
                    if alert.get('level') in ['HIGH', 'CRITICAL']
                ]
                
                if high_risk_alerts:
                    # 广播风险告警
                    await self.websocket_handler.broadcast_risk_alert({
                        'risk_score': risk_result.get('risk_score', 0),
                        'alerts': high_risk_alerts,
                        'alert_count': len(high_risk_alerts)
                    })
            
            logger.info(f"风险评估完成，风险评分: {risk_result.get('risk_score', 0)}")
            
        except Exception as e:
            logger.error(f"风险评估失败: {e}")
    
    async def sync_market_data(self):
        """同步市场数据"""
        try:
            logger.info("开始同步市场数据")
            
            # 获取活跃股票列表
            active_stocks = self.data_service.get_active_stocks()
            
            if not active_stocks:
                return
            
            # 批量同步数据
            results = self.data_service.batch_sync_daily_data(active_stocks[:5], days=7)
            
            success_count = sum(1 for success in results.values() if success)
            
            # 广播市场数据更新
            await self.websocket_handler.broadcast_market_update({
                'type': 'data_sync',
                'total_stocks': len(results),
                'success_count': success_count,
                'sync_time': datetime.now().isoformat()
            })
            
            logger.info(f"市场数据同步完成: {success_count}/{len(results)} 成功")
            
        except Exception as e:
            logger.error(f"同步市场数据失败: {e}")
    
    async def system_health_check(self):
        """系统健康检查"""
        try:
            # 检查数据库连接
            db_status = "healthy"
            try:
                db = next(get_db())
                db.execute("SELECT 1")
                db.close()
            except Exception:
                db_status = "error"
            
            # 检查WebSocket连接数
            connection_stats = self.websocket_handler.manager.get_connection_stats()
            
            # 检查任务状态
            task_status = self.scheduler.get_task_status()
            
            health_data = {
                'database': db_status,
                'websocket_connections': connection_stats['total_connections'],
                'active_tasks': len([t for t in task_status if t['enabled']]),
                'system_time': datetime.now().isoformat()
            }
            
            # 广播系统状态
            await self.websocket_handler.broadcast_market_update({
                'type': 'system_health',
                'data': health_data
            })
            
            logger.debug(f"系统健康检查完成: {health_data}")
            
        except Exception as e:
            logger.error(f"系统健康检查失败: {e}")

# 全局数据任务实例
data_tasks = DataTasks()

def get_data_tasks() -> DataTasks:
    """获取数据任务实例"""
    return data_tasks

def register_all_tasks():
    """注册所有任务"""
    data_tasks.register_tasks()
