// 简单的Electron启动测试
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动Electron应用测试...');

console.log('当前目录:', __dirname);

const electronProcess = spawn('npx', ['electron', '.', '--dev'], {
  cwd: __dirname,
  stdio: 'inherit',
  shell: true
});

electronProcess.on('error', (error) => {
  console.error('❌ Electron启动失败:', error);
});

electronProcess.on('exit', (code) => {
  console.log(`📊 Electron进程退出，代码: ${code}`);
});

// 10秒后自动退出
setTimeout(() => {
  console.log('⏰ 测试时间到，退出进程');
  electronProcess.kill();
  process.exit(0);
}, 10000);
