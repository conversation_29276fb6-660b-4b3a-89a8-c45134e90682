{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Displayble from './Displayable.js';\nimport BoundingRect from '../core/BoundingRect.js';\nvar m = [];\nvar IncrementalDisplayable = function (_super) {\n  __extends(IncrementalDisplayable, _super);\n  function IncrementalDisplayable() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.notClear = true;\n    _this.incremental = true;\n    _this._displayables = [];\n    _this._temporaryDisplayables = [];\n    _this._cursor = 0;\n    return _this;\n  }\n  IncrementalDisplayable.prototype.traverse = function (cb, context) {\n    cb.call(context, this);\n  };\n  IncrementalDisplayable.prototype.useStyle = function () {\n    this.style = {};\n  };\n  IncrementalDisplayable.prototype.getCursor = function () {\n    return this._cursor;\n  };\n  IncrementalDisplayable.prototype.innerAfterBrush = function () {\n    this._cursor = this._displayables.length;\n  };\n  IncrementalDisplayable.prototype.clearDisplaybles = function () {\n    this._displayables = [];\n    this._temporaryDisplayables = [];\n    this._cursor = 0;\n    this.markRedraw();\n    this.notClear = false;\n  };\n  IncrementalDisplayable.prototype.clearTemporalDisplayables = function () {\n    this._temporaryDisplayables = [];\n  };\n  IncrementalDisplayable.prototype.addDisplayable = function (displayable, notPersistent) {\n    if (notPersistent) {\n      this._temporaryDisplayables.push(displayable);\n    } else {\n      this._displayables.push(displayable);\n    }\n    this.markRedraw();\n  };\n  IncrementalDisplayable.prototype.addDisplayables = function (displayables, notPersistent) {\n    notPersistent = notPersistent || false;\n    for (var i = 0; i < displayables.length; i++) {\n      this.addDisplayable(displayables[i], notPersistent);\n    }\n  };\n  IncrementalDisplayable.prototype.getDisplayables = function () {\n    return this._displayables;\n  };\n  IncrementalDisplayable.prototype.getTemporalDisplayables = function () {\n    return this._temporaryDisplayables;\n  };\n  IncrementalDisplayable.prototype.eachPendingDisplayable = function (cb) {\n    for (var i = this._cursor; i < this._displayables.length; i++) {\n      cb && cb(this._displayables[i]);\n    }\n    for (var i = 0; i < this._temporaryDisplayables.length; i++) {\n      cb && cb(this._temporaryDisplayables[i]);\n    }\n  };\n  IncrementalDisplayable.prototype.update = function () {\n    this.updateTransform();\n    for (var i = this._cursor; i < this._displayables.length; i++) {\n      var displayable = this._displayables[i];\n      displayable.parent = this;\n      displayable.update();\n      displayable.parent = null;\n    }\n    for (var i = 0; i < this._temporaryDisplayables.length; i++) {\n      var displayable = this._temporaryDisplayables[i];\n      displayable.parent = this;\n      displayable.update();\n      displayable.parent = null;\n    }\n  };\n  IncrementalDisplayable.prototype.getBoundingRect = function () {\n    if (!this._rect) {\n      var rect = new BoundingRect(Infinity, Infinity, -Infinity, -Infinity);\n      for (var i = 0; i < this._displayables.length; i++) {\n        var displayable = this._displayables[i];\n        var childRect = displayable.getBoundingRect().clone();\n        if (displayable.needLocalTransform()) {\n          childRect.applyTransform(displayable.getLocalTransform(m));\n        }\n        rect.union(childRect);\n      }\n      this._rect = rect;\n    }\n    return this._rect;\n  };\n  IncrementalDisplayable.prototype.contain = function (x, y) {\n    var localPos = this.transformCoordToLocal(x, y);\n    var rect = this.getBoundingRect();\n    if (rect.contain(localPos[0], localPos[1])) {\n      for (var i = 0; i < this._displayables.length; i++) {\n        var displayable = this._displayables[i];\n        if (displayable.contain(x, y)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  };\n  return IncrementalDisplayable;\n}(Displayble);\nexport default IncrementalDisplayable;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}