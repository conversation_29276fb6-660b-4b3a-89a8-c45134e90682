"""
回测服务
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging

from strategy.backtest_engine import get_backtest_engine, BacktestResult
from strategy.strategies import get_strategy, list_strategies
from data.tushare_client import get_tushare_client

logger = logging.getLogger(__name__)

class BacktestService:
    """回测服务类"""
    
    def __init__(self):
        self.backtest_engine = get_backtest_engine()
        self.tushare_client = get_tushare_client()
    
    def run_strategy_backtest(self, 
                            strategy_name: str,
                            stock_codes: List[str],
                            start_date: str,
                            end_date: str,
                            initial_capital: float = 1000000,
                            max_position_size: float = 0.2,
                            commission_rate: float = 0.0003,
                            strategy_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """运行策略回测"""
        
        try:
            logger.info(f"开始回测策略: {strategy_name}")
            
            # 获取策略
            strategy = get_strategy(strategy_name)
            
            # 设置策略参数
            if strategy_params:
                strategy.set_parameters(**strategy_params)
            
            # 设置回测引擎参数
            self.backtest_engine.initial_capital = initial_capital
            self.backtest_engine.commission_rate = commission_rate
            
            # 获取历史数据
            historical_data = self._get_historical_data(stock_codes, start_date, end_date)
            
            if not historical_data:
                return {
                    'success': False,
                    'error': '无法获取历史数据'
                }
            
            # 创建策略函数
            def strategy_func(day_data, positions):
                return strategy.generate_signals(day_data, positions)
            
            # 运行回测
            result = self.backtest_engine.run_backtest(
                data=historical_data,
                strategy_func=strategy_func,
                start_date=start_date,
                end_date=end_date,
                max_position_size=max_position_size
            )
            
            # 格式化结果
            formatted_result = self._format_backtest_result(result, strategy_name)
            
            logger.info(f"回测完成，总收益率: {result.total_return:.2%}")
            return formatted_result
            
        except Exception as e:
            logger.error(f"回测失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def compare_strategies(self,
                          strategy_names: List[str],
                          stock_codes: List[str],
                          start_date: str,
                          end_date: str,
                          initial_capital: float = 1000000) -> Dict[str, Any]:
        """比较多个策略"""
        
        try:
            logger.info(f"开始策略比较: {strategy_names}")
            
            results = {}
            
            for strategy_name in strategy_names:
                result = self.run_strategy_backtest(
                    strategy_name=strategy_name,
                    stock_codes=stock_codes,
                    start_date=start_date,
                    end_date=end_date,
                    initial_capital=initial_capital
                )
                
                if result.get('success', False):
                    results[strategy_name] = result
            
            # 生成比较报告
            comparison = self._generate_comparison_report(results)
            
            return {
                'success': True,
                'individual_results': results,
                'comparison': comparison
            }
            
        except Exception as e:
            logger.error(f"策略比较失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def optimize_strategy_parameters(self,
                                   strategy_name: str,
                                   stock_codes: List[str],
                                   start_date: str,
                                   end_date: str,
                                   param_ranges: Dict[str, List]) -> Dict[str, Any]:
        """优化策略参数"""
        
        try:
            logger.info(f"开始参数优化: {strategy_name}")
            
            # 生成参数组合
            param_combinations = self._generate_param_combinations(param_ranges)
            
            best_result = None
            best_params = None
            best_return = float('-inf')
            
            optimization_results = []
            
            for i, params in enumerate(param_combinations):
                logger.info(f"测试参数组合 {i+1}/{len(param_combinations)}: {params}")
                
                result = self.run_strategy_backtest(
                    strategy_name=strategy_name,
                    stock_codes=stock_codes,
                    start_date=start_date,
                    end_date=end_date,
                    strategy_params=params
                )
                
                if result.get('success', False):
                    total_return = result['performance_metrics']['total_return']
                    
                    optimization_results.append({
                        'parameters': params,
                        'total_return': total_return,
                        'sharpe_ratio': result['performance_metrics']['sharpe_ratio'],
                        'max_drawdown': result['performance_metrics']['max_drawdown']
                    })
                    
                    if total_return > best_return:
                        best_return = total_return
                        best_params = params
                        best_result = result
            
            return {
                'success': True,
                'best_parameters': best_params,
                'best_result': best_result,
                'optimization_results': optimization_results,
                'total_combinations': len(param_combinations)
            }
            
        except Exception as e:
            logger.error(f"参数优化失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_available_strategies(self) -> List[Dict[str, str]]:
        """获取可用策略列表"""
        return list_strategies()
    
    def _get_historical_data(self, stock_codes: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """获取历史数据"""
        historical_data = {}
        
        for ts_code in stock_codes:
            try:
                df = self.tushare_client.get_daily_data(ts_code, start_date, end_date)
                if not df.empty:
                    historical_data[ts_code] = df
                else:
                    logger.warning(f"未获取到 {ts_code} 的历史数据")
            except Exception as e:
                logger.error(f"获取 {ts_code} 历史数据失败: {e}")
        
        return historical_data
    
    def _format_backtest_result(self, result: BacktestResult, strategy_name: str) -> Dict[str, Any]:
        """格式化回测结果"""
        return {
            'success': True,
            'strategy_name': strategy_name,
            'performance_metrics': {
                'total_return': result.total_return,
                'annual_return': result.annual_return,
                'max_drawdown': result.max_drawdown,
                'sharpe_ratio': result.sharpe_ratio,
                'win_rate': result.win_rate,
                'profit_factor': result.profit_factor
            },
            'trading_statistics': {
                'total_trades': result.total_trades,
                'profit_trades': result.profit_trades,
                'loss_trades': result.loss_trades,
                'avg_profit': result.avg_profit,
                'avg_loss': result.avg_loss
            },
            'portfolio_curve': {
                'dates': result.portfolio_values.index.tolist(),
                'values': result.portfolio_values.tolist()
            },
            'trades': [
                {
                    'ts_code': trade.ts_code,
                    'action': trade.action,
                    'shares': trade.shares,
                    'price': trade.price,
                    'date': trade.date,
                    'signal': trade.signal,
                    'commission': trade.commission
                }
                for trade in result.trades
            ],
            'final_positions': [
                {
                    'ts_code': pos.ts_code,
                    'shares': pos.shares,
                    'entry_price': pos.entry_price,
                    'entry_date': pos.entry_date
                }
                for pos in result.positions
            ]
        }
    
    def _generate_comparison_report(self, results: Dict[str, Dict]) -> Dict[str, Any]:
        """生成策略比较报告"""
        if not results:
            return {}
        
        comparison_metrics = {}
        
        # 提取各策略的关键指标
        for strategy_name, result in results.items():
            metrics = result['performance_metrics']
            comparison_metrics[strategy_name] = {
                'total_return': metrics['total_return'],
                'annual_return': metrics['annual_return'],
                'max_drawdown': metrics['max_drawdown'],
                'sharpe_ratio': metrics['sharpe_ratio'],
                'win_rate': metrics['win_rate']
            }
        
        # 找出最佳策略
        best_return_strategy = max(comparison_metrics.keys(), 
                                 key=lambda x: comparison_metrics[x]['total_return'])
        best_sharpe_strategy = max(comparison_metrics.keys(), 
                                 key=lambda x: comparison_metrics[x]['sharpe_ratio'])
        
        return {
            'metrics_comparison': comparison_metrics,
            'best_return_strategy': best_return_strategy,
            'best_sharpe_strategy': best_sharpe_strategy,
            'ranking': sorted(comparison_metrics.keys(), 
                            key=lambda x: comparison_metrics[x]['total_return'], 
                            reverse=True)
        }
    
    def _generate_param_combinations(self, param_ranges: Dict[str, List]) -> List[Dict[str, Any]]:
        """生成参数组合"""
        import itertools
        
        param_names = list(param_ranges.keys())
        param_values = list(param_ranges.values())
        
        combinations = []
        for combination in itertools.product(*param_values):
            param_dict = dict(zip(param_names, combination))
            combinations.append(param_dict)
        
        return combinations

# 全局回测服务实例
backtest_service = BacktestService()

def get_backtest_service() -> BacktestService:
    """获取回测服务实例"""
    return backtest_service
