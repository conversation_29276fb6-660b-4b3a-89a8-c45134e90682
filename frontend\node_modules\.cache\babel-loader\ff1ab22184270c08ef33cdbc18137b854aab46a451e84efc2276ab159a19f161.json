{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ChartView from '../../view/Chart.js';\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nvar BoxplotView = /** @class */function (_super) {\n  __extends(BoxplotView, _super);\n  function BoxplotView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BoxplotView.type;\n    return _this;\n  }\n  BoxplotView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var group = this.group;\n    var oldData = this._data;\n    // There is no old data only when first rendering or switching from\n    // stream mode to normal mode, where previous elements should be removed.\n    if (!this._data) {\n      group.removeAll();\n    }\n    var constDim = seriesModel.get('layout') === 'horizontal' ? 1 : 0;\n    data.diff(oldData).add(function (newIdx) {\n      if (data.hasValue(newIdx)) {\n        var itemLayout = data.getItemLayout(newIdx);\n        var symbolEl = createNormalBox(itemLayout, data, newIdx, constDim, true);\n        data.setItemGraphicEl(newIdx, symbolEl);\n        group.add(symbolEl);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var symbolEl = oldData.getItemGraphicEl(oldIdx);\n      // Empty data\n      if (!data.hasValue(newIdx)) {\n        group.remove(symbolEl);\n        return;\n      }\n      var itemLayout = data.getItemLayout(newIdx);\n      if (!symbolEl) {\n        symbolEl = createNormalBox(itemLayout, data, newIdx, constDim);\n      } else {\n        saveOldStyle(symbolEl);\n        updateNormalBoxData(itemLayout, symbolEl, data, newIdx);\n      }\n      group.add(symbolEl);\n      data.setItemGraphicEl(newIdx, symbolEl);\n    }).remove(function (oldIdx) {\n      var el = oldData.getItemGraphicEl(oldIdx);\n      el && group.remove(el);\n    }).execute();\n    this._data = data;\n  };\n  BoxplotView.prototype.remove = function (ecModel) {\n    var group = this.group;\n    var data = this._data;\n    this._data = null;\n    data && data.eachItemGraphicEl(function (el) {\n      el && group.remove(el);\n    });\n  };\n  BoxplotView.type = 'boxplot';\n  return BoxplotView;\n}(ChartView);\nvar BoxPathShape = /** @class */function () {\n  function BoxPathShape() {}\n  return BoxPathShape;\n}();\nvar BoxPath = /** @class */function (_super) {\n  __extends(BoxPath, _super);\n  function BoxPath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'boxplotBoxPath';\n    return _this;\n  }\n  BoxPath.prototype.getDefaultShape = function () {\n    return new BoxPathShape();\n  };\n  BoxPath.prototype.buildPath = function (ctx, shape) {\n    var ends = shape.points;\n    var i = 0;\n    ctx.moveTo(ends[i][0], ends[i][1]);\n    i++;\n    for (; i < 4; i++) {\n      ctx.lineTo(ends[i][0], ends[i][1]);\n    }\n    ctx.closePath();\n    for (; i < ends.length; i++) {\n      ctx.moveTo(ends[i][0], ends[i][1]);\n      i++;\n      ctx.lineTo(ends[i][0], ends[i][1]);\n    }\n  };\n  return BoxPath;\n}(Path);\nfunction createNormalBox(itemLayout, data, dataIndex, constDim, isInit) {\n  var ends = itemLayout.ends;\n  var el = new BoxPath({\n    shape: {\n      points: isInit ? transInit(ends, constDim, itemLayout) : ends\n    }\n  });\n  updateNormalBoxData(itemLayout, el, data, dataIndex, isInit);\n  return el;\n}\nfunction updateNormalBoxData(itemLayout, el, data, dataIndex, isInit) {\n  var seriesModel = data.hostModel;\n  var updateMethod = graphic[isInit ? 'initProps' : 'updateProps'];\n  updateMethod(el, {\n    shape: {\n      points: itemLayout.ends\n    }\n  }, seriesModel, dataIndex);\n  el.useStyle(data.getItemVisual(dataIndex, 'style'));\n  el.style.strokeNoScale = true;\n  el.z2 = 100;\n  var itemModel = data.getItemModel(dataIndex);\n  var emphasisModel = itemModel.getModel('emphasis');\n  setStatesStylesFromModel(el, itemModel);\n  toggleHoverEmphasis(el, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n}\nfunction transInit(points, dim, itemLayout) {\n  return zrUtil.map(points, function (point) {\n    point = point.slice();\n    point[dim] = itemLayout.initBaseline;\n    return point;\n  });\n}\nexport default BoxplotView;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}