{"ast": null, "code": "import Clip from './Clip.js';\nimport * as color from '../tool/color.js';\nimport { eqNaN, extend, isArrayLike, isFunction, isGradientObject, isNumber, isString, keys, logError, map } from '../core/util.js';\nimport easingFuncs from './easing.js';\nimport { createCubicEasingFunc } from './cubicEasing.js';\nimport { isLinearGradient, isRadialGradient } from '../svg/helper.js';\n;\nvar arraySlice = Array.prototype.slice;\nfunction interpolateNumber(p0, p1, percent) {\n  return (p1 - p0) * percent + p0;\n}\nfunction interpolate1DArray(out, p0, p1, percent) {\n  var len = p0.length;\n  for (var i = 0; i < len; i++) {\n    out[i] = interpolateNumber(p0[i], p1[i], percent);\n  }\n  return out;\n}\nfunction interpolate2DArray(out, p0, p1, percent) {\n  var len = p0.length;\n  var len2 = len && p0[0].length;\n  for (var i = 0; i < len; i++) {\n    if (!out[i]) {\n      out[i] = [];\n    }\n    for (var j = 0; j < len2; j++) {\n      out[i][j] = interpolateNumber(p0[i][j], p1[i][j], percent);\n    }\n  }\n  return out;\n}\nfunction add1DArray(out, p0, p1, sign) {\n  var len = p0.length;\n  for (var i = 0; i < len; i++) {\n    out[i] = p0[i] + p1[i] * sign;\n  }\n  return out;\n}\nfunction add2DArray(out, p0, p1, sign) {\n  var len = p0.length;\n  var len2 = len && p0[0].length;\n  for (var i = 0; i < len; i++) {\n    if (!out[i]) {\n      out[i] = [];\n    }\n    for (var j = 0; j < len2; j++) {\n      out[i][j] = p0[i][j] + p1[i][j] * sign;\n    }\n  }\n  return out;\n}\nfunction fillColorStops(val0, val1) {\n  var len0 = val0.length;\n  var len1 = val1.length;\n  var shorterArr = len0 > len1 ? val1 : val0;\n  var shorterLen = Math.min(len0, len1);\n  var last = shorterArr[shorterLen - 1] || {\n    color: [0, 0, 0, 0],\n    offset: 0\n  };\n  for (var i = shorterLen; i < Math.max(len0, len1); i++) {\n    shorterArr.push({\n      offset: last.offset,\n      color: last.color.slice()\n    });\n  }\n}\nfunction fillArray(val0, val1, arrDim) {\n  var arr0 = val0;\n  var arr1 = val1;\n  if (!arr0.push || !arr1.push) {\n    return;\n  }\n  var arr0Len = arr0.length;\n  var arr1Len = arr1.length;\n  if (arr0Len !== arr1Len) {\n    var isPreviousLarger = arr0Len > arr1Len;\n    if (isPreviousLarger) {\n      arr0.length = arr1Len;\n    } else {\n      for (var i = arr0Len; i < arr1Len; i++) {\n        arr0.push(arrDim === 1 ? arr1[i] : arraySlice.call(arr1[i]));\n      }\n    }\n  }\n  var len2 = arr0[0] && arr0[0].length;\n  for (var i = 0; i < arr0.length; i++) {\n    if (arrDim === 1) {\n      if (isNaN(arr0[i])) {\n        arr0[i] = arr1[i];\n      }\n    } else {\n      for (var j = 0; j < len2; j++) {\n        if (isNaN(arr0[i][j])) {\n          arr0[i][j] = arr1[i][j];\n        }\n      }\n    }\n  }\n}\nexport function cloneValue(value) {\n  if (isArrayLike(value)) {\n    var len = value.length;\n    if (isArrayLike(value[0])) {\n      var ret = [];\n      for (var i = 0; i < len; i++) {\n        ret.push(arraySlice.call(value[i]));\n      }\n      return ret;\n    }\n    return arraySlice.call(value);\n  }\n  return value;\n}\nfunction rgba2String(rgba) {\n  rgba[0] = Math.floor(rgba[0]) || 0;\n  rgba[1] = Math.floor(rgba[1]) || 0;\n  rgba[2] = Math.floor(rgba[2]) || 0;\n  rgba[3] = rgba[3] == null ? 1 : rgba[3];\n  return 'rgba(' + rgba.join(',') + ')';\n}\nfunction guessArrayDim(value) {\n  return isArrayLike(value && value[0]) ? 2 : 1;\n}\nvar VALUE_TYPE_NUMBER = 0;\nvar VALUE_TYPE_1D_ARRAY = 1;\nvar VALUE_TYPE_2D_ARRAY = 2;\nvar VALUE_TYPE_COLOR = 3;\nvar VALUE_TYPE_LINEAR_GRADIENT = 4;\nvar VALUE_TYPE_RADIAL_GRADIENT = 5;\nvar VALUE_TYPE_UNKOWN = 6;\nfunction isGradientValueType(valType) {\n  return valType === VALUE_TYPE_LINEAR_GRADIENT || valType === VALUE_TYPE_RADIAL_GRADIENT;\n}\nfunction isArrayValueType(valType) {\n  return valType === VALUE_TYPE_1D_ARRAY || valType === VALUE_TYPE_2D_ARRAY;\n}\nvar tmpRgba = [0, 0, 0, 0];\nvar Track = function () {\n  function Track(propName) {\n    this.keyframes = [];\n    this.discrete = false;\n    this._invalid = false;\n    this._needsSort = false;\n    this._lastFr = 0;\n    this._lastFrP = 0;\n    this.propName = propName;\n  }\n  Track.prototype.isFinished = function () {\n    return this._finished;\n  };\n  Track.prototype.setFinished = function () {\n    this._finished = true;\n    if (this._additiveTrack) {\n      this._additiveTrack.setFinished();\n    }\n  };\n  Track.prototype.needsAnimate = function () {\n    return this.keyframes.length >= 1;\n  };\n  Track.prototype.getAdditiveTrack = function () {\n    return this._additiveTrack;\n  };\n  Track.prototype.addKeyframe = function (time, rawValue, easing) {\n    this._needsSort = true;\n    var keyframes = this.keyframes;\n    var len = keyframes.length;\n    var discrete = false;\n    var valType = VALUE_TYPE_UNKOWN;\n    var value = rawValue;\n    if (isArrayLike(rawValue)) {\n      var arrayDim = guessArrayDim(rawValue);\n      valType = arrayDim;\n      if (arrayDim === 1 && !isNumber(rawValue[0]) || arrayDim === 2 && !isNumber(rawValue[0][0])) {\n        discrete = true;\n      }\n    } else {\n      if (isNumber(rawValue) && !eqNaN(rawValue)) {\n        valType = VALUE_TYPE_NUMBER;\n      } else if (isString(rawValue)) {\n        if (!isNaN(+rawValue)) {\n          valType = VALUE_TYPE_NUMBER;\n        } else {\n          var colorArray = color.parse(rawValue);\n          if (colorArray) {\n            value = colorArray;\n            valType = VALUE_TYPE_COLOR;\n          }\n        }\n      } else if (isGradientObject(rawValue)) {\n        var parsedGradient = extend({}, value);\n        parsedGradient.colorStops = map(rawValue.colorStops, function (colorStop) {\n          return {\n            offset: colorStop.offset,\n            color: color.parse(colorStop.color)\n          };\n        });\n        if (isLinearGradient(rawValue)) {\n          valType = VALUE_TYPE_LINEAR_GRADIENT;\n        } else if (isRadialGradient(rawValue)) {\n          valType = VALUE_TYPE_RADIAL_GRADIENT;\n        }\n        value = parsedGradient;\n      }\n    }\n    if (len === 0) {\n      this.valType = valType;\n    } else if (valType !== this.valType || valType === VALUE_TYPE_UNKOWN) {\n      discrete = true;\n    }\n    this.discrete = this.discrete || discrete;\n    var kf = {\n      time: time,\n      value: value,\n      rawValue: rawValue,\n      percent: 0\n    };\n    if (easing) {\n      kf.easing = easing;\n      kf.easingFunc = isFunction(easing) ? easing : easingFuncs[easing] || createCubicEasingFunc(easing);\n    }\n    keyframes.push(kf);\n    return kf;\n  };\n  Track.prototype.prepare = function (maxTime, additiveTrack) {\n    var kfs = this.keyframes;\n    if (this._needsSort) {\n      kfs.sort(function (a, b) {\n        return a.time - b.time;\n      });\n    }\n    var valType = this.valType;\n    var kfsLen = kfs.length;\n    var lastKf = kfs[kfsLen - 1];\n    var isDiscrete = this.discrete;\n    var isArr = isArrayValueType(valType);\n    var isGradient = isGradientValueType(valType);\n    for (var i = 0; i < kfsLen; i++) {\n      var kf = kfs[i];\n      var value = kf.value;\n      var lastValue = lastKf.value;\n      kf.percent = kf.time / maxTime;\n      if (!isDiscrete) {\n        if (isArr && i !== kfsLen - 1) {\n          fillArray(value, lastValue, valType);\n        } else if (isGradient) {\n          fillColorStops(value.colorStops, lastValue.colorStops);\n        }\n      }\n    }\n    if (!isDiscrete && valType !== VALUE_TYPE_RADIAL_GRADIENT && additiveTrack && this.needsAnimate() && additiveTrack.needsAnimate() && valType === additiveTrack.valType && !additiveTrack._finished) {\n      this._additiveTrack = additiveTrack;\n      var startValue = kfs[0].value;\n      for (var i = 0; i < kfsLen; i++) {\n        if (valType === VALUE_TYPE_NUMBER) {\n          kfs[i].additiveValue = kfs[i].value - startValue;\n        } else if (valType === VALUE_TYPE_COLOR) {\n          kfs[i].additiveValue = add1DArray([], kfs[i].value, startValue, -1);\n        } else if (isArrayValueType(valType)) {\n          kfs[i].additiveValue = valType === VALUE_TYPE_1D_ARRAY ? add1DArray([], kfs[i].value, startValue, -1) : add2DArray([], kfs[i].value, startValue, -1);\n        }\n      }\n    }\n  };\n  Track.prototype.step = function (target, percent) {\n    if (this._finished) {\n      return;\n    }\n    if (this._additiveTrack && this._additiveTrack._finished) {\n      this._additiveTrack = null;\n    }\n    var isAdditive = this._additiveTrack != null;\n    var valueKey = isAdditive ? 'additiveValue' : 'value';\n    var valType = this.valType;\n    var keyframes = this.keyframes;\n    var kfsNum = keyframes.length;\n    var propName = this.propName;\n    var isValueColor = valType === VALUE_TYPE_COLOR;\n    var frameIdx;\n    var lastFrame = this._lastFr;\n    var mathMin = Math.min;\n    var frame;\n    var nextFrame;\n    if (kfsNum === 1) {\n      frame = nextFrame = keyframes[0];\n    } else {\n      if (percent < 0) {\n        frameIdx = 0;\n      } else if (percent < this._lastFrP) {\n        var start = mathMin(lastFrame + 1, kfsNum - 1);\n        for (frameIdx = start; frameIdx >= 0; frameIdx--) {\n          if (keyframes[frameIdx].percent <= percent) {\n            break;\n          }\n        }\n        frameIdx = mathMin(frameIdx, kfsNum - 2);\n      } else {\n        for (frameIdx = lastFrame; frameIdx < kfsNum; frameIdx++) {\n          if (keyframes[frameIdx].percent > percent) {\n            break;\n          }\n        }\n        frameIdx = mathMin(frameIdx - 1, kfsNum - 2);\n      }\n      nextFrame = keyframes[frameIdx + 1];\n      frame = keyframes[frameIdx];\n    }\n    if (!(frame && nextFrame)) {\n      return;\n    }\n    this._lastFr = frameIdx;\n    this._lastFrP = percent;\n    var interval = nextFrame.percent - frame.percent;\n    var w = interval === 0 ? 1 : mathMin((percent - frame.percent) / interval, 1);\n    if (nextFrame.easingFunc) {\n      w = nextFrame.easingFunc(w);\n    }\n    var targetArr = isAdditive ? this._additiveValue : isValueColor ? tmpRgba : target[propName];\n    if ((isArrayValueType(valType) || isValueColor) && !targetArr) {\n      targetArr = this._additiveValue = [];\n    }\n    if (this.discrete) {\n      target[propName] = w < 1 ? frame.rawValue : nextFrame.rawValue;\n    } else if (isArrayValueType(valType)) {\n      valType === VALUE_TYPE_1D_ARRAY ? interpolate1DArray(targetArr, frame[valueKey], nextFrame[valueKey], w) : interpolate2DArray(targetArr, frame[valueKey], nextFrame[valueKey], w);\n    } else if (isGradientValueType(valType)) {\n      var val = frame[valueKey];\n      var nextVal_1 = nextFrame[valueKey];\n      var isLinearGradient_1 = valType === VALUE_TYPE_LINEAR_GRADIENT;\n      target[propName] = {\n        type: isLinearGradient_1 ? 'linear' : 'radial',\n        x: interpolateNumber(val.x, nextVal_1.x, w),\n        y: interpolateNumber(val.y, nextVal_1.y, w),\n        colorStops: map(val.colorStops, function (colorStop, idx) {\n          var nextColorStop = nextVal_1.colorStops[idx];\n          return {\n            offset: interpolateNumber(colorStop.offset, nextColorStop.offset, w),\n            color: rgba2String(interpolate1DArray([], colorStop.color, nextColorStop.color, w))\n          };\n        }),\n        global: nextVal_1.global\n      };\n      if (isLinearGradient_1) {\n        target[propName].x2 = interpolateNumber(val.x2, nextVal_1.x2, w);\n        target[propName].y2 = interpolateNumber(val.y2, nextVal_1.y2, w);\n      } else {\n        target[propName].r = interpolateNumber(val.r, nextVal_1.r, w);\n      }\n    } else if (isValueColor) {\n      interpolate1DArray(targetArr, frame[valueKey], nextFrame[valueKey], w);\n      if (!isAdditive) {\n        target[propName] = rgba2String(targetArr);\n      }\n    } else {\n      var value = interpolateNumber(frame[valueKey], nextFrame[valueKey], w);\n      if (isAdditive) {\n        this._additiveValue = value;\n      } else {\n        target[propName] = value;\n      }\n    }\n    if (isAdditive) {\n      this._addToTarget(target);\n    }\n  };\n  Track.prototype._addToTarget = function (target) {\n    var valType = this.valType;\n    var propName = this.propName;\n    var additiveValue = this._additiveValue;\n    if (valType === VALUE_TYPE_NUMBER) {\n      target[propName] = target[propName] + additiveValue;\n    } else if (valType === VALUE_TYPE_COLOR) {\n      color.parse(target[propName], tmpRgba);\n      add1DArray(tmpRgba, tmpRgba, additiveValue, 1);\n      target[propName] = rgba2String(tmpRgba);\n    } else if (valType === VALUE_TYPE_1D_ARRAY) {\n      add1DArray(target[propName], target[propName], additiveValue, 1);\n    } else if (valType === VALUE_TYPE_2D_ARRAY) {\n      add2DArray(target[propName], target[propName], additiveValue, 1);\n    }\n  };\n  return Track;\n}();\nvar Animator = function () {\n  function Animator(target, loop, allowDiscreteAnimation, additiveTo) {\n    this._tracks = {};\n    this._trackKeys = [];\n    this._maxTime = 0;\n    this._started = 0;\n    this._clip = null;\n    this._target = target;\n    this._loop = loop;\n    if (loop && additiveTo) {\n      logError('Can\\' use additive animation on looped animation.');\n      return;\n    }\n    this._additiveAnimators = additiveTo;\n    this._allowDiscrete = allowDiscreteAnimation;\n  }\n  Animator.prototype.getMaxTime = function () {\n    return this._maxTime;\n  };\n  Animator.prototype.getDelay = function () {\n    return this._delay;\n  };\n  Animator.prototype.getLoop = function () {\n    return this._loop;\n  };\n  Animator.prototype.getTarget = function () {\n    return this._target;\n  };\n  Animator.prototype.changeTarget = function (target) {\n    this._target = target;\n  };\n  Animator.prototype.when = function (time, props, easing) {\n    return this.whenWithKeys(time, props, keys(props), easing);\n  };\n  Animator.prototype.whenWithKeys = function (time, props, propNames, easing) {\n    var tracks = this._tracks;\n    for (var i = 0; i < propNames.length; i++) {\n      var propName = propNames[i];\n      var track = tracks[propName];\n      if (!track) {\n        track = tracks[propName] = new Track(propName);\n        var initialValue = void 0;\n        var additiveTrack = this._getAdditiveTrack(propName);\n        if (additiveTrack) {\n          var addtiveTrackKfs = additiveTrack.keyframes;\n          var lastFinalKf = addtiveTrackKfs[addtiveTrackKfs.length - 1];\n          initialValue = lastFinalKf && lastFinalKf.value;\n          if (additiveTrack.valType === VALUE_TYPE_COLOR && initialValue) {\n            initialValue = rgba2String(initialValue);\n          }\n        } else {\n          initialValue = this._target[propName];\n        }\n        if (initialValue == null) {\n          continue;\n        }\n        if (time > 0) {\n          track.addKeyframe(0, cloneValue(initialValue), easing);\n        }\n        this._trackKeys.push(propName);\n      }\n      track.addKeyframe(time, cloneValue(props[propName]), easing);\n    }\n    this._maxTime = Math.max(this._maxTime, time);\n    return this;\n  };\n  Animator.prototype.pause = function () {\n    this._clip.pause();\n    this._paused = true;\n  };\n  Animator.prototype.resume = function () {\n    this._clip.resume();\n    this._paused = false;\n  };\n  Animator.prototype.isPaused = function () {\n    return !!this._paused;\n  };\n  Animator.prototype.duration = function (duration) {\n    this._maxTime = duration;\n    this._force = true;\n    return this;\n  };\n  Animator.prototype._doneCallback = function () {\n    this._setTracksFinished();\n    this._clip = null;\n    var doneList = this._doneCbs;\n    if (doneList) {\n      var len = doneList.length;\n      for (var i = 0; i < len; i++) {\n        doneList[i].call(this);\n      }\n    }\n  };\n  Animator.prototype._abortedCallback = function () {\n    this._setTracksFinished();\n    var animation = this.animation;\n    var abortedList = this._abortedCbs;\n    if (animation) {\n      animation.removeClip(this._clip);\n    }\n    this._clip = null;\n    if (abortedList) {\n      for (var i = 0; i < abortedList.length; i++) {\n        abortedList[i].call(this);\n      }\n    }\n  };\n  Animator.prototype._setTracksFinished = function () {\n    var tracks = this._tracks;\n    var tracksKeys = this._trackKeys;\n    for (var i = 0; i < tracksKeys.length; i++) {\n      tracks[tracksKeys[i]].setFinished();\n    }\n  };\n  Animator.prototype._getAdditiveTrack = function (trackName) {\n    var additiveTrack;\n    var additiveAnimators = this._additiveAnimators;\n    if (additiveAnimators) {\n      for (var i = 0; i < additiveAnimators.length; i++) {\n        var track = additiveAnimators[i].getTrack(trackName);\n        if (track) {\n          additiveTrack = track;\n        }\n      }\n    }\n    return additiveTrack;\n  };\n  Animator.prototype.start = function (easing) {\n    if (this._started > 0) {\n      return;\n    }\n    this._started = 1;\n    var self = this;\n    var tracks = [];\n    var maxTime = this._maxTime || 0;\n    for (var i = 0; i < this._trackKeys.length; i++) {\n      var propName = this._trackKeys[i];\n      var track = this._tracks[propName];\n      var additiveTrack = this._getAdditiveTrack(propName);\n      var kfs = track.keyframes;\n      var kfsNum = kfs.length;\n      track.prepare(maxTime, additiveTrack);\n      if (track.needsAnimate()) {\n        if (!this._allowDiscrete && track.discrete) {\n          var lastKf = kfs[kfsNum - 1];\n          if (lastKf) {\n            self._target[track.propName] = lastKf.rawValue;\n          }\n          track.setFinished();\n        } else {\n          tracks.push(track);\n        }\n      }\n    }\n    if (tracks.length || this._force) {\n      var clip = new Clip({\n        life: maxTime,\n        loop: this._loop,\n        delay: this._delay || 0,\n        onframe: function (percent) {\n          self._started = 2;\n          var additiveAnimators = self._additiveAnimators;\n          if (additiveAnimators) {\n            var stillHasAdditiveAnimator = false;\n            for (var i = 0; i < additiveAnimators.length; i++) {\n              if (additiveAnimators[i]._clip) {\n                stillHasAdditiveAnimator = true;\n                break;\n              }\n            }\n            if (!stillHasAdditiveAnimator) {\n              self._additiveAnimators = null;\n            }\n          }\n          for (var i = 0; i < tracks.length; i++) {\n            tracks[i].step(self._target, percent);\n          }\n          var onframeList = self._onframeCbs;\n          if (onframeList) {\n            for (var i = 0; i < onframeList.length; i++) {\n              onframeList[i](self._target, percent);\n            }\n          }\n        },\n        ondestroy: function () {\n          self._doneCallback();\n        }\n      });\n      this._clip = clip;\n      if (this.animation) {\n        this.animation.addClip(clip);\n      }\n      if (easing) {\n        clip.setEasing(easing);\n      }\n    } else {\n      this._doneCallback();\n    }\n    return this;\n  };\n  Animator.prototype.stop = function (forwardToLast) {\n    if (!this._clip) {\n      return;\n    }\n    var clip = this._clip;\n    if (forwardToLast) {\n      clip.onframe(1);\n    }\n    this._abortedCallback();\n  };\n  Animator.prototype.delay = function (time) {\n    this._delay = time;\n    return this;\n  };\n  Animator.prototype.during = function (cb) {\n    if (cb) {\n      if (!this._onframeCbs) {\n        this._onframeCbs = [];\n      }\n      this._onframeCbs.push(cb);\n    }\n    return this;\n  };\n  Animator.prototype.done = function (cb) {\n    if (cb) {\n      if (!this._doneCbs) {\n        this._doneCbs = [];\n      }\n      this._doneCbs.push(cb);\n    }\n    return this;\n  };\n  Animator.prototype.aborted = function (cb) {\n    if (cb) {\n      if (!this._abortedCbs) {\n        this._abortedCbs = [];\n      }\n      this._abortedCbs.push(cb);\n    }\n    return this;\n  };\n  Animator.prototype.getClip = function () {\n    return this._clip;\n  };\n  Animator.prototype.getTrack = function (propName) {\n    return this._tracks[propName];\n  };\n  Animator.prototype.getTracks = function () {\n    var _this = this;\n    return map(this._trackKeys, function (key) {\n      return _this._tracks[key];\n    });\n  };\n  Animator.prototype.stopTracks = function (propNames, forwardToLast) {\n    if (!propNames.length || !this._clip) {\n      return true;\n    }\n    var tracks = this._tracks;\n    var tracksKeys = this._trackKeys;\n    for (var i = 0; i < propNames.length; i++) {\n      var track = tracks[propNames[i]];\n      if (track && !track.isFinished()) {\n        if (forwardToLast) {\n          track.step(this._target, 1);\n        } else if (this._started === 1) {\n          track.step(this._target, 0);\n        }\n        track.setFinished();\n      }\n    }\n    var allAborted = true;\n    for (var i = 0; i < tracksKeys.length; i++) {\n      if (!tracks[tracksKeys[i]].isFinished()) {\n        allAborted = false;\n        break;\n      }\n    }\n    if (allAborted) {\n      this._abortedCallback();\n    }\n    return allAborted;\n  };\n  Animator.prototype.saveTo = function (target, trackKeys, firstOrLast) {\n    if (!target) {\n      return;\n    }\n    trackKeys = trackKeys || this._trackKeys;\n    for (var i = 0; i < trackKeys.length; i++) {\n      var propName = trackKeys[i];\n      var track = this._tracks[propName];\n      if (!track || track.isFinished()) {\n        continue;\n      }\n      var kfs = track.keyframes;\n      var kf = kfs[firstOrLast ? 0 : kfs.length - 1];\n      if (kf) {\n        target[propName] = cloneValue(kf.rawValue);\n      }\n    }\n  };\n  Animator.prototype.__changeFinalValue = function (finalProps, trackKeys) {\n    trackKeys = trackKeys || keys(finalProps);\n    for (var i = 0; i < trackKeys.length; i++) {\n      var propName = trackKeys[i];\n      var track = this._tracks[propName];\n      if (!track) {\n        continue;\n      }\n      var kfs = track.keyframes;\n      if (kfs.length > 1) {\n        var lastKf = kfs.pop();\n        track.addKeyframe(lastKf.time, finalProps[propName]);\n        track.prepare(this._maxTime, track.getAdditiveTrack());\n      }\n    }\n  };\n  return Animator;\n}();\nexport default Animator;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}