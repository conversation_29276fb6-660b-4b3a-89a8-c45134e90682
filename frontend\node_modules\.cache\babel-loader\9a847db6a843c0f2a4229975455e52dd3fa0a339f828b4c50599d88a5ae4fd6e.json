{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Gradient from './Gradient.js';\nvar LinearGradient = function (_super) {\n  __extends(LinearGradient, _super);\n  function LinearGradient(x, y, x2, y2, colorStops, globalCoord) {\n    var _this = _super.call(this, colorStops) || this;\n    _this.x = x == null ? 0 : x;\n    _this.y = y == null ? 0 : y;\n    _this.x2 = x2 == null ? 1 : x2;\n    _this.y2 = y2 == null ? 0 : y2;\n    _this.type = 'linear';\n    _this.global = globalCoord || false;\n    return _this;\n  }\n  return LinearGradient;\n}(Gradient);\nexport default LinearGradient;\n;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}