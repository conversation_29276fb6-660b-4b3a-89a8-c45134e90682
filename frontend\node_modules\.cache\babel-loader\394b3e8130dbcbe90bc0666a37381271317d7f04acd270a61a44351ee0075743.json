{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * @file Visual solution, for consistent option specification.\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapping from './VisualMapping.js';\nimport { getItemVisualFromData, setItemVisualFromData } from './helper.js';\nvar each = zrUtil.each;\nfunction hasKeys(obj) {\n  if (obj) {\n    for (var name_1 in obj) {\n      if (obj.hasOwnProperty(name_1)) {\n        return true;\n      }\n    }\n  }\n}\nexport function createVisualMappings(option, stateList, supplementVisualOption) {\n  var visualMappings = {};\n  each(stateList, function (state) {\n    var mappings = visualMappings[state] = createMappings();\n    each(option[state], function (visualData, visualType) {\n      if (!VisualMapping.isValidType(visualType)) {\n        return;\n      }\n      var mappingOption = {\n        type: visualType,\n        visual: visualData\n      };\n      supplementVisualOption && supplementVisualOption(mappingOption, state);\n      mappings[visualType] = new VisualMapping(mappingOption);\n      // Prepare a alpha for opacity, for some case that opacity\n      // is not supported, such as rendering using gradient color.\n      if (visualType === 'opacity') {\n        mappingOption = zrUtil.clone(mappingOption);\n        mappingOption.type = 'colorAlpha';\n        mappings.__hidden.__alphaForOpacity = new VisualMapping(mappingOption);\n      }\n    });\n  });\n  return visualMappings;\n  function createMappings() {\n    var Creater = function () {};\n    // Make sure hidden fields will not be visited by\n    // object iteration (with hasOwnProperty checking).\n    Creater.prototype.__hidden = Creater.prototype;\n    var obj = new Creater();\n    return obj;\n  }\n}\nexport function replaceVisualOption(thisOption, newOption, keys) {\n  // Visual attributes merge is not supported, otherwise it\n  // brings overcomplicated merge logic. See #2853. So if\n  // newOption has anyone of these keys, all of these keys\n  // will be reset. Otherwise, all keys remain.\n  var has;\n  zrUtil.each(keys, function (key) {\n    if (newOption.hasOwnProperty(key) && hasKeys(newOption[key])) {\n      has = true;\n    }\n  });\n  has && zrUtil.each(keys, function (key) {\n    if (newOption.hasOwnProperty(key) && hasKeys(newOption[key])) {\n      thisOption[key] = zrUtil.clone(newOption[key]);\n    } else {\n      delete thisOption[key];\n    }\n  });\n}\n/**\r\n * @param stateList\r\n * @param visualMappings\r\n * @param list\r\n * @param getValueState param: valueOrIndex, return: state.\r\n * @param scope Scope for getValueState\r\n * @param dimension Concrete dimension, if used.\r\n */\n// ???! handle brush?\nexport function applyVisual(stateList, visualMappings, data, getValueState, scope, dimension) {\n  var visualTypesMap = {};\n  zrUtil.each(stateList, function (state) {\n    var visualTypes = VisualMapping.prepareVisualTypes(visualMappings[state]);\n    visualTypesMap[state] = visualTypes;\n  });\n  var dataIndex;\n  function getVisual(key) {\n    return getItemVisualFromData(data, dataIndex, key);\n  }\n  function setVisual(key, value) {\n    setItemVisualFromData(data, dataIndex, key, value);\n  }\n  if (dimension == null) {\n    data.each(eachItem);\n  } else {\n    data.each([dimension], eachItem);\n  }\n  function eachItem(valueOrIndex, index) {\n    dataIndex = dimension == null ? valueOrIndex // First argument is index\n    : index;\n    var rawDataItem = data.getRawDataItem(dataIndex);\n    // Consider performance\n    // @ts-ignore\n    if (rawDataItem && rawDataItem.visualMap === false) {\n      return;\n    }\n    var valueState = getValueState.call(scope, valueOrIndex);\n    var mappings = visualMappings[valueState];\n    var visualTypes = visualTypesMap[valueState];\n    for (var i = 0, len = visualTypes.length; i < len; i++) {\n      var type = visualTypes[i];\n      mappings[type] && mappings[type].applyVisual(valueOrIndex, getVisual, setVisual);\n    }\n  }\n}\n/**\r\n * @param data\r\n * @param stateList\r\n * @param visualMappings <state, Object.<visualType, module:echarts/visual/VisualMapping>>\r\n * @param getValueState param: valueOrIndex, return: state.\r\n * @param dim dimension or dimension index.\r\n */\nexport function incrementalApplyVisual(stateList, visualMappings, getValueState, dim) {\n  var visualTypesMap = {};\n  zrUtil.each(stateList, function (state) {\n    var visualTypes = VisualMapping.prepareVisualTypes(visualMappings[state]);\n    visualTypesMap[state] = visualTypes;\n  });\n  return {\n    progress: function progress(params, data) {\n      var dimIndex;\n      if (dim != null) {\n        dimIndex = data.getDimensionIndex(dim);\n      }\n      function getVisual(key) {\n        return getItemVisualFromData(data, dataIndex, key);\n      }\n      function setVisual(key, value) {\n        setItemVisualFromData(data, dataIndex, key, value);\n      }\n      var dataIndex;\n      var store = data.getStore();\n      while ((dataIndex = params.next()) != null) {\n        var rawDataItem = data.getRawDataItem(dataIndex);\n        // Consider performance\n        // @ts-ignore\n        if (rawDataItem && rawDataItem.visualMap === false) {\n          continue;\n        }\n        var value = dim != null ? store.get(dimIndex, dataIndex) : dataIndex;\n        var valueState = getValueState(value);\n        var mappings = visualMappings[valueState];\n        var visualTypes = visualTypesMap[valueState];\n        for (var i = 0, len = visualTypes.length; i < len; i++) {\n          var type = visualTypes[i];\n          mappings[type] && mappings[type].applyVisual(value, getVisual, setVisual);\n        }\n      }\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}