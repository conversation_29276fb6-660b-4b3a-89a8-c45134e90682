{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { getTooltipMarker, encodeHTML, makeValueReadable, convertToColorString } from '../../util/format.js';\nimport { isString, each, hasOwn, isArray, map, assert, extend } from 'zrender/lib/core/util.js';\nimport { SortOrderComparator } from '../../data/helper/dataValueHelper.js';\nimport { getRandomIdBase } from '../../util/number.js';\nvar TOOLTIP_LINE_HEIGHT_CSS = 'line-height:1';\nfunction getTooltipLineHeight(textStyle) {\n  var lineHeight = textStyle.lineHeight;\n  if (lineHeight == null) {\n    return TOOLTIP_LINE_HEIGHT_CSS;\n  } else {\n    return \"line-height:\" + encodeHTML(lineHeight + '') + \"px\";\n  }\n}\n// TODO: more textStyle option\nfunction getTooltipTextStyle(textStyle, renderMode) {\n  var nameFontColor = textStyle.color || '#6e7079';\n  var nameFontSize = textStyle.fontSize || 12;\n  var nameFontWeight = textStyle.fontWeight || '400';\n  var valueFontColor = textStyle.color || '#464646';\n  var valueFontSize = textStyle.fontSize || 14;\n  var valueFontWeight = textStyle.fontWeight || '900';\n  if (renderMode === 'html') {\n    // `textStyle` is probably from user input, should be encoded to reduce security risk.\n    return {\n      // eslint-disable-next-line max-len\n      nameStyle: \"font-size:\" + encodeHTML(nameFontSize + '') + \"px;color:\" + encodeHTML(nameFontColor) + \";font-weight:\" + encodeHTML(nameFontWeight + ''),\n      // eslint-disable-next-line max-len\n      valueStyle: \"font-size:\" + encodeHTML(valueFontSize + '') + \"px;color:\" + encodeHTML(valueFontColor) + \";font-weight:\" + encodeHTML(valueFontWeight + '')\n    };\n  } else {\n    return {\n      nameStyle: {\n        fontSize: nameFontSize,\n        fill: nameFontColor,\n        fontWeight: nameFontWeight\n      },\n      valueStyle: {\n        fontSize: valueFontSize,\n        fill: valueFontColor,\n        fontWeight: valueFontWeight\n      }\n    };\n  }\n}\n// See `TooltipMarkupLayoutIntent['innerGapLevel']`.\n// (value from UI design)\nvar HTML_GAPS = [0, 10, 20, 30];\nvar RICH_TEXT_GAPS = ['', '\\n', '\\n\\n', '\\n\\n\\n'];\n// eslint-disable-next-line max-len\nexport function createTooltipMarkup(type, option) {\n  option.type = type;\n  return option;\n}\nfunction isSectionFragment(frag) {\n  return frag.type === 'section';\n}\nfunction getBuilder(frag) {\n  return isSectionFragment(frag) ? buildSection : buildNameValue;\n}\nfunction getBlockGapLevel(frag) {\n  if (isSectionFragment(frag)) {\n    var gapLevel_1 = 0;\n    var subBlockLen = frag.blocks.length;\n    var hasInnerGap_1 = subBlockLen > 1 || subBlockLen > 0 && !frag.noHeader;\n    each(frag.blocks, function (subBlock) {\n      var subGapLevel = getBlockGapLevel(subBlock);\n      // If the some of the sub-blocks have some gaps (like 10px) inside, this block\n      // should use a larger gap (like 20px) to distinguish those sub-blocks.\n      if (subGapLevel >= gapLevel_1) {\n        gapLevel_1 = subGapLevel + +(hasInnerGap_1 && (\n        // 0 always can not be readable gap level.\n        !subGapLevel\n        // If no header, always keep the sub gap level. Otherwise\n        // look weird in case `multipleSeries`.\n        || isSectionFragment(subBlock) && !subBlock.noHeader));\n      }\n    });\n    return gapLevel_1;\n  }\n  return 0;\n}\nfunction buildSection(ctx, fragment, topMarginForOuterGap, toolTipTextStyle) {\n  var noHeader = fragment.noHeader;\n  var gaps = getGap(getBlockGapLevel(fragment));\n  var subMarkupTextList = [];\n  var subBlocks = fragment.blocks || [];\n  assert(!subBlocks || isArray(subBlocks));\n  subBlocks = subBlocks || [];\n  var orderMode = ctx.orderMode;\n  if (fragment.sortBlocks && orderMode) {\n    subBlocks = subBlocks.slice();\n    var orderMap = {\n      valueAsc: 'asc',\n      valueDesc: 'desc'\n    };\n    if (hasOwn(orderMap, orderMode)) {\n      var comparator_1 = new SortOrderComparator(orderMap[orderMode], null);\n      subBlocks.sort(function (a, b) {\n        return comparator_1.evaluate(a.sortParam, b.sortParam);\n      });\n    }\n    // FIXME 'seriesDesc' necessary?\n    else if (orderMode === 'seriesDesc') {\n      subBlocks.reverse();\n    }\n  }\n  each(subBlocks, function (subBlock, idx) {\n    var valueFormatter = fragment.valueFormatter;\n    var subMarkupText = getBuilder(subBlock)(\n    // Inherit valueFormatter\n    valueFormatter ? extend(extend({}, ctx), {\n      valueFormatter: valueFormatter\n    }) : ctx, subBlock, idx > 0 ? gaps.html : 0, toolTipTextStyle);\n    subMarkupText != null && subMarkupTextList.push(subMarkupText);\n  });\n  var subMarkupText = ctx.renderMode === 'richText' ? subMarkupTextList.join(gaps.richText) : wrapBlockHTML(toolTipTextStyle, subMarkupTextList.join(''), noHeader ? topMarginForOuterGap : gaps.html);\n  if (noHeader) {\n    return subMarkupText;\n  }\n  var displayableHeader = makeValueReadable(fragment.header, 'ordinal', ctx.useUTC);\n  var nameStyle = getTooltipTextStyle(toolTipTextStyle, ctx.renderMode).nameStyle;\n  var tooltipLineHeight = getTooltipLineHeight(toolTipTextStyle);\n  if (ctx.renderMode === 'richText') {\n    return wrapInlineNameRichText(ctx, displayableHeader, nameStyle) + gaps.richText + subMarkupText;\n  } else {\n    return wrapBlockHTML(toolTipTextStyle, \"<div style=\\\"\" + nameStyle + \";\" + tooltipLineHeight + \";\\\">\" + encodeHTML(displayableHeader) + '</div>' + subMarkupText, topMarginForOuterGap);\n  }\n}\nfunction buildNameValue(ctx, fragment, topMarginForOuterGap, toolTipTextStyle) {\n  var renderMode = ctx.renderMode;\n  var noName = fragment.noName;\n  var noValue = fragment.noValue;\n  var noMarker = !fragment.markerType;\n  var name = fragment.name;\n  var useUTC = ctx.useUTC;\n  var valueFormatter = fragment.valueFormatter || ctx.valueFormatter || function (value) {\n    value = isArray(value) ? value : [value];\n    return map(value, function (val, idx) {\n      return makeValueReadable(val, isArray(valueTypeOption) ? valueTypeOption[idx] : valueTypeOption, useUTC);\n    });\n  };\n  if (noName && noValue) {\n    return;\n  }\n  var markerStr = noMarker ? '' : ctx.markupStyleCreator.makeTooltipMarker(fragment.markerType, fragment.markerColor || '#333', renderMode);\n  var readableName = noName ? '' : makeValueReadable(name, 'ordinal', useUTC);\n  var valueTypeOption = fragment.valueType;\n  var readableValueList = noValue ? [] : valueFormatter(fragment.value, fragment.dataIndex);\n  var valueAlignRight = !noMarker || !noName;\n  // It little weird if only value next to marker but far from marker.\n  var valueCloseToMarker = !noMarker && noName;\n  var _a = getTooltipTextStyle(toolTipTextStyle, renderMode),\n    nameStyle = _a.nameStyle,\n    valueStyle = _a.valueStyle;\n  return renderMode === 'richText' ? (noMarker ? '' : markerStr) + (noName ? '' : wrapInlineNameRichText(ctx, readableName, nameStyle))\n  // Value has commas inside, so use ' ' as delimiter for multiple values.\n  + (noValue ? '' : wrapInlineValueRichText(ctx, readableValueList, valueAlignRight, valueCloseToMarker, valueStyle)) : wrapBlockHTML(toolTipTextStyle, (noMarker ? '' : markerStr) + (noName ? '' : wrapInlineNameHTML(readableName, !noMarker, nameStyle)) + (noValue ? '' : wrapInlineValueHTML(readableValueList, valueAlignRight, valueCloseToMarker, valueStyle)), topMarginForOuterGap);\n}\n/**\r\n * @return markupText. null/undefined means no content.\r\n */\nexport function buildTooltipMarkup(fragment, markupStyleCreator, renderMode, orderMode, useUTC, toolTipTextStyle) {\n  if (!fragment) {\n    return;\n  }\n  var builder = getBuilder(fragment);\n  var ctx = {\n    useUTC: useUTC,\n    renderMode: renderMode,\n    orderMode: orderMode,\n    markupStyleCreator: markupStyleCreator,\n    valueFormatter: fragment.valueFormatter\n  };\n  return builder(ctx, fragment, 0, toolTipTextStyle);\n}\nfunction getGap(gapLevel) {\n  return {\n    html: HTML_GAPS[gapLevel],\n    richText: RICH_TEXT_GAPS[gapLevel]\n  };\n}\nfunction wrapBlockHTML(textStyle, encodedContent, topGap) {\n  var clearfix = '<div style=\"clear:both\"></div>';\n  var marginCSS = \"margin: \" + topGap + \"px 0 0\";\n  var tooltipLineHeight = getTooltipLineHeight(textStyle);\n  return \"<div style=\\\"\" + marginCSS + \";\" + tooltipLineHeight + \";\\\">\" + encodedContent + clearfix + '</div>';\n}\nfunction wrapInlineNameHTML(name, leftHasMarker, style) {\n  var marginCss = leftHasMarker ? 'margin-left:2px' : '';\n  return \"<span style=\\\"\" + style + \";\" + marginCss + \"\\\">\" + encodeHTML(name) + '</span>';\n}\nfunction wrapInlineValueHTML(valueList, alignRight, valueCloseToMarker, style) {\n  // Do not too close to marker, considering there are multiple values separated by spaces.\n  var paddingStr = valueCloseToMarker ? '10px' : '20px';\n  var alignCSS = alignRight ? \"float:right;margin-left:\" + paddingStr : '';\n  valueList = isArray(valueList) ? valueList : [valueList];\n  return \"<span style=\\\"\" + alignCSS + \";\" + style + \"\\\">\"\n  // Value has commas inside, so use '  ' as delimiter for multiple values.\n  + map(valueList, function (value) {\n    return encodeHTML(value);\n  }).join('&nbsp;&nbsp;') + '</span>';\n}\nfunction wrapInlineNameRichText(ctx, name, style) {\n  return ctx.markupStyleCreator.wrapRichTextStyle(name, style);\n}\nfunction wrapInlineValueRichText(ctx, values, alignRight, valueCloseToMarker, style) {\n  var styles = [style];\n  var paddingLeft = valueCloseToMarker ? 10 : 20;\n  alignRight && styles.push({\n    padding: [0, 0, 0, paddingLeft],\n    align: 'right'\n  });\n  // Value has commas inside, so use '  ' as delimiter for multiple values.\n  return ctx.markupStyleCreator.wrapRichTextStyle(isArray(values) ? values.join('  ') : values, styles);\n}\nexport function retrieveVisualColorForTooltipMarker(series, dataIndex) {\n  var style = series.getData().getItemVisual(dataIndex, 'style');\n  var color = style[series.visualDrawType];\n  return convertToColorString(color);\n}\nexport function getPaddingFromTooltipModel(model, renderMode) {\n  var padding = model.get('padding');\n  return padding != null ? padding\n  // We give slightly different to look pretty.\n  : renderMode === 'richText' ? [8, 10] : 10;\n}\n/**\r\n * The major feature is generate styles for `renderMode: 'richText'`.\r\n * But it also serves `renderMode: 'html'` to provide\r\n * \"renderMode-independent\" API.\r\n */\nvar TooltipMarkupStyleCreator = /** @class */function () {\n  function TooltipMarkupStyleCreator() {\n    this.richTextStyles = {};\n    // Notice that \"generate a style name\" usually happens repeatedly when mouse is moving and\n    // a tooltip is displayed. So we put the `_nextStyleNameId` as a member of each creator\n    // rather than static shared by all creators (which will cause it increase to fast).\n    this._nextStyleNameId = getRandomIdBase();\n  }\n  TooltipMarkupStyleCreator.prototype._generateStyleName = function () {\n    return '__EC_aUTo_' + this._nextStyleNameId++;\n  };\n  TooltipMarkupStyleCreator.prototype.makeTooltipMarker = function (markerType, colorStr, renderMode) {\n    var markerId = renderMode === 'richText' ? this._generateStyleName() : null;\n    var marker = getTooltipMarker({\n      color: colorStr,\n      type: markerType,\n      renderMode: renderMode,\n      markerId: markerId\n    });\n    if (isString(marker)) {\n      return marker;\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(markerId);\n      }\n      this.richTextStyles[markerId] = marker.style;\n      return marker.content;\n    }\n  };\n  /**\r\n   * @usage\r\n   * ```ts\r\n   * const styledText = markupStyleCreator.wrapRichTextStyle([\r\n   *     // The styles will be auto merged.\r\n   *     {\r\n   *         fontSize: 12,\r\n   *         color: 'blue'\r\n   *     },\r\n   *     {\r\n   *         padding: 20\r\n   *     }\r\n   * ]);\r\n   * ```\r\n   */\n  TooltipMarkupStyleCreator.prototype.wrapRichTextStyle = function (text, styles) {\n    var finalStl = {};\n    if (isArray(styles)) {\n      each(styles, function (stl) {\n        return extend(finalStl, stl);\n      });\n    } else {\n      extend(finalStl, styles);\n    }\n    var styleName = this._generateStyleName();\n    this.richTextStyles[styleName] = finalStl;\n    return \"{\" + styleName + \"|\" + text + \"}\";\n  };\n  return TooltipMarkupStyleCreator;\n}();\nexport { TooltipMarkupStyleCreator };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}