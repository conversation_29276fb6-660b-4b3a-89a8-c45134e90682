"""
简化的实时系统测试
"""
import asyncio
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockWebSocket:
    """模拟WebSocket连接"""
    
    def __init__(self, client_id: str):
        self.client_id = client_id
        self.messages = []
        self.connected = True
    
    async def accept(self):
        pass
    
    async def send_text(self, data: str):
        if self.connected:
            message = json.loads(data)
            self.messages.append(message)
            logger.info(f"[{self.client_id}] 收到: {message.get('type', 'unknown')}")

class SimpleConnectionManager:
    """简化的连接管理器"""
    
    def __init__(self):
        self.connections = []
        self.subscriptions = {}
    
    async def connect(self, websocket, client_id):
        await websocket.accept()
        self.connections.append(websocket)
        self.subscriptions[websocket] = set()
        logger.info(f"客户端连接: {client_id}")
    
    def disconnect(self, websocket):
        if websocket in self.connections:
            self.connections.remove(websocket)
            if websocket in self.subscriptions:
                del self.subscriptions[websocket]
    
    async def subscribe(self, websocket, data_types):
        if websocket in self.subscriptions:
            self.subscriptions[websocket].update(data_types)
            await websocket.send_text(json.dumps({
                'type': 'subscription',
                'status': 'success',
                'subscriptions': list(self.subscriptions[websocket])
            }))
    
    async def broadcast(self, message, message_type=None):
        message['timestamp'] = datetime.now().isoformat()
        
        for ws in self.connections[:]:  # 复制列表避免修改时出错
            try:
                subscriptions = self.subscriptions.get(ws, set())
                if not message_type or message_type in subscriptions or len(subscriptions) == 0:
                    await ws.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"广播失败: {e}")
                self.disconnect(ws)

class SimpleScheduler:
    """简化的任务调度器"""
    
    def __init__(self):
        self.tasks = {}
        self.running = False
    
    def add_task(self, name, func, interval):
        self.tasks[name] = {
            'func': func,
            'interval': interval,
            'last_run': 0
        }
        logger.info(f"添加任务: {name}, 间隔: {interval}秒")
    
    async def start(self):
        self.running = True
        logger.info("启动调度器")
        
        while self.running:
            current_time = asyncio.get_event_loop().time()
            
            for name, task in self.tasks.items():
                if current_time - task['last_run'] >= task['interval']:
                    try:
                        if asyncio.iscoroutinefunction(task['func']):
                            await task['func']()
                        else:
                            task['func']()
                        task['last_run'] = current_time
                        logger.debug(f"执行任务: {name}")
                    except Exception as e:
                        logger.error(f"任务执行失败 {name}: {e}")
            
            await asyncio.sleep(1)
    
    def stop(self):
        self.running = False
        logger.info("停止调度器")

async def test_basic_websocket():
    """测试基础WebSocket功能"""
    print("=== 测试基础WebSocket功能 ===")
    
    manager = SimpleConnectionManager()
    
    # 创建模拟客户端
    ws1 = MockWebSocket("client1")
    ws2 = MockWebSocket("client2")
    
    # 连接
    await manager.connect(ws1, "client1")
    await manager.connect(ws2, "client2")
    
    # 订阅
    await manager.subscribe(ws1, ["price_data"])
    await manager.subscribe(ws2, ["market_data"])
    
    # 广播消息
    await manager.broadcast({
        'type': 'price_update',
        'ts_code': '000001.SZ',
        'price': 12.45
    }, 'price_data')
    
    await manager.broadcast({
        'type': 'market_update',
        'data': {'status': 'open'}
    }, 'market_data')
    
    # 检查结果
    print(f"Client1 收到消息: {len(ws1.messages)}")
    print(f"Client2 收到消息: {len(ws2.messages)}")
    
    return len(ws1.messages) > 0 and len(ws2.messages) > 0

async def test_scheduler():
    """测试任务调度器"""
    print("\n=== 测试任务调度器 ===")
    
    scheduler = SimpleScheduler()
    call_count = 0
    
    def test_task():
        nonlocal call_count
        call_count += 1
        logger.info(f"任务执行第 {call_count} 次")
    
    scheduler.add_task("test_task", test_task, 2)
    
    # 启动调度器
    task = asyncio.create_task(scheduler.start())
    
    # 运行5秒
    await asyncio.sleep(5)
    scheduler.stop()
    
    try:
        await asyncio.wait_for(task, timeout=1.0)
    except asyncio.TimeoutError:
        pass
    
    print(f"任务执行次数: {call_count}")
    return call_count > 0

async def test_realtime_data_simulation():
    """测试实时数据模拟"""
    print("\n=== 测试实时数据模拟 ===")
    
    manager = SimpleConnectionManager()
    scheduler = SimpleScheduler()
    
    # 创建客户端
    ws = MockWebSocket("monitor_client")
    await manager.connect(ws, "monitor_client")
    await manager.subscribe(ws, ["price_data", "signals", "alerts"])
    
    # 模拟数据更新任务
    async def update_prices():
        import random
        stocks = ['000001.SZ', '000002.SZ', '600000.SH']
        
        for stock in stocks:
            price = 10 + random.uniform(-1, 1)
            await manager.broadcast({
                'type': 'price_update',
                'ts_code': stock,
                'price': round(price, 2),
                'change_pct': round(random.uniform(-3, 3), 2)
            }, 'price_data')
    
    async def generate_signals():
        import random
        signals = ['BUY', 'SELL', 'HOLD']
        
        await manager.broadcast({
            'type': 'signal_update',
            'ts_code': '000001.SZ',
            'signal': random.choice(signals),
            'indicator': 'MACD'
        }, 'signals')
    
    async def check_risks():
        import random
        if random.random() > 0.7:  # 30%概率产生告警
            await manager.broadcast({
                'type': 'risk_alert',
                'level': 'HIGH',
                'message': '持仓比例过高',
                'ts_code': '000001.SZ'
            }, 'alerts')
    
    # 添加任务
    scheduler.add_task("update_prices", update_prices, 2)
    scheduler.add_task("generate_signals", generate_signals, 3)
    scheduler.add_task("check_risks", check_risks, 5)
    
    # 运行系统
    scheduler_task = asyncio.create_task(scheduler.start())
    
    await asyncio.sleep(10)  # 运行10秒
    
    scheduler.stop()
    try:
        await asyncio.wait_for(scheduler_task, timeout=1.0)
    except asyncio.TimeoutError:
        pass
    
    # 统计结果
    message_types = {}
    for msg in ws.messages:
        msg_type = msg.get('type', 'unknown')
        message_types[msg_type] = message_types.get(msg_type, 0) + 1
    
    print(f"总消息数: {len(ws.messages)}")
    print(f"消息类型分布: {message_types}")
    
    return len(ws.messages) > 5

async def test_performance():
    """测试性能"""
    print("\n=== 测试性能 ===")
    
    manager = SimpleConnectionManager()
    
    # 创建多个客户端
    clients = []
    for i in range(10):
        ws = MockWebSocket(f"client_{i}")
        await manager.connect(ws, f"client_{i}")
        await manager.subscribe(ws, ["price_data"])
        clients.append(ws)
    
    # 大量广播测试
    start_time = asyncio.get_event_loop().time()
    
    for i in range(100):
        await manager.broadcast({
            'type': 'price_update',
            'ts_code': f'00000{i%5}.SZ',
            'price': 10 + i * 0.01
        }, 'price_data')
    
    end_time = asyncio.get_event_loop().time()
    
    # 统计结果
    total_messages = sum(len(ws.messages) for ws in clients)
    elapsed_time = end_time - start_time
    
    print(f"客户端数量: {len(clients)}")
    print(f"广播消息数: 100")
    print(f"总接收消息数: {total_messages}")
    print(f"耗时: {elapsed_time:.3f} 秒")
    print(f"消息处理速度: {total_messages/elapsed_time:.1f} 消息/秒")
    
    return total_messages == 1000  # 10个客户端 * 100条消息

async def main():
    """主测试函数"""
    print("🚀 开始实时系统功能测试")
    print("=" * 60)
    
    tests = [
        ("基础WebSocket", test_basic_websocket),
        ("任务调度器", test_scheduler),
        ("实时数据模拟", test_realtime_data_simulation),
        ("性能测试", test_performance)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
            print(f"✅ {test_name} 测试完成")
        except Exception as e:
            print(f"❌ {test_name} 测试失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count >= 3:
        print("\n🎉 实时系统功能测试通过！")
        print("💡 核心功能:")
        print("- WebSocket实时通信 ✅")
        print("- 定时任务调度 ✅")
        print("- 数据广播机制 ✅")
        print("- 多客户端支持 ✅")
    else:
        print("\n⚠️  部分测试失败，请检查实现")

if __name__ == "__main__":
    asyncio.run(main())
