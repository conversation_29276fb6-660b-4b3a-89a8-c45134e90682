{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from '../graphic/Path.js';\nimport PathProxy from '../core/PathProxy.js';\nimport transformPath from './transformPath.js';\nimport { extend } from '../core/util.js';\nvar mathSqrt = Math.sqrt;\nvar mathSin = Math.sin;\nvar mathCos = Math.cos;\nvar PI = Math.PI;\nfunction vMag(v) {\n  return Math.sqrt(v[0] * v[0] + v[1] * v[1]);\n}\n;\nfunction vRatio(u, v) {\n  return (u[0] * v[0] + u[1] * v[1]) / (vMag(u) * vMag(v));\n}\n;\nfunction vAngle(u, v) {\n  return (u[0] * v[1] < u[1] * v[0] ? -1 : 1) * Math.acos(vRatio(u, v));\n}\n;\nfunction processArc(x1, y1, x2, y2, fa, fs, rx, ry, psiDeg, cmd, path) {\n  var psi = psiDeg * (PI / 180.0);\n  var xp = mathCos(psi) * (x1 - x2) / 2.0 + mathSin(psi) * (y1 - y2) / 2.0;\n  var yp = -1 * mathSin(psi) * (x1 - x2) / 2.0 + mathCos(psi) * (y1 - y2) / 2.0;\n  var lambda = xp * xp / (rx * rx) + yp * yp / (ry * ry);\n  if (lambda > 1) {\n    rx *= mathSqrt(lambda);\n    ry *= mathSqrt(lambda);\n  }\n  var f = (fa === fs ? -1 : 1) * mathSqrt((rx * rx * (ry * ry) - rx * rx * (yp * yp) - ry * ry * (xp * xp)) / (rx * rx * (yp * yp) + ry * ry * (xp * xp))) || 0;\n  var cxp = f * rx * yp / ry;\n  var cyp = f * -ry * xp / rx;\n  var cx = (x1 + x2) / 2.0 + mathCos(psi) * cxp - mathSin(psi) * cyp;\n  var cy = (y1 + y2) / 2.0 + mathSin(psi) * cxp + mathCos(psi) * cyp;\n  var theta = vAngle([1, 0], [(xp - cxp) / rx, (yp - cyp) / ry]);\n  var u = [(xp - cxp) / rx, (yp - cyp) / ry];\n  var v = [(-1 * xp - cxp) / rx, (-1 * yp - cyp) / ry];\n  var dTheta = vAngle(u, v);\n  if (vRatio(u, v) <= -1) {\n    dTheta = PI;\n  }\n  if (vRatio(u, v) >= 1) {\n    dTheta = 0;\n  }\n  if (dTheta < 0) {\n    var n = Math.round(dTheta / PI * 1e6) / 1e6;\n    dTheta = PI * 2 + n % 2 * PI;\n  }\n  path.addData(cmd, cx, cy, rx, ry, theta, dTheta, psi, fs);\n}\nvar commandReg = /([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig;\nvar numberReg = /-?([0-9]*\\.)?[0-9]+([eE]-?[0-9]+)?/g;\nfunction createPathProxyFromString(data) {\n  var path = new PathProxy();\n  if (!data) {\n    return path;\n  }\n  var cpx = 0;\n  var cpy = 0;\n  var subpathX = cpx;\n  var subpathY = cpy;\n  var prevCmd;\n  var CMD = PathProxy.CMD;\n  var cmdList = data.match(commandReg);\n  if (!cmdList) {\n    return path;\n  }\n  for (var l = 0; l < cmdList.length; l++) {\n    var cmdText = cmdList[l];\n    var cmdStr = cmdText.charAt(0);\n    var cmd = void 0;\n    var p = cmdText.match(numberReg) || [];\n    var pLen = p.length;\n    for (var i = 0; i < pLen; i++) {\n      p[i] = parseFloat(p[i]);\n    }\n    var off = 0;\n    while (off < pLen) {\n      var ctlPtx = void 0;\n      var ctlPty = void 0;\n      var rx = void 0;\n      var ry = void 0;\n      var psi = void 0;\n      var fa = void 0;\n      var fs = void 0;\n      var x1 = cpx;\n      var y1 = cpy;\n      var len = void 0;\n      var pathData = void 0;\n      switch (cmdStr) {\n        case 'l':\n          cpx += p[off++];\n          cpy += p[off++];\n          cmd = CMD.L;\n          path.addData(cmd, cpx, cpy);\n          break;\n        case 'L':\n          cpx = p[off++];\n          cpy = p[off++];\n          cmd = CMD.L;\n          path.addData(cmd, cpx, cpy);\n          break;\n        case 'm':\n          cpx += p[off++];\n          cpy += p[off++];\n          cmd = CMD.M;\n          path.addData(cmd, cpx, cpy);\n          subpathX = cpx;\n          subpathY = cpy;\n          cmdStr = 'l';\n          break;\n        case 'M':\n          cpx = p[off++];\n          cpy = p[off++];\n          cmd = CMD.M;\n          path.addData(cmd, cpx, cpy);\n          subpathX = cpx;\n          subpathY = cpy;\n          cmdStr = 'L';\n          break;\n        case 'h':\n          cpx += p[off++];\n          cmd = CMD.L;\n          path.addData(cmd, cpx, cpy);\n          break;\n        case 'H':\n          cpx = p[off++];\n          cmd = CMD.L;\n          path.addData(cmd, cpx, cpy);\n          break;\n        case 'v':\n          cpy += p[off++];\n          cmd = CMD.L;\n          path.addData(cmd, cpx, cpy);\n          break;\n        case 'V':\n          cpy = p[off++];\n          cmd = CMD.L;\n          path.addData(cmd, cpx, cpy);\n          break;\n        case 'C':\n          cmd = CMD.C;\n          path.addData(cmd, p[off++], p[off++], p[off++], p[off++], p[off++], p[off++]);\n          cpx = p[off - 2];\n          cpy = p[off - 1];\n          break;\n        case 'c':\n          cmd = CMD.C;\n          path.addData(cmd, p[off++] + cpx, p[off++] + cpy, p[off++] + cpx, p[off++] + cpy, p[off++] + cpx, p[off++] + cpy);\n          cpx += p[off - 2];\n          cpy += p[off - 1];\n          break;\n        case 'S':\n          ctlPtx = cpx;\n          ctlPty = cpy;\n          len = path.len();\n          pathData = path.data;\n          if (prevCmd === CMD.C) {\n            ctlPtx += cpx - pathData[len - 4];\n            ctlPty += cpy - pathData[len - 3];\n          }\n          cmd = CMD.C;\n          x1 = p[off++];\n          y1 = p[off++];\n          cpx = p[off++];\n          cpy = p[off++];\n          path.addData(cmd, ctlPtx, ctlPty, x1, y1, cpx, cpy);\n          break;\n        case 's':\n          ctlPtx = cpx;\n          ctlPty = cpy;\n          len = path.len();\n          pathData = path.data;\n          if (prevCmd === CMD.C) {\n            ctlPtx += cpx - pathData[len - 4];\n            ctlPty += cpy - pathData[len - 3];\n          }\n          cmd = CMD.C;\n          x1 = cpx + p[off++];\n          y1 = cpy + p[off++];\n          cpx += p[off++];\n          cpy += p[off++];\n          path.addData(cmd, ctlPtx, ctlPty, x1, y1, cpx, cpy);\n          break;\n        case 'Q':\n          x1 = p[off++];\n          y1 = p[off++];\n          cpx = p[off++];\n          cpy = p[off++];\n          cmd = CMD.Q;\n          path.addData(cmd, x1, y1, cpx, cpy);\n          break;\n        case 'q':\n          x1 = p[off++] + cpx;\n          y1 = p[off++] + cpy;\n          cpx += p[off++];\n          cpy += p[off++];\n          cmd = CMD.Q;\n          path.addData(cmd, x1, y1, cpx, cpy);\n          break;\n        case 'T':\n          ctlPtx = cpx;\n          ctlPty = cpy;\n          len = path.len();\n          pathData = path.data;\n          if (prevCmd === CMD.Q) {\n            ctlPtx += cpx - pathData[len - 4];\n            ctlPty += cpy - pathData[len - 3];\n          }\n          cpx = p[off++];\n          cpy = p[off++];\n          cmd = CMD.Q;\n          path.addData(cmd, ctlPtx, ctlPty, cpx, cpy);\n          break;\n        case 't':\n          ctlPtx = cpx;\n          ctlPty = cpy;\n          len = path.len();\n          pathData = path.data;\n          if (prevCmd === CMD.Q) {\n            ctlPtx += cpx - pathData[len - 4];\n            ctlPty += cpy - pathData[len - 3];\n          }\n          cpx += p[off++];\n          cpy += p[off++];\n          cmd = CMD.Q;\n          path.addData(cmd, ctlPtx, ctlPty, cpx, cpy);\n          break;\n        case 'A':\n          rx = p[off++];\n          ry = p[off++];\n          psi = p[off++];\n          fa = p[off++];\n          fs = p[off++];\n          x1 = cpx, y1 = cpy;\n          cpx = p[off++];\n          cpy = p[off++];\n          cmd = CMD.A;\n          processArc(x1, y1, cpx, cpy, fa, fs, rx, ry, psi, cmd, path);\n          break;\n        case 'a':\n          rx = p[off++];\n          ry = p[off++];\n          psi = p[off++];\n          fa = p[off++];\n          fs = p[off++];\n          x1 = cpx, y1 = cpy;\n          cpx += p[off++];\n          cpy += p[off++];\n          cmd = CMD.A;\n          processArc(x1, y1, cpx, cpy, fa, fs, rx, ry, psi, cmd, path);\n          break;\n      }\n    }\n    if (cmdStr === 'z' || cmdStr === 'Z') {\n      cmd = CMD.Z;\n      path.addData(cmd);\n      cpx = subpathX;\n      cpy = subpathY;\n    }\n    prevCmd = cmd;\n  }\n  path.toStatic();\n  return path;\n}\nvar SVGPath = function (_super) {\n  __extends(SVGPath, _super);\n  function SVGPath() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  SVGPath.prototype.applyTransform = function (m) {};\n  return SVGPath;\n}(Path);\nfunction isPathProxy(path) {\n  return path.setData != null;\n}\nfunction createPathOptions(str, opts) {\n  var pathProxy = createPathProxyFromString(str);\n  var innerOpts = extend({}, opts);\n  innerOpts.buildPath = function (path) {\n    if (isPathProxy(path)) {\n      path.setData(pathProxy.data);\n      var ctx = path.getContext();\n      if (ctx) {\n        path.rebuildPath(ctx, 1);\n      }\n    } else {\n      var ctx = path;\n      pathProxy.rebuildPath(ctx, 1);\n    }\n  };\n  innerOpts.applyTransform = function (m) {\n    transformPath(pathProxy, m);\n    this.dirtyShape();\n  };\n  return innerOpts;\n}\nexport function createFromString(str, opts) {\n  return new SVGPath(createPathOptions(str, opts));\n}\nexport function extendFromString(str, defaultOpts) {\n  var innerOpts = createPathOptions(str, defaultOpts);\n  var Sub = function (_super) {\n    __extends(Sub, _super);\n    function Sub(opts) {\n      var _this = _super.call(this, opts) || this;\n      _this.applyTransform = innerOpts.applyTransform;\n      _this.buildPath = innerOpts.buildPath;\n      return _this;\n    }\n    return Sub;\n  }(SVGPath);\n  return Sub;\n}\nexport function mergePath(pathEls, opts) {\n  var pathList = [];\n  var len = pathEls.length;\n  for (var i = 0; i < len; i++) {\n    var pathEl = pathEls[i];\n    pathList.push(pathEl.getUpdatedPathProxy(true));\n  }\n  var pathBundle = new Path(opts);\n  pathBundle.createPathProxy();\n  pathBundle.buildPath = function (path) {\n    if (isPathProxy(path)) {\n      path.appendPath(pathList);\n      var ctx = path.getContext();\n      if (ctx) {\n        path.rebuildPath(ctx, 1);\n      }\n    }\n  };\n  return pathBundle;\n}\nexport function clonePath(sourcePath, opts) {\n  opts = opts || {};\n  var path = new Path();\n  if (sourcePath.shape) {\n    path.setShape(sourcePath.shape);\n  }\n  path.setStyle(sourcePath.style);\n  if (opts.bakeTransform) {\n    transformPath(path.path, sourcePath.getComputedTransform());\n  } else {\n    if (opts.toLocal) {\n      path.setLocalTransform(sourcePath.getComputedTransform());\n    } else {\n      path.copyTransform(sourcePath);\n    }\n  }\n  path.buildPath = sourcePath.buildPath;\n  path.applyTransform = path.applyTransform;\n  path.z = sourcePath.z;\n  path.z2 = sourcePath.z2;\n  path.zlevel = sourcePath.zlevel;\n  return path;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}