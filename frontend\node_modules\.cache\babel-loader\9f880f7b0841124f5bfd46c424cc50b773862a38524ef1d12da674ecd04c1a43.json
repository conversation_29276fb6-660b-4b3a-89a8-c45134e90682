{"ast": null, "code": "import { min as v2Min, max as v2Max, scale as v2Scale, distance as v2Distance, add as v2Add, clone as v2Clone, sub as v2Sub } from '../../core/vector.js';\nexport default function smoothBezier(points, smooth, isLoop, constraint) {\n  var cps = [];\n  var v = [];\n  var v1 = [];\n  var v2 = [];\n  var prevPoint;\n  var nextPoint;\n  var min;\n  var max;\n  if (constraint) {\n    min = [Infinity, Infinity];\n    max = [-Infinity, -Infinity];\n    for (var i = 0, len = points.length; i < len; i++) {\n      v2Min(min, min, points[i]);\n      v2Max(max, max, points[i]);\n    }\n    v2Min(min, min, constraint[0]);\n    v2Max(max, max, constraint[1]);\n  }\n  for (var i = 0, len = points.length; i < len; i++) {\n    var point = points[i];\n    if (isLoop) {\n      prevPoint = points[i ? i - 1 : len - 1];\n      nextPoint = points[(i + 1) % len];\n    } else {\n      if (i === 0 || i === len - 1) {\n        cps.push(v2Clone(points[i]));\n        continue;\n      } else {\n        prevPoint = points[i - 1];\n        nextPoint = points[i + 1];\n      }\n    }\n    v2Sub(v, nextPoint, prevPoint);\n    v2Scale(v, v, smooth);\n    var d0 = v2Distance(point, prevPoint);\n    var d1 = v2Distance(point, nextPoint);\n    var sum = d0 + d1;\n    if (sum !== 0) {\n      d0 /= sum;\n      d1 /= sum;\n    }\n    v2Scale(v1, v, -d0);\n    v2Scale(v2, v, d1);\n    var cp0 = v2Add([], point, v1);\n    var cp1 = v2Add([], point, v2);\n    if (constraint) {\n      v2Max(cp0, cp0, min);\n      v2Min(cp0, cp0, max);\n      v2Max(cp1, cp1, min);\n      v2Min(cp1, cp1, max);\n    }\n    cps.push(cp0);\n    cps.push(cp1);\n  }\n  if (isLoop) {\n    cps.push(cps.shift());\n  }\n  return cps;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}