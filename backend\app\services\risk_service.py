"""
风险管理服务
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
from sqlalchemy.orm import Session

from risk.risk_manager import get_risk_manager, PositionInfo, RiskAlert
from app.models.database import get_db
from app.models.stock import Stock, Alert
from data.tushare_client import get_tushare_client

logger = logging.getLogger(__name__)

class RiskService:
    """风险管理服务类"""
    
    def __init__(self):
        self.risk_manager = get_risk_manager()
        self.tushare_client = get_tushare_client()
    
    def create_sample_portfolio(self, ts_codes: List[str], total_assets: float = 1000000) -> List[PositionInfo]:
        """创建示例投资组合（用于测试）"""
        positions = []
        
        # 为每只股票分配随机权重
        np.random.seed(42)
        weights = np.random.dirichlet(np.ones(len(ts_codes)))
        
        for i, ts_code in enumerate(ts_codes):
            try:
                # 获取当前价格（模拟）
                current_price = 10.0 + np.random.uniform(-2, 2)
                avg_cost = current_price * np.random.uniform(0.9, 1.1)
                
                # 计算持仓
                market_value = total_assets * weights[i]
                shares = int(market_value / current_price)
                actual_market_value = shares * current_price
                
                unrealized_pnl = shares * (current_price - avg_cost)
                weight = actual_market_value / total_assets
                
                position = PositionInfo(
                    ts_code=ts_code,
                    shares=shares,
                    avg_cost=avg_cost,
                    current_price=current_price,
                    market_value=actual_market_value,
                    unrealized_pnl=unrealized_pnl,
                    weight=weight
                )
                positions.append(position)
                
            except Exception as e:
                logger.error(f"创建 {ts_code} 持仓信息失败: {e}")
        
        return positions
    
    def get_sector_mapping(self, ts_codes: List[str]) -> Dict[str, str]:
        """获取股票行业映射"""
        try:
            db = next(get_db())
            stocks = db.query(Stock).filter(Stock.ts_code.in_(ts_codes)).all()
            db.close()
            
            sector_mapping = {}
            for stock in stocks:
                sector_mapping[stock.ts_code] = stock.industry or "未知"
            
            # 为没有行业信息的股票设置默认值
            for ts_code in ts_codes:
                if ts_code not in sector_mapping:
                    sector_mapping[ts_code] = "未知"
            
            return sector_mapping
            
        except Exception as e:
            logger.error(f"获取行业映射失败: {e}")
            # 返回默认映射
            return {ts_code: "未知" for ts_code in ts_codes}
    
    def generate_sample_returns(self, days: int = 60) -> pd.Series:
        """生成示例收益率数据"""
        np.random.seed(42)
        dates = pd.date_range(start=datetime.now() - timedelta(days=days), periods=days, freq='D')
        
        # 生成随机收益率
        returns = np.random.normal(0.001, 0.02, days)  # 日均收益0.1%，波动率2%
        
        return pd.Series(returns, index=dates)
    
    def generate_portfolio_values(self, returns: pd.Series, initial_value: float = 1000000) -> pd.Series:
        """根据收益率生成组合净值"""
        cumulative_returns = (1 + returns).cumprod()
        portfolio_values = initial_value * cumulative_returns
        return portfolio_values
    
    def get_volume_data(self, ts_codes: List[str]) -> Dict[str, float]:
        """获取成交量数据"""
        volume_data = {}
        
        for ts_code in ts_codes:
            try:
                # 获取最近几天的成交量数据
                end_date = datetime.now()
                start_date = end_date - timedelta(days=10)
                
                df = self.tushare_client.get_daily_data(
                    ts_code,
                    start_date.strftime('%Y%m%d'),
                    end_date.strftime('%Y%m%d')
                )
                
                if not df.empty:
                    # 计算平均成交量
                    avg_volume = df['vol'].mean()
                    volume_data[ts_code] = avg_volume
                else:
                    volume_data[ts_code] = 1000000  # 默认值
                    
            except Exception as e:
                logger.error(f"获取 {ts_code} 成交量数据失败: {e}")
                volume_data[ts_code] = 1000000  # 默认值
        
        return volume_data
    
    def perform_risk_assessment(self, ts_codes: List[str], total_assets: float = 1000000) -> Dict[str, Any]:
        """执行风险评估"""
        try:
            logger.info(f"开始风险评估，股票数量: {len(ts_codes)}")
            
            # 创建示例投资组合
            positions = self.create_sample_portfolio(ts_codes, total_assets)
            
            # 获取行业映射
            sector_mapping = self.get_sector_mapping(ts_codes)
            
            # 生成示例数据
            returns = self.generate_sample_returns()
            portfolio_values = self.generate_portfolio_values(returns, total_assets)
            benchmark_returns = self.generate_sample_returns() * 0.8  # 基准收益稍低
            
            # 获取成交量数据（简化版本）
            volume_data = {ts_code: np.random.randint(500000, 2000000) for ts_code in ts_codes}
            
            # 执行综合风险检查
            risk_result = self.risk_manager.comprehensive_risk_check(
                positions=positions,
                portfolio_values=portfolio_values,
                returns=returns,
                total_assets=total_assets,
                sector_mapping=sector_mapping,
                volume_data=volume_data,
                benchmark_returns=benchmark_returns
            )
            
            # 保存告警到数据库
            self._save_alerts_to_db(risk_result['alerts'])
            
            # 格式化结果
            formatted_result = self._format_risk_result(risk_result, positions)
            
            logger.info(f"风险评估完成，风险评分: {risk_result['risk_score']}")
            return formatted_result
            
        except Exception as e:
            logger.error(f"风险评估失败: {e}")
            return {
                'error': str(e),
                'risk_score': 0,
                'alerts': [],
                'positions': []
            }
    
    def get_risk_dashboard(self, ts_codes: List[str]) -> Dict[str, Any]:
        """获取风险监控仪表盘数据"""
        try:
            # 执行风险评估
            risk_assessment = self.perform_risk_assessment(ts_codes)
            
            # 获取历史告警
            db = next(get_db())
            recent_alerts = db.query(Alert).filter(
                Alert.created_at >= datetime.now() - timedelta(days=7)
            ).order_by(Alert.created_at.desc()).limit(20).all()
            db.close()
            
            # 统计告警数据
            alert_stats = {
                'total_alerts': len(recent_alerts),
                'high_risk_alerts': len([a for a in recent_alerts if a.severity == 'HIGH']),
                'active_alerts': len([a for a in recent_alerts if a.status == 'ACTIVE'])
            }
            
            return {
                'risk_assessment': risk_assessment,
                'alert_statistics': alert_stats,
                'recent_alerts': [
                    {
                        'id': alert.id,
                        'ts_code': alert.ts_code,
                        'alert_type': alert.alert_type,
                        'severity': alert.severity,
                        'message': alert.message,
                        'created_at': alert.created_at.isoformat()
                    }
                    for alert in recent_alerts
                ],
                'dashboard_update_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取风险仪表盘数据失败: {e}")
            return {'error': str(e)}
    
    def _save_alerts_to_db(self, alerts: List[RiskAlert]):
        """保存告警到数据库"""
        try:
            db = next(get_db())
            
            for alert in alerts:
                db_alert = Alert(
                    ts_code=alert.ts_code,
                    alert_type=alert.alert_type.value,
                    severity=alert.risk_level.value,
                    title=f"{alert.alert_type.value} 风险告警",
                    message=alert.message,
                    trigger_value=alert.current_value,
                    threshold_value=alert.threshold,
                    status='ACTIVE'
                )
                db.add(db_alert)
            
            db.commit()
            db.close()
            
            if alerts:
                logger.info(f"保存 {len(alerts)} 条风险告警到数据库")
                
        except Exception as e:
            logger.error(f"保存告警到数据库失败: {e}")
    
    def _format_risk_result(self, risk_result: Dict[str, Any], positions: List[PositionInfo]) -> Dict[str, Any]:
        """格式化风险评估结果"""
        return {
            'risk_score': risk_result['risk_score'],
            'risk_level': self._get_risk_level_description(risk_result['risk_score']),
            'total_alerts': len(risk_result['alerts']),
            'alerts_by_level': {
                level: len(alerts) for level, alerts in risk_result['risk_summary'].items()
            },
            'risk_metrics': risk_result['risk_metrics'],
            'alerts': [
                {
                    'type': alert.alert_type.value,
                    'level': alert.risk_level.value,
                    'ts_code': alert.ts_code,
                    'message': alert.message,
                    'current_value': alert.current_value,
                    'threshold': alert.threshold,
                    'timestamp': alert.timestamp.isoformat()
                }
                for alert in risk_result['alerts']
            ],
            'positions_summary': {
                'total_positions': len(positions),
                'total_market_value': sum(p.market_value for p in positions),
                'total_unrealized_pnl': sum(p.unrealized_pnl for p in positions),
                'largest_position': max(positions, key=lambda p: p.weight) if positions else None
            },
            'check_time': risk_result['check_time']
        }
    
    def _get_risk_level_description(self, risk_score: int) -> str:
        """获取风险等级描述"""
        if risk_score >= 80:
            return "极高风险"
        elif risk_score >= 60:
            return "高风险"
        elif risk_score >= 40:
            return "中等风险"
        elif risk_score >= 20:
            return "低风险"
        else:
            return "风险可控"

# 全局风险服务实例
risk_service = RiskService()

def get_risk_service() -> RiskService:
    """获取风险服务实例"""
    return risk_service
