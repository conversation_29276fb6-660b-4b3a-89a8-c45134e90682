{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar ORIGIN_METHOD = '\\0__throttleOriginMethod';\nvar RATE = '\\0__throttleRate';\nvar THROTTLE_TYPE = '\\0__throttleType';\n;\n/**\r\n * @public\r\n * @param {(Function)} fn\r\n * @param {number} [delay=0] Unit: ms.\r\n * @param {boolean} [debounce=false]\r\n *        true: If call interval less than `delay`, only the last call works.\r\n *        false: If call interval less than `delay, call works on fixed rate.\r\n * @return {(Function)} throttled fn.\r\n */\nexport function throttle(fn, delay, debounce) {\n  var currCall;\n  var lastCall = 0;\n  var lastExec = 0;\n  var timer = null;\n  var diff;\n  var scope;\n  var args;\n  var debounceNextCall;\n  delay = delay || 0;\n  function exec() {\n    lastExec = new Date().getTime();\n    timer = null;\n    fn.apply(scope, args || []);\n  }\n  var cb = function () {\n    var cbArgs = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      cbArgs[_i] = arguments[_i];\n    }\n    currCall = new Date().getTime();\n    scope = this;\n    args = cbArgs;\n    var thisDelay = debounceNextCall || delay;\n    var thisDebounce = debounceNextCall || debounce;\n    debounceNextCall = null;\n    diff = currCall - (thisDebounce ? lastCall : lastExec) - thisDelay;\n    clearTimeout(timer);\n    // Here we should make sure that: the `exec` SHOULD NOT be called later\n    // than a new call of `cb`, that is, preserving the command order. Consider\n    // calculating \"scale rate\" when roaming as an example. When a call of `cb`\n    // happens, either the `exec` is called dierectly, or the call is delayed.\n    // But the delayed call should never be later than next call of `cb`. Under\n    // this assurance, we can simply update view state each time `dispatchAction`\n    // triggered by user roaming, but not need to add extra code to avoid the\n    // state being \"rolled-back\".\n    if (thisDebounce) {\n      timer = setTimeout(exec, thisDelay);\n    } else {\n      if (diff >= 0) {\n        exec();\n      } else {\n        timer = setTimeout(exec, -diff);\n      }\n    }\n    lastCall = currCall;\n  };\n  /**\r\n   * Clear throttle.\r\n   * @public\r\n   */\n  cb.clear = function () {\n    if (timer) {\n      clearTimeout(timer);\n      timer = null;\n    }\n  };\n  /**\r\n   * Enable debounce once.\r\n   */\n  cb.debounceNextCall = function (debounceDelay) {\n    debounceNextCall = debounceDelay;\n  };\n  return cb;\n}\n/**\r\n * Create throttle method or update throttle rate.\r\n *\r\n * @example\r\n * ComponentView.prototype.render = function () {\r\n *     ...\r\n *     throttle.createOrUpdate(\r\n *         this,\r\n *         '_dispatchAction',\r\n *         this.model.get('throttle'),\r\n *         'fixRate'\r\n *     );\r\n * };\r\n * ComponentView.prototype.remove = function () {\r\n *     throttle.clear(this, '_dispatchAction');\r\n * };\r\n * ComponentView.prototype.dispose = function () {\r\n *     throttle.clear(this, '_dispatchAction');\r\n * };\r\n *\r\n */\nexport function createOrUpdate(obj, fnAttr, rate, throttleType) {\n  var fn = obj[fnAttr];\n  if (!fn) {\n    return;\n  }\n  var originFn = fn[ORIGIN_METHOD] || fn;\n  var lastThrottleType = fn[THROTTLE_TYPE];\n  var lastRate = fn[RATE];\n  if (lastRate !== rate || lastThrottleType !== throttleType) {\n    if (rate == null || !throttleType) {\n      return obj[fnAttr] = originFn;\n    }\n    fn = obj[fnAttr] = throttle(originFn, rate, throttleType === 'debounce');\n    fn[ORIGIN_METHOD] = originFn;\n    fn[THROTTLE_TYPE] = throttleType;\n    fn[RATE] = rate;\n  }\n  return fn;\n}\n/**\r\n * Clear throttle. Example see throttle.createOrUpdate.\r\n */\nexport function clear(obj, fnAttr) {\n  var fn = obj[fnAttr];\n  if (fn && fn[ORIGIN_METHOD]) {\n    // Clear throttle\n    fn.clear && fn.clear();\n    obj[fnAttr] = fn[ORIGIN_METHOD];\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}