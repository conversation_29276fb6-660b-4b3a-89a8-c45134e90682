import React from 'react';
import { Layout, Menu } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  StockOutlined,
  MonitorOutlined,
  RobotOutlined,
  SettingOutlined,
  BarChartOutlined
} from '@ant-design/icons';

const { Sider } = Layout;

const Sidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '监控仪表盘',
    },
    {
      key: '/stocks',
      icon: <StockOutlined />,
      label: '股票管理',
    },
    {
      key: '/monitoring',
      icon: <MonitorOutlined />,
      label: '实时监控',
    },
    {
      key: '/indicators',
      icon: <BarChartOutlined />,
      label: '技术指标',
    },
    {
      key: '/ai',
      icon: <RobotOutlined />,
      label: 'AI分析',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  const handleMenuClick = (e: any) => {
    navigate(e.key);
  };

  return (
    <Sider width={200} style={{ background: '#fff' }}>
      <div className="logo">
        量化交易系统
      </div>
      <Menu
        mode="inline"
        selectedKeys={[location.pathname]}
        style={{ height: '100%', borderRight: 0 }}
        items={menuItems}
        onClick={handleMenuClick}
      />
    </Sider>
  );
};

export default Sidebar;
