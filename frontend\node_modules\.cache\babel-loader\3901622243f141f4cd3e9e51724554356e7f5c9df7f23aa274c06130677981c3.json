{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { map } from 'zrender/lib/core/util.js';\nimport createRenderPlanner from '../chart/helper/createRenderPlanner.js';\nimport { isDimensionStacked } from '../data/helper/dataStackHelper.js';\nimport { createFloat32Array } from '../util/vendor.js';\nexport default function pointsLayout(seriesType, forceStoreInTypedArray) {\n  return {\n    seriesType: seriesType,\n    plan: createRenderPlanner(),\n    reset: function (seriesModel) {\n      var data = seriesModel.getData();\n      var coordSys = seriesModel.coordinateSystem;\n      var pipelineContext = seriesModel.pipelineContext;\n      var useTypedArray = forceStoreInTypedArray || pipelineContext.large;\n      if (!coordSys) {\n        return;\n      }\n      var dims = map(coordSys.dimensions, function (dim) {\n        return data.mapDimension(dim);\n      }).slice(0, 2);\n      var dimLen = dims.length;\n      var stackResultDim = data.getCalculationInfo('stackResultDimension');\n      if (isDimensionStacked(data, dims[0])) {\n        dims[0] = stackResultDim;\n      }\n      if (isDimensionStacked(data, dims[1])) {\n        dims[1] = stackResultDim;\n      }\n      var store = data.getStore();\n      var dimIdx0 = data.getDimensionIndex(dims[0]);\n      var dimIdx1 = data.getDimensionIndex(dims[1]);\n      return dimLen && {\n        progress: function (params, data) {\n          var segCount = params.end - params.start;\n          var points = useTypedArray && createFloat32Array(segCount * dimLen);\n          var tmpIn = [];\n          var tmpOut = [];\n          for (var i = params.start, offset = 0; i < params.end; i++) {\n            var point = void 0;\n            if (dimLen === 1) {\n              var x = store.get(dimIdx0, i);\n              // NOTE: Make sure the second parameter is null to use default strategy.\n              point = coordSys.dataToPoint(x, null, tmpOut);\n            } else {\n              tmpIn[0] = store.get(dimIdx0, i);\n              tmpIn[1] = store.get(dimIdx1, i);\n              // Let coordinate system to handle the NaN data.\n              point = coordSys.dataToPoint(tmpIn, null, tmpOut);\n            }\n            if (useTypedArray) {\n              points[offset++] = point[0];\n              points[offset++] = point[1];\n            } else {\n              data.setItemLayout(i, point.slice());\n            }\n          }\n          useTypedArray && data.setLayout('points', points);\n        }\n      };\n    }\n  };\n}\n;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}