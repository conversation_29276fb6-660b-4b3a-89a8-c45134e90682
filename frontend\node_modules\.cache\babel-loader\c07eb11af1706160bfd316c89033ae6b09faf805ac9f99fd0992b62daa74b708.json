{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\nimport * as React from 'react';\nexport default function FooterRow(_ref) {\n  var children = _ref.children,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"tr\", props, children);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}