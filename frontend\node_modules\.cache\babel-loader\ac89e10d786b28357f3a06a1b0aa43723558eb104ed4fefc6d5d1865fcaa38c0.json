{"ast": null, "code": "var Gradient = function () {\n  function Gradient(colorStops) {\n    this.colorStops = colorStops || [];\n  }\n  Gradient.prototype.addColorStop = function (offset, color) {\n    this.colorStops.push({\n      offset: offset,\n      color: color\n    });\n  };\n  return Gradient;\n}();\nexport default Gradient;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}