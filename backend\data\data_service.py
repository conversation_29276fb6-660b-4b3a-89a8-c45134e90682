"""
数据服务 - 统一的数据获取和存储接口
"""
import pandas as pd
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import logging
from sqlalchemy.orm import Session

from data.tushare_client import get_tushare_client
from app.models.database import get_db
from app.models.stock import Stock, StockBasic
from app.models.market_data import DailyData, TechnicalIndicator
from app.core.influxdb_client import get_influx_manager

logger = logging.getLogger(__name__)

class DataService:
    """数据服务类"""
    
    def __init__(self):
        self.tushare_client = get_tushare_client()
        self.influx_manager = get_influx_manager()
    
    def sync_stock_basic_info(self, market: str = "A股") -> int:
        """同步股票基本信息到数据库"""
        try:
            # 从Tushare获取股票基本信息
            df = self.tushare_client.get_stock_basic(market)
            if df.empty:
                logger.warning(f"未获取到{market}股票基本信息")
                return 0
            
            # 保存到数据库
            db = next(get_db())
            updated_count = 0
            
            for _, row in df.iterrows():
                # 检查股票是否已存在
                existing_stock = db.query(Stock).filter(Stock.ts_code == row['ts_code']).first()
                
                if existing_stock:
                    # 更新现有记录
                    existing_stock.name = row.get('name', existing_stock.name)
                    existing_stock.industry = row.get('industry', existing_stock.industry)
                    existing_stock.area = row.get('area', existing_stock.area)
                    existing_stock.list_date = row.get('list_date', existing_stock.list_date)
                else:
                    # 创建新记录
                    new_stock = Stock(
                        ts_code=row['ts_code'],
                        symbol=row.get('symbol', row['ts_code'].split('.')[0]),
                        name=row.get('name', ''),
                        market=market,
                        industry=row.get('industry', ''),
                        area=row.get('area', ''),
                        list_date=row.get('list_date', ''),
                        is_active=False  # 默认不监控
                    )
                    db.add(new_stock)
                
                updated_count += 1
            
            db.commit()
            db.close()
            
            logger.info(f"✅ 同步{market}股票基本信息完成，更新 {updated_count} 条记录")
            return updated_count
            
        except Exception as e:
            logger.error(f"❌ 同步{market}股票基本信息失败: {e}")
            return 0
    
    def sync_stock_daily_data(self, ts_code: str, days: int = 30) -> bool:
        """同步股票日线数据"""
        try:
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')
            
            # 获取日线数据
            df = self.tushare_client.get_daily_data(ts_code, start_str, end_str)
            if df.empty:
                logger.warning(f"未获取到 {ts_code} 的日线数据")
                return False
            
            # 转换为InfluxDB格式并存储
            points = []
            for _, row in df.iterrows():
                daily_data = DailyData(
                    ts_code=row['ts_code'],
                    trade_date=row['trade_date'],
                    open=float(row['open']),
                    high=float(row['high']),
                    low=float(row['low']),
                    close=float(row['close']),
                    pre_close=float(row['pre_close']),
                    change=float(row['change']),
                    pct_chg=float(row['pct_chg']),
                    vol=float(row['vol']),
                    amount=float(row['amount'])
                )
                points.append(daily_data.to_influx_point())
            
            # 写入InfluxDB
            success = self.influx_manager.write_points(points)
            
            if success:
                logger.info(f"✅ 同步 {ts_code} 日线数据完成，{len(points)} 条记录")
            else:
                logger.warning(f"⚠️  {ts_code} 日线数据写入InfluxDB失败，但数据获取成功")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 同步 {ts_code} 日线数据失败: {e}")
            return False
    
    def sync_stock_basic_daily(self, ts_code: str, days: int = 30) -> bool:
        """同步股票基本面数据"""
        try:
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')
            
            # 获取基本面数据
            df = self.tushare_client.get_basic_daily(ts_code, start_str, end_str)
            if df.empty:
                logger.warning(f"未获取到 {ts_code} 的基本面数据")
                return False
            
            # 保存到PostgreSQL
            db = next(get_db())
            
            for _, row in df.iterrows():
                # 检查是否已存在
                existing = db.query(StockBasic).filter(
                    StockBasic.ts_code == row['ts_code'],
                    StockBasic.trade_date == row['trade_date']
                ).first()
                
                if not existing:
                    basic_data = StockBasic(
                        ts_code=row['ts_code'],
                        trade_date=row['trade_date'],
                        pe=row.get('pe'),
                        pe_ttm=row.get('pe_ttm'),
                        pb=row.get('pb'),
                        ps=row.get('ps'),
                        ps_ttm=row.get('ps_ttm'),
                        total_mv=row.get('total_mv'),
                        circ_mv=row.get('circ_mv'),
                        turnover_rate=row.get('turnover_rate'),
                        turnover_rate_f=row.get('turnover_rate_f'),
                        volume_ratio=row.get('volume_ratio')
                    )
                    db.add(basic_data)
            
            db.commit()
            db.close()
            
            logger.info(f"✅ 同步 {ts_code} 基本面数据完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 同步 {ts_code} 基本面数据失败: {e}")
            return False
    
    def get_active_stocks(self) -> List[str]:
        """获取所有激活监控的股票代码"""
        try:
            db = next(get_db())
            stocks = db.query(Stock).filter(Stock.is_active == True).all()
            db.close()
            
            return [stock.ts_code for stock in stocks]
            
        except Exception as e:
            logger.error(f"获取激活股票列表失败: {e}")
            return []
    
    def batch_sync_daily_data(self, ts_codes: List[str] = None, days: int = 30) -> Dict[str, bool]:
        """批量同步日线数据"""
        if ts_codes is None:
            ts_codes = self.get_active_stocks()
        
        if not ts_codes:
            logger.warning("没有需要同步的股票")
            return {}
        
        results = {}
        logger.info(f"开始批量同步 {len(ts_codes)} 只股票的日线数据")
        
        for ts_code in ts_codes:
            try:
                success = self.sync_stock_daily_data(ts_code, days)
                results[ts_code] = success
                
                # 控制调用频率
                import time
                time.sleep(0.2)
                
            except Exception as e:
                logger.error(f"批量同步 {ts_code} 失败: {e}")
                results[ts_code] = False
        
        success_count = sum(1 for success in results.values() if success)
        logger.info(f"批量同步完成：{success_count}/{len(ts_codes)} 成功")
        
        return results
    
    def get_latest_price(self, ts_code: str) -> Optional[float]:
        """获取最新价格"""
        try:
            # 先尝试从InfluxDB获取
            latest_data = self.influx_manager.get_latest_data("daily_data", ts_code, "close")
            if latest_data:
                return float(latest_data.get('value', 0))
            
            # 如果InfluxDB没有数据，从Tushare获取实时数据
            df = self.tushare_client.get_realtime_data([ts_code])
            if not df.empty and 'price' in df.columns:
                return float(df.iloc[0]['price'])
            
            return None
            
        except Exception as e:
            logger.error(f"获取 {ts_code} 最新价格失败: {e}")
            return None

# 全局数据服务实例
data_service = DataService()

def get_data_service() -> DataService:
    """获取数据服务实例"""
    return data_service
