[{"C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\index.tsx": "1", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\App.tsx": "2", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\pages\\Dashboard.tsx": "3", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\pages\\AIAnalysis.tsx": "4", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\pages\\Monitoring.tsx": "5", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\pages\\StockManagement.tsx": "6", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\components\\Layout\\Header.tsx": "7", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\components\\Layout\\Sidebar.tsx": "8"}, {"size": 414, "mtime": 1753922769690, "results": "9", "hashOfConfig": "10"}, {"size": 1262, "mtime": 1753922852485, "results": "11", "hashOfConfig": "10"}, {"size": 7379, "mtime": 1753923246550, "results": "12", "hashOfConfig": "10"}, {"size": 11804, "mtime": 1753922955021, "results": "13", "hashOfConfig": "10"}, {"size": 10638, "mtime": 1753923006781, "results": "14", "hashOfConfig": "10"}, {"size": 8651, "mtime": 1753873948266, "results": "15", "hashOfConfig": "10"}, {"size": 1426, "mtime": 1753873864057, "results": "16", "hashOfConfig": "10"}, {"size": 1458, "mtime": 1753873851232, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "84tp5k", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\index.tsx", [], [], "C:\\<PERSON>_Zhang\\量化交易系统\\frontend\\src\\App.tsx", [], [], "C:\\<PERSON>_Zhang\\量化交易系统\\frontend\\src\\pages\\Dashboard.tsx", ["42"], [], "C:\\<PERSON>_Zhang\\量化交易系统\\frontend\\src\\pages\\AIAnalysis.tsx", ["43"], [], "C:\\<PERSON>_Zhang\\量化交易系统\\frontend\\src\\pages\\Monitoring.tsx", [], [], "C:\\<PERSON>_Zhang\\量化交易系统\\frontend\\src\\pages\\StockManagement.tsx", [], [], "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\components\\Layout\\Header.tsx", [], [], "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\components\\Layout\\Sidebar.tsx", [], [], {"ruleId": "44", "severity": 1, "message": "45", "line": 105, "column": 9, "nodeType": "46", "messageId": "47", "endLine": 105, "endColumn": 21}, {"ruleId": "44", "severity": 1, "message": "48", "line": 10, "column": 3, "nodeType": "46", "messageId": "47", "endLine": 10, "endColumn": 8}, "@typescript-eslint/no-unused-vars", "'alertColumns' is assigned a value but never used.", "Identifier", "unusedVar", "'Alert' is defined but never used."]