{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Displayable, { DEFAULT_COMMON_STYLE, DEFAULT_COMMON_ANIMATION_PROPS } from './Displayable.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { defaults, createObject } from '../core/util.js';\nexport var DEFAULT_IMAGE_STYLE = defaults({\n  x: 0,\n  y: 0\n}, DEFAULT_COMMON_STYLE);\nexport var DEFAULT_IMAGE_ANIMATION_PROPS = {\n  style: defaults({\n    x: true,\n    y: true,\n    width: true,\n    height: true,\n    sx: true,\n    sy: true,\n    sWidth: true,\n    sHeight: true\n  }, DEFAULT_COMMON_ANIMATION_PROPS.style)\n};\nfunction isImageLike(source) {\n  return !!(source && typeof source !== 'string' && source.width && source.height);\n}\nvar ZRImage = function (_super) {\n  __extends(ZRImage, _super);\n  function ZRImage() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  ZRImage.prototype.createStyle = function (obj) {\n    return createObject(DEFAULT_IMAGE_STYLE, obj);\n  };\n  ZRImage.prototype._getSize = function (dim) {\n    var style = this.style;\n    var size = style[dim];\n    if (size != null) {\n      return size;\n    }\n    var imageSource = isImageLike(style.image) ? style.image : this.__image;\n    if (!imageSource) {\n      return 0;\n    }\n    var otherDim = dim === 'width' ? 'height' : 'width';\n    var otherDimSize = style[otherDim];\n    if (otherDimSize == null) {\n      return imageSource[dim];\n    } else {\n      return imageSource[dim] / imageSource[otherDim] * otherDimSize;\n    }\n  };\n  ZRImage.prototype.getWidth = function () {\n    return this._getSize('width');\n  };\n  ZRImage.prototype.getHeight = function () {\n    return this._getSize('height');\n  };\n  ZRImage.prototype.getAnimationStyleProps = function () {\n    return DEFAULT_IMAGE_ANIMATION_PROPS;\n  };\n  ZRImage.prototype.getBoundingRect = function () {\n    var style = this.style;\n    if (!this._rect) {\n      this._rect = new BoundingRect(style.x || 0, style.y || 0, this.getWidth(), this.getHeight());\n    }\n    return this._rect;\n  };\n  return ZRImage;\n}(Displayable);\nZRImage.prototype.type = 'image';\nexport default ZRImage;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}