"""
简化的数据获取测试
"""
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta

# 配置token
TUSHARE_TOKEN = "772e043e246ef24189447f73f0c276e7fc98f18a0cb7cce72bd0f28d"

def test_tushare_basic():
    """基础Tushare测试"""
    print("🚀 开始Tushare基础测试")
    
    try:
        # 设置token
        ts.set_token(TUSHARE_TOKEN)
        pro = ts.pro_api()
        print("✅ Token设置成功")
        
        # 测试1: 获取交易日历
        print("\n--- 测试交易日历 ---")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        cal = pro.trade_cal(
            exchange='SSE',
            start_date=start_date.strftime('%Y%m%d'),
            end_date=end_date.strftime('%Y%m%d')
        )
        print(f"✅ 获取交易日历成功: {len(cal)} 条记录")
        
        # 测试2: 获取股票基本信息（限制数量）
        print("\n--- 测试股票基本信息 ---")
        stocks = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name')
        print(f"✅ 获取股票基本信息成功: {len(stocks)} 只股票")
        print("前3只股票:")
        print(stocks.head(3).to_string())
        
        # 测试3: 获取单只股票日线数据
        print("\n--- 测试日线数据 ---")
        test_stock = '000001.SZ'  # 平安银行
        daily = pro.daily(
            ts_code=test_stock,
            start_date=start_date.strftime('%Y%m%d'),
            end_date=end_date.strftime('%Y%m%d')
        )
        print(f"✅ 获取 {test_stock} 日线数据成功: {len(daily)} 条记录")
        if not daily.empty:
            print("最新数据:")
            print(daily.head(1).to_string())
        
        # 测试4: 获取基本面数据
        print("\n--- 测试基本面数据 ---")
        basic = pro.daily_basic(
            ts_code=test_stock,
            start_date=start_date.strftime('%Y%m%d'),
            end_date=end_date.strftime('%Y%m%d'),
            fields='ts_code,trade_date,pe,pb,total_mv'
        )
        print(f"✅ 获取 {test_stock} 基本面数据成功: {len(basic)} 条记录")
        if not basic.empty:
            print("最新数据:")
            print(basic.head(1).to_string())
        
        print("\n🎉 所有测试通过！Tushare API工作正常")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        print("可能的原因:")
        print("1. 网络连接问题")
        print("2. Token权限不足")
        print("3. API调用频率限制")
        print("4. Tushare服务异常")
        return False

def test_data_processing():
    """测试数据处理功能"""
    print("\n=== 测试数据处理功能 ===")
    
    try:
        # 创建示例数据
        data = {
            'ts_code': ['000001.SZ', '000002.SZ'],
            'trade_date': ['20241201', '20241201'],
            'open': [10.5, 20.3],
            'high': [11.0, 21.0],
            'low': [10.2, 20.0],
            'close': [10.8, 20.8],
            'vol': [1000000, 2000000],
            'amount': [10800000, 41600000]
        }
        
        df = pd.DataFrame(data)
        print("✅ 创建示例数据成功")
        print(df.to_string())
        
        # 数据清洗测试
        print("\n--- 数据清洗测试 ---")
        
        # 检查价格逻辑
        valid_data = df[
            (df['high'] >= df['low']) &
            (df['high'] >= df['open']) &
            (df['high'] >= df['close']) &
            (df['low'] <= df['open']) &
            (df['low'] <= df['close'])
        ]
        
        print(f"✅ 数据清洗完成，有效数据: {len(valid_data)} 条")
        
        # 转换为InfluxDB格式测试
        print("\n--- InfluxDB格式转换测试 ---")
        points = []
        for _, row in valid_data.iterrows():
            point = {
                "measurement": "daily_data",
                "tags": {
                    "ts_code": row['ts_code'],
                    "market": "A股"
                },
                "time": row['trade_date'],
                "fields": {
                    "open": float(row['open']),
                    "high": float(row['high']),
                    "low": float(row['low']),
                    "close": float(row['close']),
                    "vol": float(row['vol']),
                    "amount": float(row['amount'])
                }
            }
            points.append(point)
        
        print(f"✅ 转换为InfluxDB格式成功: {len(points)} 个数据点")
        print("示例数据点:")
        print(points[0])
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("量化交易系统 - 数据获取功能测试")
    print("=" * 60)
    
    # 执行测试
    test1_result = test_tushare_basic()
    test2_result = test_data_processing()
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"Tushare API测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"数据处理测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！数据获取模块可以正常工作")
    elif test1_result:
        print("\n⚠️  Tushare API正常，但数据处理有问题")
    else:
        print("\n❌ 测试失败，请检查网络连接和API配置")

if __name__ == "__main__":
    main()
