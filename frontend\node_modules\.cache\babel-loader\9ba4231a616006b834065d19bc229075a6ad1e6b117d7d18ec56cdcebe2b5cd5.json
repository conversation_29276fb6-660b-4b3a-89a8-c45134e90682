{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { lift } from 'zrender/lib/tool/color.js';\nimport { extend, isString } from 'zrender/lib/core/util.js';\nexport default function sunburstVisual(ecModel) {\n  var paletteScope = {};\n  // Default color strategy\n  function pickColor(node, seriesModel, treeHeight) {\n    // Choose color from palette based on the first level.\n    var current = node;\n    while (current && current.depth > 1) {\n      current = current.parentNode;\n    }\n    var color = seriesModel.getColorFromPalette(current.name || current.dataIndex + '', paletteScope);\n    if (node.depth > 1 && isString(color)) {\n      // Lighter on the deeper level.\n      color = lift(color, (node.depth - 1) / (treeHeight - 1) * 0.5);\n    }\n    return color;\n  }\n  ecModel.eachSeriesByType('sunburst', function (seriesModel) {\n    var data = seriesModel.getData();\n    var tree = data.tree;\n    tree.eachNode(function (node) {\n      var model = node.getModel();\n      var style = model.getModel('itemStyle').getItemStyle();\n      if (!style.fill) {\n        style.fill = pickColor(node, seriesModel, tree.root.height);\n      }\n      var existsStyle = data.ensureUniqueItemVisual(node.dataIndex, 'style');\n      extend(existsStyle, style);\n    });\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}