{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport { each, extend, isArray } from 'zrender/lib/core/util.js';\nimport { modifyHSL, modifyAlpha } from 'zrender/lib/tool/color.js';\nimport { makeInner } from '../../util/model.js';\nvar ITEM_STYLE_NORMAL = 'itemStyle';\nvar inner = makeInner();\nexport default {\n  seriesType: 'treemap',\n  reset: function (seriesModel) {\n    var tree = seriesModel.getData().tree;\n    var root = tree.root;\n    if (root.isRemoved()) {\n      return;\n    }\n    travelTree(root,\n    // Visual should calculate from tree root but not view root.\n    {}, seriesModel.getViewRoot().getAncestors(), seriesModel);\n  }\n};\nfunction travelTree(node, designatedVisual, viewRootAncestors, seriesModel) {\n  var nodeModel = node.getModel();\n  var nodeLayout = node.getLayout();\n  var data = node.hostTree.data;\n  // Optimize\n  if (!nodeLayout || nodeLayout.invisible || !nodeLayout.isInView) {\n    return;\n  }\n  var nodeItemStyleModel = nodeModel.getModel(ITEM_STYLE_NORMAL);\n  var visuals = buildVisuals(nodeItemStyleModel, designatedVisual, seriesModel);\n  var existsStyle = data.ensureUniqueItemVisual(node.dataIndex, 'style');\n  // calculate border color\n  var borderColor = nodeItemStyleModel.get('borderColor');\n  var borderColorSaturation = nodeItemStyleModel.get('borderColorSaturation');\n  var thisNodeColor;\n  if (borderColorSaturation != null) {\n    // For performance, do not always execute 'calculateColor'.\n    thisNodeColor = calculateColor(visuals);\n    borderColor = calculateBorderColor(borderColorSaturation, thisNodeColor);\n  }\n  existsStyle.stroke = borderColor;\n  var viewChildren = node.viewChildren;\n  if (!viewChildren || !viewChildren.length) {\n    thisNodeColor = calculateColor(visuals);\n    // Apply visual to this node.\n    existsStyle.fill = thisNodeColor;\n  } else {\n    var mapping_1 = buildVisualMapping(node, nodeModel, nodeLayout, nodeItemStyleModel, visuals, viewChildren);\n    // Designate visual to children.\n    each(viewChildren, function (child, index) {\n      // If higher than viewRoot, only ancestors of viewRoot is needed to visit.\n      if (child.depth >= viewRootAncestors.length || child === viewRootAncestors[child.depth]) {\n        var childVisual = mapVisual(nodeModel, visuals, child, index, mapping_1, seriesModel);\n        travelTree(child, childVisual, viewRootAncestors, seriesModel);\n      }\n    });\n  }\n}\nfunction buildVisuals(nodeItemStyleModel, designatedVisual, seriesModel) {\n  var visuals = extend({}, designatedVisual);\n  var designatedVisualItemStyle = seriesModel.designatedVisualItemStyle;\n  each(['color', 'colorAlpha', 'colorSaturation'], function (visualName) {\n    // Priority: thisNode > thisLevel > parentNodeDesignated > seriesModel\n    designatedVisualItemStyle[visualName] = designatedVisual[visualName];\n    var val = nodeItemStyleModel.get(visualName);\n    designatedVisualItemStyle[visualName] = null;\n    val != null && (visuals[visualName] = val);\n  });\n  return visuals;\n}\nfunction calculateColor(visuals) {\n  var color = getValueVisualDefine(visuals, 'color');\n  if (color) {\n    var colorAlpha = getValueVisualDefine(visuals, 'colorAlpha');\n    var colorSaturation = getValueVisualDefine(visuals, 'colorSaturation');\n    if (colorSaturation) {\n      color = modifyHSL(color, null, null, colorSaturation);\n    }\n    if (colorAlpha) {\n      color = modifyAlpha(color, colorAlpha);\n    }\n    return color;\n  }\n}\nfunction calculateBorderColor(borderColorSaturation, thisNodeColor) {\n  return thisNodeColor != null\n  // Can only be string\n  ? modifyHSL(thisNodeColor, null, null, borderColorSaturation) : null;\n}\nfunction getValueVisualDefine(visuals, name) {\n  var value = visuals[name];\n  if (value != null && value !== 'none') {\n    return value;\n  }\n}\nfunction buildVisualMapping(node, nodeModel, nodeLayout, nodeItemStyleModel, visuals, viewChildren) {\n  if (!viewChildren || !viewChildren.length) {\n    return;\n  }\n  var rangeVisual = getRangeVisual(nodeModel, 'color') || visuals.color != null && visuals.color !== 'none' && (getRangeVisual(nodeModel, 'colorAlpha') || getRangeVisual(nodeModel, 'colorSaturation'));\n  if (!rangeVisual) {\n    return;\n  }\n  var visualMin = nodeModel.get('visualMin');\n  var visualMax = nodeModel.get('visualMax');\n  var dataExtent = nodeLayout.dataExtent.slice();\n  visualMin != null && visualMin < dataExtent[0] && (dataExtent[0] = visualMin);\n  visualMax != null && visualMax > dataExtent[1] && (dataExtent[1] = visualMax);\n  var colorMappingBy = nodeModel.get('colorMappingBy');\n  var opt = {\n    type: rangeVisual.name,\n    dataExtent: dataExtent,\n    visual: rangeVisual.range\n  };\n  if (opt.type === 'color' && (colorMappingBy === 'index' || colorMappingBy === 'id')) {\n    opt.mappingMethod = 'category';\n    opt.loop = true;\n    // categories is ordinal, so do not set opt.categories.\n  } else {\n    opt.mappingMethod = 'linear';\n  }\n  var mapping = new VisualMapping(opt);\n  inner(mapping).drColorMappingBy = colorMappingBy;\n  return mapping;\n}\n// Notice: If we don't have the attribute 'colorRange', but only use\n// attribute 'color' to represent both concepts of 'colorRange' and 'color',\n// (It means 'colorRange' when 'color' is Array, means 'color' when not array),\n// this problem will be encountered:\n// If a level-1 node doesn't have children, and its siblings have children,\n// and colorRange is set on level-1, then the node cannot be colored.\n// So we separate 'colorRange' and 'color' to different attributes.\nfunction getRangeVisual(nodeModel, name) {\n  // 'colorRange', 'colorARange', 'colorSRange'.\n  // If not exists on this node, fetch from levels and series.\n  var range = nodeModel.get(name);\n  return isArray(range) && range.length ? {\n    name: name,\n    range: range\n  } : null;\n}\nfunction mapVisual(nodeModel, visuals, child, index, mapping, seriesModel) {\n  var childVisuals = extend({}, visuals);\n  if (mapping) {\n    // Only support color, colorAlpha, colorSaturation.\n    var mappingType = mapping.type;\n    var colorMappingBy = mappingType === 'color' && inner(mapping).drColorMappingBy;\n    var value = colorMappingBy === 'index' ? index : colorMappingBy === 'id' ? seriesModel.mapIdToIndex(child.getId()) : child.getValue(nodeModel.get('visualDimension'));\n    childVisuals[mappingType] = mapping.mapValueToVisual(value);\n  }\n  return childVisuals;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}