{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { updateCenterAndZoom } from '../../action/roamHelper.js';\nexport function installTreeAction(registers) {\n  registers.registerAction({\n    type: 'treeExpandAndCollapse',\n    event: 'treeExpandAndCollapse',\n    update: 'update'\n  }, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'tree',\n      query: payload\n    }, function (seriesModel) {\n      var dataIndex = payload.dataIndex;\n      var tree = seriesModel.getData().tree;\n      var node = tree.getNodeByDataIndex(dataIndex);\n      node.isExpand = !node.isExpand;\n    });\n  });\n  registers.registerAction({\n    type: 'treeRoam',\n    event: 'treeRoam',\n    // Here we set 'none' instead of 'update', because roam action\n    // just need to update the transform matrix without having to recalculate\n    // the layout. So don't need to go through the whole update process, such\n    // as 'dataPrcocess', 'coordSystemUpdate', 'layout' and so on.\n    update: 'none'\n  }, function (payload, ecModel, api) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'tree',\n      query: payload\n    }, function (seriesModel) {\n      var coordSys = seriesModel.coordinateSystem;\n      var res = updateCenterAndZoom(coordSys, payload, undefined, api);\n      seriesModel.setCenter && seriesModel.setCenter(res.center);\n      seriesModel.setZoom && seriesModel.setZoom(res.zoom);\n    });\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}