{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useEffect, useRef } from 'react';\nimport CacheMap from \"../utils/CacheMap\";\nfunction parseNumber(value) {\n  var num = parseFloat(value);\n  return isNaN(num) ? 0 : num;\n}\nexport default function useHeights(getKey, onItemAdd, onItemRemove) {\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    updatedMark = _React$useState2[0],\n    setUpdatedMark = _React$useState2[1];\n  var instanceRef = useRef(new Map());\n  var heightsRef = useRef(new CacheMap());\n  var promiseIdRef = useRef(0);\n  function cancelRaf() {\n    promiseIdRef.current += 1;\n  }\n  function collectHeight() {\n    var sync = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    cancelRaf();\n    var doCollect = function doCollect() {\n      var changed = false;\n      instanceRef.current.forEach(function (element, key) {\n        if (element && element.offsetParent) {\n          var offsetHeight = element.offsetHeight;\n          var _getComputedStyle = getComputedStyle(element),\n            marginTop = _getComputedStyle.marginTop,\n            marginBottom = _getComputedStyle.marginBottom;\n          var marginTopNum = parseNumber(marginTop);\n          var marginBottomNum = parseNumber(marginBottom);\n          var totalHeight = offsetHeight + marginTopNum + marginBottomNum;\n          if (heightsRef.current.get(key) !== totalHeight) {\n            heightsRef.current.set(key, totalHeight);\n            changed = true;\n          }\n        }\n      });\n\n      // Always trigger update mark to tell parent that should re-calculate heights when resized\n      if (changed) {\n        setUpdatedMark(function (c) {\n          return c + 1;\n        });\n      }\n    };\n    if (sync) {\n      doCollect();\n    } else {\n      promiseIdRef.current += 1;\n      var id = promiseIdRef.current;\n      Promise.resolve().then(function () {\n        if (id === promiseIdRef.current) {\n          doCollect();\n        }\n      });\n    }\n  }\n  function setInstanceRef(item, instance) {\n    var key = getKey(item);\n    var origin = instanceRef.current.get(key);\n    if (instance) {\n      instanceRef.current.set(key, instance);\n      collectHeight();\n    } else {\n      instanceRef.current.delete(key);\n    }\n\n    // Instance changed\n    if (!origin !== !instance) {\n      if (instance) {\n        onItemAdd === null || onItemAdd === void 0 || onItemAdd(item);\n      } else {\n        onItemRemove === null || onItemRemove === void 0 || onItemRemove(item);\n      }\n    }\n  }\n  useEffect(function () {\n    return cancelRaf;\n  }, []);\n  return [setInstanceRef, collectHeight, heightsRef.current, updatedMark];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}