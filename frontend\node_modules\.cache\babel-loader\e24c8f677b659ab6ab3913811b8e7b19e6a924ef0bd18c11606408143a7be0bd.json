{"ast": null, "code": "import { platformApi } from './platform.js';\nvar BUILTIN_OBJECT = reduce(['Function', 'RegExp', 'Date', 'Error', 'CanvasGradient', 'CanvasPattern', 'Image', 'Canvas'], function (obj, val) {\n  obj['[object ' + val + ']'] = true;\n  return obj;\n}, {});\nvar TYPED_ARRAY = reduce(['Int8', 'Uint8', 'Uint8Clamped', 'Int16', 'Uint16', 'Int32', 'Uint32', 'Float32', 'Float64'], function (obj, val) {\n  obj['[object ' + val + 'Array]'] = true;\n  return obj;\n}, {});\nvar objToString = Object.prototype.toString;\nvar arrayProto = Array.prototype;\nvar nativeForEach = arrayProto.forEach;\nvar nativeFilter = arrayProto.filter;\nvar nativeSlice = arrayProto.slice;\nvar nativeMap = arrayProto.map;\nvar ctorFunction = function () {}.constructor;\nvar protoFunction = ctorFunction ? ctorFunction.prototype : null;\nvar protoKey = '__proto__';\nvar idStart = 0x0907;\nexport function guid() {\n  return idStart++;\n}\nexport function logError() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  if (typeof console !== 'undefined') {\n    console.error.apply(console, args);\n  }\n}\nexport function clone(source) {\n  if (source == null || typeof source !== 'object') {\n    return source;\n  }\n  var result = source;\n  var typeStr = objToString.call(source);\n  if (typeStr === '[object Array]') {\n    if (!isPrimitive(source)) {\n      result = [];\n      for (var i = 0, len = source.length; i < len; i++) {\n        result[i] = clone(source[i]);\n      }\n    }\n  } else if (TYPED_ARRAY[typeStr]) {\n    if (!isPrimitive(source)) {\n      var Ctor = source.constructor;\n      if (Ctor.from) {\n        result = Ctor.from(source);\n      } else {\n        result = new Ctor(source.length);\n        for (var i = 0, len = source.length; i < len; i++) {\n          result[i] = source[i];\n        }\n      }\n    }\n  } else if (!BUILTIN_OBJECT[typeStr] && !isPrimitive(source) && !isDom(source)) {\n    result = {};\n    for (var key in source) {\n      if (source.hasOwnProperty(key) && key !== protoKey) {\n        result[key] = clone(source[key]);\n      }\n    }\n  }\n  return result;\n}\nexport function merge(target, source, overwrite) {\n  if (!isObject(source) || !isObject(target)) {\n    return overwrite ? clone(source) : target;\n  }\n  for (var key in source) {\n    if (source.hasOwnProperty(key) && key !== protoKey) {\n      var targetProp = target[key];\n      var sourceProp = source[key];\n      if (isObject(sourceProp) && isObject(targetProp) && !isArray(sourceProp) && !isArray(targetProp) && !isDom(sourceProp) && !isDom(targetProp) && !isBuiltInObject(sourceProp) && !isBuiltInObject(targetProp) && !isPrimitive(sourceProp) && !isPrimitive(targetProp)) {\n        merge(targetProp, sourceProp, overwrite);\n      } else if (overwrite || !(key in target)) {\n        target[key] = clone(source[key]);\n      }\n    }\n  }\n  return target;\n}\nexport function mergeAll(targetAndSources, overwrite) {\n  var result = targetAndSources[0];\n  for (var i = 1, len = targetAndSources.length; i < len; i++) {\n    result = merge(result, targetAndSources[i], overwrite);\n  }\n  return result;\n}\nexport function extend(target, source) {\n  if (Object.assign) {\n    Object.assign(target, source);\n  } else {\n    for (var key in source) {\n      if (source.hasOwnProperty(key) && key !== protoKey) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n}\nexport function defaults(target, source, overlay) {\n  var keysArr = keys(source);\n  for (var i = 0, len = keysArr.length; i < len; i++) {\n    var key = keysArr[i];\n    if (overlay ? source[key] != null : target[key] == null) {\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nexport var createCanvas = platformApi.createCanvas;\nexport function indexOf(array, value) {\n  if (array) {\n    if (array.indexOf) {\n      return array.indexOf(value);\n    }\n    for (var i = 0, len = array.length; i < len; i++) {\n      if (array[i] === value) {\n        return i;\n      }\n    }\n  }\n  return -1;\n}\nexport function inherits(clazz, baseClazz) {\n  var clazzPrototype = clazz.prototype;\n  function F() {}\n  F.prototype = baseClazz.prototype;\n  clazz.prototype = new F();\n  for (var prop in clazzPrototype) {\n    if (clazzPrototype.hasOwnProperty(prop)) {\n      clazz.prototype[prop] = clazzPrototype[prop];\n    }\n  }\n  clazz.prototype.constructor = clazz;\n  clazz.superClass = baseClazz;\n}\nexport function mixin(target, source, override) {\n  target = 'prototype' in target ? target.prototype : target;\n  source = 'prototype' in source ? source.prototype : source;\n  if (Object.getOwnPropertyNames) {\n    var keyList = Object.getOwnPropertyNames(source);\n    for (var i = 0; i < keyList.length; i++) {\n      var key = keyList[i];\n      if (key !== 'constructor') {\n        if (override ? source[key] != null : target[key] == null) {\n          target[key] = source[key];\n        }\n      }\n    }\n  } else {\n    defaults(target, source, override);\n  }\n}\nexport function isArrayLike(data) {\n  if (!data) {\n    return false;\n  }\n  if (typeof data === 'string') {\n    return false;\n  }\n  return typeof data.length === 'number';\n}\nexport function each(arr, cb, context) {\n  if (!(arr && cb)) {\n    return;\n  }\n  if (arr.forEach && arr.forEach === nativeForEach) {\n    arr.forEach(cb, context);\n  } else if (arr.length === +arr.length) {\n    for (var i = 0, len = arr.length; i < len; i++) {\n      cb.call(context, arr[i], i, arr);\n    }\n  } else {\n    for (var key in arr) {\n      if (arr.hasOwnProperty(key)) {\n        cb.call(context, arr[key], key, arr);\n      }\n    }\n  }\n}\nexport function map(arr, cb, context) {\n  if (!arr) {\n    return [];\n  }\n  if (!cb) {\n    return slice(arr);\n  }\n  if (arr.map && arr.map === nativeMap) {\n    return arr.map(cb, context);\n  } else {\n    var result = [];\n    for (var i = 0, len = arr.length; i < len; i++) {\n      result.push(cb.call(context, arr[i], i, arr));\n    }\n    return result;\n  }\n}\nexport function reduce(arr, cb, memo, context) {\n  if (!(arr && cb)) {\n    return;\n  }\n  for (var i = 0, len = arr.length; i < len; i++) {\n    memo = cb.call(context, memo, arr[i], i, arr);\n  }\n  return memo;\n}\nexport function filter(arr, cb, context) {\n  if (!arr) {\n    return [];\n  }\n  if (!cb) {\n    return slice(arr);\n  }\n  if (arr.filter && arr.filter === nativeFilter) {\n    return arr.filter(cb, context);\n  } else {\n    var result = [];\n    for (var i = 0, len = arr.length; i < len; i++) {\n      if (cb.call(context, arr[i], i, arr)) {\n        result.push(arr[i]);\n      }\n    }\n    return result;\n  }\n}\nexport function find(arr, cb, context) {\n  if (!(arr && cb)) {\n    return;\n  }\n  for (var i = 0, len = arr.length; i < len; i++) {\n    if (cb.call(context, arr[i], i, arr)) {\n      return arr[i];\n    }\n  }\n}\nexport function keys(obj) {\n  if (!obj) {\n    return [];\n  }\n  if (Object.keys) {\n    return Object.keys(obj);\n  }\n  var keyList = [];\n  for (var key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      keyList.push(key);\n    }\n  }\n  return keyList;\n}\nfunction bindPolyfill(func, context) {\n  var args = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    args[_i - 2] = arguments[_i];\n  }\n  return function () {\n    return func.apply(context, args.concat(nativeSlice.call(arguments)));\n  };\n}\nexport var bind = protoFunction && isFunction(protoFunction.bind) ? protoFunction.call.bind(protoFunction.bind) : bindPolyfill;\nfunction curry(func) {\n  var args = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    args[_i - 1] = arguments[_i];\n  }\n  return function () {\n    return func.apply(this, args.concat(nativeSlice.call(arguments)));\n  };\n}\nexport { curry };\nexport function isArray(value) {\n  if (Array.isArray) {\n    return Array.isArray(value);\n  }\n  return objToString.call(value) === '[object Array]';\n}\nexport function isFunction(value) {\n  return typeof value === 'function';\n}\nexport function isString(value) {\n  return typeof value === 'string';\n}\nexport function isStringSafe(value) {\n  return objToString.call(value) === '[object String]';\n}\nexport function isNumber(value) {\n  return typeof value === 'number';\n}\nexport function isObject(value) {\n  var type = typeof value;\n  return type === 'function' || !!value && type === 'object';\n}\nexport function isBuiltInObject(value) {\n  return !!BUILTIN_OBJECT[objToString.call(value)];\n}\nexport function isTypedArray(value) {\n  return !!TYPED_ARRAY[objToString.call(value)];\n}\nexport function isDom(value) {\n  return typeof value === 'object' && typeof value.nodeType === 'number' && typeof value.ownerDocument === 'object';\n}\nexport function isGradientObject(value) {\n  return value.colorStops != null;\n}\nexport function isImagePatternObject(value) {\n  return value.image != null;\n}\nexport function isRegExp(value) {\n  return objToString.call(value) === '[object RegExp]';\n}\nexport function eqNaN(value) {\n  return value !== value;\n}\nexport function retrieve() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  for (var i = 0, len = args.length; i < len; i++) {\n    if (args[i] != null) {\n      return args[i];\n    }\n  }\n}\nexport function retrieve2(value0, value1) {\n  return value0 != null ? value0 : value1;\n}\nexport function retrieve3(value0, value1, value2) {\n  return value0 != null ? value0 : value1 != null ? value1 : value2;\n}\nexport function slice(arr) {\n  var args = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    args[_i - 1] = arguments[_i];\n  }\n  return nativeSlice.apply(arr, args);\n}\nexport function normalizeCssArray(val) {\n  if (typeof val === 'number') {\n    return [val, val, val, val];\n  }\n  var len = val.length;\n  if (len === 2) {\n    return [val[0], val[1], val[0], val[1]];\n  } else if (len === 3) {\n    return [val[0], val[1], val[2], val[1]];\n  }\n  return val;\n}\nexport function assert(condition, message) {\n  if (!condition) {\n    throw new Error(message);\n  }\n}\nexport function trim(str) {\n  if (str == null) {\n    return null;\n  } else if (typeof str.trim === 'function') {\n    return str.trim();\n  } else {\n    return str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, '');\n  }\n}\nvar primitiveKey = '__ec_primitive__';\nexport function setAsPrimitive(obj) {\n  obj[primitiveKey] = true;\n}\nexport function isPrimitive(obj) {\n  return obj[primitiveKey];\n}\nvar MapPolyfill = function () {\n  function MapPolyfill() {\n    this.data = {};\n  }\n  MapPolyfill.prototype[\"delete\"] = function (key) {\n    var existed = this.has(key);\n    if (existed) {\n      delete this.data[key];\n    }\n    return existed;\n  };\n  MapPolyfill.prototype.has = function (key) {\n    return this.data.hasOwnProperty(key);\n  };\n  MapPolyfill.prototype.get = function (key) {\n    return this.data[key];\n  };\n  MapPolyfill.prototype.set = function (key, value) {\n    this.data[key] = value;\n    return this;\n  };\n  MapPolyfill.prototype.keys = function () {\n    return keys(this.data);\n  };\n  MapPolyfill.prototype.forEach = function (callback) {\n    var data = this.data;\n    for (var key in data) {\n      if (data.hasOwnProperty(key)) {\n        callback(data[key], key);\n      }\n    }\n  };\n  return MapPolyfill;\n}();\nvar isNativeMapSupported = typeof Map === 'function';\nfunction maybeNativeMap() {\n  return isNativeMapSupported ? new Map() : new MapPolyfill();\n}\nvar HashMap = function () {\n  function HashMap(obj) {\n    var isArr = isArray(obj);\n    this.data = maybeNativeMap();\n    var thisMap = this;\n    obj instanceof HashMap ? obj.each(visit) : obj && each(obj, visit);\n    function visit(value, key) {\n      isArr ? thisMap.set(value, key) : thisMap.set(key, value);\n    }\n  }\n  HashMap.prototype.hasKey = function (key) {\n    return this.data.has(key);\n  };\n  HashMap.prototype.get = function (key) {\n    return this.data.get(key);\n  };\n  HashMap.prototype.set = function (key, value) {\n    this.data.set(key, value);\n    return value;\n  };\n  HashMap.prototype.each = function (cb, context) {\n    this.data.forEach(function (value, key) {\n      cb.call(context, value, key);\n    });\n  };\n  HashMap.prototype.keys = function () {\n    var keys = this.data.keys();\n    return isNativeMapSupported ? Array.from(keys) : keys;\n  };\n  HashMap.prototype.removeKey = function (key) {\n    this.data[\"delete\"](key);\n  };\n  return HashMap;\n}();\nexport { HashMap };\nexport function createHashMap(obj) {\n  return new HashMap(obj);\n}\nexport function concatArray(a, b) {\n  var newArray = new a.constructor(a.length + b.length);\n  for (var i = 0; i < a.length; i++) {\n    newArray[i] = a[i];\n  }\n  var offset = a.length;\n  for (var i = 0; i < b.length; i++) {\n    newArray[i + offset] = b[i];\n  }\n  return newArray;\n}\nexport function createObject(proto, properties) {\n  var obj;\n  if (Object.create) {\n    obj = Object.create(proto);\n  } else {\n    var StyleCtor = function () {};\n    StyleCtor.prototype = proto;\n    obj = new StyleCtor();\n  }\n  if (properties) {\n    extend(obj, properties);\n  }\n  return obj;\n}\nexport function disableUserSelect(dom) {\n  var domStyle = dom.style;\n  domStyle.webkitUserSelect = 'none';\n  domStyle.userSelect = 'none';\n  domStyle.webkitTapHighlightColor = 'rgba(0,0,0,0)';\n  domStyle['-webkit-touch-callout'] = 'none';\n}\nexport function hasOwn(own, prop) {\n  return own.hasOwnProperty(prop);\n}\nexport function noop() {}\nexport var RADIAN_TO_DEGREE = 180 / Math.PI;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}