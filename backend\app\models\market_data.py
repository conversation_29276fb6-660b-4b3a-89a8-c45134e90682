"""
市场数据相关模型（用于InfluxDB的数据结构定义）
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Optional

@dataclass
class DailyData:
    """日线数据结构"""
    ts_code: str
    trade_date: str
    open: float
    high: float
    low: float
    close: float
    pre_close: float
    change: float
    pct_chg: float
    vol: float  # 成交量(手)
    amount: float  # 成交额(千元)
    
    def to_influx_point(self, measurement: str = "daily_data"):
        """转换为InfluxDB数据点格式"""
        return {
            "measurement": measurement,
            "tags": {
                "ts_code": self.ts_code,
                "market": "A股" if self.ts_code.endswith(('.SZ', '.SH')) else "港股"
            },
            "time": self.trade_date,
            "fields": {
                "open": self.open,
                "high": self.high,
                "low": self.low,
                "close": self.close,
                "pre_close": self.pre_close,
                "change": self.change,
                "pct_chg": self.pct_chg,
                "vol": self.vol,
                "amount": self.amount
            }
        }

@dataclass
class MinuteData:
    """分钟线数据结构"""
    ts_code: str
    trade_time: str
    open: float
    high: float
    low: float
    close: float
    vol: float
    amount: float
    
    def to_influx_point(self, measurement: str = "minute_data"):
        """转换为InfluxDB数据点格式"""
        return {
            "measurement": measurement,
            "tags": {
                "ts_code": self.ts_code,
                "market": "A股" if self.ts_code.endswith(('.SZ', '.SH')) else "港股"
            },
            "time": self.trade_time,
            "fields": {
                "open": self.open,
                "high": self.high,
                "low": self.low,
                "close": self.close,
                "vol": self.vol,
                "amount": self.amount
            }
        }

@dataclass
class TechnicalIndicator:
    """技术指标数据结构"""
    ts_code: str
    trade_date: str
    indicator_name: str
    value: float
    signal: Optional[str] = None  # BUY/SELL/HOLD
    
    def to_influx_point(self, measurement: str = "technical_indicators"):
        """转换为InfluxDB数据点格式"""
        fields = {
            "value": self.value
        }
        if self.signal:
            fields["signal"] = self.signal
            
        return {
            "measurement": measurement,
            "tags": {
                "ts_code": self.ts_code,
                "indicator": self.indicator_name
            },
            "time": self.trade_date,
            "fields": fields
        }

@dataclass
class RiskMetric:
    """风险指标数据结构"""
    ts_code: str
    trade_date: str
    metric_name: str
    value: float
    threshold: Optional[float] = None
    status: str = "NORMAL"  # NORMAL/WARNING/DANGER
    
    def to_influx_point(self, measurement: str = "risk_metrics"):
        """转换为InfluxDB数据点格式"""
        fields = {
            "value": self.value,
            "status": self.status
        }
        if self.threshold:
            fields["threshold"] = self.threshold
            
        return {
            "measurement": measurement,
            "tags": {
                "ts_code": self.ts_code,
                "metric": self.metric_name
            },
            "time": self.trade_date,
            "fields": fields
        }

@dataclass
class MarketSentiment:
    """市场情绪数据结构"""
    ts_code: str
    timestamp: str
    sentiment_score: float  # -1 到 1
    sentiment_label: str    # POSITIVE/NEGATIVE/NEUTRAL
    confidence: float
    source: str  # 数据来源
    
    def to_influx_point(self, measurement: str = "market_sentiment"):
        """转换为InfluxDB数据点格式"""
        return {
            "measurement": measurement,
            "tags": {
                "ts_code": self.ts_code,
                "source": self.source,
                "sentiment_label": self.sentiment_label
            },
            "time": self.timestamp,
            "fields": {
                "sentiment_score": self.sentiment_score,
                "confidence": self.confidence
            }
        }
