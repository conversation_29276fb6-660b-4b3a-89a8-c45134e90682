{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ver = exports.clear = exports.bind = void 0;\nvar _sensorPool = require(\"./sensorPool\");\n/**\n * Created by hustcc on 18/6/9.[高考时间]\n * Contract: <EMAIL>\n */\n\n/**\n * bind an element with resize callback function\n * @param {*} element\n * @param {*} cb\n */\nvar bind = function bind(element, cb) {\n  var sensor = (0, _sensorPool.getSensor)(element);\n\n  // listen with callback\n  sensor.bind(cb);\n\n  // return unbind function\n  return function () {\n    sensor.unbind(cb);\n  };\n};\n\n/**\n * clear all the listener and sensor of an element\n * @param element\n */\nexports.bind = bind;\nvar clear = function clear(element) {\n  var sensor = (0, _sensorPool.getSensor)(element);\n  (0, _sensorPool.removeSensor)(sensor);\n};\nexports.clear = clear;\nvar ver = \"1.0.2\";\nexports.ver = ver;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}