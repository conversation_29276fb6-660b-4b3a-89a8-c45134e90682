"""
简化的性能测试
"""
import sys
import os
import time

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_cache_manager():
    """测试缓存管理器"""
    print("=== 测试缓存管理器 ===")
    
    try:
        from core.cache_manager import CacheManager
        
        cache = CacheManager(max_size=100, default_ttl=10)
        
        # 测试基本操作
        print("测试基本缓存操作...")
        
        # 设置缓存
        cache.set("key1", "value1")
        cache.set("key2", {"data": "complex_value"})
        cache.set("key3", [1, 2, 3, 4, 5])
        
        # 获取缓存
        value1 = cache.get("key1")
        value2 = cache.get("key2")
        value3 = cache.get("key3")
        
        print(f"缓存测试结果:")
        print(f"  key1: {value1}")
        print(f"  key2: {value2}")
        print(f"  key3: {value3}")
        
        # 测试缓存统计
        stats = cache.get_stats()
        print(f"\n缓存统计:")
        print(f"  缓存大小: {stats['cache_size']}")
        print(f"  命中率: {stats['hit_rate']:.2%}")
        print(f"  总命中: {stats['total_hits']}")
        print(f"  总未命中: {stats['total_misses']}")
        
        return True
        
    except Exception as e:
        print(f"缓存管理器测试失败: {e}")
        return False

def test_system_metrics():
    """测试系统指标获取"""
    print("\n=== 测试系统指标获取 ===")
    
    try:
        import psutil
        
        # 获取基本系统指标
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        print("系统性能指标:")
        print(f"  CPU使用率: {cpu_percent:.1f}%")
        print(f"  内存使用率: {memory.percent:.1f}%")
        print(f"  可用内存: {memory.available / (1024**3):.2f} GB")
        print(f"  磁盘使用率: {disk.percent:.1f}%")
        print(f"  磁盘可用空间: {disk.free / (1024**3):.2f} GB")
        
        return True
        
    except Exception as e:
        print(f"系统指标测试失败: {e}")
        return False

def test_performance_service():
    """测试性能服务"""
    print("\n=== 测试性能服务 ===")
    
    try:
        from app.services.performance_service import PerformanceService
        
        service = PerformanceService()
        
        # 获取系统指标
        system_metrics = service.get_system_metrics()
        
        if 'error' in system_metrics:
            print(f"获取系统指标失败: {system_metrics['error']}")
            return False
        
        print("性能服务系统指标:")
        print(f"  CPU使用率: {system_metrics['cpu']['usage_percent']:.1f}%")
        print(f"  内存使用率: {system_metrics['memory']['usage_percent']:.1f}%")
        print(f"  磁盘使用率: {system_metrics['disk']['usage_percent']:.1f}%")
        
        # 获取缓存指标
        cache_metrics = service.get_cache_metrics()
        
        if 'error' not in cache_metrics:
            print(f"\n缓存性能指标:")
            print(f"  命中率: {cache_metrics['efficiency']['hit_rate']:.2%}")
            print(f"  效率评分: {cache_metrics['efficiency']['efficiency_score']:.1f}")
        
        return True
        
    except Exception as e:
        print(f"性能服务测试失败: {e}")
        return False

def test_ai_integration_status():
    """测试AI集成状态"""
    print("\n=== 测试AI集成状态 ===")
    
    try:
        from ai.deepseek_client import get_deepseek_client
        from app.services.ai_service import AIService
        
        # 测试DeepSeek客户端
        client = get_deepseek_client()
        if client:
            print("✅ DeepSeek客户端已初始化")
            stats = client.get_usage_stats()
            print(f"  API调用次数: {stats['call_count']}")
            print(f"  总Token数: {stats['total_tokens']}")
        else:
            print("⚠️  DeepSeek API Key未配置，使用模拟模式")
        
        # 测试AI服务
        ai_service = AIService()
        print("✅ AI服务已初始化")
        
        # 测试智能选股功能
        test_stocks = ['000001.SZ', '000002.SZ', '600000.SH']
        selection_result = ai_service.intelligent_stock_selection(test_stocks, top_n=2)
        
        if selection_result['success']:
            print("✅ 智能选股功能正常")
            print(f"  推荐股票数量: {len(selection_result.get('selected_stocks', []))}")
        else:
            print(f"⚠️  智能选股功能异常: {selection_result.get('error', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"AI集成状态测试失败: {e}")
        return False

def test_websocket_status():
    """测试WebSocket状态"""
    print("\n=== 测试WebSocket状态 ===")
    
    try:
        from app.websocket.websocket_handler import get_websocket_handler
        
        handler = get_websocket_handler()
        print("✅ WebSocket处理器已初始化")
        
        # 获取连接统计
        stats = handler.manager.get_connection_stats()
        print(f"  当前连接数: {stats['active_connections']}")
        print(f"  总连接数: {stats['total_connections']}")
        print(f"  消息发送数: {stats['messages_sent']}")
        
        return True
        
    except Exception as e:
        print(f"WebSocket状态测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始简化性能测试")
    print("=" * 50)
    
    tests = [
        ("缓存管理器", test_cache_manager),
        ("系统指标获取", test_system_metrics),
        ("性能服务", test_performance_service),
        ("AI集成状态", test_ai_integration_status),
        ("WebSocket状态", test_websocket_status)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{status} {test_name} 测试完成\n")
        except Exception as e:
            print(f"❌ {test_name} 测试失败: {e}\n")
            results.append((test_name, False))
    
    # 汇总结果
    print("=" * 50)
    print("📊 测试结果汇总:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count >= 4:
        print("\n🎉 系统核心功能测试通过！")
        print("💡 主要功能状态:")
        print("- 缓存管理 ✅")
        print("- 系统监控 ✅")
        print("- AI功能集成 ✅")
        print("- WebSocket通信 ✅")
        print("- 性能优化 ✅")
    else:
        print("\n⚠️  部分功能需要检查")

if __name__ == "__main__":
    main()
