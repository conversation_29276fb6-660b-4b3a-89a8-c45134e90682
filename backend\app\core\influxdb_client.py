"""
InfluxDB客户端配置
"""
from influxdb_client import InfluxDBClient, Point, WritePrecision
from influxdb_client.client.write_api import SYNCHRONOUS
from app.core.config import settings
import logging
from typing import List, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class InfluxDBManager:
    """InfluxDB管理器"""
    
    def __init__(self):
        self.client = None
        self.write_api = None
        self.query_api = None
        self._connect()
    
    def _connect(self):
        """连接到InfluxDB"""
        try:
            self.client = InfluxDBClient(
                url=settings.INFLUXDB_URL,
                token=settings.INFLUXDB_TOKEN,
                org=settings.INFLUXDB_ORG
            )
            self.write_api = self.client.write_api(write_options=SYNCHRONOUS)
            self.query_api = self.client.query_api()
            logger.info("✅ InfluxDB连接成功")
        except Exception as e:
            logger.error(f"❌ InfluxDB连接失败: {e}")
            # 在开发阶段，如果InfluxDB未启动，不抛出异常
            logger.warning("InfluxDB未连接，时序数据功能将不可用")
    
    def write_points(self, points: List[Dict[str, Any]], bucket: str = None):
        """写入数据点"""
        if not self.write_api:
            logger.warning("InfluxDB未连接，跳过数据写入")
            return False
        
        try:
            bucket = bucket or settings.INFLUXDB_BUCKET
            
            # 转换为InfluxDB Point对象
            influx_points = []
            for point_data in points:
                point = Point(point_data["measurement"])
                
                # 添加标签
                for tag_key, tag_value in point_data.get("tags", {}).items():
                    point = point.tag(tag_key, tag_value)
                
                # 添加字段
                for field_key, field_value in point_data.get("fields", {}).items():
                    point = point.field(field_key, field_value)
                
                # 设置时间
                if "time" in point_data:
                    point = point.time(point_data["time"])
                
                influx_points.append(point)
            
            self.write_api.write(bucket=bucket, record=influx_points)
            logger.debug(f"写入 {len(influx_points)} 个数据点到 {bucket}")
            return True
            
        except Exception as e:
            logger.error(f"数据写入失败: {e}")
            return False
    
    def query_data(self, query: str, bucket: str = None) -> List[Dict]:
        """查询数据"""
        if not self.query_api:
            logger.warning("InfluxDB未连接，无法查询数据")
            return []
        
        try:
            bucket = bucket or settings.INFLUXDB_BUCKET
            
            # 如果查询中没有指定bucket，自动添加
            if "from(bucket:" not in query:
                query = f'from(bucket:"{bucket}") |> {query}'
            
            result = self.query_api.query(query)
            
            # 转换结果为字典列表
            data = []
            for table in result:
                for record in table.records:
                    data.append({
                        "time": record.get_time(),
                        "measurement": record.get_measurement(),
                        "field": record.get_field(),
                        "value": record.get_value(),
                        **record.values
                    })
            
            return data
            
        except Exception as e:
            logger.error(f"数据查询失败: {e}")
            return []
    
    def get_latest_data(self, measurement: str, ts_code: str, field: str = "_value") -> Dict:
        """获取最新数据"""
        query = f'''
        from(bucket:"{settings.INFLUXDB_BUCKET}")
        |> range(start: -7d)
        |> filter(fn: (r) => r._measurement == "{measurement}")
        |> filter(fn: (r) => r.ts_code == "{ts_code}")
        |> filter(fn: (r) => r._field == "{field}")
        |> last()
        '''
        
        result = self.query_data(query)
        return result[0] if result else {}
    
    def get_time_series(self, measurement: str, ts_code: str, start_time: str, end_time: str = None) -> List[Dict]:
        """获取时间序列数据"""
        end_clause = f'stop: {end_time}' if end_time else ''
        
        query = f'''
        from(bucket:"{settings.INFLUXDB_BUCKET}")
        |> range(start: {start_time} {end_clause})
        |> filter(fn: (r) => r._measurement == "{measurement}")
        |> filter(fn: (r) => r.ts_code == "{ts_code}")
        |> sort(columns: ["_time"])
        '''
        
        return self.query_data(query)
    
    def close(self):
        """关闭连接"""
        if self.client:
            self.client.close()
            logger.info("InfluxDB连接已关闭")

# 全局InfluxDB管理器实例
influx_manager = InfluxDBManager()

def get_influx_manager() -> InfluxDBManager:
    """获取InfluxDB管理器实例"""
    return influx_manager
