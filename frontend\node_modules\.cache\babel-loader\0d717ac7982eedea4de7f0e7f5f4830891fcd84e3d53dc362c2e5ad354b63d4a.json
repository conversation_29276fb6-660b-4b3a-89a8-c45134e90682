{"ast": null, "code": "import { isArray, isNumber, map } from '../core/util.js';\nexport function normalizeLineDash(lineType, lineWidth) {\n  if (!lineType || lineType === 'solid' || !(lineWidth > 0)) {\n    return null;\n  }\n  return lineType === 'dashed' ? [4 * lineWidth, 2 * lineWidth] : lineType === 'dotted' ? [lineWidth] : isNumber(lineType) ? [lineType] : isArray(lineType) ? lineType : null;\n}\nexport function getLineDash(el) {\n  var style = el.style;\n  var lineDash = style.lineDash && style.lineWidth > 0 && normalizeLineDash(style.lineDash, style.lineWidth);\n  var lineDashOffset = style.lineDashOffset;\n  if (lineDash) {\n    var lineScale_1 = style.strokeNoScale && el.getLineScale ? el.getLineScale() : 1;\n    if (lineScale_1 && lineScale_1 !== 1) {\n      lineDash = map(lineDash, function (rawVal) {\n        return rawVal / lineScale_1;\n      });\n      lineDashOffset /= lineScale_1;\n    }\n  }\n  return [lineDash, lineDashOffset];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}