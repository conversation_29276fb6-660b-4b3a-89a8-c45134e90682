const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');

// 导入服务管理器
const ServiceManager = require('./src/services/service-manager');
const ConfigManager = require('./src/services/config-manager');
const HealthChecker = require('./src/services/health-checker');

class QuantTradingApp {
  constructor() {
    this.mainWindow = null;
    this.serviceManager = new ServiceManager();
    this.configManager = new ConfigManager();
    this.healthChecker = new HealthChecker(this.serviceManager);
    this.isDev = process.argv.includes('--dev');
  }

  async initialize() {
    // 设置应用事件监听
    this.setupAppEvents();
    
    // 设置IPC通信
    this.setupIPC();
    
    // 创建主窗口
    await this.createMainWindow();
    
    // 初始化配置
    await this.configManager.initialize();
    
    // 检查首次运行
    await this.checkFirstRun();
  }

  setupAppEvents() {
    // 应用准备就绪
    app.whenReady().then(() => {
      this.initialize();
    });

    // 所有窗口关闭
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        this.cleanup();
        app.quit();
      }
    });

    // 应用激活
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createMainWindow();
      }
    });

    // 应用退出前
    app.on('before-quit', () => {
      this.cleanup();
    });
  }

  async createMainWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1000,
      height: 700,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js')
      },
      icon: path.join(__dirname, 'assets/icon.png'),
      show: false,
      titleBarStyle: 'default',
      autoHideMenuBar: true
    });

    // 加载主页面
    await this.mainWindow.loadFile('src/renderer/index.html');

    // 窗口准备显示
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
      
      // 开发模式下打开开发者工具
      if (this.isDev) {
        this.mainWindow.webContents.openDevTools();
      }
    });

    // 窗口关闭事件
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // 阻止外部链接在应用内打开
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });
  }

  setupIPC() {
    // 获取应用信息
    ipcMain.handle('get-app-info', () => {
      return {
        version: app.getVersion(),
        name: app.getName(),
        isDev: this.isDev
      };
    });

    // 配置管理
    ipcMain.handle('load-config', () => {
      return this.configManager.loadConfig();
    });

    ipcMain.handle('save-config', async (event, config) => {
      return await this.configManager.saveConfig(config);
    });

    ipcMain.handle('validate-token', async (event, source, token) => {
      return await this.configManager.validateToken(source, token);
    });

    // 服务管理
    ipcMain.handle('start-services', async () => {
      return await this.serviceManager.startAllServices();
    });

    ipcMain.handle('stop-services', async () => {
      return await this.serviceManager.stopAllServices();
    });

    ipcMain.handle('get-service-status', () => {
      return this.serviceManager.getServiceStatus();
    });

    ipcMain.handle('restart-services', async () => {
      await this.serviceManager.stopAllServices();
      return await this.serviceManager.startAllServices();
    });

    // 健康检查
    ipcMain.handle('start-health-monitoring', () => {
      return this.healthChecker.startMonitoring();
    });

    ipcMain.handle('stop-health-monitoring', () => {
      return this.healthChecker.stopMonitoring();
    });

    // 打开外部链接
    ipcMain.handle('open-external', (event, url) => {
      shell.openExternal(url);
    });

    // 显示消息框
    ipcMain.handle('show-message', (event, options) => {
      return dialog.showMessageBox(this.mainWindow, options);
    });

    // 页面导航
    ipcMain.handle('navigate-to', (event, page) => {
      const pagePath = path.join(__dirname, 'src/renderer', `${page}.html`);
      if (fs.existsSync(pagePath)) {
        this.mainWindow.loadFile(pagePath);
        return true;
      }
      return false;
    });

    // 获取日志
    ipcMain.handle('get-logs', () => {
      return this.serviceManager.getLogs();
    });
  }

  async checkFirstRun() {
    const config = await this.configManager.loadConfig();
    if (config.system.first_run) {
      // 首次运行，显示欢迎信息
      setTimeout(() => {
        this.mainWindow.webContents.send('first-run-welcome');
      }, 1000);
    }
  }

  async cleanup() {
    try {
      // 停止健康监控
      this.healthChecker.stopMonitoring();
      
      // 停止所有服务
      await this.serviceManager.stopAllServices();
      
      console.log('应用清理完成');
    } catch (error) {
      console.error('应用清理失败:', error);
    }
  }
}

// 创建应用实例
const quantApp = new QuantTradingApp();

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  dialog.showErrorBox('应用错误', `发生未预期的错误: ${error.message}`);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});

// 导出应用实例（用于测试）
module.exports = quantApp;
