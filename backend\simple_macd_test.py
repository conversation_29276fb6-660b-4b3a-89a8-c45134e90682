"""
简化的MACD测试
"""
import pandas as pd
import numpy as np
import ta

# 创建测试数据
np.random.seed(42)
dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
prices = [10.0]

for i in range(29):
    change = np.random.uniform(-0.02, 0.02)
    price = prices[-1] * (1 + change)
    prices.append(price)

data = pd.DataFrame({
    'trade_date': [d.strftime('%Y%m%d') for d in dates],
    'close': prices
})

print("测试数据:")
print(data.head())

try:
    print("\n测试MACD计算...")
    
    # 使用ta库计算MACD
    macd_indicator = ta.trend.MACD(data['close'], window_fast=12, window_slow=26, window_sign=9)
    
    macd_line = macd_indicator.macd()
    macd_signal = macd_indicator.macd_signal()
    macd_histogram = macd_indicator.macd_diff()
    
    print("✅ MACD计算成功")
    print(f"MACD最新值: {macd_line.iloc[-1]:.4f}")
    print(f"Signal最新值: {macd_signal.iloc[-1]:.4f}")
    print(f"Histogram最新值: {macd_histogram.iloc[-1]:.4f}")
    
except Exception as e:
    print(f"❌ MACD计算失败: {e}")

# 测试手动MACD计算
try:
    print("\n测试手动MACD计算...")
    
    # 计算EMA
    ema12 = data['close'].ewm(span=12).mean()
    ema26 = data['close'].ewm(span=26).mean()
    
    # MACD线
    macd_manual = ema12 - ema26
    
    # 信号线
    signal_manual = macd_manual.ewm(span=9).mean()
    
    # 柱状图
    histogram_manual = macd_manual - signal_manual
    
    print("✅ 手动MACD计算成功")
    print(f"手动MACD最新值: {macd_manual.iloc[-1]:.4f}")
    print(f"手动Signal最新值: {signal_manual.iloc[-1]:.4f}")
    print(f"手动Histogram最新值: {histogram_manual.iloc[-1]:.4f}")
    
except Exception as e:
    print(f"❌ 手动MACD计算失败: {e}")
