"""
技术指标计算测试
"""
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy.indicators import get_technical_indicators

def create_sample_data():
    """创建示例数据"""
    # 生成30天的模拟股价数据
    dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
    
    # 模拟价格走势
    np.random.seed(42)
    base_price = 10.0
    prices = []
    
    for i in range(30):
        if i == 0:
            price = base_price
        else:
            # 随机波动 -3% 到 +3%
            change = np.random.uniform(-0.03, 0.03)
            price = prices[-1] * (1 + change)
        prices.append(price)
    
    # 创建OHLC数据
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        high = close * np.random.uniform(1.001, 1.02)
        low = close * np.random.uniform(0.98, 0.999)
        open_price = close * np.random.uniform(0.99, 1.01)
        volume = np.random.randint(100000, 1000000)
        
        data.append({
            'trade_date': date.strftime('%Y%m%d'),
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2),
            'vol': volume,
            'amount': volume * close
        })
    
    return pd.DataFrame(data)

def test_moving_averages():
    """测试移动平均线"""
    print("=== 测试移动平均线 ===")
    
    calculator = get_technical_indicators()
    data = create_sample_data()
    
    result = calculator.calculate_ma(data, [5, 10, 20])
    
    print(f"数据长度: {len(result)}")
    print("最新5条MA数据:")
    ma_columns = ['trade_date', 'close', 'ma_5', 'ma_10', 'ma_20']
    print(result[ma_columns].tail().to_string())
    
    # 验证MA计算
    manual_ma5 = data['close'].tail(5).mean()
    calculated_ma5 = result['ma_5'].iloc[-1]
    
    print(f"\n验证MA5计算:")
    print(f"手动计算: {manual_ma5:.4f}")
    print(f"程序计算: {calculated_ma5:.4f}")
    print(f"差异: {abs(manual_ma5 - calculated_ma5):.6f}")
    
    return abs(manual_ma5 - calculated_ma5) < 0.001

def test_macd():
    """测试MACD指标"""
    print("\n=== 测试MACD指标 ===")
    
    calculator = get_technical_indicators()
    data = create_sample_data()
    
    result = calculator.calculate_macd(data)
    
    print("最新5条MACD数据:")
    macd_columns = ['trade_date', 'close', 'macd', 'macd_signal', 'macd_histogram', 'macd_signal_flag']
    print(result[macd_columns].tail().to_string())
    
    # 检查MACD值是否合理
    latest = result.iloc[-1]
    macd_valid = pd.notna(latest['macd']) and abs(latest['macd']) < 10
    
    print(f"\nMACD值验证: {macd_valid}")
    print(f"MACD: {latest['macd']:.4f}")
    print(f"Signal: {latest['macd_signal']:.4f}")
    print(f"Histogram: {latest['macd_histogram']:.4f}")
    print(f"交易信号: {latest['macd_signal_flag']}")
    
    return macd_valid

def test_rsi():
    """测试RSI指标"""
    print("\n=== 测试RSI指标 ===")
    
    calculator = get_technical_indicators()
    data = create_sample_data()
    
    result = calculator.calculate_rsi(data)
    
    print("最新5条RSI数据:")
    rsi_columns = ['trade_date', 'close', 'rsi', 'rsi_signal']
    print(result[rsi_columns].tail().to_string())
    
    # 检查RSI值是否在0-100范围内
    latest_rsi = result['rsi'].iloc[-1]
    rsi_valid = pd.notna(latest_rsi) and 0 <= latest_rsi <= 100
    
    print(f"\nRSI值验证: {rsi_valid}")
    print(f"RSI: {latest_rsi:.2f}")
    print(f"交易信号: {result['rsi_signal'].iloc[-1]}")
    
    return rsi_valid

def test_bollinger_bands():
    """测试布林带"""
    print("\n=== 测试布林带 ===")
    
    calculator = get_technical_indicators()
    data = create_sample_data()
    
    result = calculator.calculate_bollinger_bands(data)
    
    print("最新5条布林带数据:")
    bb_columns = ['trade_date', 'close', 'bb_upper', 'bb_middle', 'bb_lower', 'bb_signal']
    print(result[bb_columns].tail().to_string())
    
    # 检查布林带逻辑：上轨 > 中轨 > 下轨
    latest = result.iloc[-1]
    bb_valid = (pd.notna(latest['bb_upper']) and 
                pd.notna(latest['bb_middle']) and 
                pd.notna(latest['bb_lower']) and
                latest['bb_upper'] > latest['bb_middle'] > latest['bb_lower'])
    
    print(f"\n布林带逻辑验证: {bb_valid}")
    print(f"上轨: {latest['bb_upper']:.4f}")
    print(f"中轨: {latest['bb_middle']:.4f}")
    print(f"下轨: {latest['bb_lower']:.4f}")
    print(f"交易信号: {latest['bb_signal']}")
    
    return bb_valid

def test_all_indicators():
    """测试所有指标"""
    print("\n=== 测试所有指标综合计算 ===")
    
    calculator = get_technical_indicators()
    data = create_sample_data()
    
    result = calculator.calculate_all_indicators(data)
    
    print(f"计算完成，数据长度: {len(result)}")
    print(f"指标列数: {len(result.columns)}")
    
    # 显示最新数据的主要指标
    latest = result.iloc[-1]
    
    print("\n最新指标值:")
    print(f"收盘价: {latest['close']:.2f}")
    print(f"MA5: {latest.get('ma_5', 'N/A')}")
    print(f"MA20: {latest.get('ma_20', 'N/A')}")
    print(f"MACD: {latest.get('macd', 'N/A')}")
    print(f"RSI: {latest.get('rsi', 'N/A')}")
    print(f"布林带宽度: {latest.get('bb_width', 'N/A')}")
    print(f"综合信号: {latest.get('综合信号', 'N/A')}")
    
    # 统计各指标的信号
    signal_columns = ['macd_signal_flag', 'rsi_signal', 'bb_signal', 'kdj_signal']
    signals = {}
    
    for col in signal_columns:
        if col in result.columns:
            latest_signal = result[col].iloc[-1]
            signals[col] = latest_signal
    
    print(f"\n各指标信号: {signals}")
    
    return len(result.columns) > 20  # 应该有很多指标列

def test_performance():
    """测试计算性能"""
    print("\n=== 测试计算性能 ===")
    
    import time
    
    calculator = get_technical_indicators()
    
    # 测试不同数据量的计算时间
    data_sizes = [30, 60, 120, 250]  # 1个月、2个月、4个月、1年
    
    for size in data_sizes:
        # 生成指定大小的数据
        dates = pd.date_range(start='2023-01-01', periods=size, freq='D')
        np.random.seed(42)
        
        data = []
        base_price = 10.0
        for i, date in enumerate(dates):
            price = base_price * (1 + np.random.uniform(-0.02, 0.02))
            data.append({
                'trade_date': date.strftime('%Y%m%d'),
                'open': price * 0.99,
                'high': price * 1.01,
                'low': price * 0.98,
                'close': price,
                'vol': np.random.randint(100000, 1000000),
                'amount': np.random.randint(1000000, 10000000)
            })
        
        df = pd.DataFrame(data)
        
        # 计算时间
        start_time = time.time()
        result = calculator.calculate_all_indicators(df)
        end_time = time.time()
        
        calculation_time = end_time - start_time
        print(f"数据量 {size} 条，计算时间: {calculation_time:.3f} 秒")
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始技术指标计算测试")
    print("=" * 60)
    
    tests = [
        ("移动平均线", test_moving_averages),
        ("MACD指标", test_macd),
        ("RSI指标", test_rsi),
        ("布林带", test_bollinger_bands),
        ("所有指标", test_all_indicators),
        ("性能测试", test_performance)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✅ {test_name} 测试完成")
        except Exception as e:
            print(f"❌ {test_name} 测试失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count >= 4:
        print("\n🎉 技术指标计算引擎测试通过！")
        print("💡 功能特点:")
        print("- 支持常用技术指标计算")
        print("- 自动生成交易信号")
        print("- 综合信号判断")
        print("- 高效的批量计算")
    else:
        print("\n⚠️  部分测试失败，请检查指标计算逻辑")

if __name__ == "__main__":
    main()
