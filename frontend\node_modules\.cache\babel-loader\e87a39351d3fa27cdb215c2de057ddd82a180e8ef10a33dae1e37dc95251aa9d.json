{"ast": null, "code": "import { devicePixelRatio } from '../config.js';\nimport * as util from '../core/util.js';\nimport Layer from './Layer.js';\nimport requestAnimationFrame from '../animation/requestAnimationFrame.js';\nimport env from '../core/env.js';\nimport { brush, brushSingle } from './graphic.js';\nimport { REDRAW_BIT } from '../graphic/constants.js';\nimport { getSize } from './helper.js';\nvar HOVER_LAYER_ZLEVEL = 1e5;\nvar CANVAS_ZLEVEL = 314159;\nvar EL_AFTER_INCREMENTAL_INC = 0.01;\nvar INCREMENTAL_INC = 0.001;\nfunction isLayerValid(layer) {\n  if (!layer) {\n    return false;\n  }\n  if (layer.__builtin__) {\n    return true;\n  }\n  if (typeof layer.resize !== 'function' || typeof layer.refresh !== 'function') {\n    return false;\n  }\n  return true;\n}\nfunction createRoot(width, height) {\n  var domRoot = document.createElement('div');\n  domRoot.style.cssText = ['position:relative', 'width:' + width + 'px', 'height:' + height + 'px', 'padding:0', 'margin:0', 'border-width:0'].join(';') + ';';\n  return domRoot;\n}\nvar CanvasPainter = function () {\n  function CanvasPainter(root, storage, opts, id) {\n    this.type = 'canvas';\n    this._zlevelList = [];\n    this._prevDisplayList = [];\n    this._layers = {};\n    this._layerConfig = {};\n    this._needsManuallyCompositing = false;\n    this.type = 'canvas';\n    var singleCanvas = !root.nodeName || root.nodeName.toUpperCase() === 'CANVAS';\n    this._opts = opts = util.extend({}, opts || {});\n    this.dpr = opts.devicePixelRatio || devicePixelRatio;\n    this._singleCanvas = singleCanvas;\n    this.root = root;\n    var rootStyle = root.style;\n    if (rootStyle) {\n      util.disableUserSelect(root);\n      root.innerHTML = '';\n    }\n    this.storage = storage;\n    var zlevelList = this._zlevelList;\n    this._prevDisplayList = [];\n    var layers = this._layers;\n    if (!singleCanvas) {\n      this._width = getSize(root, 0, opts);\n      this._height = getSize(root, 1, opts);\n      var domRoot = this._domRoot = createRoot(this._width, this._height);\n      root.appendChild(domRoot);\n    } else {\n      var rootCanvas = root;\n      var width = rootCanvas.width;\n      var height = rootCanvas.height;\n      if (opts.width != null) {\n        width = opts.width;\n      }\n      if (opts.height != null) {\n        height = opts.height;\n      }\n      this.dpr = opts.devicePixelRatio || 1;\n      rootCanvas.width = width * this.dpr;\n      rootCanvas.height = height * this.dpr;\n      this._width = width;\n      this._height = height;\n      var mainLayer = new Layer(rootCanvas, this, this.dpr);\n      mainLayer.__builtin__ = true;\n      mainLayer.initContext();\n      layers[CANVAS_ZLEVEL] = mainLayer;\n      mainLayer.zlevel = CANVAS_ZLEVEL;\n      zlevelList.push(CANVAS_ZLEVEL);\n      this._domRoot = root;\n    }\n  }\n  CanvasPainter.prototype.getType = function () {\n    return 'canvas';\n  };\n  CanvasPainter.prototype.isSingleCanvas = function () {\n    return this._singleCanvas;\n  };\n  CanvasPainter.prototype.getViewportRoot = function () {\n    return this._domRoot;\n  };\n  CanvasPainter.prototype.getViewportRootOffset = function () {\n    var viewportRoot = this.getViewportRoot();\n    if (viewportRoot) {\n      return {\n        offsetLeft: viewportRoot.offsetLeft || 0,\n        offsetTop: viewportRoot.offsetTop || 0\n      };\n    }\n  };\n  CanvasPainter.prototype.refresh = function (paintAll) {\n    var list = this.storage.getDisplayList(true);\n    var prevList = this._prevDisplayList;\n    var zlevelList = this._zlevelList;\n    this._redrawId = Math.random();\n    this._paintList(list, prevList, paintAll, this._redrawId);\n    for (var i = 0; i < zlevelList.length; i++) {\n      var z = zlevelList[i];\n      var layer = this._layers[z];\n      if (!layer.__builtin__ && layer.refresh) {\n        var clearColor = i === 0 ? this._backgroundColor : null;\n        layer.refresh(clearColor);\n      }\n    }\n    if (this._opts.useDirtyRect) {\n      this._prevDisplayList = list.slice();\n    }\n    return this;\n  };\n  CanvasPainter.prototype.refreshHover = function () {\n    this._paintHoverList(this.storage.getDisplayList(false));\n  };\n  CanvasPainter.prototype._paintHoverList = function (list) {\n    var len = list.length;\n    var hoverLayer = this._hoverlayer;\n    hoverLayer && hoverLayer.clear();\n    if (!len) {\n      return;\n    }\n    var scope = {\n      inHover: true,\n      viewWidth: this._width,\n      viewHeight: this._height\n    };\n    var ctx;\n    for (var i = 0; i < len; i++) {\n      var el = list[i];\n      if (el.__inHover) {\n        if (!hoverLayer) {\n          hoverLayer = this._hoverlayer = this.getLayer(HOVER_LAYER_ZLEVEL);\n        }\n        if (!ctx) {\n          ctx = hoverLayer.ctx;\n          ctx.save();\n        }\n        brush(ctx, el, scope, i === len - 1);\n      }\n    }\n    if (ctx) {\n      ctx.restore();\n    }\n  };\n  CanvasPainter.prototype.getHoverLayer = function () {\n    return this.getLayer(HOVER_LAYER_ZLEVEL);\n  };\n  CanvasPainter.prototype.paintOne = function (ctx, el) {\n    brushSingle(ctx, el);\n  };\n  CanvasPainter.prototype._paintList = function (list, prevList, paintAll, redrawId) {\n    if (this._redrawId !== redrawId) {\n      return;\n    }\n    paintAll = paintAll || false;\n    this._updateLayerStatus(list);\n    var _a = this._doPaintList(list, prevList, paintAll),\n      finished = _a.finished,\n      needsRefreshHover = _a.needsRefreshHover;\n    if (this._needsManuallyCompositing) {\n      this._compositeManually();\n    }\n    if (needsRefreshHover) {\n      this._paintHoverList(list);\n    }\n    if (!finished) {\n      var self_1 = this;\n      requestAnimationFrame(function () {\n        self_1._paintList(list, prevList, paintAll, redrawId);\n      });\n    } else {\n      this.eachLayer(function (layer) {\n        layer.afterBrush && layer.afterBrush();\n      });\n    }\n  };\n  CanvasPainter.prototype._compositeManually = function () {\n    var ctx = this.getLayer(CANVAS_ZLEVEL).ctx;\n    var width = this._domRoot.width;\n    var height = this._domRoot.height;\n    ctx.clearRect(0, 0, width, height);\n    this.eachBuiltinLayer(function (layer) {\n      if (layer.virtual) {\n        ctx.drawImage(layer.dom, 0, 0, width, height);\n      }\n    });\n  };\n  CanvasPainter.prototype._doPaintList = function (list, prevList, paintAll) {\n    var _this = this;\n    var layerList = [];\n    var useDirtyRect = this._opts.useDirtyRect;\n    for (var zi = 0; zi < this._zlevelList.length; zi++) {\n      var zlevel = this._zlevelList[zi];\n      var layer = this._layers[zlevel];\n      if (layer.__builtin__ && layer !== this._hoverlayer && (layer.__dirty || paintAll)) {\n        layerList.push(layer);\n      }\n    }\n    var finished = true;\n    var needsRefreshHover = false;\n    var _loop_1 = function (k) {\n      var layer = layerList[k];\n      var ctx = layer.ctx;\n      var repaintRects = useDirtyRect && layer.createRepaintRects(list, prevList, this_1._width, this_1._height);\n      var start = paintAll ? layer.__startIndex : layer.__drawIndex;\n      var useTimer = !paintAll && layer.incremental && Date.now;\n      var startTime = useTimer && Date.now();\n      var clearColor = layer.zlevel === this_1._zlevelList[0] ? this_1._backgroundColor : null;\n      if (layer.__startIndex === layer.__endIndex) {\n        layer.clear(false, clearColor, repaintRects);\n      } else if (start === layer.__startIndex) {\n        var firstEl = list[start];\n        if (!firstEl.incremental || !firstEl.notClear || paintAll) {\n          layer.clear(false, clearColor, repaintRects);\n        }\n      }\n      if (start === -1) {\n        console.error('For some unknown reason. drawIndex is -1');\n        start = layer.__startIndex;\n      }\n      var i;\n      var repaint = function (repaintRect) {\n        var scope = {\n          inHover: false,\n          allClipped: false,\n          prevEl: null,\n          viewWidth: _this._width,\n          viewHeight: _this._height\n        };\n        for (i = start; i < layer.__endIndex; i++) {\n          var el = list[i];\n          if (el.__inHover) {\n            needsRefreshHover = true;\n          }\n          _this._doPaintEl(el, layer, useDirtyRect, repaintRect, scope, i === layer.__endIndex - 1);\n          if (useTimer) {\n            var dTime = Date.now() - startTime;\n            if (dTime > 15) {\n              break;\n            }\n          }\n        }\n        if (scope.prevElClipPaths) {\n          ctx.restore();\n        }\n      };\n      if (repaintRects) {\n        if (repaintRects.length === 0) {\n          i = layer.__endIndex;\n        } else {\n          var dpr = this_1.dpr;\n          for (var r = 0; r < repaintRects.length; ++r) {\n            var rect = repaintRects[r];\n            ctx.save();\n            ctx.beginPath();\n            ctx.rect(rect.x * dpr, rect.y * dpr, rect.width * dpr, rect.height * dpr);\n            ctx.clip();\n            repaint(rect);\n            ctx.restore();\n          }\n        }\n      } else {\n        ctx.save();\n        repaint();\n        ctx.restore();\n      }\n      layer.__drawIndex = i;\n      if (layer.__drawIndex < layer.__endIndex) {\n        finished = false;\n      }\n    };\n    var this_1 = this;\n    for (var k = 0; k < layerList.length; k++) {\n      _loop_1(k);\n    }\n    if (env.wxa) {\n      util.each(this._layers, function (layer) {\n        if (layer && layer.ctx && layer.ctx.draw) {\n          layer.ctx.draw();\n        }\n      });\n    }\n    return {\n      finished: finished,\n      needsRefreshHover: needsRefreshHover\n    };\n  };\n  CanvasPainter.prototype._doPaintEl = function (el, currentLayer, useDirtyRect, repaintRect, scope, isLast) {\n    var ctx = currentLayer.ctx;\n    if (useDirtyRect) {\n      var paintRect = el.getPaintRect();\n      if (!repaintRect || paintRect && paintRect.intersect(repaintRect)) {\n        brush(ctx, el, scope, isLast);\n        el.setPrevPaintRect(paintRect);\n      }\n    } else {\n      brush(ctx, el, scope, isLast);\n    }\n  };\n  CanvasPainter.prototype.getLayer = function (zlevel, virtual) {\n    if (this._singleCanvas && !this._needsManuallyCompositing) {\n      zlevel = CANVAS_ZLEVEL;\n    }\n    var layer = this._layers[zlevel];\n    if (!layer) {\n      layer = new Layer('zr_' + zlevel, this, this.dpr);\n      layer.zlevel = zlevel;\n      layer.__builtin__ = true;\n      if (this._layerConfig[zlevel]) {\n        util.merge(layer, this._layerConfig[zlevel], true);\n      } else if (this._layerConfig[zlevel - EL_AFTER_INCREMENTAL_INC]) {\n        util.merge(layer, this._layerConfig[zlevel - EL_AFTER_INCREMENTAL_INC], true);\n      }\n      if (virtual) {\n        layer.virtual = virtual;\n      }\n      this.insertLayer(zlevel, layer);\n      layer.initContext();\n    }\n    return layer;\n  };\n  CanvasPainter.prototype.insertLayer = function (zlevel, layer) {\n    var layersMap = this._layers;\n    var zlevelList = this._zlevelList;\n    var len = zlevelList.length;\n    var domRoot = this._domRoot;\n    var prevLayer = null;\n    var i = -1;\n    if (layersMap[zlevel]) {\n      if (process.env.NODE_ENV !== 'production') {\n        util.logError('ZLevel ' + zlevel + ' has been used already');\n      }\n      return;\n    }\n    if (!isLayerValid(layer)) {\n      if (process.env.NODE_ENV !== 'production') {\n        util.logError('Layer of zlevel ' + zlevel + ' is not valid');\n      }\n      return;\n    }\n    if (len > 0 && zlevel > zlevelList[0]) {\n      for (i = 0; i < len - 1; i++) {\n        if (zlevelList[i] < zlevel && zlevelList[i + 1] > zlevel) {\n          break;\n        }\n      }\n      prevLayer = layersMap[zlevelList[i]];\n    }\n    zlevelList.splice(i + 1, 0, zlevel);\n    layersMap[zlevel] = layer;\n    if (!layer.virtual) {\n      if (prevLayer) {\n        var prevDom = prevLayer.dom;\n        if (prevDom.nextSibling) {\n          domRoot.insertBefore(layer.dom, prevDom.nextSibling);\n        } else {\n          domRoot.appendChild(layer.dom);\n        }\n      } else {\n        if (domRoot.firstChild) {\n          domRoot.insertBefore(layer.dom, domRoot.firstChild);\n        } else {\n          domRoot.appendChild(layer.dom);\n        }\n      }\n    }\n    layer.painter || (layer.painter = this);\n  };\n  CanvasPainter.prototype.eachLayer = function (cb, context) {\n    var zlevelList = this._zlevelList;\n    for (var i = 0; i < zlevelList.length; i++) {\n      var z = zlevelList[i];\n      cb.call(context, this._layers[z], z);\n    }\n  };\n  CanvasPainter.prototype.eachBuiltinLayer = function (cb, context) {\n    var zlevelList = this._zlevelList;\n    for (var i = 0; i < zlevelList.length; i++) {\n      var z = zlevelList[i];\n      var layer = this._layers[z];\n      if (layer.__builtin__) {\n        cb.call(context, layer, z);\n      }\n    }\n  };\n  CanvasPainter.prototype.eachOtherLayer = function (cb, context) {\n    var zlevelList = this._zlevelList;\n    for (var i = 0; i < zlevelList.length; i++) {\n      var z = zlevelList[i];\n      var layer = this._layers[z];\n      if (!layer.__builtin__) {\n        cb.call(context, layer, z);\n      }\n    }\n  };\n  CanvasPainter.prototype.getLayers = function () {\n    return this._layers;\n  };\n  CanvasPainter.prototype._updateLayerStatus = function (list) {\n    this.eachBuiltinLayer(function (layer, z) {\n      layer.__dirty = layer.__used = false;\n    });\n    function updatePrevLayer(idx) {\n      if (prevLayer) {\n        if (prevLayer.__endIndex !== idx) {\n          prevLayer.__dirty = true;\n        }\n        prevLayer.__endIndex = idx;\n      }\n    }\n    if (this._singleCanvas) {\n      for (var i_1 = 1; i_1 < list.length; i_1++) {\n        var el = list[i_1];\n        if (el.zlevel !== list[i_1 - 1].zlevel || el.incremental) {\n          this._needsManuallyCompositing = true;\n          break;\n        }\n      }\n    }\n    var prevLayer = null;\n    var incrementalLayerCount = 0;\n    var prevZlevel;\n    var i;\n    for (i = 0; i < list.length; i++) {\n      var el = list[i];\n      var zlevel = el.zlevel;\n      var layer = void 0;\n      if (prevZlevel !== zlevel) {\n        prevZlevel = zlevel;\n        incrementalLayerCount = 0;\n      }\n      if (el.incremental) {\n        layer = this.getLayer(zlevel + INCREMENTAL_INC, this._needsManuallyCompositing);\n        layer.incremental = true;\n        incrementalLayerCount = 1;\n      } else {\n        layer = this.getLayer(zlevel + (incrementalLayerCount > 0 ? EL_AFTER_INCREMENTAL_INC : 0), this._needsManuallyCompositing);\n      }\n      if (!layer.__builtin__) {\n        util.logError('ZLevel ' + zlevel + ' has been used by unkown layer ' + layer.id);\n      }\n      if (layer !== prevLayer) {\n        layer.__used = true;\n        if (layer.__startIndex !== i) {\n          layer.__dirty = true;\n        }\n        layer.__startIndex = i;\n        if (!layer.incremental) {\n          layer.__drawIndex = i;\n        } else {\n          layer.__drawIndex = -1;\n        }\n        updatePrevLayer(i);\n        prevLayer = layer;\n      }\n      if (el.__dirty & REDRAW_BIT && !el.__inHover) {\n        layer.__dirty = true;\n        if (layer.incremental && layer.__drawIndex < 0) {\n          layer.__drawIndex = i;\n        }\n      }\n    }\n    updatePrevLayer(i);\n    this.eachBuiltinLayer(function (layer, z) {\n      if (!layer.__used && layer.getElementCount() > 0) {\n        layer.__dirty = true;\n        layer.__startIndex = layer.__endIndex = layer.__drawIndex = 0;\n      }\n      if (layer.__dirty && layer.__drawIndex < 0) {\n        layer.__drawIndex = layer.__startIndex;\n      }\n    });\n  };\n  CanvasPainter.prototype.clear = function () {\n    this.eachBuiltinLayer(this._clearLayer);\n    return this;\n  };\n  CanvasPainter.prototype._clearLayer = function (layer) {\n    layer.clear();\n  };\n  CanvasPainter.prototype.setBackgroundColor = function (backgroundColor) {\n    this._backgroundColor = backgroundColor;\n    util.each(this._layers, function (layer) {\n      layer.setUnpainted();\n    });\n  };\n  CanvasPainter.prototype.configLayer = function (zlevel, config) {\n    if (config) {\n      var layerConfig = this._layerConfig;\n      if (!layerConfig[zlevel]) {\n        layerConfig[zlevel] = config;\n      } else {\n        util.merge(layerConfig[zlevel], config, true);\n      }\n      for (var i = 0; i < this._zlevelList.length; i++) {\n        var _zlevel = this._zlevelList[i];\n        if (_zlevel === zlevel || _zlevel === zlevel + EL_AFTER_INCREMENTAL_INC) {\n          var layer = this._layers[_zlevel];\n          util.merge(layer, layerConfig[zlevel], true);\n        }\n      }\n    }\n  };\n  CanvasPainter.prototype.delLayer = function (zlevel) {\n    var layers = this._layers;\n    var zlevelList = this._zlevelList;\n    var layer = layers[zlevel];\n    if (!layer) {\n      return;\n    }\n    layer.dom.parentNode.removeChild(layer.dom);\n    delete layers[zlevel];\n    zlevelList.splice(util.indexOf(zlevelList, zlevel), 1);\n  };\n  CanvasPainter.prototype.resize = function (width, height) {\n    if (!this._domRoot.style) {\n      if (width == null || height == null) {\n        return;\n      }\n      this._width = width;\n      this._height = height;\n      this.getLayer(CANVAS_ZLEVEL).resize(width, height);\n    } else {\n      var domRoot = this._domRoot;\n      domRoot.style.display = 'none';\n      var opts = this._opts;\n      var root = this.root;\n      width != null && (opts.width = width);\n      height != null && (opts.height = height);\n      width = getSize(root, 0, opts);\n      height = getSize(root, 1, opts);\n      domRoot.style.display = '';\n      if (this._width !== width || height !== this._height) {\n        domRoot.style.width = width + 'px';\n        domRoot.style.height = height + 'px';\n        for (var id in this._layers) {\n          if (this._layers.hasOwnProperty(id)) {\n            this._layers[id].resize(width, height);\n          }\n        }\n        this.refresh(true);\n      }\n      this._width = width;\n      this._height = height;\n    }\n    return this;\n  };\n  CanvasPainter.prototype.clearLayer = function (zlevel) {\n    var layer = this._layers[zlevel];\n    if (layer) {\n      layer.clear();\n    }\n  };\n  CanvasPainter.prototype.dispose = function () {\n    this.root.innerHTML = '';\n    this.root = this.storage = this._domRoot = this._layers = null;\n  };\n  CanvasPainter.prototype.getRenderedCanvas = function (opts) {\n    opts = opts || {};\n    if (this._singleCanvas && !this._compositeManually) {\n      return this._layers[CANVAS_ZLEVEL].dom;\n    }\n    var imageLayer = new Layer('image', this, opts.pixelRatio || this.dpr);\n    imageLayer.initContext();\n    imageLayer.clear(false, opts.backgroundColor || this._backgroundColor);\n    var ctx = imageLayer.ctx;\n    if (opts.pixelRatio <= this.dpr) {\n      this.refresh();\n      var width_1 = imageLayer.dom.width;\n      var height_1 = imageLayer.dom.height;\n      this.eachLayer(function (layer) {\n        if (layer.__builtin__) {\n          ctx.drawImage(layer.dom, 0, 0, width_1, height_1);\n        } else if (layer.renderToCanvas) {\n          ctx.save();\n          layer.renderToCanvas(ctx);\n          ctx.restore();\n        }\n      });\n    } else {\n      var scope = {\n        inHover: false,\n        viewWidth: this._width,\n        viewHeight: this._height\n      };\n      var displayList = this.storage.getDisplayList(true);\n      for (var i = 0, len = displayList.length; i < len; i++) {\n        var el = displayList[i];\n        brush(ctx, el, scope, i === len - 1);\n      }\n    }\n    return imageLayer.dom;\n  };\n  CanvasPainter.prototype.getWidth = function () {\n    return this._width;\n  };\n  CanvasPainter.prototype.getHeight = function () {\n    return this._height;\n  };\n  return CanvasPainter;\n}();\nexport default CanvasPainter;\n;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}