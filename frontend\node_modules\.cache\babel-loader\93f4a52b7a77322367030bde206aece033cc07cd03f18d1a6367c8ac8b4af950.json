{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\nvar _object = require(\"./object\");\nvar _resizeObserver = require(\"./resizeObserver\");\n/**\n * Created by hustcc on 18/7/5.\n * Contract: <EMAIL>\n */\n\n/**\n * sensor strategies\n */\n// export const createSensor = createObjectSensor;\nvar createSensor = typeof ResizeObserver !== 'undefined' ? _resizeObserver.createSensor : _object.createSensor;\nexports.createSensor = createSensor;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}