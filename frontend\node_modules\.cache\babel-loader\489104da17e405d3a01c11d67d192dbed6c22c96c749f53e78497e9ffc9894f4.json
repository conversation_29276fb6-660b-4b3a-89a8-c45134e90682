{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { indexOf, createHashMap, assert } from 'zrender/lib/core/util.js';\nexport var DATA_ZOOM_AXIS_DIMENSIONS = ['x', 'y', 'radius', 'angle', 'single'];\n// Supported coords.\n// FIXME: polar has been broken (but rarely used).\nvar SERIES_COORDS = ['cartesian2d', 'polar', 'singleAxis'];\nexport function isCoordSupported(seriesModel) {\n  var coordType = seriesModel.get('coordinateSystem');\n  return indexOf(SERIES_COORDS, coordType) >= 0;\n}\nexport function getAxisMainType(axisDim) {\n  if (process.env.NODE_ENV !== 'production') {\n    assert(axisDim);\n  }\n  return axisDim + 'Axis';\n}\nexport function getAxisIndexPropName(axisDim) {\n  if (process.env.NODE_ENV !== 'production') {\n    assert(axisDim);\n  }\n  return axisDim + 'AxisIndex';\n}\nexport function getAxisIdPropName(axisDim) {\n  if (process.env.NODE_ENV !== 'production') {\n    assert(axisDim);\n  }\n  return axisDim + 'AxisId';\n}\n/**\r\n * If two dataZoomModels has the same axis controlled, we say that they are 'linked'.\r\n * This function finds all linked dataZoomModels start from the given payload.\r\n */\nexport function findEffectedDataZooms(ecModel, payload) {\n  // Key: `DataZoomAxisDimension`\n  var axisRecords = createHashMap();\n  var effectedModels = [];\n  // Key: uid of dataZoomModel\n  var effectedModelMap = createHashMap();\n  // Find the dataZooms specified by payload.\n  ecModel.eachComponent({\n    mainType: 'dataZoom',\n    query: payload\n  }, function (dataZoomModel) {\n    if (!effectedModelMap.get(dataZoomModel.uid)) {\n      addToEffected(dataZoomModel);\n    }\n  });\n  // Start from the given dataZoomModels, travel the graph to find\n  // all of the linked dataZoom models.\n  var foundNewLink;\n  do {\n    foundNewLink = false;\n    ecModel.eachComponent('dataZoom', processSingle);\n  } while (foundNewLink);\n  function processSingle(dataZoomModel) {\n    if (!effectedModelMap.get(dataZoomModel.uid) && isLinked(dataZoomModel)) {\n      addToEffected(dataZoomModel);\n      foundNewLink = true;\n    }\n  }\n  function addToEffected(dataZoom) {\n    effectedModelMap.set(dataZoom.uid, true);\n    effectedModels.push(dataZoom);\n    markAxisControlled(dataZoom);\n  }\n  function isLinked(dataZoomModel) {\n    var isLink = false;\n    dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n      var axisIdxArr = axisRecords.get(axisDim);\n      if (axisIdxArr && axisIdxArr[axisIndex]) {\n        isLink = true;\n      }\n    });\n    return isLink;\n  }\n  function markAxisControlled(dataZoomModel) {\n    dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n      (axisRecords.get(axisDim) || axisRecords.set(axisDim, []))[axisIndex] = true;\n    });\n  }\n  return effectedModels;\n}\n/**\r\n * Find the first target coordinate system.\r\n * Available after model built.\r\n *\r\n * @return Like {\r\n *                  grid: [\r\n *                      {model: coord0, axisModels: [axis1, axis3], coordIndex: 1},\r\n *                      {model: coord1, axisModels: [axis0, axis2], coordIndex: 0},\r\n *                      ...\r\n *                  ],  // cartesians must not be null/undefined.\r\n *                  polar: [\r\n *                      {model: coord0, axisModels: [axis4], coordIndex: 0},\r\n *                      ...\r\n *                  ],  // polars must not be null/undefined.\r\n *                  singleAxis: [\r\n *                      {model: coord0, axisModels: [], coordIndex: 0}\r\n *                  ]\r\n *              }\r\n */\nexport function collectReferCoordSysModelInfo(dataZoomModel) {\n  var ecModel = dataZoomModel.ecModel;\n  var coordSysInfoWrap = {\n    infoList: [],\n    infoMap: createHashMap()\n  };\n  dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n    var axisModel = ecModel.getComponent(getAxisMainType(axisDim), axisIndex);\n    if (!axisModel) {\n      return;\n    }\n    var coordSysModel = axisModel.getCoordSysModel();\n    if (!coordSysModel) {\n      return;\n    }\n    var coordSysUid = coordSysModel.uid;\n    var coordSysInfo = coordSysInfoWrap.infoMap.get(coordSysUid);\n    if (!coordSysInfo) {\n      coordSysInfo = {\n        model: coordSysModel,\n        axisModels: []\n      };\n      coordSysInfoWrap.infoList.push(coordSysInfo);\n      coordSysInfoWrap.infoMap.set(coordSysUid, coordSysInfo);\n    }\n    coordSysInfo.axisModels.push(axisModel);\n  });\n  return coordSysInfoWrap;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}