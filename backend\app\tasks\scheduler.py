"""
定时任务调度器
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Callable, Any
from dataclasses import dataclass
import threading

logger = logging.getLogger(__name__)

@dataclass
class ScheduledTask:
    """定时任务"""
    name: str
    func: Callable
    interval: int  # 间隔秒数
    last_run: datetime = None
    next_run: datetime = None
    is_running: bool = False
    enabled: bool = True

class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        self.tasks: Dict[str, ScheduledTask] = {}
        self.running = False
        self.loop_task = None
        self._lock = threading.Lock()
    
    def add_task(self, name: str, func: Callable, interval: int, enabled: bool = True):
        """添加定时任务"""
        with self._lock:
            task = ScheduledTask(
                name=name,
                func=func,
                interval=interval,
                next_run=datetime.now() + timedelta(seconds=interval),
                enabled=enabled
            )
            self.tasks[name] = task
            logger.info(f"添加定时任务: {name}, 间隔: {interval}秒")
    
    def remove_task(self, name: str):
        """移除定时任务"""
        with self._lock:
            if name in self.tasks:
                del self.tasks[name]
                logger.info(f"移除定时任务: {name}")
    
    def enable_task(self, name: str):
        """启用任务"""
        with self._lock:
            if name in self.tasks:
                self.tasks[name].enabled = True
                logger.info(f"启用任务: {name}")
    
    def disable_task(self, name: str):
        """禁用任务"""
        with self._lock:
            if name in self.tasks:
                self.tasks[name].enabled = False
                logger.info(f"禁用任务: {name}")
    
    async def start(self):
        """启动调度器"""
        if self.running:
            logger.warning("调度器已在运行")
            return
        
        self.running = True
        logger.info("启动任务调度器")
        
        while self.running:
            try:
                await self._check_and_run_tasks()
                await asyncio.sleep(1)  # 每秒检查一次
            except Exception as e:
                logger.error(f"调度器运行异常: {e}")
                await asyncio.sleep(5)  # 异常时等待5秒
    
    def stop(self):
        """停止调度器"""
        self.running = False
        logger.info("停止任务调度器")
    
    async def _check_and_run_tasks(self):
        """检查并运行到期的任务"""
        now = datetime.now()
        
        for task_name, task in list(self.tasks.items()):
            if not task.enabled or task.is_running:
                continue
            
            if task.next_run and now >= task.next_run:
                await self._run_task(task)
    
    async def _run_task(self, task: ScheduledTask):
        """运行单个任务"""
        task.is_running = True
        task.last_run = datetime.now()
        
        try:
            logger.debug(f"执行任务: {task.name}")
            
            # 根据函数类型调用
            if asyncio.iscoroutinefunction(task.func):
                await task.func()
            else:
                # 在线程池中运行同步函数
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, task.func)
            
            logger.debug(f"任务完成: {task.name}")
            
        except Exception as e:
            logger.error(f"任务执行失败 {task.name}: {e}")
        
        finally:
            task.is_running = False
            task.next_run = datetime.now() + timedelta(seconds=task.interval)
    
    def get_task_status(self) -> List[Dict[str, Any]]:
        """获取所有任务状态"""
        status = []
        for task in self.tasks.values():
            status.append({
                'name': task.name,
                'interval': task.interval,
                'last_run': task.last_run.isoformat() if task.last_run else None,
                'next_run': task.next_run.isoformat() if task.next_run else None,
                'is_running': task.is_running,
                'enabled': task.enabled
            })
        return status

# 全局调度器实例
scheduler = TaskScheduler()

def get_scheduler() -> TaskScheduler:
    """获取调度器实例"""
    return scheduler
