{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport createSeriesDataSimply from '../helper/createSeriesDataSimply.js';\nimport SeriesModel from '../../model/Series.js';\nimport geoSourceManager from '../../coord/geo/geoSourceManager.js';\nimport { makeSeriesEncodeForNameBased } from '../../data/helper/sourceHelper.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nimport { createSymbol } from '../../util/symbol.js';\nvar MapSeries = /** @class */function (_super) {\n  __extends(MapSeries, _super);\n  function MapSeries() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MapSeries.type;\n    // Only first map series of same mapType will drawMap.\n    _this.needsDrawMap = false;\n    // Group of all map series with same mapType\n    _this.seriesGroup = [];\n    _this.getTooltipPosition = function (dataIndex) {\n      if (dataIndex != null) {\n        var name_1 = this.getData().getName(dataIndex);\n        var geo = this.coordinateSystem;\n        var region = geo.getRegion(name_1);\n        return region && geo.dataToPoint(region.getCenter());\n      }\n    };\n    return _this;\n  }\n  MapSeries.prototype.getInitialData = function (option) {\n    var data = createSeriesDataSimply(this, {\n      coordDimensions: ['value'],\n      encodeDefaulter: zrUtil.curry(makeSeriesEncodeForNameBased, this)\n    });\n    var dataNameIndexMap = zrUtil.createHashMap();\n    var toAppendItems = [];\n    for (var i = 0, len = data.count(); i < len; i++) {\n      var name_2 = data.getName(i);\n      dataNameIndexMap.set(name_2, i);\n    }\n    var geoSource = geoSourceManager.load(this.getMapType(), this.option.nameMap, this.option.nameProperty);\n    zrUtil.each(geoSource.regions, function (region) {\n      var name = region.name;\n      var dataNameIdx = dataNameIndexMap.get(name);\n      // apply specified echarts style in GeoJSON data\n      var specifiedGeoJSONRegionStyle = region.properties && region.properties.echartsStyle;\n      var dataItem;\n      if (dataNameIdx == null) {\n        dataItem = {\n          name: name\n        };\n        toAppendItems.push(dataItem);\n      } else {\n        dataItem = data.getRawDataItem(dataNameIdx);\n      }\n      specifiedGeoJSONRegionStyle && zrUtil.merge(dataItem, specifiedGeoJSONRegionStyle);\n    });\n    // Complete data with missing regions. The consequent processes (like visual\n    // map and render) can not be performed without a \"full data\". For example,\n    // find `dataIndex` by name.\n    data.appendData(toAppendItems);\n    return data;\n  };\n  /**\r\n   * If no host geo model, return null, which means using a\r\n   * inner exclusive geo model.\r\n   */\n  MapSeries.prototype.getHostGeoModel = function () {\n    var geoIndex = this.option.geoIndex;\n    return geoIndex != null ? this.ecModel.getComponent('geo', geoIndex) : null;\n  };\n  MapSeries.prototype.getMapType = function () {\n    return (this.getHostGeoModel() || this).option.map;\n  };\n  // _fillOption(option, mapName) {\n  // Shallow clone\n  // option = zrUtil.extend({}, option);\n  // option.data = geoCreator.getFilledRegions(option.data, mapName, option.nameMap);\n  // return option;\n  // }\n  MapSeries.prototype.getRawValue = function (dataIndex) {\n    // Use value stored in data instead because it is calculated from multiple series\n    // FIXME Provide all value of multiple series ?\n    var data = this.getData();\n    return data.get(data.mapDimension('value'), dataIndex);\n  };\n  /**\r\n   * Get model of region\r\n   */\n  MapSeries.prototype.getRegionModel = function (regionName) {\n    var data = this.getData();\n    return data.getItemModel(data.indexOfName(regionName));\n  };\n  /**\r\n   * Map tooltip formatter\r\n   */\n  MapSeries.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    // FIXME orignalData and data is a bit confusing\n    var data = this.getData();\n    var value = this.getRawValue(dataIndex);\n    var name = data.getName(dataIndex);\n    var seriesGroup = this.seriesGroup;\n    var seriesNames = [];\n    for (var i = 0; i < seriesGroup.length; i++) {\n      var otherIndex = seriesGroup[i].originalData.indexOfName(name);\n      var valueDim = data.mapDimension('value');\n      if (!isNaN(seriesGroup[i].originalData.get(valueDim, otherIndex))) {\n        seriesNames.push(seriesGroup[i].name);\n      }\n    }\n    return createTooltipMarkup('section', {\n      header: seriesNames.join(', '),\n      noHeader: !seriesNames.length,\n      blocks: [createTooltipMarkup('nameValue', {\n        name: name,\n        value: value\n      })]\n    });\n  };\n  MapSeries.prototype.setZoom = function (zoom) {\n    this.option.zoom = zoom;\n  };\n  MapSeries.prototype.setCenter = function (center) {\n    this.option.center = center;\n  };\n  MapSeries.prototype.getLegendIcon = function (opt) {\n    var iconType = opt.icon || 'roundRect';\n    var icon = createSymbol(iconType, 0, 0, opt.itemWidth, opt.itemHeight, opt.itemStyle.fill);\n    icon.setStyle(opt.itemStyle);\n    // Map do not use itemStyle.borderWidth as border width\n    icon.style.stroke = 'none';\n    // No rotation because no series visual symbol for map\n    if (iconType.indexOf('empty') > -1) {\n      icon.style.stroke = icon.style.fill;\n      icon.style.fill = '#fff';\n      icon.style.lineWidth = 2;\n    }\n    return icon;\n  };\n  MapSeries.type = 'series.map';\n  MapSeries.dependencies = ['geo'];\n  MapSeries.layoutMode = 'box';\n  MapSeries.defaultOption = {\n    // 一级层叠\n    // zlevel: 0,\n    // 二级层叠\n    z: 2,\n    coordinateSystem: 'geo',\n    // map should be explicitly specified since ec3.\n    map: '',\n    // If `geoIndex` is not specified, a exclusive geo will be\n    // created. Otherwise use the specified geo component, and\n    // `map` and `mapType` are ignored.\n    // geoIndex: 0,\n    // 'center' | 'left' | 'right' | 'x%' | {number}\n    left: 'center',\n    // 'center' | 'top' | 'bottom' | 'x%' | {number}\n    top: 'center',\n    // right\n    // bottom\n    // width:\n    // height\n    // Aspect is width / height. Inited to be geoJson bbox aspect\n    // This parameter is used for scale this aspect\n    // Default value:\n    // for geoSVG source: 1,\n    // for geoJSON source: 0.75.\n    aspectScale: null,\n    // Layout with center and size\n    // If you want to put map in a fixed size box with right aspect ratio\n    // This two properties may be more convenient.\n    // layoutCenter: [50%, 50%]\n    // layoutSize: 100\n    showLegendSymbol: true,\n    // Define left-top, right-bottom coords to control view\n    // For example, [ [180, 90], [-180, -90] ],\n    // higher priority than center and zoom\n    boundingCoords: null,\n    // Default on center of map\n    center: null,\n    zoom: 1,\n    scaleLimit: null,\n    selectedMode: true,\n    label: {\n      show: false,\n      color: '#000'\n    },\n    // scaleLimit: null,\n    itemStyle: {\n      borderWidth: 0.5,\n      borderColor: '#444',\n      areaColor: '#eee'\n    },\n    emphasis: {\n      label: {\n        show: true,\n        color: 'rgb(100,0,0)'\n      },\n      itemStyle: {\n        areaColor: 'rgba(255,215,0,0.8)'\n      }\n    },\n    select: {\n      label: {\n        show: true,\n        color: 'rgb(100,0,0)'\n      },\n      itemStyle: {\n        color: 'rgba(255,215,0,0.8)'\n      }\n    },\n    nameProperty: 'name'\n  };\n  return MapSeries;\n}(SeriesModel);\nexport default MapSeries;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}