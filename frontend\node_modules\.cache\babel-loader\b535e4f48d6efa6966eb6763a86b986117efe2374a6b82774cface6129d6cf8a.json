{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport * as visualSolution from '../../visual/visualSolution.js';\nimport { makeBrushCommonSelectorForSeries } from './selector.js';\nimport * as throttleUtil from '../../util/throttle.js';\nimport BrushTargetManager from '../helper/BrushTargetManager.js';\nvar STATE_LIST = ['inBrush', 'outOfBrush'];\nvar DISPATCH_METHOD = '__ecBrushSelect';\nvar DISPATCH_FLAG = '__ecInBrushSelectEvent';\n;\nexport function layoutCovers(ecModel) {\n  ecModel.eachComponent({\n    mainType: 'brush'\n  }, function (brushModel) {\n    var brushTargetManager = brushModel.brushTargetManager = new BrushTargetManager(brushModel.option, ecModel);\n    brushTargetManager.setInputRanges(brushModel.areas, ecModel);\n  });\n}\n/**\r\n * Register the visual encoding if this modules required.\r\n */\nexport default function brushVisual(ecModel, api, payload) {\n  var brushSelected = [];\n  var throttleType;\n  var throttleDelay;\n  ecModel.eachComponent({\n    mainType: 'brush'\n  }, function (brushModel) {\n    payload && payload.type === 'takeGlobalCursor' && brushModel.setBrushOption(payload.key === 'brush' ? payload.brushOption : {\n      brushType: false\n    });\n  });\n  layoutCovers(ecModel);\n  ecModel.eachComponent({\n    mainType: 'brush'\n  }, function (brushModel, brushIndex) {\n    var thisBrushSelected = {\n      brushId: brushModel.id,\n      brushIndex: brushIndex,\n      brushName: brushModel.name,\n      areas: zrUtil.clone(brushModel.areas),\n      selected: []\n    };\n    // Every brush component exists in event params, convenient\n    // for user to find by index.\n    brushSelected.push(thisBrushSelected);\n    var brushOption = brushModel.option;\n    var brushLink = brushOption.brushLink;\n    var linkedSeriesMap = [];\n    var selectedDataIndexForLink = [];\n    var rangeInfoBySeries = [];\n    var hasBrushExists = false;\n    if (!brushIndex) {\n      // Only the first throttle setting works.\n      throttleType = brushOption.throttleType;\n      throttleDelay = brushOption.throttleDelay;\n    }\n    // Add boundingRect and selectors to range.\n    var areas = zrUtil.map(brushModel.areas, function (area) {\n      var builder = boundingRectBuilders[area.brushType];\n      var selectableArea = zrUtil.defaults({\n        boundingRect: builder ? builder(area) : void 0\n      }, area);\n      selectableArea.selectors = makeBrushCommonSelectorForSeries(selectableArea);\n      return selectableArea;\n    });\n    var visualMappings = visualSolution.createVisualMappings(brushModel.option, STATE_LIST, function (mappingOption) {\n      mappingOption.mappingMethod = 'fixed';\n    });\n    zrUtil.isArray(brushLink) && zrUtil.each(brushLink, function (seriesIndex) {\n      linkedSeriesMap[seriesIndex] = 1;\n    });\n    function linkOthers(seriesIndex) {\n      return brushLink === 'all' || !!linkedSeriesMap[seriesIndex];\n    }\n    // If no supported brush or no brush on the series,\n    // all visuals should be in original state.\n    function brushed(rangeInfoList) {\n      return !!rangeInfoList.length;\n    }\n    /**\r\n     * Logic for each series: (If the logic has to be modified one day, do it carefully!)\r\n     *\r\n     * ( brushed ┬ && ┬hasBrushExist ┬ && linkOthers  ) => StepA: ┬record, ┬ StepB: ┬visualByRecord.\r\n     *   !brushed┘    ├hasBrushExist ┤                            └nothing,┘        ├visualByRecord.\r\n     *                └!hasBrushExist┘                                              └nothing.\r\n     * ( !brushed  && ┬hasBrushExist ┬ && linkOthers  ) => StepA:  nothing,  StepB: ┬visualByRecord.\r\n     *                └!hasBrushExist┘                                              └nothing.\r\n     * ( brushed ┬ &&                     !linkOthers ) => StepA:  nothing,  StepB: ┬visualByCheck.\r\n     *   !brushed┘                                                                  └nothing.\r\n     * ( !brushed  &&                     !linkOthers ) => StepA:  nothing,  StepB:  nothing.\r\n     */\n    // Step A\n    ecModel.eachSeries(function (seriesModel, seriesIndex) {\n      var rangeInfoList = rangeInfoBySeries[seriesIndex] = [];\n      seriesModel.subType === 'parallel' ? stepAParallel(seriesModel, seriesIndex) : stepAOthers(seriesModel, seriesIndex, rangeInfoList);\n    });\n    function stepAParallel(seriesModel, seriesIndex) {\n      var coordSys = seriesModel.coordinateSystem;\n      hasBrushExists = hasBrushExists || coordSys.hasAxisBrushed();\n      linkOthers(seriesIndex) && coordSys.eachActiveState(seriesModel.getData(), function (activeState, dataIndex) {\n        activeState === 'active' && (selectedDataIndexForLink[dataIndex] = 1);\n      });\n    }\n    function stepAOthers(seriesModel, seriesIndex, rangeInfoList) {\n      if (!seriesModel.brushSelector || brushModelNotControll(brushModel, seriesIndex)) {\n        return;\n      }\n      zrUtil.each(areas, function (area) {\n        if (brushModel.brushTargetManager.controlSeries(area, seriesModel, ecModel)) {\n          rangeInfoList.push(area);\n        }\n        hasBrushExists = hasBrushExists || brushed(rangeInfoList);\n      });\n      if (linkOthers(seriesIndex) && brushed(rangeInfoList)) {\n        var data_1 = seriesModel.getData();\n        data_1.each(function (dataIndex) {\n          if (checkInRange(seriesModel, rangeInfoList, data_1, dataIndex)) {\n            selectedDataIndexForLink[dataIndex] = 1;\n          }\n        });\n      }\n    }\n    // Step B\n    ecModel.eachSeries(function (seriesModel, seriesIndex) {\n      var seriesBrushSelected = {\n        seriesId: seriesModel.id,\n        seriesIndex: seriesIndex,\n        seriesName: seriesModel.name,\n        dataIndex: []\n      };\n      // Every series exists in event params, convenient\n      // for user to find series by seriesIndex.\n      thisBrushSelected.selected.push(seriesBrushSelected);\n      var rangeInfoList = rangeInfoBySeries[seriesIndex];\n      var data = seriesModel.getData();\n      var getValueState = linkOthers(seriesIndex) ? function (dataIndex) {\n        return selectedDataIndexForLink[dataIndex] ? (seriesBrushSelected.dataIndex.push(data.getRawIndex(dataIndex)), 'inBrush') : 'outOfBrush';\n      } : function (dataIndex) {\n        return checkInRange(seriesModel, rangeInfoList, data, dataIndex) ? (seriesBrushSelected.dataIndex.push(data.getRawIndex(dataIndex)), 'inBrush') : 'outOfBrush';\n      };\n      // If no supported brush or no brush, all visuals are in original state.\n      (linkOthers(seriesIndex) ? hasBrushExists : brushed(rangeInfoList)) && visualSolution.applyVisual(STATE_LIST, visualMappings, data, getValueState);\n    });\n  });\n  dispatchAction(api, throttleType, throttleDelay, brushSelected, payload);\n}\n;\nfunction dispatchAction(api, throttleType, throttleDelay, brushSelected, payload) {\n  // This event will not be triggered when `setOpion`, otherwise dead lock may\n  // triggered when do `setOption` in event listener, which we do not find\n  // satisfactory way to solve yet. Some considered resolutions:\n  // (a) Diff with prevoius selected data ant only trigger event when changed.\n  // But store previous data and diff precisely (i.e., not only by dataIndex, but\n  // also detect value changes in selected data) might bring complexity or fragility.\n  // (b) Use spectial param like `silent` to suppress event triggering.\n  // But such kind of volatile param may be weird in `setOption`.\n  if (!payload) {\n    return;\n  }\n  var zr = api.getZr();\n  if (zr[DISPATCH_FLAG]) {\n    return;\n  }\n  if (!zr[DISPATCH_METHOD]) {\n    zr[DISPATCH_METHOD] = doDispatch;\n  }\n  var fn = throttleUtil.createOrUpdate(zr, DISPATCH_METHOD, throttleDelay, throttleType);\n  fn(api, brushSelected);\n}\nfunction doDispatch(api, brushSelected) {\n  if (!api.isDisposed()) {\n    var zr = api.getZr();\n    zr[DISPATCH_FLAG] = true;\n    api.dispatchAction({\n      type: 'brushSelect',\n      batch: brushSelected\n    });\n    zr[DISPATCH_FLAG] = false;\n  }\n}\nfunction checkInRange(seriesModel, rangeInfoList, data, dataIndex) {\n  for (var i = 0, len = rangeInfoList.length; i < len; i++) {\n    var area = rangeInfoList[i];\n    if (seriesModel.brushSelector(dataIndex, data, area.selectors, area)) {\n      return true;\n    }\n  }\n}\nfunction brushModelNotControll(brushModel, seriesIndex) {\n  var seriesIndices = brushModel.option.seriesIndex;\n  return seriesIndices != null && seriesIndices !== 'all' && (zrUtil.isArray(seriesIndices) ? zrUtil.indexOf(seriesIndices, seriesIndex) < 0 : seriesIndex !== seriesIndices);\n}\nvar boundingRectBuilders = {\n  rect: function (area) {\n    return getBoundingRectFromMinMax(area.range);\n  },\n  polygon: function (area) {\n    var minMax;\n    var range = area.range;\n    for (var i = 0, len = range.length; i < len; i++) {\n      minMax = minMax || [[Infinity, -Infinity], [Infinity, -Infinity]];\n      var rg = range[i];\n      rg[0] < minMax[0][0] && (minMax[0][0] = rg[0]);\n      rg[0] > minMax[0][1] && (minMax[0][1] = rg[0]);\n      rg[1] < minMax[1][0] && (minMax[1][0] = rg[1]);\n      rg[1] > minMax[1][1] && (minMax[1][1] = rg[1]);\n    }\n    return minMax && getBoundingRectFromMinMax(minMax);\n  }\n};\nfunction getBoundingRectFromMinMax(minMax) {\n  return new BoundingRect(minMax[0][0], minMax[1][0], minMax[0][1] - minMax[0][0], minMax[1][1] - minMax[1][0]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}