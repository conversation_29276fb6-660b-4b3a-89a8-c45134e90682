{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as pathTool from 'zrender/lib/tool/path.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as vector from 'zrender/lib/core/vector.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport Transformable from 'zrender/lib/core/Transformable.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport Group from 'zrender/lib/graphic/Group.js';\nimport ZRText from 'zrender/lib/graphic/Text.js';\nimport Circle from 'zrender/lib/graphic/shape/Circle.js';\nimport Ellipse from 'zrender/lib/graphic/shape/Ellipse.js';\nimport Sector from 'zrender/lib/graphic/shape/Sector.js';\nimport Ring from 'zrender/lib/graphic/shape/Ring.js';\nimport Polygon from 'zrender/lib/graphic/shape/Polygon.js';\nimport Polyline from 'zrender/lib/graphic/shape/Polyline.js';\nimport Rect from 'zrender/lib/graphic/shape/Rect.js';\nimport Line from 'zrender/lib/graphic/shape/Line.js';\nimport BezierCurve from 'zrender/lib/graphic/shape/BezierCurve.js';\nimport Arc from 'zrender/lib/graphic/shape/Arc.js';\nimport CompoundPath from 'zrender/lib/graphic/CompoundPath.js';\nimport LinearGradient from 'zrender/lib/graphic/LinearGradient.js';\nimport RadialGradient from 'zrender/lib/graphic/RadialGradient.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport OrientedBoundingRect from 'zrender/lib/core/OrientedBoundingRect.js';\nimport Point from 'zrender/lib/core/Point.js';\nimport IncrementalDisplayable from 'zrender/lib/graphic/IncrementalDisplayable.js';\nimport * as subPixelOptimizeUtil from 'zrender/lib/graphic/helper/subPixelOptimize.js';\nimport { extend, isArrayLike, map, defaults, isString, keys, each, hasOwn, isArray } from 'zrender/lib/core/util.js';\nimport { getECData } from './innerStore.js';\nimport { updateProps, initProps, removeElement, removeElementWithFadeOut, isElementRemoved } from '../animation/basicTransition.js';\n/**\r\n * @deprecated export for compatitable reason\r\n */\nexport { updateProps, initProps, removeElement, removeElementWithFadeOut, isElementRemoved };\nvar mathMax = Math.max;\nvar mathMin = Math.min;\nvar _customShapeMap = {};\n/**\r\n * Extend shape with parameters\r\n */\nexport function extendShape(opts) {\n  return Path.extend(opts);\n}\nvar extendPathFromString = pathTool.extendFromString;\n/**\r\n * Extend path\r\n */\nexport function extendPath(pathData, opts) {\n  return extendPathFromString(pathData, opts);\n}\n/**\r\n * Register a user defined shape.\r\n * The shape class can be fetched by `getShapeClass`\r\n * This method will overwrite the registered shapes, including\r\n * the registered built-in shapes, if using the same `name`.\r\n * The shape can be used in `custom series` and\r\n * `graphic component` by declaring `{type: name}`.\r\n *\r\n * @param name\r\n * @param ShapeClass Can be generated by `extendShape`.\r\n */\nexport function registerShape(name, ShapeClass) {\n  _customShapeMap[name] = ShapeClass;\n}\n/**\r\n * Find shape class registered by `registerShape`. Usually used in\r\n * fetching user defined shape.\r\n *\r\n * [Caution]:\r\n * (1) This method **MUST NOT be used inside echarts !!!**, unless it is prepared\r\n * to use user registered shapes.\r\n * Because the built-in shape (see `getBuiltInShape`) will be registered by\r\n * `registerShape` by default. That enables users to get both built-in\r\n * shapes as well as the shapes belonging to themsleves. But users can overwrite\r\n * the built-in shapes by using names like 'circle', 'rect' via calling\r\n * `registerShape`. So the echarts inner featrues should not fetch shapes from here\r\n * in case that it is overwritten by users, except that some features, like\r\n * `custom series`, `graphic component`, do it deliberately.\r\n *\r\n * (2) In the features like `custom series`, `graphic component`, the user input\r\n * `{tpye: 'xxx'}` does not only specify shapes but also specify other graphic\r\n * elements like `'group'`, `'text'`, `'image'` or event `'path'`. Those names\r\n * are reserved names, that is, if some user registers a shape named `'image'`,\r\n * the shape will not be used. If we intending to add some more reserved names\r\n * in feature, that might bring break changes (disable some existing user shape\r\n * names). But that case probably rarely happens. So we don't make more mechanism\r\n * to resolve this issue here.\r\n *\r\n * @param name\r\n * @return The shape class. If not found, return nothing.\r\n */\nexport function getShapeClass(name) {\n  if (_customShapeMap.hasOwnProperty(name)) {\n    return _customShapeMap[name];\n  }\n}\n/**\r\n * Create a path element from path data string\r\n * @param pathData\r\n * @param opts\r\n * @param rect\r\n * @param layout 'center' or 'cover' default to be cover\r\n */\nexport function makePath(pathData, opts, rect, layout) {\n  var path = pathTool.createFromString(pathData, opts);\n  if (rect) {\n    if (layout === 'center') {\n      rect = centerGraphic(rect, path.getBoundingRect());\n    }\n    resizePath(path, rect);\n  }\n  return path;\n}\n/**\r\n * Create a image element from image url\r\n * @param imageUrl image url\r\n * @param opts options\r\n * @param rect constrain rect\r\n * @param layout 'center' or 'cover'. Default to be 'cover'\r\n */\nexport function makeImage(imageUrl, rect, layout) {\n  var zrImg = new ZRImage({\n    style: {\n      image: imageUrl,\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height\n    },\n    onload: function (img) {\n      if (layout === 'center') {\n        var boundingRect = {\n          width: img.width,\n          height: img.height\n        };\n        zrImg.setStyle(centerGraphic(rect, boundingRect));\n      }\n    }\n  });\n  return zrImg;\n}\n/**\r\n * Get position of centered element in bounding box.\r\n *\r\n * @param  rect         element local bounding box\r\n * @param  boundingRect constraint bounding box\r\n * @return element position containing x, y, width, and height\r\n */\nfunction centerGraphic(rect, boundingRect) {\n  // Set rect to center, keep width / height ratio.\n  var aspect = boundingRect.width / boundingRect.height;\n  var width = rect.height * aspect;\n  var height;\n  if (width <= rect.width) {\n    height = rect.height;\n  } else {\n    width = rect.width;\n    height = width / aspect;\n  }\n  var cx = rect.x + rect.width / 2;\n  var cy = rect.y + rect.height / 2;\n  return {\n    x: cx - width / 2,\n    y: cy - height / 2,\n    width: width,\n    height: height\n  };\n}\nexport var mergePath = pathTool.mergePath;\n/**\r\n * Resize a path to fit the rect\r\n * @param path\r\n * @param rect\r\n */\nexport function resizePath(path, rect) {\n  if (!path.applyTransform) {\n    return;\n  }\n  var pathRect = path.getBoundingRect();\n  var m = pathRect.calculateTransform(rect);\n  path.applyTransform(m);\n}\n/**\r\n * Sub pixel optimize line for canvas\r\n */\nexport function subPixelOptimizeLine(shape, lineWidth) {\n  subPixelOptimizeUtil.subPixelOptimizeLine(shape, shape, {\n    lineWidth: lineWidth\n  });\n  return shape;\n}\n/**\r\n * Sub pixel optimize rect for canvas\r\n */\nexport function subPixelOptimizeRect(param) {\n  subPixelOptimizeUtil.subPixelOptimizeRect(param.shape, param.shape, param.style);\n  return param;\n}\n/**\r\n * Sub pixel optimize for canvas\r\n *\r\n * @param position Coordinate, such as x, y\r\n * @param lineWidth Should be nonnegative integer.\r\n * @param positiveOrNegative Default false (negative).\r\n * @return Optimized position.\r\n */\nexport var subPixelOptimize = subPixelOptimizeUtil.subPixelOptimize;\n/**\r\n * Get transform matrix of target (param target),\r\n * in coordinate of its ancestor (param ancestor)\r\n *\r\n * @param target\r\n * @param [ancestor]\r\n */\nexport function getTransform(target, ancestor) {\n  var mat = matrix.identity([]);\n  while (target && target !== ancestor) {\n    matrix.mul(mat, target.getLocalTransform(), mat);\n    target = target.parent;\n  }\n  return mat;\n}\n/**\r\n * Apply transform to an vertex.\r\n * @param target [x, y]\r\n * @param transform Can be:\r\n *      + Transform matrix: like [1, 0, 0, 1, 0, 0]\r\n *      + {position, rotation, scale}, the same as `zrender/Transformable`.\r\n * @param invert Whether use invert matrix.\r\n * @return [x, y]\r\n */\nexport function applyTransform(target, transform, invert) {\n  if (transform && !isArrayLike(transform)) {\n    transform = Transformable.getLocalTransform(transform);\n  }\n  if (invert) {\n    transform = matrix.invert([], transform);\n  }\n  return vector.applyTransform([], target, transform);\n}\n/**\r\n * @param direction 'left' 'right' 'top' 'bottom'\r\n * @param transform Transform matrix: like [1, 0, 0, 1, 0, 0]\r\n * @param invert Whether use invert matrix.\r\n * @return Transformed direction. 'left' 'right' 'top' 'bottom'\r\n */\nexport function transformDirection(direction, transform, invert) {\n  // Pick a base, ensure that transform result will not be (0, 0).\n  var hBase = transform[4] === 0 || transform[5] === 0 || transform[0] === 0 ? 1 : Math.abs(2 * transform[4] / transform[0]);\n  var vBase = transform[4] === 0 || transform[5] === 0 || transform[2] === 0 ? 1 : Math.abs(2 * transform[4] / transform[2]);\n  var vertex = [direction === 'left' ? -hBase : direction === 'right' ? hBase : 0, direction === 'top' ? -vBase : direction === 'bottom' ? vBase : 0];\n  vertex = applyTransform(vertex, transform, invert);\n  return Math.abs(vertex[0]) > Math.abs(vertex[1]) ? vertex[0] > 0 ? 'right' : 'left' : vertex[1] > 0 ? 'bottom' : 'top';\n}\nfunction isNotGroup(el) {\n  return !el.isGroup;\n}\nfunction isPath(el) {\n  return el.shape != null;\n}\n/**\r\n * Apply group transition animation from g1 to g2.\r\n * If no animatableModel, no animation.\r\n */\nexport function groupTransition(g1, g2, animatableModel) {\n  if (!g1 || !g2) {\n    return;\n  }\n  function getElMap(g) {\n    var elMap = {};\n    g.traverse(function (el) {\n      if (isNotGroup(el) && el.anid) {\n        elMap[el.anid] = el;\n      }\n    });\n    return elMap;\n  }\n  function getAnimatableProps(el) {\n    var obj = {\n      x: el.x,\n      y: el.y,\n      rotation: el.rotation\n    };\n    if (isPath(el)) {\n      obj.shape = extend({}, el.shape);\n    }\n    return obj;\n  }\n  var elMap1 = getElMap(g1);\n  g2.traverse(function (el) {\n    if (isNotGroup(el) && el.anid) {\n      var oldEl = elMap1[el.anid];\n      if (oldEl) {\n        var newProp = getAnimatableProps(el);\n        el.attr(getAnimatableProps(oldEl));\n        updateProps(el, newProp, animatableModel, getECData(el).dataIndex);\n      }\n    }\n  });\n}\nexport function clipPointsByRect(points, rect) {\n  // FIXME: This way might be incorrect when graphic clipped by a corner\n  // and when element has a border.\n  return map(points, function (point) {\n    var x = point[0];\n    x = mathMax(x, rect.x);\n    x = mathMin(x, rect.x + rect.width);\n    var y = point[1];\n    y = mathMax(y, rect.y);\n    y = mathMin(y, rect.y + rect.height);\n    return [x, y];\n  });\n}\n/**\r\n * Return a new clipped rect. If rect size are negative, return undefined.\r\n */\nexport function clipRectByRect(targetRect, rect) {\n  var x = mathMax(targetRect.x, rect.x);\n  var x2 = mathMin(targetRect.x + targetRect.width, rect.x + rect.width);\n  var y = mathMax(targetRect.y, rect.y);\n  var y2 = mathMin(targetRect.y + targetRect.height, rect.y + rect.height);\n  // If the total rect is cliped, nothing, including the border,\n  // should be painted. So return undefined.\n  if (x2 >= x && y2 >= y) {\n    return {\n      x: x,\n      y: y,\n      width: x2 - x,\n      height: y2 - y\n    };\n  }\n}\nexport function createIcon(iconStr,\n// Support 'image://' or 'path://' or direct svg path.\nopt, rect) {\n  var innerOpts = extend({\n    rectHover: true\n  }, opt);\n  var style = innerOpts.style = {\n    strokeNoScale: true\n  };\n  rect = rect || {\n    x: -1,\n    y: -1,\n    width: 2,\n    height: 2\n  };\n  if (iconStr) {\n    return iconStr.indexOf('image://') === 0 ? (style.image = iconStr.slice(8), defaults(style, rect), new ZRImage(innerOpts)) : makePath(iconStr.replace('path://', ''), innerOpts, rect, 'center');\n  }\n}\n/**\r\n * Return `true` if the given line (line `a`) and the given polygon\r\n * are intersect.\r\n * Note that we do not count colinear as intersect here because no\r\n * requirement for that. We could do that if required in future.\r\n */\nexport function linePolygonIntersect(a1x, a1y, a2x, a2y, points) {\n  for (var i = 0, p2 = points[points.length - 1]; i < points.length; i++) {\n    var p = points[i];\n    if (lineLineIntersect(a1x, a1y, a2x, a2y, p[0], p[1], p2[0], p2[1])) {\n      return true;\n    }\n    p2 = p;\n  }\n}\n/**\r\n * Return `true` if the given two lines (line `a` and line `b`)\r\n * are intersect.\r\n * Note that we do not count colinear as intersect here because no\r\n * requirement for that. We could do that if required in future.\r\n */\nexport function lineLineIntersect(a1x, a1y, a2x, a2y, b1x, b1y, b2x, b2y) {\n  // let `vec_m` to be `vec_a2 - vec_a1` and `vec_n` to be `vec_b2 - vec_b1`.\n  var mx = a2x - a1x;\n  var my = a2y - a1y;\n  var nx = b2x - b1x;\n  var ny = b2y - b1y;\n  // `vec_m` and `vec_n` are parallel iff\n  //     existing `k` such that `vec_m = k · vec_n`, equivalent to `vec_m X vec_n = 0`.\n  var nmCrossProduct = crossProduct2d(nx, ny, mx, my);\n  if (nearZero(nmCrossProduct)) {\n    return false;\n  }\n  // `vec_m` and `vec_n` are intersect iff\n  //     existing `p` and `q` in [0, 1] such that `vec_a1 + p * vec_m = vec_b1 + q * vec_n`,\n  //     such that `q = ((vec_a1 - vec_b1) X vec_m) / (vec_n X vec_m)`\n  //           and `p = ((vec_a1 - vec_b1) X vec_n) / (vec_n X vec_m)`.\n  var b1a1x = a1x - b1x;\n  var b1a1y = a1y - b1y;\n  var q = crossProduct2d(b1a1x, b1a1y, mx, my) / nmCrossProduct;\n  if (q < 0 || q > 1) {\n    return false;\n  }\n  var p = crossProduct2d(b1a1x, b1a1y, nx, ny) / nmCrossProduct;\n  if (p < 0 || p > 1) {\n    return false;\n  }\n  return true;\n}\n/**\r\n * Cross product of 2-dimension vector.\r\n */\nfunction crossProduct2d(x1, y1, x2, y2) {\n  return x1 * y2 - x2 * y1;\n}\nfunction nearZero(val) {\n  return val <= 1e-6 && val >= -1e-6;\n}\nexport function setTooltipConfig(opt) {\n  var itemTooltipOption = opt.itemTooltipOption;\n  var componentModel = opt.componentModel;\n  var itemName = opt.itemName;\n  var itemTooltipOptionObj = isString(itemTooltipOption) ? {\n    formatter: itemTooltipOption\n  } : itemTooltipOption;\n  var mainType = componentModel.mainType;\n  var componentIndex = componentModel.componentIndex;\n  var formatterParams = {\n    componentType: mainType,\n    name: itemName,\n    $vars: ['name']\n  };\n  formatterParams[mainType + 'Index'] = componentIndex;\n  var formatterParamsExtra = opt.formatterParamsExtra;\n  if (formatterParamsExtra) {\n    each(keys(formatterParamsExtra), function (key) {\n      if (!hasOwn(formatterParams, key)) {\n        formatterParams[key] = formatterParamsExtra[key];\n        formatterParams.$vars.push(key);\n      }\n    });\n  }\n  var ecData = getECData(opt.el);\n  ecData.componentMainType = mainType;\n  ecData.componentIndex = componentIndex;\n  ecData.tooltipConfig = {\n    name: itemName,\n    option: defaults({\n      content: itemName,\n      encodeHTMLContent: true,\n      formatterParams: formatterParams\n    }, itemTooltipOptionObj)\n  };\n}\nfunction traverseElement(el, cb) {\n  var stopped;\n  // TODO\n  // Polyfill for fixing zrender group traverse don't visit it's root issue.\n  if (el.isGroup) {\n    stopped = cb(el);\n  }\n  if (!stopped) {\n    el.traverse(cb);\n  }\n}\nexport function traverseElements(els, cb) {\n  if (els) {\n    if (isArray(els)) {\n      for (var i = 0; i < els.length; i++) {\n        traverseElement(els[i], cb);\n      }\n    } else {\n      traverseElement(els, cb);\n    }\n  }\n}\n// Register built-in shapes. These shapes might be overwritten\n// by users, although we do not recommend that.\nregisterShape('circle', Circle);\nregisterShape('ellipse', Ellipse);\nregisterShape('sector', Sector);\nregisterShape('ring', Ring);\nregisterShape('polygon', Polygon);\nregisterShape('polyline', Polyline);\nregisterShape('rect', Rect);\nregisterShape('line', Line);\nregisterShape('bezierCurve', BezierCurve);\nregisterShape('arc', Arc);\nexport { Group, ZRImage as Image, ZRText as Text, Circle, Ellipse, Sector, Ring, Polygon, Polyline, Rect, Line, BezierCurve, Arc, IncrementalDisplayable, CompoundPath, LinearGradient, RadialGradient, BoundingRect, OrientedBoundingRect, Point, Path };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}