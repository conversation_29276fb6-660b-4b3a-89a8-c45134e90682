{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar opacityAccessPath = ['lineStyle', 'opacity'];\nvar parallelVisual = {\n  seriesType: 'parallel',\n  reset: function (seriesModel, ecModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    var opacityMap = {\n      normal: seriesModel.get(['lineStyle', 'opacity']),\n      active: seriesModel.get('activeOpacity'),\n      inactive: seriesModel.get('inactiveOpacity')\n    };\n    return {\n      progress: function (params, data) {\n        coordSys.eachActiveState(data, function (activeState, dataIndex) {\n          var opacity = opacityMap[activeState];\n          if (activeState === 'normal' && data.hasItemOption) {\n            var itemOpacity = data.getItemModel(dataIndex).get(opacityAccessPath, true);\n            itemOpacity != null && (opacity = itemOpacity);\n          }\n          var existsStyle = data.ensureUniqueItemVisual(dataIndex, 'style');\n          existsStyle.opacity = opacity;\n        }, params.start, params.end);\n      }\n    };\n  }\n};\nexport default parallelVisual;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}