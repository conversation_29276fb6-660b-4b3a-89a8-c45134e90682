{"ast": null, "code": "import * as vec2 from './vector.js';\nimport BoundingRect from './BoundingRect.js';\nimport { devicePixelRatio as dpr } from '../config.js';\nimport { fromLine, fromCubic, fromQuadratic, fromArc } from './bbox.js';\nimport { cubicLength, cubicSubdivide, quadraticLength, quadraticSubdivide } from './curve.js';\nvar CMD = {\n  M: 1,\n  L: 2,\n  C: 3,\n  Q: 4,\n  A: 5,\n  Z: 6,\n  R: 7\n};\nvar tmpOutX = [];\nvar tmpOutY = [];\nvar min = [];\nvar max = [];\nvar min2 = [];\nvar max2 = [];\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar mathCos = Math.cos;\nvar mathSin = Math.sin;\nvar mathAbs = Math.abs;\nvar PI = Math.PI;\nvar PI2 = PI * 2;\nvar hasTypedArray = typeof Float32Array !== 'undefined';\nvar tmpAngles = [];\nfunction modPI2(radian) {\n  var n = Math.round(radian / PI * 1e8) / 1e8;\n  return n % 2 * PI;\n}\nexport function normalizeArcAngles(angles, anticlockwise) {\n  var newStartAngle = modPI2(angles[0]);\n  if (newStartAngle < 0) {\n    newStartAngle += PI2;\n  }\n  var delta = newStartAngle - angles[0];\n  var newEndAngle = angles[1];\n  newEndAngle += delta;\n  if (!anticlockwise && newEndAngle - newStartAngle >= PI2) {\n    newEndAngle = newStartAngle + PI2;\n  } else if (anticlockwise && newStartAngle - newEndAngle >= PI2) {\n    newEndAngle = newStartAngle - PI2;\n  } else if (!anticlockwise && newStartAngle > newEndAngle) {\n    newEndAngle = newStartAngle + (PI2 - modPI2(newStartAngle - newEndAngle));\n  } else if (anticlockwise && newStartAngle < newEndAngle) {\n    newEndAngle = newStartAngle - (PI2 - modPI2(newEndAngle - newStartAngle));\n  }\n  angles[0] = newStartAngle;\n  angles[1] = newEndAngle;\n}\nvar PathProxy = function () {\n  function PathProxy(notSaveData) {\n    this.dpr = 1;\n    this._xi = 0;\n    this._yi = 0;\n    this._x0 = 0;\n    this._y0 = 0;\n    this._len = 0;\n    if (notSaveData) {\n      this._saveData = false;\n    }\n    if (this._saveData) {\n      this.data = [];\n    }\n  }\n  PathProxy.prototype.increaseVersion = function () {\n    this._version++;\n  };\n  PathProxy.prototype.getVersion = function () {\n    return this._version;\n  };\n  PathProxy.prototype.setScale = function (sx, sy, segmentIgnoreThreshold) {\n    segmentIgnoreThreshold = segmentIgnoreThreshold || 0;\n    if (segmentIgnoreThreshold > 0) {\n      this._ux = mathAbs(segmentIgnoreThreshold / dpr / sx) || 0;\n      this._uy = mathAbs(segmentIgnoreThreshold / dpr / sy) || 0;\n    }\n  };\n  PathProxy.prototype.setDPR = function (dpr) {\n    this.dpr = dpr;\n  };\n  PathProxy.prototype.setContext = function (ctx) {\n    this._ctx = ctx;\n  };\n  PathProxy.prototype.getContext = function () {\n    return this._ctx;\n  };\n  PathProxy.prototype.beginPath = function () {\n    this._ctx && this._ctx.beginPath();\n    this.reset();\n    return this;\n  };\n  PathProxy.prototype.reset = function () {\n    if (this._saveData) {\n      this._len = 0;\n    }\n    if (this._pathSegLen) {\n      this._pathSegLen = null;\n      this._pathLen = 0;\n    }\n    this._version++;\n  };\n  PathProxy.prototype.moveTo = function (x, y) {\n    this._drawPendingPt();\n    this.addData(CMD.M, x, y);\n    this._ctx && this._ctx.moveTo(x, y);\n    this._x0 = x;\n    this._y0 = y;\n    this._xi = x;\n    this._yi = y;\n    return this;\n  };\n  PathProxy.prototype.lineTo = function (x, y) {\n    var dx = mathAbs(x - this._xi);\n    var dy = mathAbs(y - this._yi);\n    var exceedUnit = dx > this._ux || dy > this._uy;\n    this.addData(CMD.L, x, y);\n    if (this._ctx && exceedUnit) {\n      this._ctx.lineTo(x, y);\n    }\n    if (exceedUnit) {\n      this._xi = x;\n      this._yi = y;\n      this._pendingPtDist = 0;\n    } else {\n      var d2 = dx * dx + dy * dy;\n      if (d2 > this._pendingPtDist) {\n        this._pendingPtX = x;\n        this._pendingPtY = y;\n        this._pendingPtDist = d2;\n      }\n    }\n    return this;\n  };\n  PathProxy.prototype.bezierCurveTo = function (x1, y1, x2, y2, x3, y3) {\n    this._drawPendingPt();\n    this.addData(CMD.C, x1, y1, x2, y2, x3, y3);\n    if (this._ctx) {\n      this._ctx.bezierCurveTo(x1, y1, x2, y2, x3, y3);\n    }\n    this._xi = x3;\n    this._yi = y3;\n    return this;\n  };\n  PathProxy.prototype.quadraticCurveTo = function (x1, y1, x2, y2) {\n    this._drawPendingPt();\n    this.addData(CMD.Q, x1, y1, x2, y2);\n    if (this._ctx) {\n      this._ctx.quadraticCurveTo(x1, y1, x2, y2);\n    }\n    this._xi = x2;\n    this._yi = y2;\n    return this;\n  };\n  PathProxy.prototype.arc = function (cx, cy, r, startAngle, endAngle, anticlockwise) {\n    this._drawPendingPt();\n    tmpAngles[0] = startAngle;\n    tmpAngles[1] = endAngle;\n    normalizeArcAngles(tmpAngles, anticlockwise);\n    startAngle = tmpAngles[0];\n    endAngle = tmpAngles[1];\n    var delta = endAngle - startAngle;\n    this.addData(CMD.A, cx, cy, r, r, startAngle, delta, 0, anticlockwise ? 0 : 1);\n    this._ctx && this._ctx.arc(cx, cy, r, startAngle, endAngle, anticlockwise);\n    this._xi = mathCos(endAngle) * r + cx;\n    this._yi = mathSin(endAngle) * r + cy;\n    return this;\n  };\n  PathProxy.prototype.arcTo = function (x1, y1, x2, y2, radius) {\n    this._drawPendingPt();\n    if (this._ctx) {\n      this._ctx.arcTo(x1, y1, x2, y2, radius);\n    }\n    return this;\n  };\n  PathProxy.prototype.rect = function (x, y, w, h) {\n    this._drawPendingPt();\n    this._ctx && this._ctx.rect(x, y, w, h);\n    this.addData(CMD.R, x, y, w, h);\n    return this;\n  };\n  PathProxy.prototype.closePath = function () {\n    this._drawPendingPt();\n    this.addData(CMD.Z);\n    var ctx = this._ctx;\n    var x0 = this._x0;\n    var y0 = this._y0;\n    if (ctx) {\n      ctx.closePath();\n    }\n    this._xi = x0;\n    this._yi = y0;\n    return this;\n  };\n  PathProxy.prototype.fill = function (ctx) {\n    ctx && ctx.fill();\n    this.toStatic();\n  };\n  PathProxy.prototype.stroke = function (ctx) {\n    ctx && ctx.stroke();\n    this.toStatic();\n  };\n  PathProxy.prototype.len = function () {\n    return this._len;\n  };\n  PathProxy.prototype.setData = function (data) {\n    var len = data.length;\n    if (!(this.data && this.data.length === len) && hasTypedArray) {\n      this.data = new Float32Array(len);\n    }\n    for (var i = 0; i < len; i++) {\n      this.data[i] = data[i];\n    }\n    this._len = len;\n  };\n  PathProxy.prototype.appendPath = function (path) {\n    if (!(path instanceof Array)) {\n      path = [path];\n    }\n    var len = path.length;\n    var appendSize = 0;\n    var offset = this._len;\n    for (var i = 0; i < len; i++) {\n      appendSize += path[i].len();\n    }\n    if (hasTypedArray && this.data instanceof Float32Array) {\n      this.data = new Float32Array(offset + appendSize);\n    }\n    for (var i = 0; i < len; i++) {\n      var appendPathData = path[i].data;\n      for (var k = 0; k < appendPathData.length; k++) {\n        this.data[offset++] = appendPathData[k];\n      }\n    }\n    this._len = offset;\n  };\n  PathProxy.prototype.addData = function (cmd, a, b, c, d, e, f, g, h) {\n    if (!this._saveData) {\n      return;\n    }\n    var data = this.data;\n    if (this._len + arguments.length > data.length) {\n      this._expandData();\n      data = this.data;\n    }\n    for (var i = 0; i < arguments.length; i++) {\n      data[this._len++] = arguments[i];\n    }\n  };\n  PathProxy.prototype._drawPendingPt = function () {\n    if (this._pendingPtDist > 0) {\n      this._ctx && this._ctx.lineTo(this._pendingPtX, this._pendingPtY);\n      this._pendingPtDist = 0;\n    }\n  };\n  PathProxy.prototype._expandData = function () {\n    if (!(this.data instanceof Array)) {\n      var newData = [];\n      for (var i = 0; i < this._len; i++) {\n        newData[i] = this.data[i];\n      }\n      this.data = newData;\n    }\n  };\n  PathProxy.prototype.toStatic = function () {\n    if (!this._saveData) {\n      return;\n    }\n    this._drawPendingPt();\n    var data = this.data;\n    if (data instanceof Array) {\n      data.length = this._len;\n      if (hasTypedArray && this._len > 11) {\n        this.data = new Float32Array(data);\n      }\n    }\n  };\n  PathProxy.prototype.getBoundingRect = function () {\n    min[0] = min[1] = min2[0] = min2[1] = Number.MAX_VALUE;\n    max[0] = max[1] = max2[0] = max2[1] = -Number.MAX_VALUE;\n    var data = this.data;\n    var xi = 0;\n    var yi = 0;\n    var x0 = 0;\n    var y0 = 0;\n    var i;\n    for (i = 0; i < this._len;) {\n      var cmd = data[i++];\n      var isFirst = i === 1;\n      if (isFirst) {\n        xi = data[i];\n        yi = data[i + 1];\n        x0 = xi;\n        y0 = yi;\n      }\n      switch (cmd) {\n        case CMD.M:\n          xi = x0 = data[i++];\n          yi = y0 = data[i++];\n          min2[0] = x0;\n          min2[1] = y0;\n          max2[0] = x0;\n          max2[1] = y0;\n          break;\n        case CMD.L:\n          fromLine(xi, yi, data[i], data[i + 1], min2, max2);\n          xi = data[i++];\n          yi = data[i++];\n          break;\n        case CMD.C:\n          fromCubic(xi, yi, data[i++], data[i++], data[i++], data[i++], data[i], data[i + 1], min2, max2);\n          xi = data[i++];\n          yi = data[i++];\n          break;\n        case CMD.Q:\n          fromQuadratic(xi, yi, data[i++], data[i++], data[i], data[i + 1], min2, max2);\n          xi = data[i++];\n          yi = data[i++];\n          break;\n        case CMD.A:\n          var cx = data[i++];\n          var cy = data[i++];\n          var rx = data[i++];\n          var ry = data[i++];\n          var startAngle = data[i++];\n          var endAngle = data[i++] + startAngle;\n          i += 1;\n          var anticlockwise = !data[i++];\n          if (isFirst) {\n            x0 = mathCos(startAngle) * rx + cx;\n            y0 = mathSin(startAngle) * ry + cy;\n          }\n          fromArc(cx, cy, rx, ry, startAngle, endAngle, anticlockwise, min2, max2);\n          xi = mathCos(endAngle) * rx + cx;\n          yi = mathSin(endAngle) * ry + cy;\n          break;\n        case CMD.R:\n          x0 = xi = data[i++];\n          y0 = yi = data[i++];\n          var width = data[i++];\n          var height = data[i++];\n          fromLine(x0, y0, x0 + width, y0 + height, min2, max2);\n          break;\n        case CMD.Z:\n          xi = x0;\n          yi = y0;\n          break;\n      }\n      vec2.min(min, min, min2);\n      vec2.max(max, max, max2);\n    }\n    if (i === 0) {\n      min[0] = min[1] = max[0] = max[1] = 0;\n    }\n    return new BoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n  };\n  PathProxy.prototype._calculateLength = function () {\n    var data = this.data;\n    var len = this._len;\n    var ux = this._ux;\n    var uy = this._uy;\n    var xi = 0;\n    var yi = 0;\n    var x0 = 0;\n    var y0 = 0;\n    if (!this._pathSegLen) {\n      this._pathSegLen = [];\n    }\n    var pathSegLen = this._pathSegLen;\n    var pathTotalLen = 0;\n    var segCount = 0;\n    for (var i = 0; i < len;) {\n      var cmd = data[i++];\n      var isFirst = i === 1;\n      if (isFirst) {\n        xi = data[i];\n        yi = data[i + 1];\n        x0 = xi;\n        y0 = yi;\n      }\n      var l = -1;\n      switch (cmd) {\n        case CMD.M:\n          xi = x0 = data[i++];\n          yi = y0 = data[i++];\n          break;\n        case CMD.L:\n          {\n            var x2 = data[i++];\n            var y2 = data[i++];\n            var dx = x2 - xi;\n            var dy = y2 - yi;\n            if (mathAbs(dx) > ux || mathAbs(dy) > uy || i === len - 1) {\n              l = Math.sqrt(dx * dx + dy * dy);\n              xi = x2;\n              yi = y2;\n            }\n            break;\n          }\n        case CMD.C:\n          {\n            var x1 = data[i++];\n            var y1 = data[i++];\n            var x2 = data[i++];\n            var y2 = data[i++];\n            var x3 = data[i++];\n            var y3 = data[i++];\n            l = cubicLength(xi, yi, x1, y1, x2, y2, x3, y3, 10);\n            xi = x3;\n            yi = y3;\n            break;\n          }\n        case CMD.Q:\n          {\n            var x1 = data[i++];\n            var y1 = data[i++];\n            var x2 = data[i++];\n            var y2 = data[i++];\n            l = quadraticLength(xi, yi, x1, y1, x2, y2, 10);\n            xi = x2;\n            yi = y2;\n            break;\n          }\n        case CMD.A:\n          var cx = data[i++];\n          var cy = data[i++];\n          var rx = data[i++];\n          var ry = data[i++];\n          var startAngle = data[i++];\n          var delta = data[i++];\n          var endAngle = delta + startAngle;\n          i += 1;\n          if (isFirst) {\n            x0 = mathCos(startAngle) * rx + cx;\n            y0 = mathSin(startAngle) * ry + cy;\n          }\n          l = mathMax(rx, ry) * mathMin(PI2, Math.abs(delta));\n          xi = mathCos(endAngle) * rx + cx;\n          yi = mathSin(endAngle) * ry + cy;\n          break;\n        case CMD.R:\n          {\n            x0 = xi = data[i++];\n            y0 = yi = data[i++];\n            var width = data[i++];\n            var height = data[i++];\n            l = width * 2 + height * 2;\n            break;\n          }\n        case CMD.Z:\n          {\n            var dx = x0 - xi;\n            var dy = y0 - yi;\n            l = Math.sqrt(dx * dx + dy * dy);\n            xi = x0;\n            yi = y0;\n            break;\n          }\n      }\n      if (l >= 0) {\n        pathSegLen[segCount++] = l;\n        pathTotalLen += l;\n      }\n    }\n    this._pathLen = pathTotalLen;\n    return pathTotalLen;\n  };\n  PathProxy.prototype.rebuildPath = function (ctx, percent) {\n    var d = this.data;\n    var ux = this._ux;\n    var uy = this._uy;\n    var len = this._len;\n    var x0;\n    var y0;\n    var xi;\n    var yi;\n    var x;\n    var y;\n    var drawPart = percent < 1;\n    var pathSegLen;\n    var pathTotalLen;\n    var accumLength = 0;\n    var segCount = 0;\n    var displayedLength;\n    var pendingPtDist = 0;\n    var pendingPtX;\n    var pendingPtY;\n    if (drawPart) {\n      if (!this._pathSegLen) {\n        this._calculateLength();\n      }\n      pathSegLen = this._pathSegLen;\n      pathTotalLen = this._pathLen;\n      displayedLength = percent * pathTotalLen;\n      if (!displayedLength) {\n        return;\n      }\n    }\n    lo: for (var i = 0; i < len;) {\n      var cmd = d[i++];\n      var isFirst = i === 1;\n      if (isFirst) {\n        xi = d[i];\n        yi = d[i + 1];\n        x0 = xi;\n        y0 = yi;\n      }\n      if (cmd !== CMD.L && pendingPtDist > 0) {\n        ctx.lineTo(pendingPtX, pendingPtY);\n        pendingPtDist = 0;\n      }\n      switch (cmd) {\n        case CMD.M:\n          x0 = xi = d[i++];\n          y0 = yi = d[i++];\n          ctx.moveTo(xi, yi);\n          break;\n        case CMD.L:\n          {\n            x = d[i++];\n            y = d[i++];\n            var dx = mathAbs(x - xi);\n            var dy = mathAbs(y - yi);\n            if (dx > ux || dy > uy) {\n              if (drawPart) {\n                var l = pathSegLen[segCount++];\n                if (accumLength + l > displayedLength) {\n                  var t = (displayedLength - accumLength) / l;\n                  ctx.lineTo(xi * (1 - t) + x * t, yi * (1 - t) + y * t);\n                  break lo;\n                }\n                accumLength += l;\n              }\n              ctx.lineTo(x, y);\n              xi = x;\n              yi = y;\n              pendingPtDist = 0;\n            } else {\n              var d2 = dx * dx + dy * dy;\n              if (d2 > pendingPtDist) {\n                pendingPtX = x;\n                pendingPtY = y;\n                pendingPtDist = d2;\n              }\n            }\n            break;\n          }\n        case CMD.C:\n          {\n            var x1 = d[i++];\n            var y1 = d[i++];\n            var x2 = d[i++];\n            var y2 = d[i++];\n            var x3 = d[i++];\n            var y3 = d[i++];\n            if (drawPart) {\n              var l = pathSegLen[segCount++];\n              if (accumLength + l > displayedLength) {\n                var t = (displayedLength - accumLength) / l;\n                cubicSubdivide(xi, x1, x2, x3, t, tmpOutX);\n                cubicSubdivide(yi, y1, y2, y3, t, tmpOutY);\n                ctx.bezierCurveTo(tmpOutX[1], tmpOutY[1], tmpOutX[2], tmpOutY[2], tmpOutX[3], tmpOutY[3]);\n                break lo;\n              }\n              accumLength += l;\n            }\n            ctx.bezierCurveTo(x1, y1, x2, y2, x3, y3);\n            xi = x3;\n            yi = y3;\n            break;\n          }\n        case CMD.Q:\n          {\n            var x1 = d[i++];\n            var y1 = d[i++];\n            var x2 = d[i++];\n            var y2 = d[i++];\n            if (drawPart) {\n              var l = pathSegLen[segCount++];\n              if (accumLength + l > displayedLength) {\n                var t = (displayedLength - accumLength) / l;\n                quadraticSubdivide(xi, x1, x2, t, tmpOutX);\n                quadraticSubdivide(yi, y1, y2, t, tmpOutY);\n                ctx.quadraticCurveTo(tmpOutX[1], tmpOutY[1], tmpOutX[2], tmpOutY[2]);\n                break lo;\n              }\n              accumLength += l;\n            }\n            ctx.quadraticCurveTo(x1, y1, x2, y2);\n            xi = x2;\n            yi = y2;\n            break;\n          }\n        case CMD.A:\n          var cx = d[i++];\n          var cy = d[i++];\n          var rx = d[i++];\n          var ry = d[i++];\n          var startAngle = d[i++];\n          var delta = d[i++];\n          var psi = d[i++];\n          var anticlockwise = !d[i++];\n          var r = rx > ry ? rx : ry;\n          var isEllipse = mathAbs(rx - ry) > 1e-3;\n          var endAngle = startAngle + delta;\n          var breakBuild = false;\n          if (drawPart) {\n            var l = pathSegLen[segCount++];\n            if (accumLength + l > displayedLength) {\n              endAngle = startAngle + delta * (displayedLength - accumLength) / l;\n              breakBuild = true;\n            }\n            accumLength += l;\n          }\n          if (isEllipse && ctx.ellipse) {\n            ctx.ellipse(cx, cy, rx, ry, psi, startAngle, endAngle, anticlockwise);\n          } else {\n            ctx.arc(cx, cy, r, startAngle, endAngle, anticlockwise);\n          }\n          if (breakBuild) {\n            break lo;\n          }\n          if (isFirst) {\n            x0 = mathCos(startAngle) * rx + cx;\n            y0 = mathSin(startAngle) * ry + cy;\n          }\n          xi = mathCos(endAngle) * rx + cx;\n          yi = mathSin(endAngle) * ry + cy;\n          break;\n        case CMD.R:\n          x0 = xi = d[i];\n          y0 = yi = d[i + 1];\n          x = d[i++];\n          y = d[i++];\n          var width = d[i++];\n          var height = d[i++];\n          if (drawPart) {\n            var l = pathSegLen[segCount++];\n            if (accumLength + l > displayedLength) {\n              var d_1 = displayedLength - accumLength;\n              ctx.moveTo(x, y);\n              ctx.lineTo(x + mathMin(d_1, width), y);\n              d_1 -= width;\n              if (d_1 > 0) {\n                ctx.lineTo(x + width, y + mathMin(d_1, height));\n              }\n              d_1 -= height;\n              if (d_1 > 0) {\n                ctx.lineTo(x + mathMax(width - d_1, 0), y + height);\n              }\n              d_1 -= width;\n              if (d_1 > 0) {\n                ctx.lineTo(x, y + mathMax(height - d_1, 0));\n              }\n              break lo;\n            }\n            accumLength += l;\n          }\n          ctx.rect(x, y, width, height);\n          break;\n        case CMD.Z:\n          if (drawPart) {\n            var l = pathSegLen[segCount++];\n            if (accumLength + l > displayedLength) {\n              var t = (displayedLength - accumLength) / l;\n              ctx.lineTo(xi * (1 - t) + x0 * t, yi * (1 - t) + y0 * t);\n              break lo;\n            }\n            accumLength += l;\n          }\n          ctx.closePath();\n          xi = x0;\n          yi = y0;\n      }\n    }\n  };\n  PathProxy.prototype.clone = function () {\n    var newProxy = new PathProxy();\n    var data = this.data;\n    newProxy.data = data.slice ? data.slice() : Array.prototype.slice.call(data);\n    newProxy._len = this._len;\n    return newProxy;\n  };\n  PathProxy.CMD = CMD;\n  PathProxy.initDefaultProps = function () {\n    var proto = PathProxy.prototype;\n    proto._saveData = true;\n    proto._ux = 0;\n    proto._uy = 0;\n    proto._pendingPtDist = 0;\n    proto._version = 0;\n  }();\n  return PathProxy;\n}();\nexport default PathProxy;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}