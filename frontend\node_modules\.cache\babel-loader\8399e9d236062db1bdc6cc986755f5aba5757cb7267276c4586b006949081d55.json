{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isTypedArray, clone, createHashMap, isArray, isObject, isArrayLike, hasOwn, assert, each, map, isNumber, isString, keys } from 'zrender/lib/core/util.js';\nimport { SOURCE_FORMAT_ORIGINAL, SERIES_LAYOUT_BY_COLUMN, SOURCE_FORMAT_UNKNOWN, SOURCE_FORMAT_KEYED_COLUMNS, SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ARRAY_ROWS, SOURCE_FORMAT_OBJECT_ROWS, SERIES_LAYOUT_BY_ROW } from '../util/types.js';\nimport { getDataItemValue } from '../util/model.js';\nimport { BE_ORDINAL, guessOrdinal } from './helper/sourceHelper.js';\n;\n// @inner\nvar SourceImpl = /** @class */function () {\n  function SourceImpl(fields) {\n    this.data = fields.data || (fields.sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS ? {} : []);\n    this.sourceFormat = fields.sourceFormat || SOURCE_FORMAT_UNKNOWN;\n    // Visit config\n    this.seriesLayoutBy = fields.seriesLayoutBy || SERIES_LAYOUT_BY_COLUMN;\n    this.startIndex = fields.startIndex || 0;\n    this.dimensionsDetectedCount = fields.dimensionsDetectedCount;\n    this.metaRawOption = fields.metaRawOption;\n    var dimensionsDefine = this.dimensionsDefine = fields.dimensionsDefine;\n    if (dimensionsDefine) {\n      for (var i = 0; i < dimensionsDefine.length; i++) {\n        var dim = dimensionsDefine[i];\n        if (dim.type == null) {\n          if (guessOrdinal(this, i) === BE_ORDINAL.Must) {\n            dim.type = 'ordinal';\n          }\n        }\n      }\n    }\n  }\n  return SourceImpl;\n}();\nexport function isSourceInstance(val) {\n  return val instanceof SourceImpl;\n}\n/**\r\n * Create a source from option.\r\n * NOTE: Created source is immutable. Don't change any properties in it.\r\n */\nexport function createSource(sourceData, thisMetaRawOption,\n// can be null. If not provided, auto detect it from `sourceData`.\nsourceFormat) {\n  sourceFormat = sourceFormat || detectSourceFormat(sourceData);\n  var seriesLayoutBy = thisMetaRawOption.seriesLayoutBy;\n  var determined = determineSourceDimensions(sourceData, sourceFormat, seriesLayoutBy, thisMetaRawOption.sourceHeader, thisMetaRawOption.dimensions);\n  var source = new SourceImpl({\n    data: sourceData,\n    sourceFormat: sourceFormat,\n    seriesLayoutBy: seriesLayoutBy,\n    dimensionsDefine: determined.dimensionsDefine,\n    startIndex: determined.startIndex,\n    dimensionsDetectedCount: determined.dimensionsDetectedCount,\n    metaRawOption: clone(thisMetaRawOption)\n  });\n  return source;\n}\n/**\r\n * Wrap original series data for some compatibility cases.\r\n */\nexport function createSourceFromSeriesDataOption(data) {\n  return new SourceImpl({\n    data: data,\n    sourceFormat: isTypedArray(data) ? SOURCE_FORMAT_TYPED_ARRAY : SOURCE_FORMAT_ORIGINAL\n  });\n}\n/**\r\n * Clone source but excludes source data.\r\n */\nexport function cloneSourceShallow(source) {\n  return new SourceImpl({\n    data: source.data,\n    sourceFormat: source.sourceFormat,\n    seriesLayoutBy: source.seriesLayoutBy,\n    dimensionsDefine: clone(source.dimensionsDefine),\n    startIndex: source.startIndex,\n    dimensionsDetectedCount: source.dimensionsDetectedCount\n  });\n}\n/**\r\n * Note: An empty array will be detected as `SOURCE_FORMAT_ARRAY_ROWS`.\r\n */\nexport function detectSourceFormat(data) {\n  var sourceFormat = SOURCE_FORMAT_UNKNOWN;\n  if (isTypedArray(data)) {\n    sourceFormat = SOURCE_FORMAT_TYPED_ARRAY;\n  } else if (isArray(data)) {\n    // FIXME Whether tolerate null in top level array?\n    if (data.length === 0) {\n      sourceFormat = SOURCE_FORMAT_ARRAY_ROWS;\n    }\n    for (var i = 0, len = data.length; i < len; i++) {\n      var item = data[i];\n      if (item == null) {\n        continue;\n      } else if (isArray(item) || isTypedArray(item)) {\n        sourceFormat = SOURCE_FORMAT_ARRAY_ROWS;\n        break;\n      } else if (isObject(item)) {\n        sourceFormat = SOURCE_FORMAT_OBJECT_ROWS;\n        break;\n      }\n    }\n  } else if (isObject(data)) {\n    for (var key in data) {\n      if (hasOwn(data, key) && isArrayLike(data[key])) {\n        sourceFormat = SOURCE_FORMAT_KEYED_COLUMNS;\n        break;\n      }\n    }\n  }\n  return sourceFormat;\n}\n/**\r\n * Determine the source definitions from data standalone dimensions definitions\r\n * are not specified.\r\n */\nfunction determineSourceDimensions(data, sourceFormat, seriesLayoutBy, sourceHeader,\n// standalone raw dimensions definition, like:\n// {\n//     dimensions: ['aa', 'bb', { name: 'cc', type: 'time' }]\n// }\n// in `dataset` or `series`\ndimensionsDefine) {\n  var dimensionsDetectedCount;\n  var startIndex;\n  // PENDING: Could data be null/undefined here?\n  // currently, if `dataset.source` not specified, error thrown.\n  // if `series.data` not specified, nothing rendered without error thrown.\n  // Should test these cases.\n  if (!data) {\n    return {\n      dimensionsDefine: normalizeDimensionsOption(dimensionsDefine),\n      startIndex: startIndex,\n      dimensionsDetectedCount: dimensionsDetectedCount\n    };\n  }\n  if (sourceFormat === SOURCE_FORMAT_ARRAY_ROWS) {\n    var dataArrayRows = data;\n    // Rule: Most of the first line are string: it is header.\n    // Caution: consider a line with 5 string and 1 number,\n    // it still can not be sure it is a head, because the\n    // 5 string may be 5 values of category columns.\n    if (sourceHeader === 'auto' || sourceHeader == null) {\n      arrayRowsTravelFirst(function (val) {\n        // '-' is regarded as null/undefined.\n        if (val != null && val !== '-') {\n          if (isString(val)) {\n            startIndex == null && (startIndex = 1);\n          } else {\n            startIndex = 0;\n          }\n        }\n        // 10 is an experience number, avoid long loop.\n      }, seriesLayoutBy, dataArrayRows, 10);\n    } else {\n      startIndex = isNumber(sourceHeader) ? sourceHeader : sourceHeader ? 1 : 0;\n    }\n    if (!dimensionsDefine && startIndex === 1) {\n      dimensionsDefine = [];\n      arrayRowsTravelFirst(function (val, index) {\n        dimensionsDefine[index] = val != null ? val + '' : '';\n      }, seriesLayoutBy, dataArrayRows, Infinity);\n    }\n    dimensionsDetectedCount = dimensionsDefine ? dimensionsDefine.length : seriesLayoutBy === SERIES_LAYOUT_BY_ROW ? dataArrayRows.length : dataArrayRows[0] ? dataArrayRows[0].length : null;\n  } else if (sourceFormat === SOURCE_FORMAT_OBJECT_ROWS) {\n    if (!dimensionsDefine) {\n      dimensionsDefine = objectRowsCollectDimensions(data);\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS) {\n    if (!dimensionsDefine) {\n      dimensionsDefine = [];\n      each(data, function (colArr, key) {\n        dimensionsDefine.push(key);\n      });\n    }\n  } else if (sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n    var value0 = getDataItemValue(data[0]);\n    dimensionsDetectedCount = isArray(value0) && value0.length || 1;\n  } else if (sourceFormat === SOURCE_FORMAT_TYPED_ARRAY) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(!!dimensionsDefine, 'dimensions must be given if data is TypedArray.');\n    }\n  }\n  return {\n    startIndex: startIndex,\n    dimensionsDefine: normalizeDimensionsOption(dimensionsDefine),\n    dimensionsDetectedCount: dimensionsDetectedCount\n  };\n}\nfunction objectRowsCollectDimensions(data) {\n  var firstIndex = 0;\n  var obj;\n  while (firstIndex < data.length && !(obj = data[firstIndex++])) {} // jshint ignore: line\n  if (obj) {\n    return keys(obj);\n  }\n}\n// Consider dimensions defined like ['A', 'price', 'B', 'price', 'C', 'price'],\n// which is reasonable. But dimension name is duplicated.\n// Returns undefined or an array contains only object without null/undefined or string.\nfunction normalizeDimensionsOption(dimensionsDefine) {\n  if (!dimensionsDefine) {\n    // The meaning of null/undefined is different from empty array.\n    return;\n  }\n  var nameMap = createHashMap();\n  return map(dimensionsDefine, function (rawItem, index) {\n    rawItem = isObject(rawItem) ? rawItem : {\n      name: rawItem\n    };\n    // Other fields will be discarded.\n    var item = {\n      name: rawItem.name,\n      displayName: rawItem.displayName,\n      type: rawItem.type\n    };\n    // User can set null in dimensions.\n    // We don't auto specify name, otherwise a given name may\n    // cause it to be referred unexpectedly.\n    if (item.name == null) {\n      return item;\n    }\n    // Also consider number form like 2012.\n    item.name += '';\n    // User may also specify displayName.\n    // displayName will always exists except user not\n    // specified or dim name is not specified or detected.\n    // (A auto generated dim name will not be used as\n    // displayName).\n    if (item.displayName == null) {\n      item.displayName = item.name;\n    }\n    var exist = nameMap.get(item.name);\n    if (!exist) {\n      nameMap.set(item.name, {\n        count: 1\n      });\n    } else {\n      item.name += '-' + exist.count++;\n    }\n    return item;\n  });\n}\nfunction arrayRowsTravelFirst(cb, seriesLayoutBy, data, maxLoop) {\n  if (seriesLayoutBy === SERIES_LAYOUT_BY_ROW) {\n    for (var i = 0; i < data.length && i < maxLoop; i++) {\n      cb(data[i] ? data[i][0] : null, i);\n    }\n  } else {\n    var value0 = data[0] || [];\n    for (var i = 0; i < value0.length && i < maxLoop; i++) {\n      cb(value0[i], i);\n    }\n  }\n}\nexport function shouldRetrieveDataByName(source) {\n  var sourceFormat = source.sourceFormat;\n  return sourceFormat === SOURCE_FORMAT_OBJECT_ROWS || sourceFormat === SOURCE_FORMAT_KEYED_COLUMNS;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}