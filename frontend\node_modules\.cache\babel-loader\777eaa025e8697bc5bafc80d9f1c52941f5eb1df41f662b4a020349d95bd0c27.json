{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nvar ExtraContent = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var position = props.position,\n    prefixCls = props.prefixCls,\n    extra = props.extra;\n  if (!extra) {\n    return null;\n  }\n  var content;\n\n  // Parse extra\n  var assertExtra = {};\n  if (_typeof(extra) === 'object' && ! /*#__PURE__*/React.isValidElement(extra)) {\n    assertExtra = extra;\n  } else {\n    assertExtra.right = extra;\n  }\n  if (position === 'right') {\n    content = assertExtra.right;\n  }\n  if (position === 'left') {\n    content = assertExtra.left;\n  }\n  return content ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-extra-content\"),\n    ref: ref\n  }, content) : null;\n});\nif (process.env.NODE_ENV !== 'production') {\n  ExtraContent.displayName = 'ExtraContent';\n}\nexport default ExtraContent;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}