{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { extend } from 'zrender/lib/core/util.js';\nfunction normalize(a) {\n  if (!(a instanceof Array)) {\n    a = [a, a];\n  }\n  return a;\n}\nexport default function graphEdgeVisual(ecModel) {\n  ecModel.eachSeriesByType('graph', function (seriesModel) {\n    var graph = seriesModel.getGraph();\n    var edgeData = seriesModel.getEdgeData();\n    var symbolType = normalize(seriesModel.get('edgeSymbol'));\n    var symbolSize = normalize(seriesModel.get('edgeSymbolSize'));\n    // const colorQuery = ['lineStyle', 'color'] as const;\n    // const opacityQuery = ['lineStyle', 'opacity'] as const;\n    edgeData.setVisual('fromSymbol', symbolType && symbolType[0]);\n    edgeData.setVisual('toSymbol', symbolType && symbolType[1]);\n    edgeData.setVisual('fromSymbolSize', symbolSize && symbolSize[0]);\n    edgeData.setVisual('toSymbolSize', symbolSize && symbolSize[1]);\n    edgeData.setVisual('style', seriesModel.getModel('lineStyle').getLineStyle());\n    edgeData.each(function (idx) {\n      var itemModel = edgeData.getItemModel(idx);\n      var edge = graph.getEdgeByIndex(idx);\n      var symbolType = normalize(itemModel.getShallow('symbol', true));\n      var symbolSize = normalize(itemModel.getShallow('symbolSize', true));\n      // Edge visual must after node visual\n      var style = itemModel.getModel('lineStyle').getLineStyle();\n      var existsStyle = edgeData.ensureUniqueItemVisual(idx, 'style');\n      extend(existsStyle, style);\n      switch (existsStyle.stroke) {\n        case 'source':\n          {\n            var nodeStyle = edge.node1.getVisual('style');\n            existsStyle.stroke = nodeStyle && nodeStyle.fill;\n            break;\n          }\n        case 'target':\n          {\n            var nodeStyle = edge.node2.getVisual('style');\n            existsStyle.stroke = nodeStyle && nodeStyle.fill;\n            break;\n          }\n      }\n      symbolType[0] && edge.setVisual('fromSymbol', symbolType[0]);\n      symbolType[1] && edge.setVisual('toSymbol', symbolType[1]);\n      symbolSize[0] && edge.setVisual('fromSymbolSize', symbolSize[0]);\n      symbolSize[1] && edge.setVisual('toSymbolSize', symbolSize[1]);\n    });\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}