{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\nvar _debounce = _interopRequireDefault(require(\"../debounce\"));\nvar _constant = require(\"../constant\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\n\nvar createSensor = function createSensor(element, whenDestroy) {\n  var sensor = undefined;\n  // callback\n  var listeners = [];\n\n  /**\n   * create object DOM of sensor\n   * @returns {HTMLObjectElement}\n   */\n  var newSensor = function newSensor() {\n    // adjust style\n    if (getComputedStyle(element).position === 'static') {\n      element.style.position = 'relative';\n    }\n    var obj = document.createElement('object');\n    obj.onload = function () {\n      obj.contentDocument.defaultView.addEventListener('resize', resizeListener);\n      // 直接触发一次 resize\n      resizeListener();\n    };\n    obj.style.display = 'block';\n    obj.style.position = 'absolute';\n    obj.style.top = '0';\n    obj.style.left = '0';\n    obj.style.height = '100%';\n    obj.style.width = '100%';\n    obj.style.overflow = 'hidden';\n    obj.style.pointerEvents = 'none';\n    obj.style.zIndex = '-1';\n    obj.style.opacity = '0';\n    obj.setAttribute('class', _constant.SensorClassName);\n    obj.setAttribute('tabindex', _constant.SensorTabIndex);\n    obj.type = 'text/html';\n\n    // append into dom\n    element.appendChild(obj);\n    // for ie, should set data attribute delay, or will be white screen\n    obj.data = 'about:blank';\n    return obj;\n  };\n\n  /**\n   * trigger listeners\n   */\n  var resizeListener = (0, _debounce[\"default\"])(function () {\n    // trigger all listener\n    listeners.forEach(function (listener) {\n      listener(element);\n    });\n  });\n\n  /**\n   * listen with one callback function\n   * @param cb\n   */\n  var bind = function bind(cb) {\n    // if not exist sensor, then create one\n    if (!sensor) {\n      sensor = newSensor();\n    }\n    if (listeners.indexOf(cb) === -1) {\n      listeners.push(cb);\n    }\n  };\n\n  /**\n   * destroy all\n   */\n  var destroy = function destroy() {\n    if (sensor && sensor.parentNode) {\n      if (sensor.contentDocument) {\n        // remote event\n        sensor.contentDocument.defaultView.removeEventListener('resize', resizeListener);\n      }\n      // remove dom\n      sensor.parentNode.removeChild(sensor);\n      // initial variable\n      element.removeAttribute(_constant.SizeSensorId);\n      sensor = undefined;\n      listeners = [];\n      whenDestroy && whenDestroy();\n    }\n  };\n\n  /**\n   * cancel listener bind\n   * @param cb\n   */\n  var unbind = function unbind(cb) {\n    var idx = listeners.indexOf(cb);\n    if (idx !== -1) {\n      listeners.splice(idx, 1);\n    }\n\n    // no listener, and sensor is exist\n    // then destroy the sensor\n    if (listeners.length === 0 && sensor) {\n      destroy();\n    }\n  };\n  return {\n    element: element,\n    bind: bind,\n    destroy: destroy,\n    unbind: unbind\n  };\n};\nexports.createSensor = createSensor;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}