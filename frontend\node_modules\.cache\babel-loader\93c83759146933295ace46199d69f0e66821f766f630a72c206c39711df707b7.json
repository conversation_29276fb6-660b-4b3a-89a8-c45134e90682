{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport Model from '../../model/Model.js';\nimport { each, curry, clone, defaults, isArray, indexOf } from 'zrender/lib/core/util.js';\n// Build axisPointerModel, mergin tooltip.axisPointer model for each axis.\n// allAxesInfo should be updated when setOption performed.\nexport function collect(ecModel, api) {\n  var result = {\n    /**\r\n     * key: makeKey(axis.model)\r\n     * value: {\r\n     *      axis,\r\n     *      coordSys,\r\n     *      axisPointerModel,\r\n     *      triggerTooltip,\r\n     *      triggerEmphasis,\r\n     *      involveSeries,\r\n     *      snap,\r\n     *      seriesModels,\r\n     *      seriesDataCount\r\n     * }\r\n     */\n    axesInfo: {},\n    seriesInvolved: false,\n    /**\r\n     * key: makeKey(coordSys.model)\r\n     * value: Object: key makeKey(axis.model), value: axisInfo\r\n     */\n    coordSysAxesInfo: {},\n    coordSysMap: {}\n  };\n  collectAxesInfo(result, ecModel, api);\n  // Check seriesInvolved for performance, in case too many series in some chart.\n  result.seriesInvolved && collectSeriesInfo(result, ecModel);\n  return result;\n}\nfunction collectAxesInfo(result, ecModel, api) {\n  var globalTooltipModel = ecModel.getComponent('tooltip');\n  var globalAxisPointerModel = ecModel.getComponent('axisPointer');\n  // links can only be set on global.\n  var linksOption = globalAxisPointerModel.get('link', true) || [];\n  var linkGroups = [];\n  // Collect axes info.\n  each(api.getCoordinateSystems(), function (coordSys) {\n    // Some coordinate system do not support axes, like geo.\n    if (!coordSys.axisPointerEnabled) {\n      return;\n    }\n    var coordSysKey = makeKey(coordSys.model);\n    var axesInfoInCoordSys = result.coordSysAxesInfo[coordSysKey] = {};\n    result.coordSysMap[coordSysKey] = coordSys;\n    // Set tooltip (like 'cross') is a convenient way to show axisPointer\n    // for user. So we enable setting tooltip on coordSys model.\n    var coordSysModel = coordSys.model;\n    var baseTooltipModel = coordSysModel.getModel('tooltip', globalTooltipModel);\n    each(coordSys.getAxes(), curry(saveTooltipAxisInfo, false, null));\n    // If axis tooltip used, choose tooltip axis for each coordSys.\n    // Notice this case: coordSys is `grid` but not `cartesian2D` here.\n    if (coordSys.getTooltipAxes && globalTooltipModel\n    // If tooltip.showContent is set as false, tooltip will not\n    // show but axisPointer will show as normal.\n    && baseTooltipModel.get('show')) {\n      // Compatible with previous logic. But series.tooltip.trigger: 'axis'\n      // or series.data[n].tooltip.trigger: 'axis' are not support any more.\n      var triggerAxis = baseTooltipModel.get('trigger') === 'axis';\n      var cross = baseTooltipModel.get(['axisPointer', 'type']) === 'cross';\n      var tooltipAxes = coordSys.getTooltipAxes(baseTooltipModel.get(['axisPointer', 'axis']));\n      if (triggerAxis || cross) {\n        each(tooltipAxes.baseAxes, curry(saveTooltipAxisInfo, cross ? 'cross' : true, triggerAxis));\n      }\n      if (cross) {\n        each(tooltipAxes.otherAxes, curry(saveTooltipAxisInfo, 'cross', false));\n      }\n    }\n    // fromTooltip: true | false | 'cross'\n    // triggerTooltip: true | false | null\n    function saveTooltipAxisInfo(fromTooltip, triggerTooltip, axis) {\n      var axisPointerModel = axis.model.getModel('axisPointer', globalAxisPointerModel);\n      var axisPointerShow = axisPointerModel.get('show');\n      if (!axisPointerShow || axisPointerShow === 'auto' && !fromTooltip && !isHandleTrigger(axisPointerModel)) {\n        return;\n      }\n      if (triggerTooltip == null) {\n        triggerTooltip = axisPointerModel.get('triggerTooltip');\n      }\n      axisPointerModel = fromTooltip ? makeAxisPointerModel(axis, baseTooltipModel, globalAxisPointerModel, ecModel, fromTooltip, triggerTooltip) : axisPointerModel;\n      var snap = axisPointerModel.get('snap');\n      var triggerEmphasis = axisPointerModel.get('triggerEmphasis');\n      var axisKey = makeKey(axis.model);\n      var involveSeries = triggerTooltip || snap || axis.type === 'category';\n      // If result.axesInfo[key] exist, override it (tooltip has higher priority).\n      var axisInfo = result.axesInfo[axisKey] = {\n        key: axisKey,\n        axis: axis,\n        coordSys: coordSys,\n        axisPointerModel: axisPointerModel,\n        triggerTooltip: triggerTooltip,\n        triggerEmphasis: triggerEmphasis,\n        involveSeries: involveSeries,\n        snap: snap,\n        useHandle: isHandleTrigger(axisPointerModel),\n        seriesModels: [],\n        linkGroup: null\n      };\n      axesInfoInCoordSys[axisKey] = axisInfo;\n      result.seriesInvolved = result.seriesInvolved || involveSeries;\n      var groupIndex = getLinkGroupIndex(linksOption, axis);\n      if (groupIndex != null) {\n        var linkGroup = linkGroups[groupIndex] || (linkGroups[groupIndex] = {\n          axesInfo: {}\n        });\n        linkGroup.axesInfo[axisKey] = axisInfo;\n        linkGroup.mapper = linksOption[groupIndex].mapper;\n        axisInfo.linkGroup = linkGroup;\n      }\n    }\n  });\n}\nfunction makeAxisPointerModel(axis, baseTooltipModel, globalAxisPointerModel, ecModel, fromTooltip, triggerTooltip) {\n  var tooltipAxisPointerModel = baseTooltipModel.getModel('axisPointer');\n  var fields = ['type', 'snap', 'lineStyle', 'shadowStyle', 'label', 'animation', 'animationDurationUpdate', 'animationEasingUpdate', 'z'];\n  var volatileOption = {};\n  each(fields, function (field) {\n    volatileOption[field] = clone(tooltipAxisPointerModel.get(field));\n  });\n  // category axis do not auto snap, otherwise some tick that do not\n  // has value can not be hovered. value/time/log axis default snap if\n  // triggered from tooltip and trigger tooltip.\n  volatileOption.snap = axis.type !== 'category' && !!triggerTooltip;\n  // Compatible with previous behavior, tooltip axis does not show label by default.\n  // Only these properties can be overridden from tooltip to axisPointer.\n  if (tooltipAxisPointerModel.get('type') === 'cross') {\n    volatileOption.type = 'line';\n  }\n  var labelOption = volatileOption.label || (volatileOption.label = {});\n  // Follow the convention, do not show label when triggered by tooltip by default.\n  labelOption.show == null && (labelOption.show = false);\n  if (fromTooltip === 'cross') {\n    // When 'cross', both axes show labels.\n    var tooltipAxisPointerLabelShow = tooltipAxisPointerModel.get(['label', 'show']);\n    labelOption.show = tooltipAxisPointerLabelShow != null ? tooltipAxisPointerLabelShow : true;\n    // If triggerTooltip, this is a base axis, which should better not use cross style\n    // (cross style is dashed by default)\n    if (!triggerTooltip) {\n      var crossStyle = volatileOption.lineStyle = tooltipAxisPointerModel.get('crossStyle');\n      crossStyle && defaults(labelOption, crossStyle.textStyle);\n    }\n  }\n  return axis.model.getModel('axisPointer', new Model(volatileOption, globalAxisPointerModel, ecModel));\n}\nfunction collectSeriesInfo(result, ecModel) {\n  // Prepare data for axis trigger\n  ecModel.eachSeries(function (seriesModel) {\n    // Notice this case: this coordSys is `cartesian2D` but not `grid`.\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesTooltipTrigger = seriesModel.get(['tooltip', 'trigger'], true);\n    var seriesTooltipShow = seriesModel.get(['tooltip', 'show'], true);\n    if (!coordSys || seriesTooltipTrigger === 'none' || seriesTooltipTrigger === false || seriesTooltipTrigger === 'item' || seriesTooltipShow === false || seriesModel.get(['axisPointer', 'show'], true) === false) {\n      return;\n    }\n    each(result.coordSysAxesInfo[makeKey(coordSys.model)], function (axisInfo) {\n      var axis = axisInfo.axis;\n      if (coordSys.getAxis(axis.dim) === axis) {\n        axisInfo.seriesModels.push(seriesModel);\n        axisInfo.seriesDataCount == null && (axisInfo.seriesDataCount = 0);\n        axisInfo.seriesDataCount += seriesModel.getData().count();\n      }\n    });\n  });\n}\n/**\r\n * For example:\r\n * {\r\n *     axisPointer: {\r\n *         links: [{\r\n *             xAxisIndex: [2, 4],\r\n *             yAxisIndex: 'all'\r\n *         }, {\r\n *             xAxisId: ['a5', 'a7'],\r\n *             xAxisName: 'xxx'\r\n *         }]\r\n *     }\r\n * }\r\n */\nfunction getLinkGroupIndex(linksOption, axis) {\n  var axisModel = axis.model;\n  var dim = axis.dim;\n  for (var i = 0; i < linksOption.length; i++) {\n    var linkOption = linksOption[i] || {};\n    if (checkPropInLink(linkOption[dim + 'AxisId'], axisModel.id) || checkPropInLink(linkOption[dim + 'AxisIndex'], axisModel.componentIndex) || checkPropInLink(linkOption[dim + 'AxisName'], axisModel.name)) {\n      return i;\n    }\n  }\n}\nfunction checkPropInLink(linkPropValue, axisPropValue) {\n  return linkPropValue === 'all' || isArray(linkPropValue) && indexOf(linkPropValue, axisPropValue) >= 0 || linkPropValue === axisPropValue;\n}\nexport function fixValue(axisModel) {\n  var axisInfo = getAxisInfo(axisModel);\n  if (!axisInfo) {\n    return;\n  }\n  var axisPointerModel = axisInfo.axisPointerModel;\n  var scale = axisInfo.axis.scale;\n  var option = axisPointerModel.option;\n  var status = axisPointerModel.get('status');\n  var value = axisPointerModel.get('value');\n  // Parse init value for category and time axis.\n  if (value != null) {\n    value = scale.parse(value);\n  }\n  var useHandle = isHandleTrigger(axisPointerModel);\n  // If `handle` used, `axisPointer` will always be displayed, so value\n  // and status should be initialized.\n  if (status == null) {\n    option.status = useHandle ? 'show' : 'hide';\n  }\n  var extent = scale.getExtent().slice();\n  extent[0] > extent[1] && extent.reverse();\n  if (\n  // Pick a value on axis when initializing.\n  value == null\n  // If both `handle` and `dataZoom` are used, value may be out of axis extent,\n  // where we should re-pick a value to keep `handle` displaying normally.\n  || value > extent[1]) {\n    // Make handle displayed on the end of the axis when init, which looks better.\n    value = extent[1];\n  }\n  if (value < extent[0]) {\n    value = extent[0];\n  }\n  option.value = value;\n  if (useHandle) {\n    option.status = axisInfo.axis.scale.isBlank() ? 'hide' : 'show';\n  }\n}\nexport function getAxisInfo(axisModel) {\n  var coordSysAxesInfo = (axisModel.ecModel.getComponent('axisPointer') || {}).coordSysAxesInfo;\n  return coordSysAxesInfo && coordSysAxesInfo.axesInfo[makeKey(axisModel)];\n}\nexport function getAxisPointerModel(axisModel) {\n  var axisInfo = getAxisInfo(axisModel);\n  return axisInfo && axisInfo.axisPointerModel;\n}\nfunction isHandleTrigger(axisPointerModel) {\n  return !!axisPointerModel.get(['handle', 'show']);\n}\n/**\r\n * @param {module:echarts/model/Model} model\r\n * @return {string} unique key\r\n */\nexport function makeKey(model) {\n  return model.type + '||' + model.id;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}