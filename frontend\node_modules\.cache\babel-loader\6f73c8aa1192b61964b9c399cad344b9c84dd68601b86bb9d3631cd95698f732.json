{"ast": null, "code": "var _jsxFileName = \"C:\\\\Owen_Zhang\\\\\\u91CF\\u5316\\u4EA4\\u6613\\u7CFB\\u7EDF\\\\frontend\\\\src\\\\pages\\\\Monitoring.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Table, Tag, Progress, Statistic, Alert, Space, Button, Select, DatePicker } from 'antd';\nimport { MonitorOutlined, WarningOutlined, TrendingUpOutlined, TrendingDownOutlined, ReloadOutlined } from '@ant-design/icons';\nimport ReactECharts from 'echarts-for-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst Monitoring = () => {\n  _s();\n  const [monitoringData, setMonitoringData] = useState([]);\n  const [signals, setSignals] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedStock, setSelectedStock] = useState('000001.SZ');\n\n  // 模拟数据\n  useEffect(() => {\n    const mockData = [{\n      ts_code: '000001.SZ',\n      name: '平安银行',\n      price: 12.45,\n      change_pct: 1.22,\n      ma5: 12.30,\n      ma20: 12.10,\n      rsi: 65.5,\n      macd_signal: 'BUY',\n      risk_score: 25,\n      volume_ratio: 1.8\n    }, {\n      ts_code: '000002.SZ',\n      name: '万科A',\n      price: 18.76,\n      change_pct: -1.21,\n      ma5: 18.90,\n      ma20: 19.20,\n      rsi: 45.2,\n      macd_signal: 'SELL',\n      risk_score: 60,\n      volume_ratio: 0.9\n    }, {\n      ts_code: '600000.SH',\n      name: '浦发银行',\n      price: 9.87,\n      change_pct: 0.82,\n      ma5: 9.75,\n      ma20: 9.60,\n      rsi: 58.3,\n      macd_signal: 'HOLD',\n      risk_score: 35,\n      volume_ratio: 1.2\n    }];\n    const mockSignals = [{\n      ts_code: '000001.SZ',\n      name: '平安银行',\n      indicator: 'MACD',\n      signal: 'BUY',\n      value: 0.15,\n      time: '10:30'\n    }, {\n      ts_code: '000002.SZ',\n      name: '万科A',\n      indicator: 'RSI',\n      signal: 'SELL',\n      value: 75.2,\n      time: '10:25'\n    }, {\n      ts_code: '600000.SH',\n      name: '浦发银行',\n      indicator: 'MA',\n      signal: 'BUY',\n      value: 9.87,\n      time: '10:20'\n    }, {\n      ts_code: '000001.SZ',\n      name: '平安银行',\n      indicator: 'KDJ',\n      signal: 'BUY',\n      value: 85.6,\n      time: '10:15'\n    }];\n    setMonitoringData(mockData);\n    setSignals(mockSignals);\n  }, []);\n  const columns = [{\n    title: '股票',\n    dataIndex: 'name',\n    key: 'name',\n    render: (name, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.ts_code\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '当前价格',\n    dataIndex: 'price',\n    key: 'price',\n    render: (price, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"\\xA5\", price.toFixed(2)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: record.change_pct >= 0 ? '#3f8600' : '#cf1322'\n        },\n        children: [record.change_pct >= 0 ? '+' : '', record.change_pct.toFixed(2), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '技术指标',\n    key: 'indicators',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"MA5: \", record.ma5.toFixed(2)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"MA20: \", record.ma20.toFixed(2)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"RSI: \", record.rsi.toFixed(1)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '信号',\n    dataIndex: 'macd_signal',\n    key: 'macd_signal',\n    render: signal => {\n      const color = signal === 'BUY' ? 'green' : signal === 'SELL' ? 'red' : 'blue';\n      const icon = signal === 'BUY' ? /*#__PURE__*/_jsxDEV(TrendingUpOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 41\n      }, this) : signal === 'SELL' ? /*#__PURE__*/_jsxDEV(TrendingDownOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 41\n      }, this) : null;\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: color,\n        icon: icon,\n        children: signal\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '风险评分',\n    dataIndex: 'risk_score',\n    key: 'risk_score',\n    render: score => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Progress, {\n        percent: score,\n        size: \"small\",\n        strokeColor: score > 60 ? '#ff4d4f' : score > 30 ? '#faad14' : '#52c41a',\n        showInfo: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px'\n        },\n        children: [score, \"/100\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '量比',\n    dataIndex: 'volume_ratio',\n    key: 'volume_ratio',\n    render: ratio => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        color: ratio > 1.5 ? '#3f8600' : ratio < 0.8 ? '#cf1322' : '#666'\n      },\n      children: ratio.toFixed(1)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 9\n    }, this)\n  }];\n  const signalColumns = [{\n    title: '股票',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '指标',\n    dataIndex: 'indicator',\n    key: 'indicator'\n  }, {\n    title: '信号',\n    dataIndex: 'signal',\n    key: 'signal',\n    render: signal => {\n      const color = signal === 'BUY' ? 'green' : signal === 'SELL' ? 'red' : 'blue';\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: color,\n        children: signal\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '数值',\n    dataIndex: 'value',\n    key: 'value',\n    render: value => value.toFixed(2)\n  }, {\n    title: '时间',\n    dataIndex: 'time',\n    key: 'time'\n  }];\n\n  // 实时价格图表配置\n  const priceChartOption = {\n    title: {\n      text: '实时价格监控',\n      left: 'center'\n    },\n    tooltip: {\n      trigger: 'axis'\n    },\n    legend: {\n      data: ['价格', 'MA5', 'MA20'],\n      bottom: 0\n    },\n    xAxis: {\n      type: 'category',\n      data: ['09:30', '10:00', '10:30', '11:00', '11:30', '13:00', '13:30', '14:00', '14:30', '15:00']\n    },\n    yAxis: {\n      type: 'value',\n      scale: true\n    },\n    series: [{\n      name: '价格',\n      type: 'line',\n      data: [12.30, 12.35, 12.40, 12.38, 12.42, 12.45, 12.48, 12.46, 12.44, 12.45],\n      lineStyle: {\n        color: '#1890ff'\n      }\n    }, {\n      name: 'MA5',\n      type: 'line',\n      data: [12.25, 12.28, 12.30, 12.32, 12.33, 12.34, 12.35, 12.34, 12.33, 12.30],\n      lineStyle: {\n        color: '#52c41a'\n      }\n    }, {\n      name: 'MA20',\n      type: 'line',\n      data: [12.10, 12.11, 12.12, 12.13, 12.14, 12.15, 12.16, 12.15, 12.14, 12.10],\n      lineStyle: {\n        color: '#faad14'\n      }\n    }]\n  };\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setLoading(false);\n      // 这里可以重新获取数据\n    }, 1000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u9009\\u62E9\\u80A1\\u7968:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: selectedStock,\n              onChange: setSelectedStock,\n              style: {\n                width: 150\n              },\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"000001.SZ\",\n                children: \"\\u5E73\\u5B89\\u94F6\\u884C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"000002.SZ\",\n                children: \"\\u4E07\\u79D1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"600000.SH\",\n                children: \"\\u6D66\\u53D1\\u94F6\\u884C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u65F6\\u95F4\\u8303\\u56F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RangePicker, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 21\n            }, this),\n            onClick: handleRefresh,\n            loading: loading,\n            children: \"\\u5237\\u65B0\\u6570\\u636E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '16px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u76D1\\u63A7\\u80A1\\u7968\",\n            value: monitoringData.length,\n            prefix: /*#__PURE__*/_jsxDEV(MonitorOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 23\n            }, this),\n            suffix: \"\\u53EA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4E70\\u5165\\u4FE1\\u53F7\",\n            value: signals.filter(s => s.signal === 'BUY').length,\n            valueStyle: {\n              color: '#3f8600'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(TrendingUpOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 23\n            }, this),\n            suffix: \"\\u4E2A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5356\\u51FA\\u4FE1\\u53F7\",\n            value: signals.filter(s => s.signal === 'SELL').length,\n            valueStyle: {\n              color: '#cf1322'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(TrendingDownOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 23\n            }, this),\n            suffix: \"\\u4E2A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u98CE\\u9669\\u544A\\u8B66\",\n            value: monitoringData.filter(d => d.risk_score > 50).length,\n            valueStyle: {\n              color: '#faad14'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(WarningOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 23\n            }, this),\n            suffix: \"\\u4E2A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 16,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5B9E\\u65F6\\u76D1\\u63A7\",\n          style: {\n            marginBottom: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: columns,\n            dataSource: monitoringData,\n            rowKey: \"ts_code\",\n            pagination: false,\n            size: \"middle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u4EF7\\u683C\\u8D70\\u52BF\",\n          children: /*#__PURE__*/_jsxDEV(ReactECharts, {\n            option: priceChartOption,\n            style: {\n              height: '300px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u4EA4\\u6613\\u4FE1\\u53F7\",\n          style: {\n            marginBottom: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: signalColumns,\n            dataSource: signals,\n            rowKey: record => `${record.ts_code}-${record.indicator}-${record.time}`,\n            pagination: false,\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7CFB\\u7EDF\\u72B6\\u6001\",\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u7CFB\\u7EDF\\u8FD0\\u884C\\u6B63\\u5E38\",\n            description: \"\\u6570\\u636E\\u66F4\\u65B0\\u65F6\\u95F4: 2024-01-20 10:30:15\",\n            type: \"success\",\n            showIcon: true,\n            style: {\n              marginBottom: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u6CE8\\u610F\",\n            description: \"\\u4E07\\u79D1A\\u98CE\\u9669\\u8BC4\\u5206\\u8F83\\u9AD8\\uFF0C\\u8BF7\\u5173\\u6CE8\",\n            type: \"warning\",\n            showIcon: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 277,\n    columnNumber: 5\n  }, this);\n};\n_s(Monitoring, \"egIMCFFX+PPxSZAdU8uePaWSV1k=\");\n_c = Monitoring;\nexport default Monitoring;\nvar _c;\n$RefreshReg$(_c, \"Monitoring\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Table", "Tag", "Progress", "Statistic", "<PERSON><PERSON>", "Space", "<PERSON><PERSON>", "Select", "DatePicker", "MonitorOutlined", "WarningOutlined", "TrendingUpOutlined", "TrendingDownOutlined", "ReloadOutlined", "ReactECharts", "jsxDEV", "_jsxDEV", "Option", "RangePicker", "Monitoring", "_s", "monitoringData", "setMonitoringData", "signals", "setSignals", "loading", "setLoading", "selectedStock", "setSelectedStock", "mockData", "ts_code", "name", "price", "change_pct", "ma5", "ma20", "rsi", "macd_signal", "risk_score", "volume_ratio", "mockSignals", "indicator", "signal", "value", "time", "columns", "title", "dataIndex", "key", "render", "record", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "color", "toFixed", "_", "icon", "score", "percent", "size", "strokeColor", "showInfo", "ratio", "signalColumns", "priceChartOption", "text", "left", "tooltip", "trigger", "legend", "data", "bottom", "xAxis", "type", "yAxis", "scale", "series", "lineStyle", "handleRefresh", "setTimeout", "padding", "marginBottom", "gutter", "align", "onChange", "width", "onClick", "span", "length", "prefix", "suffix", "filter", "s", "valueStyle", "d", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "option", "height", "message", "description", "showIcon", "_c", "$RefreshReg$"], "sources": ["C:/<PERSON>_<PERSON>/量化交易系统/frontend/src/pages/Monitoring.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  Row, \n  Col, \n  Table, \n  Tag, \n  Progress, \n  Statistic, \n  Alert,\n  Space,\n  Button,\n  Select,\n  DatePicker\n} from 'antd';\nimport { \n  MonitorOutlined,\n  WarningOutlined,\n  TrendingUpOutlined,\n  TrendingDownOutlined,\n  ReloadOutlined\n} from '@ant-design/icons';\nimport ReactECharts from 'echarts-for-react';\n\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\n\ninterface MonitoringData {\n  ts_code: string;\n  name: string;\n  price: number;\n  change_pct: number;\n  ma5: number;\n  ma20: number;\n  rsi: number;\n  macd_signal: string;\n  risk_score: number;\n  volume_ratio: number;\n}\n\ninterface TechnicalSignal {\n  ts_code: string;\n  name: string;\n  indicator: string;\n  signal: string;\n  value: number;\n  time: string;\n}\n\nconst Monitoring: React.FC = () => {\n  const [monitoringData, setMonitoringData] = useState<MonitoringData[]>([]);\n  const [signals, setSignals] = useState<TechnicalSignal[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedStock, setSelectedStock] = useState<string>('000001.SZ');\n\n  // 模拟数据\n  useEffect(() => {\n    const mockData: MonitoringData[] = [\n      {\n        ts_code: '000001.SZ',\n        name: '平安银行',\n        price: 12.45,\n        change_pct: 1.22,\n        ma5: 12.30,\n        ma20: 12.10,\n        rsi: 65.5,\n        macd_signal: 'BUY',\n        risk_score: 25,\n        volume_ratio: 1.8\n      },\n      {\n        ts_code: '000002.SZ',\n        name: '万科A',\n        price: 18.76,\n        change_pct: -1.21,\n        ma5: 18.90,\n        ma20: 19.20,\n        rsi: 45.2,\n        macd_signal: 'SELL',\n        risk_score: 60,\n        volume_ratio: 0.9\n      },\n      {\n        ts_code: '600000.SH',\n        name: '浦发银行',\n        price: 9.87,\n        change_pct: 0.82,\n        ma5: 9.75,\n        ma20: 9.60,\n        rsi: 58.3,\n        macd_signal: 'HOLD',\n        risk_score: 35,\n        volume_ratio: 1.2\n      }\n    ];\n\n    const mockSignals: TechnicalSignal[] = [\n      { ts_code: '000001.SZ', name: '平安银行', indicator: 'MACD', signal: 'BUY', value: 0.15, time: '10:30' },\n      { ts_code: '000002.SZ', name: '万科A', indicator: 'RSI', signal: 'SELL', value: 75.2, time: '10:25' },\n      { ts_code: '600000.SH', name: '浦发银行', indicator: 'MA', signal: 'BUY', value: 9.87, time: '10:20' },\n      { ts_code: '000001.SZ', name: '平安银行', indicator: 'KDJ', signal: 'BUY', value: 85.6, time: '10:15' },\n    ];\n\n    setMonitoringData(mockData);\n    setSignals(mockSignals);\n  }, []);\n\n  const columns = [\n    {\n      title: '股票',\n      dataIndex: 'name',\n      key: 'name',\n      render: (name: string, record: MonitoringData) => (\n        <div>\n          <div>{name}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>{record.ts_code}</div>\n        </div>\n      )\n    },\n    {\n      title: '当前价格',\n      dataIndex: 'price',\n      key: 'price',\n      render: (price: number, record: MonitoringData) => (\n        <div>\n          <div>¥{price.toFixed(2)}</div>\n          <div style={{ \n            fontSize: '12px', \n            color: record.change_pct >= 0 ? '#3f8600' : '#cf1322' \n          }}>\n            {record.change_pct >= 0 ? '+' : ''}{record.change_pct.toFixed(2)}%\n          </div>\n        </div>\n      )\n    },\n    {\n      title: '技术指标',\n      key: 'indicators',\n      render: (_, record: MonitoringData) => (\n        <div>\n          <div>MA5: {record.ma5.toFixed(2)}</div>\n          <div>MA20: {record.ma20.toFixed(2)}</div>\n          <div>RSI: {record.rsi.toFixed(1)}</div>\n        </div>\n      )\n    },\n    {\n      title: '信号',\n      dataIndex: 'macd_signal',\n      key: 'macd_signal',\n      render: (signal: string) => {\n        const color = signal === 'BUY' ? 'green' : signal === 'SELL' ? 'red' : 'blue';\n        const icon = signal === 'BUY' ? <TrendingUpOutlined /> : \n                    signal === 'SELL' ? <TrendingDownOutlined /> : null;\n        return (\n          <Tag color={color} icon={icon}>\n            {signal}\n          </Tag>\n        );\n      }\n    },\n    {\n      title: '风险评分',\n      dataIndex: 'risk_score',\n      key: 'risk_score',\n      render: (score: number) => (\n        <div>\n          <Progress \n            percent={score} \n            size=\"small\" \n            strokeColor={score > 60 ? '#ff4d4f' : score > 30 ? '#faad14' : '#52c41a'}\n            showInfo={false}\n          />\n          <div style={{ fontSize: '12px' }}>{score}/100</div>\n        </div>\n      )\n    },\n    {\n      title: '量比',\n      dataIndex: 'volume_ratio',\n      key: 'volume_ratio',\n      render: (ratio: number) => (\n        <span style={{ \n          color: ratio > 1.5 ? '#3f8600' : ratio < 0.8 ? '#cf1322' : '#666' \n        }}>\n          {ratio.toFixed(1)}\n        </span>\n      )\n    }\n  ];\n\n  const signalColumns = [\n    {\n      title: '股票',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '指标',\n      dataIndex: 'indicator',\n      key: 'indicator',\n    },\n    {\n      title: '信号',\n      dataIndex: 'signal',\n      key: 'signal',\n      render: (signal: string) => {\n        const color = signal === 'BUY' ? 'green' : signal === 'SELL' ? 'red' : 'blue';\n        return <Tag color={color}>{signal}</Tag>;\n      }\n    },\n    {\n      title: '数值',\n      dataIndex: 'value',\n      key: 'value',\n      render: (value: number) => value.toFixed(2)\n    },\n    {\n      title: '时间',\n      dataIndex: 'time',\n      key: 'time',\n    }\n  ];\n\n  // 实时价格图表配置\n  const priceChartOption = {\n    title: {\n      text: '实时价格监控',\n      left: 'center'\n    },\n    tooltip: {\n      trigger: 'axis'\n    },\n    legend: {\n      data: ['价格', 'MA5', 'MA20'],\n      bottom: 0\n    },\n    xAxis: {\n      type: 'category',\n      data: ['09:30', '10:00', '10:30', '11:00', '11:30', '13:00', '13:30', '14:00', '14:30', '15:00']\n    },\n    yAxis: {\n      type: 'value',\n      scale: true\n    },\n    series: [\n      {\n        name: '价格',\n        type: 'line',\n        data: [12.30, 12.35, 12.40, 12.38, 12.42, 12.45, 12.48, 12.46, 12.44, 12.45],\n        lineStyle: { color: '#1890ff' }\n      },\n      {\n        name: 'MA5',\n        type: 'line',\n        data: [12.25, 12.28, 12.30, 12.32, 12.33, 12.34, 12.35, 12.34, 12.33, 12.30],\n        lineStyle: { color: '#52c41a' }\n      },\n      {\n        name: 'MA20',\n        type: 'line',\n        data: [12.10, 12.11, 12.12, 12.13, 12.14, 12.15, 12.16, 12.15, 12.14, 12.10],\n        lineStyle: { color: '#faad14' }\n      }\n    ]\n  };\n\n  const handleRefresh = () => {\n    setLoading(true);\n    setTimeout(() => {\n      setLoading(false);\n      // 这里可以重新获取数据\n    }, 1000);\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      {/* 控制面板 */}\n      <Card style={{ marginBottom: '16px' }}>\n        <Row gutter={16} align=\"middle\">\n          <Col>\n            <Space>\n              <span>选择股票:</span>\n              <Select \n                value={selectedStock} \n                onChange={setSelectedStock}\n                style={{ width: 150 }}\n              >\n                <Option value=\"000001.SZ\">平安银行</Option>\n                <Option value=\"000002.SZ\">万科A</Option>\n                <Option value=\"600000.SH\">浦发银行</Option>\n              </Select>\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              <span>时间范围:</span>\n              <RangePicker />\n            </Space>\n          </Col>\n          <Col>\n            <Button \n              type=\"primary\" \n              icon={<ReloadOutlined />} \n              onClick={handleRefresh}\n              loading={loading}\n            >\n              刷新数据\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 统计概览 */}\n      <Row gutter={16} style={{ marginBottom: '16px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"监控股票\"\n              value={monitoringData.length}\n              prefix={<MonitorOutlined />}\n              suffix=\"只\"\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"买入信号\"\n              value={signals.filter(s => s.signal === 'BUY').length}\n              valueStyle={{ color: '#3f8600' }}\n              prefix={<TrendingUpOutlined />}\n              suffix=\"个\"\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"卖出信号\"\n              value={signals.filter(s => s.signal === 'SELL').length}\n              valueStyle={{ color: '#cf1322' }}\n              prefix={<TrendingDownOutlined />}\n              suffix=\"个\"\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"风险告警\"\n              value={monitoringData.filter(d => d.risk_score > 50).length}\n              valueStyle={{ color: '#faad14' }}\n              prefix={<WarningOutlined />}\n              suffix=\"个\"\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 主要内容 */}\n      <Row gutter={16}>\n        <Col span={16}>\n          <Card title=\"实时监控\" style={{ marginBottom: '16px' }}>\n            <Table\n              columns={columns}\n              dataSource={monitoringData}\n              rowKey=\"ts_code\"\n              pagination={false}\n              size=\"middle\"\n            />\n          </Card>\n          \n          <Card title=\"价格走势\">\n            <ReactECharts option={priceChartOption} style={{ height: '300px' }} />\n          </Card>\n        </Col>\n        \n        <Col span={8}>\n          <Card title=\"交易信号\" style={{ marginBottom: '16px' }}>\n            <Table\n              columns={signalColumns}\n              dataSource={signals}\n              rowKey={(record) => `${record.ts_code}-${record.indicator}-${record.time}`}\n              pagination={false}\n              size=\"small\"\n            />\n          </Card>\n          \n          <Card title=\"系统状态\">\n            <Alert\n              message=\"系统运行正常\"\n              description=\"数据更新时间: 2024-01-20 10:30:15\"\n              type=\"success\"\n              showIcon\n              style={{ marginBottom: '16px' }}\n            />\n            <Alert\n              message=\"注意\"\n              description=\"万科A风险评分较高，请关注\"\n              type=\"warning\"\n              showIcon\n            />\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default Monitoring;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,GAAG,EACHC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,UAAU,QACL,MAAM;AACb,SACEC,eAAe,EACfC,eAAe,EACfC,kBAAkB,EAClBC,oBAAoB,EACpBC,cAAc,QACT,mBAAmB;AAC1B,OAAOC,YAAY,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAM;EAAEC;AAAO,CAAC,GAAGV,MAAM;AACzB,MAAM;EAAEW;AAAY,CAAC,GAAGV,UAAU;AAwBlC,MAAMW,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAmB,EAAE,CAAC;EAC1E,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAoB,EAAE,CAAC;EAC7D,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAS,WAAW,CAAC;;EAEvE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMiC,QAA0B,GAAG,CACjC;MACEC,OAAO,EAAE,WAAW;MACpBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,KAAK;MACZC,UAAU,EAAE,IAAI;MAChBC,GAAG,EAAE,KAAK;MACVC,IAAI,EAAE,KAAK;MACXC,GAAG,EAAE,IAAI;MACTC,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE;IAChB,CAAC,EACD;MACET,OAAO,EAAE,WAAW;MACpBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,KAAK;MACZC,UAAU,EAAE,CAAC,IAAI;MACjBC,GAAG,EAAE,KAAK;MACVC,IAAI,EAAE,KAAK;MACXC,GAAG,EAAE,IAAI;MACTC,WAAW,EAAE,MAAM;MACnBC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE;IAChB,CAAC,EACD;MACET,OAAO,EAAE,WAAW;MACpBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE,IAAI;MAChBC,GAAG,EAAE,IAAI;MACTC,IAAI,EAAE,IAAI;MACVC,GAAG,EAAE,IAAI;MACTC,WAAW,EAAE,MAAM;MACnBC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE;IAChB,CAAC,CACF;IAED,MAAMC,WAA8B,GAAG,CACrC;MAAEV,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE,MAAM;MAAEU,SAAS,EAAE,MAAM;MAAEC,MAAM,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACpG;MAAEd,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE,KAAK;MAAEU,SAAS,EAAE,KAAK;MAAEC,MAAM,EAAE,MAAM;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACnG;MAAEd,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE,MAAM;MAAEU,SAAS,EAAE,IAAI;MAAEC,MAAM,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAClG;MAAEd,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE,MAAM;MAAEU,SAAS,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE;IAAQ,CAAC,CACpG;IAEDtB,iBAAiB,CAACO,QAAQ,CAAC;IAC3BL,UAAU,CAACgB,WAAW,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAAClB,IAAY,EAAEmB,MAAsB,kBAC3ClC,OAAA;MAAAmC,QAAA,gBACEnC,OAAA;QAAAmC,QAAA,EAAMpB;MAAI;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjBvC,OAAA;QAAKwC,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAP,QAAA,EAAED,MAAM,CAACpB;MAAO;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE;EAET,CAAC,EACD;IACET,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAEA,CAACjB,KAAa,EAAEkB,MAAsB,kBAC5ClC,OAAA;MAAAmC,QAAA,gBACEnC,OAAA;QAAAmC,QAAA,GAAK,MAAC,EAACnB,KAAK,CAAC2B,OAAO,CAAC,CAAC,CAAC;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9BvC,OAAA;QAAKwC,KAAK,EAAE;UACVC,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAER,MAAM,CAACjB,UAAU,IAAI,CAAC,GAAG,SAAS,GAAG;QAC9C,CAAE;QAAAkB,QAAA,GACCD,MAAM,CAACjB,UAAU,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEiB,MAAM,CAACjB,UAAU,CAAC0B,OAAO,CAAC,CAAC,CAAC,EAAC,GACnE;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACET,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAEA,CAACW,CAAC,EAAEV,MAAsB,kBAChClC,OAAA;MAAAmC,QAAA,gBACEnC,OAAA;QAAAmC,QAAA,GAAK,OAAK,EAACD,MAAM,CAAChB,GAAG,CAACyB,OAAO,CAAC,CAAC,CAAC;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCvC,OAAA;QAAAmC,QAAA,GAAK,QAAM,EAACD,MAAM,CAACf,IAAI,CAACwB,OAAO,CAAC,CAAC,CAAC;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzCvC,OAAA;QAAAmC,QAAA,GAAK,OAAK,EAACD,MAAM,CAACd,GAAG,CAACuB,OAAO,CAAC,CAAC,CAAC;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC;EAET,CAAC,EACD;IACET,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAGP,MAAc,IAAK;MAC1B,MAAMgB,KAAK,GAAGhB,MAAM,KAAK,KAAK,GAAG,OAAO,GAAGA,MAAM,KAAK,MAAM,GAAG,KAAK,GAAG,MAAM;MAC7E,MAAMmB,IAAI,GAAGnB,MAAM,KAAK,KAAK,gBAAG1B,OAAA,CAACL,kBAAkB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAC1Cb,MAAM,KAAK,MAAM,gBAAG1B,OAAA,CAACJ,oBAAoB;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAAG,IAAI;MAC/D,oBACEvC,OAAA,CAACf,GAAG;QAACyD,KAAK,EAAEA,KAAM;QAACG,IAAI,EAAEA,IAAK;QAAAV,QAAA,EAC3BT;MAAM;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEV;EACF,CAAC,EACD;IACET,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGa,KAAa,iBACpB9C,OAAA;MAAAmC,QAAA,gBACEnC,OAAA,CAACd,QAAQ;QACP6D,OAAO,EAAED,KAAM;QACfE,IAAI,EAAC,OAAO;QACZC,WAAW,EAAEH,KAAK,GAAG,EAAE,GAAG,SAAS,GAAGA,KAAK,GAAG,EAAE,GAAG,SAAS,GAAG,SAAU;QACzEI,QAAQ,EAAE;MAAM;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eACFvC,OAAA;QAAKwC,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAN,QAAA,GAAEW,KAAK,EAAC,MAAI;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD;EAET,CAAC,EACD;IACET,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAGkB,KAAa,iBACpBnD,OAAA;MAAMwC,KAAK,EAAE;QACXE,KAAK,EAAES,KAAK,GAAG,GAAG,GAAG,SAAS,GAAGA,KAAK,GAAG,GAAG,GAAG,SAAS,GAAG;MAC7D,CAAE;MAAAhB,QAAA,EACCgB,KAAK,CAACR,OAAO,CAAC,CAAC;IAAC;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb;EAEV,CAAC,CACF;EAED,MAAMa,aAAa,GAAG,CACpB;IACEtB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGP,MAAc,IAAK;MAC1B,MAAMgB,KAAK,GAAGhB,MAAM,KAAK,KAAK,GAAG,OAAO,GAAGA,MAAM,KAAK,MAAM,GAAG,KAAK,GAAG,MAAM;MAC7E,oBAAO1B,OAAA,CAACf,GAAG;QAACyD,KAAK,EAAEA,KAAM;QAAAP,QAAA,EAAET;MAAM;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAC1C;EACF,CAAC,EACD;IACET,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAGN,KAAa,IAAKA,KAAK,CAACgB,OAAO,CAAC,CAAC;EAC5C,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,CACF;;EAED;EACA,MAAMqB,gBAAgB,GAAG;IACvBvB,KAAK,EAAE;MACLwB,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE;IACR,CAAC;IACDC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE;MACNC,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC;MAC3BC,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MACLC,IAAI,EAAE,UAAU;MAChBH,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACjG,CAAC;IACDI,KAAK,EAAE;MACLD,IAAI,EAAE,OAAO;MACbE,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE,CACN;MACElD,IAAI,EAAE,IAAI;MACV+C,IAAI,EAAE,MAAM;MACZH,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAC5EO,SAAS,EAAE;QAAExB,KAAK,EAAE;MAAU;IAChC,CAAC,EACD;MACE3B,IAAI,EAAE,KAAK;MACX+C,IAAI,EAAE,MAAM;MACZH,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAC5EO,SAAS,EAAE;QAAExB,KAAK,EAAE;MAAU;IAChC,CAAC,EACD;MACE3B,IAAI,EAAE,MAAM;MACZ+C,IAAI,EAAE,MAAM;MACZH,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAC5EO,SAAS,EAAE;QAAExB,KAAK,EAAE;MAAU;IAChC,CAAC;EAEL,CAAC;EAED,MAAMyB,aAAa,GAAGA,CAAA,KAAM;IAC1BzD,UAAU,CAAC,IAAI,CAAC;IAChB0D,UAAU,CAAC,MAAM;MACf1D,UAAU,CAAC,KAAK,CAAC;MACjB;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACEV,OAAA;IAAKwC,KAAK,EAAE;MAAE6B,OAAO,EAAE;IAAO,CAAE;IAAAlC,QAAA,gBAE9BnC,OAAA,CAACnB,IAAI;MAAC2D,KAAK,EAAE;QAAE8B,YAAY,EAAE;MAAO,CAAE;MAAAnC,QAAA,eACpCnC,OAAA,CAAClB,GAAG;QAACyF,MAAM,EAAE,EAAG;QAACC,KAAK,EAAC,QAAQ;QAAArC,QAAA,gBAC7BnC,OAAA,CAACjB,GAAG;UAAAoD,QAAA,eACFnC,OAAA,CAACX,KAAK;YAAA8C,QAAA,gBACJnC,OAAA;cAAAmC,QAAA,EAAM;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClBvC,OAAA,CAACT,MAAM;cACLoC,KAAK,EAAEhB,aAAc;cACrB8D,QAAQ,EAAE7D,gBAAiB;cAC3B4B,KAAK,EAAE;gBAAEkC,KAAK,EAAE;cAAI,CAAE;cAAAvC,QAAA,gBAEtBnC,OAAA,CAACC,MAAM;gBAAC0B,KAAK,EAAC,WAAW;gBAAAQ,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvCvC,OAAA,CAACC,MAAM;gBAAC0B,KAAK,EAAC,WAAW;gBAAAQ,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCvC,OAAA,CAACC,MAAM;gBAAC0B,KAAK,EAAC,WAAW;gBAAAQ,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNvC,OAAA,CAACjB,GAAG;UAAAoD,QAAA,eACFnC,OAAA,CAACX,KAAK;YAAA8C,QAAA,gBACJnC,OAAA;cAAAmC,QAAA,EAAM;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClBvC,OAAA,CAACE,WAAW;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNvC,OAAA,CAACjB,GAAG;UAAAoD,QAAA,eACFnC,OAAA,CAACV,MAAM;YACLwE,IAAI,EAAC,SAAS;YACdjB,IAAI,eAAE7C,OAAA,CAACH,cAAc;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBoC,OAAO,EAAER,aAAc;YACvB1D,OAAO,EAAEA,OAAQ;YAAA0B,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPvC,OAAA,CAAClB,GAAG;MAACyF,MAAM,EAAE,EAAG;MAAC/B,KAAK,EAAE;QAAE8B,YAAY,EAAE;MAAO,CAAE;MAAAnC,QAAA,gBAC/CnC,OAAA,CAACjB,GAAG;QAAC6F,IAAI,EAAE,CAAE;QAAAzC,QAAA,eACXnC,OAAA,CAACnB,IAAI;UAAAsD,QAAA,eACHnC,OAAA,CAACb,SAAS;YACR2C,KAAK,EAAC,0BAAM;YACZH,KAAK,EAAEtB,cAAc,CAACwE,MAAO;YAC7BC,MAAM,eAAE9E,OAAA,CAACP,eAAe;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BwC,MAAM,EAAC;UAAG;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvC,OAAA,CAACjB,GAAG;QAAC6F,IAAI,EAAE,CAAE;QAAAzC,QAAA,eACXnC,OAAA,CAACnB,IAAI;UAAAsD,QAAA,eACHnC,OAAA,CAACb,SAAS;YACR2C,KAAK,EAAC,0BAAM;YACZH,KAAK,EAAEpB,OAAO,CAACyE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACvD,MAAM,KAAK,KAAK,CAAC,CAACmD,MAAO;YACtDK,UAAU,EAAE;cAAExC,KAAK,EAAE;YAAU,CAAE;YACjCoC,MAAM,eAAE9E,OAAA,CAACL,kBAAkB;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC/BwC,MAAM,EAAC;UAAG;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvC,OAAA,CAACjB,GAAG;QAAC6F,IAAI,EAAE,CAAE;QAAAzC,QAAA,eACXnC,OAAA,CAACnB,IAAI;UAAAsD,QAAA,eACHnC,OAAA,CAACb,SAAS;YACR2C,KAAK,EAAC,0BAAM;YACZH,KAAK,EAAEpB,OAAO,CAACyE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACvD,MAAM,KAAK,MAAM,CAAC,CAACmD,MAAO;YACvDK,UAAU,EAAE;cAAExC,KAAK,EAAE;YAAU,CAAE;YACjCoC,MAAM,eAAE9E,OAAA,CAACJ,oBAAoB;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACjCwC,MAAM,EAAC;UAAG;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvC,OAAA,CAACjB,GAAG;QAAC6F,IAAI,EAAE,CAAE;QAAAzC,QAAA,eACXnC,OAAA,CAACnB,IAAI;UAAAsD,QAAA,eACHnC,OAAA,CAACb,SAAS;YACR2C,KAAK,EAAC,0BAAM;YACZH,KAAK,EAAEtB,cAAc,CAAC2E,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC7D,UAAU,GAAG,EAAE,CAAC,CAACuD,MAAO;YAC5DK,UAAU,EAAE;cAAExC,KAAK,EAAE;YAAU,CAAE;YACjCoC,MAAM,eAAE9E,OAAA,CAACN,eAAe;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BwC,MAAM,EAAC;UAAG;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvC,OAAA,CAAClB,GAAG;MAACyF,MAAM,EAAE,EAAG;MAAApC,QAAA,gBACdnC,OAAA,CAACjB,GAAG;QAAC6F,IAAI,EAAE,EAAG;QAAAzC,QAAA,gBACZnC,OAAA,CAACnB,IAAI;UAACiD,KAAK,EAAC,0BAAM;UAACU,KAAK,EAAE;YAAE8B,YAAY,EAAE;UAAO,CAAE;UAAAnC,QAAA,eACjDnC,OAAA,CAAChB,KAAK;YACJ6C,OAAO,EAAEA,OAAQ;YACjBuD,UAAU,EAAE/E,cAAe;YAC3BgF,MAAM,EAAC,SAAS;YAChBC,UAAU,EAAE,KAAM;YAClBtC,IAAI,EAAC;UAAQ;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPvC,OAAA,CAACnB,IAAI;UAACiD,KAAK,EAAC,0BAAM;UAAAK,QAAA,eAChBnC,OAAA,CAACF,YAAY;YAACyF,MAAM,EAAElC,gBAAiB;YAACb,KAAK,EAAE;cAAEgD,MAAM,EAAE;YAAQ;UAAE;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENvC,OAAA,CAACjB,GAAG;QAAC6F,IAAI,EAAE,CAAE;QAAAzC,QAAA,gBACXnC,OAAA,CAACnB,IAAI;UAACiD,KAAK,EAAC,0BAAM;UAACU,KAAK,EAAE;YAAE8B,YAAY,EAAE;UAAO,CAAE;UAAAnC,QAAA,eACjDnC,OAAA,CAAChB,KAAK;YACJ6C,OAAO,EAAEuB,aAAc;YACvBgC,UAAU,EAAE7E,OAAQ;YACpB8E,MAAM,EAAGnD,MAAM,IAAK,GAAGA,MAAM,CAACpB,OAAO,IAAIoB,MAAM,CAACT,SAAS,IAAIS,MAAM,CAACN,IAAI,EAAG;YAC3E0D,UAAU,EAAE,KAAM;YAClBtC,IAAI,EAAC;UAAO;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPvC,OAAA,CAACnB,IAAI;UAACiD,KAAK,EAAC,0BAAM;UAAAK,QAAA,gBAChBnC,OAAA,CAACZ,KAAK;YACJqG,OAAO,EAAC,sCAAQ;YAChBC,WAAW,EAAC,2DAA6B;YACzC5B,IAAI,EAAC,SAAS;YACd6B,QAAQ;YACRnD,KAAK,EAAE;cAAE8B,YAAY,EAAE;YAAO;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACFvC,OAAA,CAACZ,KAAK;YACJqG,OAAO,EAAC,cAAI;YACZC,WAAW,EAAC,2EAAe;YAC3B5B,IAAI,EAAC,SAAS;YACd6B,QAAQ;UAAA;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnC,EAAA,CAvWID,UAAoB;AAAAyF,EAAA,GAApBzF,UAAoB;AAyW1B,eAAeA,UAAU;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}