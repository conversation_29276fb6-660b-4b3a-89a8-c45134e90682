{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport Cartesian from './Cartesian.js';\nimport { invert } from 'zrender/lib/core/matrix.js';\nimport { applyTransform } from 'zrender/lib/core/vector.js';\nexport var cartesian2DDimensions = ['x', 'y'];\nfunction canCalculateAffineTransform(scale) {\n  return scale.type === 'interval' || scale.type === 'time';\n}\nvar Cartesian2D = /** @class */function (_super) {\n  __extends(Cartesian2D, _super);\n  function Cartesian2D() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'cartesian2d';\n    _this.dimensions = cartesian2DDimensions;\n    return _this;\n  }\n  /**\r\n   * Calculate an affine transform matrix if two axes are time or value.\r\n   * It's mainly for accelartion on the large time series data.\r\n   */\n  Cartesian2D.prototype.calcAffineTransform = function () {\n    this._transform = this._invTransform = null;\n    var xAxisScale = this.getAxis('x').scale;\n    var yAxisScale = this.getAxis('y').scale;\n    if (!canCalculateAffineTransform(xAxisScale) || !canCalculateAffineTransform(yAxisScale)) {\n      return;\n    }\n    var xScaleExtent = xAxisScale.getExtent();\n    var yScaleExtent = yAxisScale.getExtent();\n    var start = this.dataToPoint([xScaleExtent[0], yScaleExtent[0]]);\n    var end = this.dataToPoint([xScaleExtent[1], yScaleExtent[1]]);\n    var xScaleSpan = xScaleExtent[1] - xScaleExtent[0];\n    var yScaleSpan = yScaleExtent[1] - yScaleExtent[0];\n    if (!xScaleSpan || !yScaleSpan) {\n      return;\n    }\n    // Accelerate data to point calculation on the special large time series data.\n    var scaleX = (end[0] - start[0]) / xScaleSpan;\n    var scaleY = (end[1] - start[1]) / yScaleSpan;\n    var translateX = start[0] - xScaleExtent[0] * scaleX;\n    var translateY = start[1] - yScaleExtent[0] * scaleY;\n    var m = this._transform = [scaleX, 0, 0, scaleY, translateX, translateY];\n    this._invTransform = invert([], m);\n  };\n  /**\r\n   * Base axis will be used on stacking.\r\n   */\n  Cartesian2D.prototype.getBaseAxis = function () {\n    return this.getAxesByScale('ordinal')[0] || this.getAxesByScale('time')[0] || this.getAxis('x');\n  };\n  Cartesian2D.prototype.containPoint = function (point) {\n    var axisX = this.getAxis('x');\n    var axisY = this.getAxis('y');\n    return axisX.contain(axisX.toLocalCoord(point[0])) && axisY.contain(axisY.toLocalCoord(point[1]));\n  };\n  Cartesian2D.prototype.containData = function (data) {\n    return this.getAxis('x').containData(data[0]) && this.getAxis('y').containData(data[1]);\n  };\n  Cartesian2D.prototype.containZone = function (data1, data2) {\n    var zoneDiag1 = this.dataToPoint(data1);\n    var zoneDiag2 = this.dataToPoint(data2);\n    var area = this.getArea();\n    var zone = new BoundingRect(zoneDiag1[0], zoneDiag1[1], zoneDiag2[0] - zoneDiag1[0], zoneDiag2[1] - zoneDiag1[1]);\n    return area.intersect(zone);\n  };\n  Cartesian2D.prototype.dataToPoint = function (data, clamp, out) {\n    out = out || [];\n    var xVal = data[0];\n    var yVal = data[1];\n    // Fast path\n    if (this._transform\n    // It's supported that if data is like `[Inifity, 123]`, where only Y pixel calculated.\n    && xVal != null && isFinite(xVal) && yVal != null && isFinite(yVal)) {\n      return applyTransform(out, data, this._transform);\n    }\n    var xAxis = this.getAxis('x');\n    var yAxis = this.getAxis('y');\n    out[0] = xAxis.toGlobalCoord(xAxis.dataToCoord(xVal, clamp));\n    out[1] = yAxis.toGlobalCoord(yAxis.dataToCoord(yVal, clamp));\n    return out;\n  };\n  Cartesian2D.prototype.clampData = function (data, out) {\n    var xScale = this.getAxis('x').scale;\n    var yScale = this.getAxis('y').scale;\n    var xAxisExtent = xScale.getExtent();\n    var yAxisExtent = yScale.getExtent();\n    var x = xScale.parse(data[0]);\n    var y = yScale.parse(data[1]);\n    out = out || [];\n    out[0] = Math.min(Math.max(Math.min(xAxisExtent[0], xAxisExtent[1]), x), Math.max(xAxisExtent[0], xAxisExtent[1]));\n    out[1] = Math.min(Math.max(Math.min(yAxisExtent[0], yAxisExtent[1]), y), Math.max(yAxisExtent[0], yAxisExtent[1]));\n    return out;\n  };\n  Cartesian2D.prototype.pointToData = function (point, clamp) {\n    var out = [];\n    if (this._invTransform) {\n      return applyTransform(out, point, this._invTransform);\n    }\n    var xAxis = this.getAxis('x');\n    var yAxis = this.getAxis('y');\n    out[0] = xAxis.coordToData(xAxis.toLocalCoord(point[0]), clamp);\n    out[1] = yAxis.coordToData(yAxis.toLocalCoord(point[1]), clamp);\n    return out;\n  };\n  Cartesian2D.prototype.getOtherAxis = function (axis) {\n    return this.getAxis(axis.dim === 'x' ? 'y' : 'x');\n  };\n  /**\r\n   * Get rect area of cartesian.\r\n   * Area will have a contain function to determine if a point is in the coordinate system.\r\n   */\n  Cartesian2D.prototype.getArea = function (tolerance) {\n    tolerance = tolerance || 0;\n    var xExtent = this.getAxis('x').getGlobalExtent();\n    var yExtent = this.getAxis('y').getGlobalExtent();\n    var x = Math.min(xExtent[0], xExtent[1]) - tolerance;\n    var y = Math.min(yExtent[0], yExtent[1]) - tolerance;\n    var width = Math.max(xExtent[0], xExtent[1]) - x + tolerance;\n    var height = Math.max(yExtent[0], yExtent[1]) - y + tolerance;\n    return new BoundingRect(x, y, width, height);\n  };\n  return Cartesian2D;\n}(Cartesian);\n;\nexport default Cartesian2D;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}