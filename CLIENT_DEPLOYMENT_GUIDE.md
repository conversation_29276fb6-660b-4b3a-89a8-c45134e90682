# 客户部署指南

## 🚀 量化交易系统 - 客户部署手册

**系统版本**: v1.0 Final Release  
**交付日期**: 2025-07-31  
**适用环境**: Windows/Linux/macOS  

---

## 📋 系统概述

### 系统特点
- ✅ **AI增强**: 集成DeepSeek大模型智能分析
- ✅ **实时监控**: WebSocket实时数据推送
- ✅ **技术分析**: 5种主流技术指标
- ✅ **风险管理**: 多维度风险控制
- ✅ **专业界面**: 企业级可视化界面

### 核心功能
1. **实时数据监控**: A股+港股实时价格和指标
2. **AI智能分析**: 情绪分析、风险识别、投资建议
3. **技术指标**: MA、MACD、RSI、布林带、KDJ
4. **风险管理**: 持仓监控、回撤控制、风险评分
5. **可视化界面**: 专业图表和实时数据展示

---

## 🛠️ 环境要求

### 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **磁盘**: 10GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **Python**: 3.12或更高版本
- **Node.js**: 16.0或更高版本
- **npm**: 8.0或更高版本
- **操作系统**: Windows 10+/Linux/macOS

---

## 📦 安装部署

### 第一步：环境准备

#### 1. 安装Python
```bash
# Windows: 从官网下载安装
https://www.python.org/downloads/

# Linux (Ubuntu/Debian)
sudo apt update
sudo apt install python3.12 python3-pip

# macOS (使用Homebrew)
brew install python@3.12
```

#### 2. 安装Node.js
```bash
# Windows: 从官网下载安装
https://nodejs.org/

# Linux (Ubuntu/Debian)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# macOS (使用Homebrew)
brew install node
```

### 第二步：系统部署

#### 1. 解压系统文件
```bash
# 解压到目标目录
unzip 量化交易系统.zip
cd 量化交易系统
```

#### 2. 安装后端依赖
```bash
cd backend
pip install -r requirements.txt
```

#### 3. 安装前端依赖
```bash
cd ../frontend
npm install
```

### 第三步：配置验证

#### 1. 检查API配置
```bash
cd ../backend
python -c "from app.core.config import settings; print('DeepSeek API:', 'OK' if settings.DEEPSEEK_API_KEY else 'NOT SET')"
```

#### 2. 运行系统检查
```bash
python quick_system_check.py
```

---

## 🚀 启动系统

### 方式一：手动启动（推荐用于测试）

#### 1. 启动后端服务
```bash
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 2. 启动前端服务（新终端）
```bash
cd frontend
npm start
```

### 方式二：生产环境启动

#### 1. 后端生产启动
```bash
cd backend
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

#### 2. 前端构建部署
```bash
cd frontend
npm run build
# 将build目录部署到Web服务器
```

---

## 🌐 访问系统

### 系统地址
- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

### 主要页面
1. **仪表盘**: http://localhost:3000/ - 实时监控
2. **AI分析**: http://localhost:3000/ai-analysis - AI智能分析
3. **股票管理**: http://localhost:3000/stocks - 股票管理
4. **监控中心**: http://localhost:3000/monitoring - 系统监控

---

## 🔧 功能验证

### 基础功能测试
1. **访问仪表盘**: 确认页面正常加载
2. **查看实时数据**: 观察股票价格更新
3. **技术指标**: 检查图表显示正常
4. **AI分析**: 测试新闻情绪分析功能

### AI功能测试
1. **进入AI分析页面**: http://localhost:3000/ai-analysis
2. **测试情绪分析**: 输入新闻文本，点击"AI分析"
3. **查看投资建议**: 检查AI推荐结果
4. **生成日报**: 点击"生成今日报告"

### 实时功能测试
1. **WebSocket连接**: 观察数据自动更新
2. **风险告警**: 检查风险提示功能
3. **缓存性能**: 观察页面响应速度

---

## ⚙️ 配置说明

### API配置
系统已预配置以下API：
- **DeepSeek API**: sk-165fc7c...fa4006 (已配置)
- **Tushare Token**: 自动管理 (Token Manager)

### 自定义配置
如需修改配置，编辑以下文件：
```bash
# 后端配置
backend/app/core/config.py

# 前端配置
frontend/src/config/
```

### 数据库配置
系统支持多种存储模式：
- **文件存储**: 默认模式，无需额外配置
- **PostgreSQL**: 可选，需要安装数据库
- **InfluxDB**: 可选，用于时序数据

---

## 🔍 故障排除

### 常见问题

#### 1. 后端启动失败
**问题**: `ModuleNotFoundError`
**解决**: 
```bash
cd backend
pip install -r requirements.txt
```

#### 2. 前端无法访问
**问题**: 页面无法加载
**解决**: 
- 确认后端服务已启动
- 检查端口8000是否被占用
- 查看浏览器控制台错误信息

#### 3. AI功能异常
**问题**: AI分析返回错误
**解决**: 
- 检查网络连接
- 验证DeepSeek API Key有效性
- 查看后端日志错误信息

#### 4. 数据更新慢
**问题**: 股票数据不更新
**解决**: 
- 检查Tushare Token有效性
- 确认网络连接稳定
- 重启后端服务

### 日志查看
```bash
# 后端日志
cd backend
uvicorn app.main:app --log-level debug

# 前端日志
# 查看浏览器开发者工具控制台
```

---

## 📊 性能优化

### 系统性能
- **并发用户**: 支持50+并发用户
- **响应时间**: API响应 < 500ms
- **内存使用**: 正常运行 < 2GB
- **CPU使用**: 正常负载 < 30%

### 优化建议
1. **增加内存**: 8GB+ 推荐
2. **SSD硬盘**: 提升I/O性能
3. **稳定网络**: 确保数据获取稳定
4. **定期重启**: 每周重启一次服务

---

## 🛡️ 安全建议

### 网络安全
- 使用防火墙限制访问端口
- 定期更新系统和依赖
- 监控异常访问日志

### 数据安全
- 定期备份重要数据
- 保护API密钥安全
- 限制系统访问权限

---

## 📞 技术支持

### 自助排查
1. **查看文档**: 阅读完整的项目文档
2. **检查日志**: 查看系统运行日志
3. **重启服务**: 尝试重启后端和前端服务
4. **清除缓存**: 清除浏览器缓存

### 系统监控
- **系统状态**: http://localhost:8000/health
- **API文档**: http://localhost:8000/docs
- **性能监控**: 查看系统资源使用情况

### 维护建议
- **定期更新**: 关注系统更新
- **数据备份**: 定期备份重要数据
- **性能监控**: 监控系统性能指标
- **安全检查**: 定期检查安全配置

---

## 🎯 使用建议

### 最佳实践
1. **渐进使用**: 先熟悉基础功能，再使用高级功能
2. **数据验证**: 重要决策前验证数据准确性
3. **风险控制**: 合理设置风险参数
4. **定期检查**: 定期检查系统运行状态

### 功能建议
1. **实时监控**: 重点关注风险告警
2. **AI分析**: 结合人工判断使用AI建议
3. **技术指标**: 多指标综合分析
4. **历史回测**: 验证策略有效性

---

## 📈 系统扩展

### 可扩展功能
- **更多数据源**: 集成其他数据提供商
- **更多指标**: 添加自定义技术指标
- **策略回测**: 历史数据回测功能
- **移动端**: 开发移动应用
- **云部署**: 部署到云服务器

### 集成建议
- **数据库**: 集成专业数据库
- **消息队列**: 添加消息队列系统
- **监控系统**: 集成专业监控工具
- **负载均衡**: 多实例负载均衡

---

## ✅ 部署检查清单

### 部署前检查
- [ ] Python 3.12+ 已安装
- [ ] Node.js 16+ 已安装
- [ ] 系统文件已解压
- [ ] 网络连接正常

### 安装检查
- [ ] 后端依赖安装完成
- [ ] 前端依赖安装完成
- [ ] 系统检查脚本通过
- [ ] API配置验证通过

### 功能检查
- [ ] 后端服务启动成功
- [ ] 前端界面访问正常
- [ ] 实时数据更新正常
- [ ] AI功能测试通过
- [ ] 技术指标显示正常

### 性能检查
- [ ] 页面响应速度正常
- [ ] 内存使用在合理范围
- [ ] CPU使用率正常
- [ ] 网络连接稳定

---

**🎉 恭喜！您的量化交易系统已成功部署！**

系统现在已完全就绪，可以开始使用所有功能。如有任何问题，请参考故障排除部分或查看详细的技术文档。
