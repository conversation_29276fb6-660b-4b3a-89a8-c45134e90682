{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as graphic from '../../util/graphic.js';\nimport LineGroup from './Line.js';\nimport { getLabelStatesModels } from '../../label/labelStyle.js';\nvar LineDraw = /** @class */function () {\n  function LineDraw(LineCtor) {\n    this.group = new graphic.Group();\n    this._LineCtor = LineCtor || LineGroup;\n  }\n  LineDraw.prototype.updateData = function (lineData) {\n    var _this = this;\n    // Remove progressive els.\n    this._progressiveEls = null;\n    var lineDraw = this;\n    var group = lineDraw.group;\n    var oldLineData = lineDraw._lineData;\n    lineDraw._lineData = lineData;\n    // There is no oldLineData only when first rendering or switching from\n    // stream mode to normal mode, where previous elements should be removed.\n    if (!oldLineData) {\n      group.removeAll();\n    }\n    var seriesScope = makeSeriesScope(lineData);\n    lineData.diff(oldLineData).add(function (idx) {\n      _this._doAdd(lineData, idx, seriesScope);\n    }).update(function (newIdx, oldIdx) {\n      _this._doUpdate(oldLineData, lineData, oldIdx, newIdx, seriesScope);\n    }).remove(function (idx) {\n      group.remove(oldLineData.getItemGraphicEl(idx));\n    }).execute();\n  };\n  ;\n  LineDraw.prototype.updateLayout = function () {\n    var lineData = this._lineData;\n    // Do not support update layout in incremental mode.\n    if (!lineData) {\n      return;\n    }\n    lineData.eachItemGraphicEl(function (el, idx) {\n      el.updateLayout(lineData, idx);\n    }, this);\n  };\n  ;\n  LineDraw.prototype.incrementalPrepareUpdate = function (lineData) {\n    this._seriesScope = makeSeriesScope(lineData);\n    this._lineData = null;\n    this.group.removeAll();\n  };\n  ;\n  LineDraw.prototype.incrementalUpdate = function (taskParams, lineData) {\n    this._progressiveEls = [];\n    function updateIncrementalAndHover(el) {\n      if (!el.isGroup && !isEffectObject(el)) {\n        el.incremental = true;\n        el.ensureState('emphasis').hoverLayer = true;\n      }\n    }\n    for (var idx = taskParams.start; idx < taskParams.end; idx++) {\n      var itemLayout = lineData.getItemLayout(idx);\n      if (lineNeedsDraw(itemLayout)) {\n        var el = new this._LineCtor(lineData, idx, this._seriesScope);\n        el.traverse(updateIncrementalAndHover);\n        this.group.add(el);\n        lineData.setItemGraphicEl(idx, el);\n        this._progressiveEls.push(el);\n      }\n    }\n  };\n  ;\n  LineDraw.prototype.remove = function () {\n    this.group.removeAll();\n  };\n  ;\n  LineDraw.prototype.eachRendered = function (cb) {\n    graphic.traverseElements(this._progressiveEls || this.group, cb);\n  };\n  LineDraw.prototype._doAdd = function (lineData, idx, seriesScope) {\n    var itemLayout = lineData.getItemLayout(idx);\n    if (!lineNeedsDraw(itemLayout)) {\n      return;\n    }\n    var el = new this._LineCtor(lineData, idx, seriesScope);\n    lineData.setItemGraphicEl(idx, el);\n    this.group.add(el);\n  };\n  LineDraw.prototype._doUpdate = function (oldLineData, newLineData, oldIdx, newIdx, seriesScope) {\n    var itemEl = oldLineData.getItemGraphicEl(oldIdx);\n    if (!lineNeedsDraw(newLineData.getItemLayout(newIdx))) {\n      this.group.remove(itemEl);\n      return;\n    }\n    if (!itemEl) {\n      itemEl = new this._LineCtor(newLineData, newIdx, seriesScope);\n    } else {\n      itemEl.updateData(newLineData, newIdx, seriesScope);\n    }\n    newLineData.setItemGraphicEl(newIdx, itemEl);\n    this.group.add(itemEl);\n  };\n  return LineDraw;\n}();\nfunction isEffectObject(el) {\n  return el.animators && el.animators.length > 0;\n}\nfunction makeSeriesScope(lineData) {\n  var hostModel = lineData.hostModel;\n  var emphasisModel = hostModel.getModel('emphasis');\n  return {\n    lineStyle: hostModel.getModel('lineStyle').getLineStyle(),\n    emphasisLineStyle: emphasisModel.getModel(['lineStyle']).getLineStyle(),\n    blurLineStyle: hostModel.getModel(['blur', 'lineStyle']).getLineStyle(),\n    selectLineStyle: hostModel.getModel(['select', 'lineStyle']).getLineStyle(),\n    emphasisDisabled: emphasisModel.get('disabled'),\n    blurScope: emphasisModel.get('blurScope'),\n    focus: emphasisModel.get('focus'),\n    labelStatesModels: getLabelStatesModels(hostModel)\n  };\n}\nfunction isPointNaN(pt) {\n  return isNaN(pt[0]) || isNaN(pt[1]);\n}\nfunction lineNeedsDraw(pts) {\n  return pts && !isPointNaN(pts[0]) && !isPointNaN(pts[1]);\n}\nexport default LineDraw;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}