{"ast": null, "code": "var _jsxFileName = \"C:\\\\Owen_Zhang\\\\\\u91CF\\u5316\\u4EA4\\u6613\\u7CFB\\u7EDF\\\\frontend\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Layout, Menu } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { DashboardOutlined, StockOutlined, MonitorOutlined, RobotOutlined, SettingOutlined, BarChartOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Sider\n} = Layout;\nconst Sidebar = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const menuItems = [{\n    key: '/',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this),\n    label: '监控仪表盘'\n  }, {\n    key: '/stocks',\n    icon: /*#__PURE__*/_jsxDEV(StockOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this),\n    label: '股票管理'\n  }, {\n    key: '/monitoring',\n    icon: /*#__PURE__*/_jsxDEV(MonitorOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this),\n    label: '实时监控'\n  }, {\n    key: '/indicators',\n    icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this),\n    label: '技术指标'\n  }, {\n    key: '/ai',\n    icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this),\n    label: 'AI分析'\n  }, {\n    key: '/settings',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 13\n    }, this),\n    label: '系统设置'\n  }];\n  const handleMenuClick = e => {\n    navigate(e.key);\n  };\n  return /*#__PURE__*/_jsxDEV(Sider, {\n    width: 200,\n    style: {\n      background: '#fff'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"logo\",\n      children: \"\\u91CF\\u5316\\u4EA4\\u6613\\u7CFB\\u7EDF\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      mode: \"inline\",\n      selectedKeys: [location.pathname],\n      style: {\n        height: '100%',\n        borderRight: 0\n      },\n      items: menuItems,\n      onClick: handleMenuClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"VDZHUspDq9N5O9RWjniBrjgIdAA=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Layout", "<PERSON><PERSON>", "useNavigate", "useLocation", "DashboardOutlined", "StockOutlined", "MonitorOutlined", "RobotOutlined", "SettingOutlined", "BarChartOutlined", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "Sidebar", "_s", "navigate", "location", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "handleMenuClick", "e", "width", "style", "background", "children", "className", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "height", "borderRight", "items", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/<PERSON>_<PERSON>/量化交易系统/frontend/src/components/Layout/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { Layout, Menu } from 'antd';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  DashboardOutlined,\n  StockOutlined,\n  MonitorOutlined,\n  RobotOutlined,\n  SettingOutlined,\n  BarChartOutlined\n} from '@ant-design/icons';\n\nconst { Sider } = Layout;\n\nconst Sidebar: React.FC = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const menuItems = [\n    {\n      key: '/',\n      icon: <DashboardOutlined />,\n      label: '监控仪表盘',\n    },\n    {\n      key: '/stocks',\n      icon: <StockOutlined />,\n      label: '股票管理',\n    },\n    {\n      key: '/monitoring',\n      icon: <MonitorOutlined />,\n      label: '实时监控',\n    },\n    {\n      key: '/indicators',\n      icon: <BarChartOutlined />,\n      label: '技术指标',\n    },\n    {\n      key: '/ai',\n      icon: <RobotOutlined />,\n      label: 'AI分析',\n    },\n    {\n      key: '/settings',\n      icon: <SettingOutlined />,\n      label: '系统设置',\n    },\n  ];\n\n  const handleMenuClick = (e: any) => {\n    navigate(e.key);\n  };\n\n  return (\n    <Sider width={200} style={{ background: '#fff' }}>\n      <div className=\"logo\">\n        量化交易系统\n      </div>\n      <Menu\n        mode=\"inline\"\n        selectedKeys={[location.pathname]}\n        style={{ height: '100%', borderRight: 0 }}\n        items={menuItems}\n        onClick={handleMenuClick}\n      />\n    </Sider>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,IAAI,QAAQ,MAAM;AACnC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,iBAAiB,EACjBC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,eAAe,EACfC,gBAAgB,QACX,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC;AAAM,CAAC,GAAGZ,MAAM;AAExB,MAAMa,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,MAAMc,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,GAAG;IACRC,IAAI,eAAER,OAAA,CAACP,iBAAiB;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,SAAS;IACdC,IAAI,eAAER,OAAA,CAACN,aAAa;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAa;IAClBC,IAAI,eAAER,OAAA,CAACL,eAAe;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,aAAa;IAClBC,IAAI,eAAER,OAAA,CAACF,gBAAgB;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,KAAK;IACVC,IAAI,eAAER,OAAA,CAACJ,aAAa;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAER,OAAA,CAACH,eAAe;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,eAAe,GAAIC,CAAM,IAAK;IAClCX,QAAQ,CAACW,CAAC,CAACR,GAAG,CAAC;EACjB,CAAC;EAED,oBACEP,OAAA,CAACC,KAAK;IAACe,KAAK,EAAE,GAAI;IAACC,KAAK,EAAE;MAAEC,UAAU,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC/CnB,OAAA;MAAKoB,SAAS,EAAC,MAAM;MAAAD,QAAA,EAAC;IAEtB;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNZ,OAAA,CAACV,IAAI;MACH+B,IAAI,EAAC,QAAQ;MACbC,YAAY,EAAE,CAACjB,QAAQ,CAACkB,QAAQ,CAAE;MAClCN,KAAK,EAAE;QAAEO,MAAM,EAAE,MAAM;QAAEC,WAAW,EAAE;MAAE,CAAE;MAC1CC,KAAK,EAAEpB,SAAU;MACjBqB,OAAO,EAAEb;IAAgB;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEZ,CAAC;AAACT,EAAA,CAvDID,OAAiB;EAAA,QACJX,WAAW,EACXC,WAAW;AAAA;AAAAoC,EAAA,GAFxB1B,OAAiB;AAyDvB,eAAeA,OAAO;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}