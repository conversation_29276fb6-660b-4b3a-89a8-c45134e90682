{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/* global Uint32Array, Float64Array, Float32Array */\nimport SeriesModel from '../../model/Series.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport { concatArray, mergeAll, map, isNumber } from 'zrender/lib/core/util.js';\nimport CoordinateSystem from '../../core/CoordinateSystem.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nvar Uint32Arr = typeof Uint32Array === 'undefined' ? Array : Uint32Array;\nvar Float64Arr = typeof Float64Array === 'undefined' ? Array : Float64Array;\nfunction compatEc2(seriesOpt) {\n  var data = seriesOpt.data;\n  if (data && data[0] && data[0][0] && data[0][0].coord) {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn('Lines data configuration has been changed to' + ' { coords:[[1,2],[2,3]] }');\n    }\n    seriesOpt.data = map(data, function (itemOpt) {\n      var coords = [itemOpt[0].coord, itemOpt[1].coord];\n      var target = {\n        coords: coords\n      };\n      if (itemOpt[0].name) {\n        target.fromName = itemOpt[0].name;\n      }\n      if (itemOpt[1].name) {\n        target.toName = itemOpt[1].name;\n      }\n      return mergeAll([target, itemOpt[0], itemOpt[1]]);\n    });\n  }\n}\nvar LinesSeriesModel = /** @class */function (_super) {\n  __extends(LinesSeriesModel, _super);\n  function LinesSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = LinesSeriesModel.type;\n    _this.visualStyleAccessPath = 'lineStyle';\n    _this.visualDrawType = 'stroke';\n    return _this;\n  }\n  LinesSeriesModel.prototype.init = function (option) {\n    // The input data may be null/undefined.\n    option.data = option.data || [];\n    // Not using preprocessor because mergeOption may not have series.type\n    compatEc2(option);\n    var result = this._processFlatCoordsArray(option.data);\n    this._flatCoords = result.flatCoords;\n    this._flatCoordsOffset = result.flatCoordsOffset;\n    if (result.flatCoords) {\n      option.data = new Float32Array(result.count);\n    }\n    _super.prototype.init.apply(this, arguments);\n  };\n  LinesSeriesModel.prototype.mergeOption = function (option) {\n    compatEc2(option);\n    if (option.data) {\n      // Only update when have option data to merge.\n      var result = this._processFlatCoordsArray(option.data);\n      this._flatCoords = result.flatCoords;\n      this._flatCoordsOffset = result.flatCoordsOffset;\n      if (result.flatCoords) {\n        option.data = new Float32Array(result.count);\n      }\n    }\n    _super.prototype.mergeOption.apply(this, arguments);\n  };\n  LinesSeriesModel.prototype.appendData = function (params) {\n    var result = this._processFlatCoordsArray(params.data);\n    if (result.flatCoords) {\n      if (!this._flatCoords) {\n        this._flatCoords = result.flatCoords;\n        this._flatCoordsOffset = result.flatCoordsOffset;\n      } else {\n        this._flatCoords = concatArray(this._flatCoords, result.flatCoords);\n        this._flatCoordsOffset = concatArray(this._flatCoordsOffset, result.flatCoordsOffset);\n      }\n      params.data = new Float32Array(result.count);\n    }\n    this.getRawData().appendData(params.data);\n  };\n  LinesSeriesModel.prototype._getCoordsFromItemModel = function (idx) {\n    var itemModel = this.getData().getItemModel(idx);\n    var coords = itemModel.option instanceof Array ? itemModel.option : itemModel.getShallow('coords');\n    if (process.env.NODE_ENV !== 'production') {\n      if (!(coords instanceof Array && coords.length > 0 && coords[0] instanceof Array)) {\n        throw new Error('Invalid coords ' + JSON.stringify(coords) + '. Lines must have 2d coords array in data item.');\n      }\n    }\n    return coords;\n  };\n  LinesSeriesModel.prototype.getLineCoordsCount = function (idx) {\n    if (this._flatCoordsOffset) {\n      return this._flatCoordsOffset[idx * 2 + 1];\n    } else {\n      return this._getCoordsFromItemModel(idx).length;\n    }\n  };\n  LinesSeriesModel.prototype.getLineCoords = function (idx, out) {\n    if (this._flatCoordsOffset) {\n      var offset = this._flatCoordsOffset[idx * 2];\n      var len = this._flatCoordsOffset[idx * 2 + 1];\n      for (var i = 0; i < len; i++) {\n        out[i] = out[i] || [];\n        out[i][0] = this._flatCoords[offset + i * 2];\n        out[i][1] = this._flatCoords[offset + i * 2 + 1];\n      }\n      return len;\n    } else {\n      var coords = this._getCoordsFromItemModel(idx);\n      for (var i = 0; i < coords.length; i++) {\n        out[i] = out[i] || [];\n        out[i][0] = coords[i][0];\n        out[i][1] = coords[i][1];\n      }\n      return coords.length;\n    }\n  };\n  LinesSeriesModel.prototype._processFlatCoordsArray = function (data) {\n    var startOffset = 0;\n    if (this._flatCoords) {\n      startOffset = this._flatCoords.length;\n    }\n    // Stored as a typed array. In format\n    // Points Count(2) | x | y | x | y | Points Count(3) | x |  y | x | y | x | y |\n    if (isNumber(data[0])) {\n      var len = data.length;\n      // Store offset and len of each segment\n      var coordsOffsetAndLenStorage = new Uint32Arr(len);\n      var coordsStorage = new Float64Arr(len);\n      var coordsCursor = 0;\n      var offsetCursor = 0;\n      var dataCount = 0;\n      for (var i = 0; i < len;) {\n        dataCount++;\n        var count = data[i++];\n        // Offset\n        coordsOffsetAndLenStorage[offsetCursor++] = coordsCursor + startOffset;\n        // Len\n        coordsOffsetAndLenStorage[offsetCursor++] = count;\n        for (var k = 0; k < count; k++) {\n          var x = data[i++];\n          var y = data[i++];\n          coordsStorage[coordsCursor++] = x;\n          coordsStorage[coordsCursor++] = y;\n          if (i > len) {\n            if (process.env.NODE_ENV !== 'production') {\n              throw new Error('Invalid data format.');\n            }\n          }\n        }\n      }\n      return {\n        flatCoordsOffset: new Uint32Array(coordsOffsetAndLenStorage.buffer, 0, offsetCursor),\n        flatCoords: coordsStorage,\n        count: dataCount\n      };\n    }\n    return {\n      flatCoordsOffset: null,\n      flatCoords: null,\n      count: data.length\n    };\n  };\n  LinesSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    if (process.env.NODE_ENV !== 'production') {\n      var CoordSys = CoordinateSystem.get(option.coordinateSystem);\n      if (!CoordSys) {\n        throw new Error('Unknown coordinate system ' + option.coordinateSystem);\n      }\n    }\n    var lineData = new SeriesData(['value'], this);\n    lineData.hasItemOption = false;\n    lineData.initData(option.data, [], function (dataItem, dimName, dataIndex, dimIndex) {\n      // dataItem is simply coords\n      if (dataItem instanceof Array) {\n        return NaN;\n      } else {\n        lineData.hasItemOption = true;\n        var value = dataItem.value;\n        if (value != null) {\n          return value instanceof Array ? value[dimIndex] : value;\n        }\n      }\n    });\n    return lineData;\n  };\n  LinesSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var itemModel = data.getItemModel(dataIndex);\n    var name = itemModel.get('name');\n    if (name) {\n      return name;\n    }\n    var fromName = itemModel.get('fromName');\n    var toName = itemModel.get('toName');\n    var nameArr = [];\n    fromName != null && nameArr.push(fromName);\n    toName != null && nameArr.push(toName);\n    return createTooltipMarkup('nameValue', {\n      name: nameArr.join(' > ')\n    });\n  };\n  LinesSeriesModel.prototype.preventIncremental = function () {\n    return !!this.get(['effect', 'show']);\n  };\n  LinesSeriesModel.prototype.getProgressive = function () {\n    var progressive = this.option.progressive;\n    if (progressive == null) {\n      return this.option.large ? 1e4 : this.get('progressive');\n    }\n    return progressive;\n  };\n  LinesSeriesModel.prototype.getProgressiveThreshold = function () {\n    var progressiveThreshold = this.option.progressiveThreshold;\n    if (progressiveThreshold == null) {\n      return this.option.large ? 2e4 : this.get('progressiveThreshold');\n    }\n    return progressiveThreshold;\n  };\n  LinesSeriesModel.prototype.getZLevelKey = function () {\n    var effectModel = this.getModel('effect');\n    var trailLength = effectModel.get('trailLength');\n    return this.getData().count() > this.getProgressiveThreshold()\n    // Each progressive series has individual key.\n    ? this.id : effectModel.get('show') && trailLength > 0 ? trailLength + '' : '';\n  };\n  LinesSeriesModel.type = 'series.lines';\n  LinesSeriesModel.dependencies = ['grid', 'polar', 'geo', 'calendar'];\n  LinesSeriesModel.defaultOption = {\n    coordinateSystem: 'geo',\n    // zlevel: 0,\n    z: 2,\n    legendHoverLink: true,\n    // Cartesian coordinate system\n    xAxisIndex: 0,\n    yAxisIndex: 0,\n    symbol: ['none', 'none'],\n    symbolSize: [10, 10],\n    // Geo coordinate system\n    geoIndex: 0,\n    effect: {\n      show: false,\n      period: 4,\n      constantSpeed: 0,\n      symbol: 'circle',\n      symbolSize: 3,\n      loop: true,\n      trailLength: 0.2\n    },\n    large: false,\n    // Available when large is true\n    largeThreshold: 2000,\n    polyline: false,\n    clip: true,\n    label: {\n      show: false,\n      position: 'end'\n      // distance: 5,\n      // formatter: 标签文本格式器，同Tooltip.formatter，不支持异步回调\n    },\n    lineStyle: {\n      opacity: 0.5\n    }\n  };\n  return LinesSeriesModel;\n}(SeriesModel);\nexport default LinesSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}