{"ast": null, "code": "var _jsxFileName = \"C:\\\\Owen_Zhang\\\\\\u91CF\\u5316\\u4EA4\\u6613\\u7CFB\\u7EDF\\\\frontend\\\\src\\\\pages\\\\StockManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Modal, Form, Input, Select, Switch, Space, message, Tag, Popconfirm } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, SyncOutlined, StockOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst StockManagement = () => {\n  _s();\n  const [stocks, setStocks] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingStock, setEditingStock] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  useEffect(() => {\n    const mockStocks = [{\n      id: 1,\n      ts_code: '000001.SZ',\n      symbol: '000001',\n      name: '平安银行',\n      market: 'A股',\n      industry: '银行',\n      is_active: true,\n      created_at: '2024-01-15'\n    }, {\n      id: 2,\n      ts_code: '000002.SZ',\n      symbol: '000002',\n      name: '万科A',\n      market: 'A股',\n      industry: '房地产',\n      is_active: true,\n      created_at: '2024-01-15'\n    }, {\n      id: 3,\n      ts_code: '600000.SH',\n      symbol: '600000',\n      name: '浦发银行',\n      market: 'A股',\n      industry: '银行',\n      is_active: false,\n      created_at: '2024-01-16'\n    }, {\n      id: 4,\n      ts_code: '00700.HK',\n      symbol: '00700',\n      name: '腾讯控股',\n      market: '港股',\n      industry: '互联网',\n      is_active: false,\n      created_at: '2024-01-16'\n    }];\n    setStocks(mockStocks);\n  }, []);\n  const columns = [{\n    title: '股票代码',\n    dataIndex: 'ts_code',\n    key: 'ts_code',\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 33\n    }, this)\n  }, {\n    title: '股票名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '市场',\n    dataIndex: 'market',\n    key: 'market',\n    render: market => /*#__PURE__*/_jsxDEV(Tag, {\n      color: market === 'A股' ? 'green' : 'orange',\n      children: market\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '行业',\n    dataIndex: 'industry',\n    key: 'industry'\n  }, {\n    title: '监控状态',\n    dataIndex: 'is_active',\n    key: 'is_active',\n    render: (isActive, record) => /*#__PURE__*/_jsxDEV(Switch, {\n      checked: isActive,\n      onChange: checked => handleToggleMonitoring(record.id, checked),\n      checkedChildren: \"\\u76D1\\u63A7\\u4E2D\",\n      unCheckedChildren: \"\\u5DF2\\u505C\\u6B62\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '添加时间',\n    dataIndex: 'created_at',\n    key: 'created_at'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(SyncOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleSyncData(record.ts_code),\n        children: \"\\u540C\\u6B65\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u53EA\\u80A1\\u7968\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 21\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 9\n    }, this)\n  }];\n  const handleAdd = () => {\n    setEditingStock(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = stock => {\n    setEditingStock(stock);\n    form.setFieldsValue(stock);\n    setModalVisible(true);\n  };\n  const handleDelete = id => {\n    setStocks(stocks.filter(stock => stock.id !== id));\n    message.success('删除成功');\n  };\n  const handleToggleMonitoring = (id, isActive) => {\n    setStocks(stocks.map(stock => stock.id === id ? {\n      ...stock,\n      is_active: isActive\n    } : stock));\n    message.success(isActive ? '已开启监控' : '已停止监控');\n  };\n  const handleSyncData = tsCode => {\n    setLoading(true);\n    // 模拟同步数据\n    setTimeout(() => {\n      setLoading(false);\n      message.success(`${tsCode} 数据同步完成`);\n    }, 2000);\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingStock) {\n        // 编辑\n        setStocks(stocks.map(stock => stock.id === editingStock.id ? {\n          ...stock,\n          ...values\n        } : stock));\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newStock = {\n          id: Date.now(),\n          ...values,\n          created_at: new Date().toISOString().split('T')[0]\n        };\n        setStocks([...stocks, newStock]);\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n  const handleModalCancel = () => {\n    setModalVisible(false);\n  };\n  const handleBatchSync = () => {\n    const activeStocks = stocks.filter(stock => stock.is_active);\n    if (activeStocks.length === 0) {\n      message.warning('没有需要同步的股票');\n      return;\n    }\n    setLoading(true);\n    setTimeout(() => {\n      setLoading(false);\n      message.success(`批量同步完成，共同步 ${activeStocks.length} 只股票`);\n    }, 3000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(StockOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this), \"\\u80A1\\u7968\\u7BA1\\u7406\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 11\n      }, this),\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(SyncOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 21\n          }, this),\n          onClick: handleBatchSync,\n          loading: loading,\n          children: \"\\u6279\\u91CF\\u540C\\u6B65\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 21\n          }, this),\n          onClick: handleAdd,\n          children: \"\\u6DFB\\u52A0\\u80A1\\u7968\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: stocks,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          total: stocks.length,\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 条记录`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingStock ? '编辑股票' : '添加股票',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: handleModalCancel,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          market: 'A股',\n          is_active: true\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"ts_code\",\n          label: \"\\u80A1\\u7968\\u4EE3\\u7801\",\n          rules: [{\n            required: true,\n            message: '请输入股票代码'\n          }, {\n            pattern: /^[0-9]{6}\\.(SZ|SH|HK)$/,\n            message: '请输入正确的股票代码格式'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u4F8B\\u5982\\uFF1A000001.SZ\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u80A1\\u7968\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入股票名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u4F8B\\u5982\\uFF1A\\u5E73\\u5B89\\u94F6\\u884C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"market\",\n          label: \"\\u5E02\\u573A\",\n          rules: [{\n            required: true,\n            message: '请选择市场'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"A\\u80A1\",\n              children: \"A\\u80A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u6E2F\\u80A1\",\n              children: \"\\u6E2F\\u80A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"industry\",\n          label: \"\\u884C\\u4E1A\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u4F8B\\u5982\\uFF1A\\u94F6\\u884C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"is_active\",\n          label: \"\\u662F\\u5426\\u76D1\\u63A7\",\n          valuePropName: \"checked\",\n          children: /*#__PURE__*/_jsxDEV(Switch, {\n            checkedChildren: \"\\u662F\",\n            unCheckedChildren: \"\\u5426\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n};\n_s(StockManagement, \"e6IW5iAgHWzLeg4HfFOEzzCPNMs=\", false, function () {\n  return [Form.useForm];\n});\n_c = StockManagement;\nexport default StockManagement;\nvar _c;\n$RefreshReg$(_c, \"StockManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "Switch", "Space", "message", "Tag", "Popconfirm", "PlusOutlined", "EditOutlined", "DeleteOutlined", "SyncOutlined", "StockOutlined", "jsxDEV", "_jsxDEV", "Option", "StockManagement", "_s", "stocks", "setStocks", "loading", "setLoading", "modalVisible", "setModalVisible", "editingStock", "setEditingStock", "form", "useForm", "mockStocks", "id", "ts_code", "symbol", "name", "market", "industry", "is_active", "created_at", "columns", "title", "dataIndex", "key", "render", "text", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isActive", "record", "checked", "onChange", "handleToggleMonitoring", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "_", "size", "type", "icon", "onClick", "handleEdit", "handleSyncData", "onConfirm", "handleDelete", "okText", "cancelText", "danger", "handleAdd", "resetFields", "stock", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "success", "map", "tsCode", "setTimeout", "handleModalOk", "values", "validateFields", "newStock", "Date", "now", "toISOString", "split", "error", "console", "handleModalCancel", "handleBatchSync", "activeStocks", "length", "warning", "style", "padding", "extra", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "total", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "open", "onOk", "onCancel", "width", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "pattern", "placeholder", "value", "valuePropName", "_c", "$RefreshReg$"], "sources": ["C:/<PERSON>_<PERSON>/量化交易系统/frontend/src/pages/StockManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  Table, \n  Button, \n  Modal, \n  Form, \n  Input, \n  Select, \n  Switch, \n  Space, \n  message,\n  Tag,\n  Popconfirm\n} from 'antd';\nimport { \n  PlusOutlined, \n  EditOutlined, \n  DeleteOutlined, \n  SyncOutlined,\n  StockOutlined\n} from '@ant-design/icons';\n\nconst { Option } = Select;\n\ninterface Stock {\n  id: number;\n  ts_code: string;\n  symbol: string;\n  name: string;\n  market: string;\n  industry: string;\n  is_active: boolean;\n  created_at: string;\n}\n\nconst StockManagement: React.FC = () => {\n  const [stocks, setStocks] = useState<Stock[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingStock, setEditingStock] = useState<Stock | null>(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  useEffect(() => {\n    const mockStocks: Stock[] = [\n      {\n        id: 1,\n        ts_code: '000001.SZ',\n        symbol: '000001',\n        name: '平安银行',\n        market: 'A股',\n        industry: '银行',\n        is_active: true,\n        created_at: '2024-01-15'\n      },\n      {\n        id: 2,\n        ts_code: '000002.SZ',\n        symbol: '000002',\n        name: '万科A',\n        market: 'A股',\n        industry: '房地产',\n        is_active: true,\n        created_at: '2024-01-15'\n      },\n      {\n        id: 3,\n        ts_code: '600000.SH',\n        symbol: '600000',\n        name: '浦发银行',\n        market: 'A股',\n        industry: '银行',\n        is_active: false,\n        created_at: '2024-01-16'\n      },\n      {\n        id: 4,\n        ts_code: '00700.HK',\n        symbol: '00700',\n        name: '腾讯控股',\n        market: '港股',\n        industry: '互联网',\n        is_active: false,\n        created_at: '2024-01-16'\n      }\n    ];\n\n    setStocks(mockStocks);\n  }, []);\n\n  const columns = [\n    {\n      title: '股票代码',\n      dataIndex: 'ts_code',\n      key: 'ts_code',\n      render: (text: string) => <Tag color=\"blue\">{text}</Tag>\n    },\n    {\n      title: '股票名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '市场',\n      dataIndex: 'market',\n      key: 'market',\n      render: (market: string) => (\n        <Tag color={market === 'A股' ? 'green' : 'orange'}>{market}</Tag>\n      )\n    },\n    {\n      title: '行业',\n      dataIndex: 'industry',\n      key: 'industry',\n    },\n    {\n      title: '监控状态',\n      dataIndex: 'is_active',\n      key: 'is_active',\n      render: (isActive: boolean, record: Stock) => (\n        <Switch\n          checked={isActive}\n          onChange={(checked) => handleToggleMonitoring(record.id, checked)}\n          checkedChildren=\"监控中\"\n          unCheckedChildren=\"已停止\"\n        />\n      )\n    },\n    {\n      title: '添加时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record: Stock) => (\n        <Space size=\"middle\">\n          <Button\n            type=\"link\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Button\n            type=\"link\"\n            icon={<SyncOutlined />}\n            onClick={() => handleSyncData(record.ts_code)}\n          >\n            同步数据\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这只股票吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button\n              type=\"link\"\n              danger\n              icon={<DeleteOutlined />}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  const handleAdd = () => {\n    setEditingStock(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (stock: Stock) => {\n    setEditingStock(stock);\n    form.setFieldsValue(stock);\n    setModalVisible(true);\n  };\n\n  const handleDelete = (id: number) => {\n    setStocks(stocks.filter(stock => stock.id !== id));\n    message.success('删除成功');\n  };\n\n  const handleToggleMonitoring = (id: number, isActive: boolean) => {\n    setStocks(stocks.map(stock => \n      stock.id === id ? { ...stock, is_active: isActive } : stock\n    ));\n    message.success(isActive ? '已开启监控' : '已停止监控');\n  };\n\n  const handleSyncData = (tsCode: string) => {\n    setLoading(true);\n    // 模拟同步数据\n    setTimeout(() => {\n      setLoading(false);\n      message.success(`${tsCode} 数据同步完成`);\n    }, 2000);\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (editingStock) {\n        // 编辑\n        setStocks(stocks.map(stock => \n          stock.id === editingStock.id ? { ...stock, ...values } : stock\n        ));\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newStock: Stock = {\n          id: Date.now(),\n          ...values,\n          created_at: new Date().toISOString().split('T')[0]\n        };\n        setStocks([...stocks, newStock]);\n        message.success('添加成功');\n      }\n      \n      setModalVisible(false);\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const handleModalCancel = () => {\n    setModalVisible(false);\n  };\n\n  const handleBatchSync = () => {\n    const activeStocks = stocks.filter(stock => stock.is_active);\n    if (activeStocks.length === 0) {\n      message.warning('没有需要同步的股票');\n      return;\n    }\n\n    setLoading(true);\n    setTimeout(() => {\n      setLoading(false);\n      message.success(`批量同步完成，共同步 ${activeStocks.length} 只股票`);\n    }, 3000);\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Card\n        title={\n          <Space>\n            <StockOutlined />\n            股票管理\n          </Space>\n        }\n        extra={\n          <Space>\n            <Button\n              type=\"primary\"\n              icon={<SyncOutlined />}\n              onClick={handleBatchSync}\n              loading={loading}\n            >\n              批量同步\n            </Button>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              添加股票\n            </Button>\n          </Space>\n        }\n      >\n        <Table\n          columns={columns}\n          dataSource={stocks}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            total: stocks.length,\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 条记录`,\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingStock ? '编辑股票' : '添加股票'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            market: 'A股',\n            is_active: true\n          }}\n        >\n          <Form.Item\n            name=\"ts_code\"\n            label=\"股票代码\"\n            rules={[\n              { required: true, message: '请输入股票代码' },\n              { pattern: /^[0-9]{6}\\.(SZ|SH|HK)$/, message: '请输入正确的股票代码格式' }\n            ]}\n          >\n            <Input placeholder=\"例如：000001.SZ\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"name\"\n            label=\"股票名称\"\n            rules={[{ required: true, message: '请输入股票名称' }]}\n          >\n            <Input placeholder=\"例如：平安银行\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"market\"\n            label=\"市场\"\n            rules={[{ required: true, message: '请选择市场' }]}\n          >\n            <Select>\n              <Option value=\"A股\">A股</Option>\n              <Option value=\"港股\">港股</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"industry\"\n            label=\"行业\"\n          >\n            <Input placeholder=\"例如：银行\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"is_active\"\n            label=\"是否监控\"\n            valuePropName=\"checked\"\n          >\n            <Switch checkedChildren=\"是\" unCheckedChildren=\"否\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default StockManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,GAAG,EACHC,UAAU,QACL,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,aAAa,QACR,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC;AAAO,CAAC,GAAGb,MAAM;AAazB,MAAMc,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAe,IAAI,CAAC;EACpE,MAAM,CAACgC,IAAI,CAAC,GAAG1B,IAAI,CAAC2B,OAAO,CAAC,CAAC;;EAE7B;EACAhC,SAAS,CAAC,MAAM;IACd,MAAMiC,UAAmB,GAAG,CAC1B;MACEC,EAAE,EAAE,CAAC;MACLC,OAAO,EAAE,WAAW;MACpBC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE;IACd,CAAC,EACD;MACEP,EAAE,EAAE,CAAC;MACLC,OAAO,EAAE,WAAW;MACpBC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,KAAK;MACXC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE;IACd,CAAC,EACD;MACEP,EAAE,EAAE,CAAC;MACLC,OAAO,EAAE,WAAW;MACpBC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE;IACd,CAAC,EACD;MACEP,EAAE,EAAE,CAAC;MACLC,OAAO,EAAE,UAAU;MACnBC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE;IACd,CAAC,CACF;IAEDjB,SAAS,CAACS,UAAU,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,MAAM,EAAGC,IAAY,iBAAK5B,OAAA,CAACR,GAAG;MAACqC,KAAK,EAAC,MAAM;MAAAC,QAAA,EAAEF;IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EACzD,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGR,MAAc,iBACrBnB,OAAA,CAACR,GAAG;MAACqC,KAAK,EAAEV,MAAM,KAAK,IAAI,GAAG,OAAO,GAAG,QAAS;MAAAW,QAAA,EAAEX;IAAM;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEnE,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAEA,CAACQ,QAAiB,EAAEC,MAAa,kBACvCpC,OAAA,CAACX,MAAM;MACLgD,OAAO,EAAEF,QAAS;MAClBG,QAAQ,EAAGD,OAAO,IAAKE,sBAAsB,CAACH,MAAM,CAACrB,EAAE,EAAEsB,OAAO,CAAE;MAClEG,eAAe,EAAC,oBAAK;MACrBC,iBAAiB,EAAC;IAAK;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAEL,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACe,CAAC,EAAEN,MAAa,kBACvBpC,OAAA,CAACV,KAAK;MAACqD,IAAI,EAAC,QAAQ;MAAAb,QAAA,gBAClB9B,OAAA,CAAChB,MAAM;QACL4D,IAAI,EAAC,MAAM;QACXC,IAAI,eAAE7C,OAAA,CAACL,YAAY;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBY,OAAO,EAAEA,CAAA,KAAMC,UAAU,CAACX,MAAM,CAAE;QAAAN,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlC,OAAA,CAAChB,MAAM;QACL4D,IAAI,EAAC,MAAM;QACXC,IAAI,eAAE7C,OAAA,CAACH,YAAY;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBY,OAAO,EAAEA,CAAA,KAAME,cAAc,CAACZ,MAAM,CAACpB,OAAO,CAAE;QAAAc,QAAA,EAC/C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlC,OAAA,CAACP,UAAU;QACT+B,KAAK,EAAC,oEAAa;QACnByB,SAAS,EAAEA,CAAA,KAAMC,YAAY,CAACd,MAAM,CAACrB,EAAE,CAAE;QACzCoC,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAtB,QAAA,eAEf9B,OAAA,CAAChB,MAAM;UACL4D,IAAI,EAAC,MAAM;UACXS,MAAM;UACNR,IAAI,eAAE7C,OAAA,CAACJ,cAAc;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAC1B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,MAAMoB,SAAS,GAAGA,CAAA,KAAM;IACtB3C,eAAe,CAAC,IAAI,CAAC;IACrBC,IAAI,CAAC2C,WAAW,CAAC,CAAC;IAClB9C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMsC,UAAU,GAAIS,KAAY,IAAK;IACnC7C,eAAe,CAAC6C,KAAK,CAAC;IACtB5C,IAAI,CAAC6C,cAAc,CAACD,KAAK,CAAC;IAC1B/C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMyC,YAAY,GAAInC,EAAU,IAAK;IACnCV,SAAS,CAACD,MAAM,CAACsD,MAAM,CAACF,KAAK,IAAIA,KAAK,CAACzC,EAAE,KAAKA,EAAE,CAAC,CAAC;IAClDxB,OAAO,CAACoE,OAAO,CAAC,MAAM,CAAC;EACzB,CAAC;EAED,MAAMpB,sBAAsB,GAAGA,CAACxB,EAAU,EAAEoB,QAAiB,KAAK;IAChE9B,SAAS,CAACD,MAAM,CAACwD,GAAG,CAACJ,KAAK,IACxBA,KAAK,CAACzC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGyC,KAAK;MAAEnC,SAAS,EAAEc;IAAS,CAAC,GAAGqB,KACxD,CAAC,CAAC;IACFjE,OAAO,CAACoE,OAAO,CAACxB,QAAQ,GAAG,OAAO,GAAG,OAAO,CAAC;EAC/C,CAAC;EAED,MAAMa,cAAc,GAAIa,MAAc,IAAK;IACzCtD,UAAU,CAAC,IAAI,CAAC;IAChB;IACAuD,UAAU,CAAC,MAAM;MACfvD,UAAU,CAAC,KAAK,CAAC;MACjBhB,OAAO,CAACoE,OAAO,CAAC,GAAGE,MAAM,SAAS,CAAC;IACrC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAME,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMpD,IAAI,CAACqD,cAAc,CAAC,CAAC;MAE1C,IAAIvD,YAAY,EAAE;QAChB;QACAL,SAAS,CAACD,MAAM,CAACwD,GAAG,CAACJ,KAAK,IACxBA,KAAK,CAACzC,EAAE,KAAKL,YAAY,CAACK,EAAE,GAAG;UAAE,GAAGyC,KAAK;UAAE,GAAGQ;QAAO,CAAC,GAAGR,KAC3D,CAAC,CAAC;QACFjE,OAAO,CAACoE,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL;QACA,MAAMO,QAAe,GAAG;UACtBnD,EAAE,EAAEoD,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,GAAGJ,MAAM;UACT1C,UAAU,EAAE,IAAI6C,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QACnD,CAAC;QACDjE,SAAS,CAAC,CAAC,GAAGD,MAAM,EAAE8D,QAAQ,CAAC,CAAC;QAChC3E,OAAO,CAACoE,OAAO,CAAC,MAAM,CAAC;MACzB;MAEAlD,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAO8D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMiE,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,YAAY,GAAGvE,MAAM,CAACsD,MAAM,CAACF,KAAK,IAAIA,KAAK,CAACnC,SAAS,CAAC;IAC5D,IAAIsD,YAAY,CAACC,MAAM,KAAK,CAAC,EAAE;MAC7BrF,OAAO,CAACsF,OAAO,CAAC,WAAW,CAAC;MAC5B;IACF;IAEAtE,UAAU,CAAC,IAAI,CAAC;IAChBuD,UAAU,CAAC,MAAM;MACfvD,UAAU,CAAC,KAAK,CAAC;MACjBhB,OAAO,CAACoE,OAAO,CAAC,cAAcgB,YAAY,CAACC,MAAM,MAAM,CAAC;IAC1D,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACE5E,OAAA;IAAK8E,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAjD,QAAA,gBAC9B9B,OAAA,CAAClB,IAAI;MACH0C,KAAK,eACHxB,OAAA,CAACV,KAAK;QAAAwC,QAAA,gBACJ9B,OAAA,CAACF,aAAa;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAEnB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;MACD8C,KAAK,eACHhF,OAAA,CAACV,KAAK;QAAAwC,QAAA,gBACJ9B,OAAA,CAAChB,MAAM;UACL4D,IAAI,EAAC,SAAS;UACdC,IAAI,eAAE7C,OAAA,CAACH,YAAY;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBY,OAAO,EAAE4B,eAAgB;UACzBpE,OAAO,EAAEA,OAAQ;UAAAwB,QAAA,EAClB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlC,OAAA,CAAChB,MAAM;UACL4D,IAAI,EAAC,SAAS;UACdC,IAAI,eAAE7C,OAAA,CAACN,YAAY;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBY,OAAO,EAAEQ,SAAU;UAAAxB,QAAA,EACpB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MAAAJ,QAAA,eAED9B,OAAA,CAACjB,KAAK;QACJwC,OAAO,EAAEA,OAAQ;QACjB0D,UAAU,EAAE7E,MAAO;QACnB8E,MAAM,EAAC,IAAI;QACX5E,OAAO,EAAEA,OAAQ;QACjB6E,UAAU,EAAE;UACVC,KAAK,EAAEhF,MAAM,CAACwE,MAAM;UACpBS,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGJ,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAArD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEPlC,OAAA,CAACf,KAAK;MACJuC,KAAK,EAAEd,YAAY,GAAG,MAAM,GAAG,MAAO;MACtC+E,IAAI,EAAEjF,YAAa;MACnBkF,IAAI,EAAE3B,aAAc;MACpB4B,QAAQ,EAAElB,iBAAkB;MAC5BmB,KAAK,EAAE,GAAI;MAAA9D,QAAA,eAEX9B,OAAA,CAACd,IAAI;QACH0B,IAAI,EAAEA,IAAK;QACXiF,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACb3E,MAAM,EAAE,IAAI;UACZE,SAAS,EAAE;QACb,CAAE;QAAAS,QAAA,gBAEF9B,OAAA,CAACd,IAAI,CAAC6G,IAAI;UACR7E,IAAI,EAAC,SAAS;UACd8E,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAE3G,OAAO,EAAE;UAAU,CAAC,EACtC;YAAE4G,OAAO,EAAE,wBAAwB;YAAE5G,OAAO,EAAE;UAAe,CAAC,CAC9D;UAAAuC,QAAA,eAEF9B,OAAA,CAACb,KAAK;YAACiH,WAAW,EAAC;UAAc;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEZlC,OAAA,CAACd,IAAI,CAAC6G,IAAI;UACR7E,IAAI,EAAC,MAAM;UACX8E,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE3G,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAuC,QAAA,eAEhD9B,OAAA,CAACb,KAAK;YAACiH,WAAW,EAAC;UAAS;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZlC,OAAA,CAACd,IAAI,CAAC6G,IAAI;UACR7E,IAAI,EAAC,QAAQ;UACb8E,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE3G,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAuC,QAAA,eAE9C9B,OAAA,CAACZ,MAAM;YAAA0C,QAAA,gBACL9B,OAAA,CAACC,MAAM;cAACoG,KAAK,EAAC,SAAI;cAAAvE,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9BlC,OAAA,CAACC,MAAM;cAACoG,KAAK,EAAC,cAAI;cAAAvE,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZlC,OAAA,CAACd,IAAI,CAAC6G,IAAI;UACR7E,IAAI,EAAC,UAAU;UACf8E,KAAK,EAAC,cAAI;UAAAlE,QAAA,eAEV9B,OAAA,CAACb,KAAK;YAACiH,WAAW,EAAC;UAAO;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEZlC,OAAA,CAACd,IAAI,CAAC6G,IAAI;UACR7E,IAAI,EAAC,WAAW;UAChB8E,KAAK,EAAC,0BAAM;UACZM,aAAa,EAAC,SAAS;UAAAxE,QAAA,eAEvB9B,OAAA,CAACX,MAAM;YAACmD,eAAe,EAAC,QAAG;YAACC,iBAAiB,EAAC;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAjUID,eAAyB;EAAA,QAKdhB,IAAI,CAAC2B,OAAO;AAAA;AAAA0F,EAAA,GALvBrG,eAAyB;AAmU/B,eAAeA,eAAe;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}