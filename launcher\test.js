/**
 * 启动器测试脚本
 * 用于测试各个模块的功能
 */

const path = require('path');
const fs = require('fs');

// 模拟Electron环境
const mockElectron = {
  app: {
    getPath: (name) => {
      const paths = {
        userData: path.join(__dirname, 'test-data'),
        home: require('os').homedir(),
        temp: require('os').tmpdir()
      };
      return paths[name] || paths.temp;
    },
    getVersion: () => '1.0.0',
    getName: () => '量化交易监控系统'
  }
};

// 设置模拟环境 - 使用Module._load来拦截require
const Module = require('module');
const originalLoad = Module._load;
Module._load = function(request, parent) {
  if (request === 'electron') {
    return mockElectron;
  }
  return originalLoad.apply(this, arguments);
};

// 导入要测试的模块
const ConfigManager = require('./src/services/config-manager');
const ServiceManager = require('./src/services/service-manager');

async function testConfigManager() {
  console.log('\n🧪 测试配置管理器...');
  
  try {
    const configManager = new ConfigManager();
    
    // 测试初始化
    console.log('📝 测试初始化...');
    await configManager.initialize();
    console.log('✅ 初始化成功');
    
    // 测试加载配置
    console.log('📖 测试加载配置...');
    const config = await configManager.loadConfig();
    console.log('✅ 配置加载成功:', Object.keys(config));
    
    // 测试保存配置
    console.log('💾 测试保存配置...');
    config.system.test_flag = true;
    await configManager.saveConfig(config);
    console.log('✅ 配置保存成功');
    
    // 测试Token验证
    console.log('🔐 测试Token验证...');
    const validationResult = await configManager.validateToken('tushare', 'test-token-123456789012345678901234567890');
    console.log('✅ Token验证结果:', validationResult);
    
    // 测试配置重置
    console.log('🔄 测试配置重置...');
    await configManager.resetConfig();
    console.log('✅ 配置重置成功');
    
    console.log('✅ 配置管理器测试完成');
    
  } catch (error) {
    console.error('❌ 配置管理器测试失败:', error);
  }
}

async function testServiceManager() {
  console.log('\n🧪 测试服务管理器...');
  
  try {
    const serviceManager = new ServiceManager();
    
    // 测试获取服务状态
    console.log('📊 测试获取服务状态...');
    const status = serviceManager.getServiceStatus();
    console.log('✅ 服务状态:', status);
    
    // 测试端口检查
    console.log('🔍 测试端口检查...');
    try {
      await serviceManager.checkPorts();
      console.log('✅ 端口检查通过');
    } catch (error) {
      console.log('⚠️ 端口检查失败:', error.message);
    }
    
    // 测试日志获取
    console.log('📋 测试日志获取...');
    const logs = serviceManager.getLogs();
    console.log('✅ 日志获取成功，后端日志数:', logs.backend.length, '前端日志数:', logs.frontend.length);
    
    console.log('✅ 服务管理器测试完成');
    
  } catch (error) {
    console.error('❌ 服务管理器测试失败:', error);
  }
}

async function testFileStructure() {
  console.log('\n🧪 测试文件结构...');
  
  const requiredFiles = [
    'package.json',
    'main.js',
    'preload.js',
    'src/services/config-manager.js',
    'src/services/service-manager.js',
    'src/services/health-checker.js',
    'src/renderer/index.html'
  ];
  
  let allFilesExist = true;
  
  for (const file of requiredFiles) {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file}`);
    } else {
      console.log(`❌ ${file} - 文件不存在`);
      allFilesExist = false;
    }
  }
  
  if (allFilesExist) {
    console.log('✅ 所有必需文件都存在');
  } else {
    console.log('❌ 部分文件缺失');
  }
}

async function testPackageJson() {
  console.log('\n🧪 测试package.json配置...');
  
  try {
    const packagePath = path.join(__dirname, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // 检查必需字段
    const requiredFields = ['name', 'version', 'main', 'scripts', 'dependencies', 'devDependencies'];
    
    for (const field of requiredFields) {
      if (packageJson[field]) {
        console.log(`✅ ${field}: 已配置`);
      } else {
        console.log(`❌ ${field}: 缺失`);
      }
    }
    
    // 检查必需的依赖
    const requiredDeps = ['express', 'axios', 'find-free-port'];
    const requiredDevDeps = ['electron', 'electron-builder'];
    
    console.log('\n📦 检查依赖:');
    for (const dep of requiredDeps) {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
      } else {
        console.log(`❌ ${dep}: 缺失`);
      }
    }
    
    console.log('\n🛠️ 检查开发依赖:');
    for (const dep of requiredDevDeps) {
      if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
        console.log(`✅ ${dep}: ${packageJson.devDependencies[dep]}`);
      } else {
        console.log(`❌ ${dep}: 缺失`);
      }
    }
    
    console.log('✅ package.json检查完成');
    
  } catch (error) {
    console.error('❌ package.json检查失败:', error);
  }
}

async function runAllTests() {
  console.log('🚀 开始启动器测试...\n');
  
  // 创建测试数据目录
  const testDataDir = path.join(__dirname, 'test-data');
  if (!fs.existsSync(testDataDir)) {
    fs.mkdirSync(testDataDir, { recursive: true });
  }
  
  await testFileStructure();
  await testPackageJson();
  await testConfigManager();
  await testServiceManager();
  
  console.log('\n🎉 所有测试完成！');
  
  // 清理测试数据
  if (fs.existsSync(testDataDir)) {
    fs.rmSync(testDataDir, { recursive: true, force: true });
    console.log('🧹 测试数据已清理');
  }
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testConfigManager,
  testServiceManager,
  testFileStructure,
  testPackageJson,
  runAllTests
};
