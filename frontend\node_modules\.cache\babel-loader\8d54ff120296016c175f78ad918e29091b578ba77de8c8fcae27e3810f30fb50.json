{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { each, bind } from 'zrender/lib/core/util.js';\nimport SeriesModel from '../../model/Series.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nvar ParallelSeriesModel = /** @class */function (_super) {\n  __extends(ParallelSeriesModel, _super);\n  function ParallelSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelSeriesModel.type;\n    _this.visualStyleAccessPath = 'lineStyle';\n    _this.visualDrawType = 'stroke';\n    return _this;\n  }\n  ParallelSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: bind(makeDefaultEncode, null, this)\n    });\n  };\n  /**\r\n   * User can get data raw indices on 'axisAreaSelected' event received.\r\n   *\r\n   * @return Raw indices\r\n   */\n  ParallelSeriesModel.prototype.getRawIndicesByActiveState = function (activeState) {\n    var coordSys = this.coordinateSystem;\n    var data = this.getData();\n    var indices = [];\n    coordSys.eachActiveState(data, function (theActiveState, dataIndex) {\n      if (activeState === theActiveState) {\n        indices.push(data.getRawIndex(dataIndex));\n      }\n    });\n    return indices;\n  };\n  ParallelSeriesModel.type = 'series.parallel';\n  ParallelSeriesModel.dependencies = ['parallel'];\n  ParallelSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'parallel',\n    parallelIndex: 0,\n    label: {\n      show: false\n    },\n    inactiveOpacity: 0.05,\n    activeOpacity: 1,\n    lineStyle: {\n      width: 1,\n      opacity: 0.45,\n      type: 'solid'\n    },\n    emphasis: {\n      label: {\n        show: false\n      }\n    },\n    progressive: 500,\n    smooth: false,\n    animationEasing: 'linear'\n  };\n  return ParallelSeriesModel;\n}(SeriesModel);\nfunction makeDefaultEncode(seriesModel) {\n  // The mapping of parallelAxis dimension to data dimension can\n  // be specified in parallelAxis.option.dim. For example, if\n  // parallelAxis.option.dim is 'dim3', it mapping to the third\n  // dimension of data. But `data.encode` has higher priority.\n  // Moreover, parallelModel.dimension should not be regarded as data\n  // dimensions. Consider dimensions = ['dim4', 'dim2', 'dim6'];\n  var parallelModel = seriesModel.ecModel.getComponent('parallel', seriesModel.get('parallelIndex'));\n  if (!parallelModel) {\n    return;\n  }\n  var encodeDefine = {};\n  each(parallelModel.dimensions, function (axisDim) {\n    var dataDimIndex = convertDimNameToNumber(axisDim);\n    encodeDefine[axisDim] = dataDimIndex;\n  });\n  return encodeDefine;\n}\nfunction convertDimNameToNumber(dimName) {\n  return +dimName.replace('dim', '');\n}\nexport default ParallelSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}