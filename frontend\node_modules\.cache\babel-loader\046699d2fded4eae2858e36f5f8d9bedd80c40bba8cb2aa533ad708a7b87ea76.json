{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nexport default function sankeyVisual(ecModel) {\n  ecModel.eachSeriesByType('sankey', function (seriesModel) {\n    var graph = seriesModel.getGraph();\n    var nodes = graph.nodes;\n    var edges = graph.edges;\n    if (nodes.length) {\n      var minValue_1 = Infinity;\n      var maxValue_1 = -Infinity;\n      zrUtil.each(nodes, function (node) {\n        var nodeValue = node.getLayout().value;\n        if (nodeValue < minValue_1) {\n          minValue_1 = nodeValue;\n        }\n        if (nodeValue > maxValue_1) {\n          maxValue_1 = nodeValue;\n        }\n      });\n      zrUtil.each(nodes, function (node) {\n        var mapping = new VisualMapping({\n          type: 'color',\n          mappingMethod: 'linear',\n          dataExtent: [minValue_1, maxValue_1],\n          visual: seriesModel.get('color')\n        });\n        var mapValueToColor = mapping.mapValueToVisual(node.getLayout().value);\n        var customColor = node.getModel().get(['itemStyle', 'color']);\n        if (customColor != null) {\n          node.setVisual('color', customColor);\n          node.setVisual('style', {\n            fill: customColor\n          });\n        } else {\n          node.setVisual('color', mapValueToColor);\n          node.setVisual('style', {\n            fill: mapValueToColor\n          });\n        }\n      });\n    }\n    if (edges.length) {\n      zrUtil.each(edges, function (edge) {\n        var edgeStyle = edge.getModel().get('lineStyle');\n        edge.setVisual('style', edgeStyle);\n      });\n    }\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}