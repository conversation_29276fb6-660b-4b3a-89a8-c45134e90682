{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesData from '../helper/createSeriesData.js';\nimport { makeInner } from '../../util/model.js';\nimport SeriesModel from '../../model/Series.js';\n// Also compat with ec4, where\n// `visual('color') visual('borderColor')` is supported.\nexport var STYLE_VISUAL_TYPE = {\n  color: 'fill',\n  borderColor: 'stroke'\n};\nexport var NON_STYLE_VISUAL_PROPS = {\n  symbol: 1,\n  symbolSize: 1,\n  symbolKeepAspect: 1,\n  legendIcon: 1,\n  visualMeta: 1,\n  liftZ: 1,\n  decal: 1\n};\n;\nexport var customInnerStore = makeInner();\nvar CustomSeriesModel = /** @class */function (_super) {\n  __extends(CustomSeriesModel, _super);\n  function CustomSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CustomSeriesModel.type;\n    return _this;\n  }\n  CustomSeriesModel.prototype.optionUpdated = function () {\n    this.currentZLevel = this.get('zlevel', true);\n    this.currentZ = this.get('z', true);\n  };\n  CustomSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this);\n  };\n  CustomSeriesModel.prototype.getDataParams = function (dataIndex, dataType, el) {\n    var params = _super.prototype.getDataParams.call(this, dataIndex, dataType);\n    el && (params.info = customInnerStore(el).info);\n    return params;\n  };\n  CustomSeriesModel.type = 'series.custom';\n  CustomSeriesModel.dependencies = ['grid', 'polar', 'geo', 'singleAxis', 'calendar'];\n  CustomSeriesModel.defaultOption = {\n    coordinateSystem: 'cartesian2d',\n    // zlevel: 0,\n    z: 2,\n    legendHoverLink: true,\n    // Custom series will not clip by default.\n    // Some case will use custom series to draw label\n    // For example https://echarts.apache.org/examples/en/editor.html?c=custom-gantt-flight\n    clip: false\n    // Cartesian coordinate system\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    // Polar coordinate system\n    // polarIndex: 0,\n    // Geo coordinate system\n    // geoIndex: 0,\n  };\n  return CustomSeriesModel;\n}(SeriesModel);\nexport default CustomSeriesModel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}