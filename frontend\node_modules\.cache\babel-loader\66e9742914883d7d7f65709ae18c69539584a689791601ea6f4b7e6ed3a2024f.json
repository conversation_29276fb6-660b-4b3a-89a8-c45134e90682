{"ast": null, "code": "var _jsxFileName = \"C:\\\\Owen_Zhang\\\\\\u91CF\\u5316\\u4EA4\\u6613\\u7CFB\\u7EDF\\\\frontend\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Statistic, Table, Tag, Progress, Alert } from 'antd';\nimport { ArrowUpOutlined, ArrowDownOutlined, DollarOutlined, StockOutlined, WarningOutlined, TrendingUpOutlined } from '@ant-design/icons';\nimport ReactECharts from 'echarts-for-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [marketData, setMarketData] = useState([]);\n  const [riskAlerts, setRiskAlerts] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // 模拟数据\n  useEffect(() => {\n    const mockMarketData = [{\n      ts_code: '000001.SZ',\n      name: '平安银行',\n      price: 12.45,\n      change: 0.15,\n      change_pct: 1.22,\n      volume: 1234567\n    }, {\n      ts_code: '000002.SZ',\n      name: '万科A',\n      price: 18.76,\n      change: -0.23,\n      change_pct: -1.21,\n      volume: 987654\n    }, {\n      ts_code: '600000.SH',\n      name: '浦发银行',\n      price: 9.87,\n      change: 0.08,\n      change_pct: 0.82,\n      volume: 2345678\n    }, {\n      ts_code: '600036.SH',\n      name: '招商银行',\n      price: 35.42,\n      change: 0.67,\n      change_pct: 1.93,\n      volume: 1876543\n    }, {\n      ts_code: '000858.SZ',\n      name: '五粮液',\n      price: 128.90,\n      change: -2.10,\n      change_pct: -1.60,\n      volume: 654321\n    }];\n    const mockRiskAlerts = [{\n      id: '1',\n      type: '持仓风险',\n      level: 'HIGH',\n      message: '平安银行持仓比例过高 (15.2%)',\n      time: '10:30'\n    }, {\n      id: '2',\n      type: '波动率',\n      level: 'MEDIUM',\n      message: '组合波动率超过阈值 (28.5%)',\n      time: '09:45'\n    }, {\n      id: '3',\n      type: '回撤',\n      level: 'LOW',\n      message: '当前回撤 3.2%，接近预警线',\n      time: '09:15'\n    }];\n    setTimeout(() => {\n      setMarketData(mockMarketData);\n      setRiskAlerts(mockRiskAlerts);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  // 表格列定义\n  const columns = [{\n    title: '股票代码',\n    dataIndex: 'ts_code',\n    key: 'ts_code'\n  }, {\n    title: '股票名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '当前价格',\n    dataIndex: 'price',\n    key: 'price',\n    render: price => `¥${price.toFixed(2)}`\n  }, {\n    title: '涨跌额',\n    dataIndex: 'change',\n    key: 'change',\n    render: change => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        color: change >= 0 ? '#3f8600' : '#cf1322'\n      },\n      children: [change >= 0 ? '+' : '', change.toFixed(2)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '涨跌幅',\n    dataIndex: 'change_pct',\n    key: 'change_pct',\n    render: pct => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        color: pct >= 0 ? '#3f8600' : '#cf1322'\n      },\n      children: [pct >= 0 ? /*#__PURE__*/_jsxDEV(ArrowUpOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 23\n      }, this) : /*#__PURE__*/_jsxDEV(ArrowDownOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 45\n      }, this), Math.abs(pct).toFixed(2), \"%\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '成交量',\n    dataIndex: 'volume',\n    key: 'volume',\n    render: volume => (volume / 10000).toFixed(1) + '万'\n  }];\n\n  // 风险告警列定义\n  const alertColumns = [{\n    title: '类型',\n    dataIndex: 'type',\n    key: 'type'\n  }, {\n    title: '等级',\n    dataIndex: 'level',\n    key: 'level',\n    render: level => {\n      const color = level === 'HIGH' ? 'red' : level === 'MEDIUM' ? 'orange' : 'blue';\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: color,\n        children: level\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '消息',\n    dataIndex: 'message',\n    key: 'message'\n  }, {\n    title: '时间',\n    dataIndex: 'time',\n    key: 'time'\n  }];\n\n  // 图表配置\n  const chartOption = {\n    title: {\n      text: '组合净值走势',\n      left: 'center'\n    },\n    tooltip: {\n      trigger: 'axis'\n    },\n    xAxis: {\n      type: 'category',\n      data: ['09:30', '10:00', '10:30', '11:00', '11:30', '13:00', '13:30', '14:00', '14:30', '15:00']\n    },\n    yAxis: {\n      type: 'value',\n      scale: true\n    },\n    series: [{\n      name: '净值',\n      type: 'line',\n      data: [1.000, 1.002, 0.998, 1.005, 1.008, 1.012, 1.015, 1.018, 1.020, 1.025],\n      smooth: true,\n      lineStyle: {\n        color: '#1890ff'\n      },\n      areaStyle: {\n        color: {\n          type: 'linear',\n          x: 0,\n          y: 0,\n          x2: 0,\n          y2: 1,\n          colorStops: [{\n            offset: 0,\n            color: 'rgba(24, 144, 255, 0.3)'\n          }, {\n            offset: 1,\n            color: 'rgba(24, 144, 255, 0.1)'\n          }]\n        }\n      }\n    }]\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u8D44\\u4EA7\",\n            value: 1250000,\n            precision: 2,\n            valueStyle: {\n              color: '#3f8600'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 23\n            }, this),\n            suffix: \"\\u5143\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4ECA\\u65E5\\u6536\\u76CA\",\n            value: 12500,\n            precision: 2,\n            valueStyle: {\n              color: '#3f8600'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(ArrowUpOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 23\n            }, this),\n            suffix: \"\\u5143\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6301\\u4ED3\\u80A1\\u7968\",\n            value: 5,\n            valueStyle: {\n              color: '#1890ff'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(StockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 23\n            }, this),\n            suffix: \"\\u53EA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u98CE\\u9669\\u8BC4\\u5206\",\n            value: 35,\n            valueStyle: {\n              color: '#faad14'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(WarningOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 23\n            }, this),\n            suffix: \"/100\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: 35,\n            showInfo: false,\n            strokeColor: \"#faad14\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7EC4\\u5408\\u51C0\\u503C\\u8D70\\u52BF\",\n          extra: /*#__PURE__*/_jsxDEV(TrendingUpOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 39\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(ReactECharts, {\n            option: chartOption,\n            style: {\n              height: '300px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u98CE\\u9669\\u544A\\u8B66\",\n          extra: /*#__PURE__*/_jsxDEV(WarningOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 37\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: '300px',\n              overflowY: 'auto'\n            },\n            children: riskAlerts.map(alert => /*#__PURE__*/_jsxDEV(Alert, {\n              message: alert.type,\n              description: alert.message,\n              type: alert.level === 'HIGH' ? 'error' : alert.level === 'MEDIUM' ? 'warning' : 'info',\n              showIcon: true,\n              style: {\n                marginBottom: '8px'\n              }\n            }, alert.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6301\\u4ED3\\u80A1\\u7968\",\n          extra: \"\\u5B9E\\u65F6\\u66F4\\u65B0\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: columns,\n            dataSource: marketData,\n            rowKey: \"ts_code\",\n            loading: loading,\n            pagination: false,\n            size: \"middle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"XYw3IEVCrM0ZdxSHlPPN+xpWRwg=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "Statistic", "Table", "Tag", "Progress", "<PERSON><PERSON>", "ArrowUpOutlined", "ArrowDownOutlined", "DollarOutlined", "StockOutlined", "WarningOutlined", "TrendingUpOutlined", "ReactECharts", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "marketData", "setMarketData", "riskAlerts", "setRisk<PERSON><PERSON><PERSON>", "loading", "setLoading", "mockMarketData", "ts_code", "name", "price", "change", "change_pct", "volume", "mockRiskAlerts", "id", "type", "level", "message", "time", "setTimeout", "columns", "title", "dataIndex", "key", "render", "toFixed", "style", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pct", "Math", "abs", "alertColumns", "chartOption", "text", "left", "tooltip", "trigger", "xAxis", "data", "yAxis", "scale", "series", "smooth", "lineStyle", "areaStyle", "x", "y", "x2", "y2", "colorStops", "offset", "padding", "gutter", "marginBottom", "span", "value", "precision", "valueStyle", "prefix", "suffix", "percent", "showInfo", "strokeColor", "extra", "option", "height", "overflowY", "map", "alert", "description", "showIcon", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "size", "_c", "$RefreshReg$"], "sources": ["C:/<PERSON>_<PERSON>/量化交易系统/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Statistic, Table, Tag, Progress, Alert } from 'antd';\nimport { \n  ArrowUpOutlined, \n  ArrowDownOutlined, \n  DollarOutlined,\n  StockOutlined,\n  WarningOutlined,\n  TrendingUpOutlined\n} from '@ant-design/icons';\nimport ReactECharts from 'echarts-for-react';\n\ninterface MarketData {\n  ts_code: string;\n  name: string;\n  price: number;\n  change: number;\n  change_pct: number;\n  volume: number;\n}\n\ninterface RiskAlert {\n  id: string;\n  type: string;\n  level: string;\n  message: string;\n  time: string;\n}\n\nconst Dashboard: React.FC = () => {\n  const [marketData, setMarketData] = useState<MarketData[]>([]);\n  const [riskAlerts, setRiskAlerts] = useState<RiskAlert[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  // 模拟数据\n  useEffect(() => {\n    const mockMarketData: MarketData[] = [\n      { ts_code: '000001.SZ', name: '平安银行', price: 12.45, change: 0.15, change_pct: 1.22, volume: 1234567 },\n      { ts_code: '000002.SZ', name: '万科A', price: 18.76, change: -0.23, change_pct: -1.21, volume: 987654 },\n      { ts_code: '600000.SH', name: '浦发银行', price: 9.87, change: 0.08, change_pct: 0.82, volume: 2345678 },\n      { ts_code: '600036.SH', name: '招商银行', price: 35.42, change: 0.67, change_pct: 1.93, volume: 1876543 },\n      { ts_code: '000858.SZ', name: '五粮液', price: 128.90, change: -2.10, change_pct: -1.60, volume: 654321 },\n    ];\n\n    const mockRiskAlerts: RiskAlert[] = [\n      { id: '1', type: '持仓风险', level: 'HIGH', message: '平安银行持仓比例过高 (15.2%)', time: '10:30' },\n      { id: '2', type: '波动率', level: 'MEDIUM', message: '组合波动率超过阈值 (28.5%)', time: '09:45' },\n      { id: '3', type: '回撤', level: 'LOW', message: '当前回撤 3.2%，接近预警线', time: '09:15' },\n    ];\n\n    setTimeout(() => {\n      setMarketData(mockMarketData);\n      setRiskAlerts(mockRiskAlerts);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  // 表格列定义\n  const columns = [\n    {\n      title: '股票代码',\n      dataIndex: 'ts_code',\n      key: 'ts_code',\n    },\n    {\n      title: '股票名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '当前价格',\n      dataIndex: 'price',\n      key: 'price',\n      render: (price: number) => `¥${price.toFixed(2)}`,\n    },\n    {\n      title: '涨跌额',\n      dataIndex: 'change',\n      key: 'change',\n      render: (change: number) => (\n        <span style={{ color: change >= 0 ? '#3f8600' : '#cf1322' }}>\n          {change >= 0 ? '+' : ''}{change.toFixed(2)}\n        </span>\n      ),\n    },\n    {\n      title: '涨跌幅',\n      dataIndex: 'change_pct',\n      key: 'change_pct',\n      render: (pct: number) => (\n        <span style={{ color: pct >= 0 ? '#3f8600' : '#cf1322' }}>\n          {pct >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}\n          {Math.abs(pct).toFixed(2)}%\n        </span>\n      ),\n    },\n    {\n      title: '成交量',\n      dataIndex: 'volume',\n      key: 'volume',\n      render: (volume: number) => (volume / 10000).toFixed(1) + '万',\n    },\n  ];\n\n  // 风险告警列定义\n  const alertColumns = [\n    {\n      title: '类型',\n      dataIndex: 'type',\n      key: 'type',\n    },\n    {\n      title: '等级',\n      dataIndex: 'level',\n      key: 'level',\n      render: (level: string) => {\n        const color = level === 'HIGH' ? 'red' : level === 'MEDIUM' ? 'orange' : 'blue';\n        return <Tag color={color}>{level}</Tag>;\n      },\n    },\n    {\n      title: '消息',\n      dataIndex: 'message',\n      key: 'message',\n    },\n    {\n      title: '时间',\n      dataIndex: 'time',\n      key: 'time',\n    },\n  ];\n\n  // 图表配置\n  const chartOption = {\n    title: {\n      text: '组合净值走势',\n      left: 'center'\n    },\n    tooltip: {\n      trigger: 'axis'\n    },\n    xAxis: {\n      type: 'category',\n      data: ['09:30', '10:00', '10:30', '11:00', '11:30', '13:00', '13:30', '14:00', '14:30', '15:00']\n    },\n    yAxis: {\n      type: 'value',\n      scale: true\n    },\n    series: [{\n      name: '净值',\n      type: 'line',\n      data: [1.000, 1.002, 0.998, 1.005, 1.008, 1.012, 1.015, 1.018, 1.020, 1.025],\n      smooth: true,\n      lineStyle: {\n        color: '#1890ff'\n      },\n      areaStyle: {\n        color: {\n          type: 'linear',\n          x: 0,\n          y: 0,\n          x2: 0,\n          y2: 1,\n          colorStops: [{\n            offset: 0, color: 'rgba(24, 144, 255, 0.3)'\n          }, {\n            offset: 1, color: 'rgba(24, 144, 255, 0.1)'\n          }]\n        }\n      }\n    }]\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总资产\"\n              value={1250000}\n              precision={2}\n              valueStyle={{ color: '#3f8600' }}\n              prefix={<DollarOutlined />}\n              suffix=\"元\"\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"今日收益\"\n              value={12500}\n              precision={2}\n              valueStyle={{ color: '#3f8600' }}\n              prefix={<ArrowUpOutlined />}\n              suffix=\"元\"\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"持仓股票\"\n              value={5}\n              valueStyle={{ color: '#1890ff' }}\n              prefix={<StockOutlined />}\n              suffix=\"只\"\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"风险评分\"\n              value={35}\n              valueStyle={{ color: '#faad14' }}\n              prefix={<WarningOutlined />}\n              suffix=\"/100\"\n            />\n            <Progress percent={35} showInfo={false} strokeColor=\"#faad14\" />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 图表和告警 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={16}>\n          <Card title=\"组合净值走势\" extra={<TrendingUpOutlined />}>\n            <ReactECharts option={chartOption} style={{ height: '300px' }} />\n          </Card>\n        </Col>\n        <Col span={8}>\n          <Card title=\"风险告警\" extra={<WarningOutlined />}>\n            <div style={{ height: '300px', overflowY: 'auto' }}>\n              {riskAlerts.map(alert => (\n                <Alert\n                  key={alert.id}\n                  message={alert.type}\n                  description={alert.message}\n                  type={alert.level === 'HIGH' ? 'error' : alert.level === 'MEDIUM' ? 'warning' : 'info'}\n                  showIcon\n                  style={{ marginBottom: '8px' }}\n                />\n              ))}\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 股票列表 */}\n      <Row>\n        <Col span={24}>\n          <Card title=\"持仓股票\" extra=\"实时更新\">\n            <Table\n              columns={columns}\n              dataSource={marketData}\n              rowKey=\"ts_code\"\n              loading={loading}\n              pagination={false}\n              size=\"middle\"\n            />\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,MAAM;AAC7E,SACEC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,kBAAkB,QACb,mBAAmB;AAC1B,OAAOC,YAAY,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAmB7C,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAe,EAAE,CAAC;EAC9D,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAM0B,cAA4B,GAAG,CACnC;MAAEC,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAE,IAAI;MAAEC,UAAU,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAQ,CAAC,EACrG;MAAEL,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAE,CAAC,IAAI;MAAEC,UAAU,EAAE,CAAC,IAAI;MAAEC,MAAM,EAAE;IAAO,CAAC,EACrG;MAAEL,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAE,IAAI;MAAEC,UAAU,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAQ,CAAC,EACpG;MAAEL,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAE,IAAI;MAAEC,UAAU,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAQ,CAAC,EACrG;MAAEL,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE,CAAC,IAAI;MAAEC,UAAU,EAAE,CAAC,IAAI;MAAEC,MAAM,EAAE;IAAO,CAAC,CACvG;IAED,MAAMC,cAA2B,GAAG,CAClC;MAAEC,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,MAAM;MAAEC,OAAO,EAAE,oBAAoB;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACtF;MAAEJ,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE,QAAQ;MAAEC,OAAO,EAAE,mBAAmB;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACtF;MAAEJ,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,IAAI;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,iBAAiB;MAAEC,IAAI,EAAE;IAAQ,CAAC,CACjF;IAEDC,UAAU,CAAC,MAAM;MACflB,aAAa,CAACK,cAAc,CAAC;MAC7BH,aAAa,CAACU,cAAc,CAAC;MAC7BR,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMe,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAGf,KAAa,IAAK,IAAIA,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC;EACjD,CAAC,EACD;IACEJ,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGd,MAAc,iBACrBb,OAAA;MAAM6B,KAAK,EAAE;QAAEC,KAAK,EAAEjB,MAAM,IAAI,CAAC,GAAG,SAAS,GAAG;MAAU,CAAE;MAAAkB,QAAA,GACzDlB,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEA,MAAM,CAACe,OAAO,CAAC,CAAC,CAAC;IAAA;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC;EAEV,CAAC,EACD;IACEX,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGS,GAAW,iBAClBpC,OAAA;MAAM6B,KAAK,EAAE;QAAEC,KAAK,EAAEM,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG;MAAU,CAAE;MAAAL,QAAA,GACtDK,GAAG,IAAI,CAAC,gBAAGpC,OAAA,CAACR,eAAe;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGnC,OAAA,CAACP,iBAAiB;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACtDE,IAAI,CAACC,GAAG,CAACF,GAAG,CAAC,CAACR,OAAO,CAAC,CAAC,CAAC,EAAC,GAC5B;IAAA;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEV,CAAC,EACD;IACEX,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGZ,MAAc,IAAK,CAACA,MAAM,GAAG,KAAK,EAAEa,OAAO,CAAC,CAAC,CAAC,GAAG;EAC5D,CAAC,CACF;;EAED;EACA,MAAMW,YAAY,GAAG,CACnB;IACEf,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAGR,KAAa,IAAK;MACzB,MAAMW,KAAK,GAAGX,KAAK,KAAK,MAAM,GAAG,KAAK,GAAGA,KAAK,KAAK,QAAQ,GAAG,QAAQ,GAAG,MAAM;MAC/E,oBAAOnB,OAAA,CAACX,GAAG;QAACyC,KAAK,EAAEA,KAAM;QAAAC,QAAA,EAAEZ;MAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACzC;EACF,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,CACF;;EAED;EACA,MAAMc,WAAW,GAAG;IAClBhB,KAAK,EAAE;MACLiB,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE;IACR,CAAC;IACDC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACL3B,IAAI,EAAE,UAAU;MAChB4B,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACjG,CAAC;IACDC,KAAK,EAAE;MACL7B,IAAI,EAAE,OAAO;MACb8B,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE,CAAC;MACPtC,IAAI,EAAE,IAAI;MACVO,IAAI,EAAE,MAAM;MACZ4B,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAC5EI,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE;QACTrB,KAAK,EAAE;MACT,CAAC;MACDsB,SAAS,EAAE;QACTtB,KAAK,EAAE;UACLZ,IAAI,EAAE,QAAQ;UACdmC,CAAC,EAAE,CAAC;UACJC,CAAC,EAAE,CAAC;UACJC,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,CAAC;UACLC,UAAU,EAAE,CAAC;YACXC,MAAM,EAAE,CAAC;YAAE5B,KAAK,EAAE;UACpB,CAAC,EAAE;YACD4B,MAAM,EAAE,CAAC;YAAE5B,KAAK,EAAE;UACpB,CAAC;QACH;MACF;IACF,CAAC;EACH,CAAC;EAED,oBACE9B,OAAA;IAAK6B,KAAK,EAAE;MAAE8B,OAAO,EAAE;IAAO,CAAE;IAAA5B,QAAA,gBAE9B/B,OAAA,CAAChB,GAAG;MAAC4E,MAAM,EAAE,EAAG;MAAC/B,KAAK,EAAE;QAAEgC,YAAY,EAAE;MAAO,CAAE;MAAA9B,QAAA,gBAC/C/B,OAAA,CAACf,GAAG;QAAC6E,IAAI,EAAE,CAAE;QAAA/B,QAAA,eACX/B,OAAA,CAACd,IAAI;UAAA6C,QAAA,eACH/B,OAAA,CAACb,SAAS;YACRqC,KAAK,EAAC,oBAAK;YACXuC,KAAK,EAAE,OAAQ;YACfC,SAAS,EAAE,CAAE;YACbC,UAAU,EAAE;cAAEnC,KAAK,EAAE;YAAU,CAAE;YACjCoC,MAAM,eAAElE,OAAA,CAACN,cAAc;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BgC,MAAM,EAAC;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnC,OAAA,CAACf,GAAG;QAAC6E,IAAI,EAAE,CAAE;QAAA/B,QAAA,eACX/B,OAAA,CAACd,IAAI;UAAA6C,QAAA,eACH/B,OAAA,CAACb,SAAS;YACRqC,KAAK,EAAC,0BAAM;YACZuC,KAAK,EAAE,KAAM;YACbC,SAAS,EAAE,CAAE;YACbC,UAAU,EAAE;cAAEnC,KAAK,EAAE;YAAU,CAAE;YACjCoC,MAAM,eAAElE,OAAA,CAACR,eAAe;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BgC,MAAM,EAAC;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnC,OAAA,CAACf,GAAG;QAAC6E,IAAI,EAAE,CAAE;QAAA/B,QAAA,eACX/B,OAAA,CAACd,IAAI;UAAA6C,QAAA,eACH/B,OAAA,CAACb,SAAS;YACRqC,KAAK,EAAC,0BAAM;YACZuC,KAAK,EAAE,CAAE;YACTE,UAAU,EAAE;cAAEnC,KAAK,EAAE;YAAU,CAAE;YACjCoC,MAAM,eAAElE,OAAA,CAACL,aAAa;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BgC,MAAM,EAAC;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnC,OAAA,CAACf,GAAG;QAAC6E,IAAI,EAAE,CAAE;QAAA/B,QAAA,eACX/B,OAAA,CAACd,IAAI;UAAA6C,QAAA,gBACH/B,OAAA,CAACb,SAAS;YACRqC,KAAK,EAAC,0BAAM;YACZuC,KAAK,EAAE,EAAG;YACVE,UAAU,EAAE;cAAEnC,KAAK,EAAE;YAAU,CAAE;YACjCoC,MAAM,eAAElE,OAAA,CAACJ,eAAe;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BgC,MAAM,EAAC;UAAM;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACFnC,OAAA,CAACV,QAAQ;YAAC8E,OAAO,EAAE,EAAG;YAACC,QAAQ,EAAE,KAAM;YAACC,WAAW,EAAC;UAAS;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA,CAAChB,GAAG;MAAC4E,MAAM,EAAE,EAAG;MAAC/B,KAAK,EAAE;QAAEgC,YAAY,EAAE;MAAO,CAAE;MAAA9B,QAAA,gBAC/C/B,OAAA,CAACf,GAAG;QAAC6E,IAAI,EAAE,EAAG;QAAA/B,QAAA,eACZ/B,OAAA,CAACd,IAAI;UAACsC,KAAK,EAAC,sCAAQ;UAAC+C,KAAK,eAAEvE,OAAA,CAACH,kBAAkB;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,eACjD/B,OAAA,CAACF,YAAY;YAAC0E,MAAM,EAAEhC,WAAY;YAACX,KAAK,EAAE;cAAE4C,MAAM,EAAE;YAAQ;UAAE;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnC,OAAA,CAACf,GAAG;QAAC6E,IAAI,EAAE,CAAE;QAAA/B,QAAA,eACX/B,OAAA,CAACd,IAAI;UAACsC,KAAK,EAAC,0BAAM;UAAC+C,KAAK,eAAEvE,OAAA,CAACJ,eAAe;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,eAC5C/B,OAAA;YAAK6B,KAAK,EAAE;cAAE4C,MAAM,EAAE,OAAO;cAAEC,SAAS,EAAE;YAAO,CAAE;YAAA3C,QAAA,EAChD1B,UAAU,CAACsE,GAAG,CAACC,KAAK,iBACnB5E,OAAA,CAACT,KAAK;cAEJ6B,OAAO,EAAEwD,KAAK,CAAC1D,IAAK;cACpB2D,WAAW,EAAED,KAAK,CAACxD,OAAQ;cAC3BF,IAAI,EAAE0D,KAAK,CAACzD,KAAK,KAAK,MAAM,GAAG,OAAO,GAAGyD,KAAK,CAACzD,KAAK,KAAK,QAAQ,GAAG,SAAS,GAAG,MAAO;cACvF2D,QAAQ;cACRjD,KAAK,EAAE;gBAAEgC,YAAY,EAAE;cAAM;YAAE,GAL1Be,KAAK,CAAC3D,EAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMd,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA,CAAChB,GAAG;MAAA+C,QAAA,eACF/B,OAAA,CAACf,GAAG;QAAC6E,IAAI,EAAE,EAAG;QAAA/B,QAAA,eACZ/B,OAAA,CAACd,IAAI;UAACsC,KAAK,EAAC,0BAAM;UAAC+C,KAAK,EAAC,0BAAM;UAAAxC,QAAA,eAC7B/B,OAAA,CAACZ,KAAK;YACJmC,OAAO,EAAEA,OAAQ;YACjBwD,UAAU,EAAE5E,UAAW;YACvB6E,MAAM,EAAC,SAAS;YAChBzE,OAAO,EAAEA,OAAQ;YACjB0E,UAAU,EAAE,KAAM;YAClBC,IAAI,EAAC;UAAQ;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CAhPID,SAAmB;AAAAkF,EAAA,GAAnBlF,SAAmB;AAkPzB,eAAeA,SAAS;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}