{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar KEY_DELIMITER = '-->';\n/**\r\n * params handler\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @returns {*}\r\n */\nvar getAutoCurvenessParams = function (seriesModel) {\n  return seriesModel.get('autoCurveness') || null;\n};\n/**\r\n * Generate a list of edge curvatures, 20 is the default\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @param {number} appendLength\r\n * @return  20 => [0, -0.2, 0.2, -0.4, 0.4, -0.6, 0.6, -0.8, 0.8, -1, 1, -1.2, 1.2, -1.4, 1.4, -1.6, 1.6, -1.8, 1.8, -2]\r\n */\nvar createCurveness = function (seriesModel, appendLength) {\n  var autoCurvenessParmas = getAutoCurvenessParams(seriesModel);\n  var length = 20;\n  var curvenessList = [];\n  // handler the function set\n  if (zrUtil.isNumber(autoCurvenessParmas)) {\n    length = autoCurvenessParmas;\n  } else if (zrUtil.isArray(autoCurvenessParmas)) {\n    seriesModel.__curvenessList = autoCurvenessParmas;\n    return;\n  }\n  // append length\n  if (appendLength > length) {\n    length = appendLength;\n  }\n  // make sure the length is even\n  var len = length % 2 ? length + 2 : length + 3;\n  curvenessList = [];\n  for (var i = 0; i < len; i++) {\n    curvenessList.push((i % 2 ? i + 1 : i) / 10 * (i % 2 ? -1 : 1));\n  }\n  seriesModel.__curvenessList = curvenessList;\n};\n/**\r\n * Create different cache key data in the positive and negative directions, in order to set the curvature later\r\n * @param {number|string|module:echarts/data/Graph.Node} n1\r\n * @param {number|string|module:echarts/data/Graph.Node} n2\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @returns {string} key\r\n */\nvar getKeyOfEdges = function (n1, n2, seriesModel) {\n  var source = [n1.id, n1.dataIndex].join('.');\n  var target = [n2.id, n2.dataIndex].join('.');\n  return [seriesModel.uid, source, target].join(KEY_DELIMITER);\n};\n/**\r\n * get opposite key\r\n * @param {string} key\r\n * @returns {string}\r\n */\nvar getOppositeKey = function (key) {\n  var keys = key.split(KEY_DELIMITER);\n  return [keys[0], keys[2], keys[1]].join(KEY_DELIMITER);\n};\n/**\r\n * get edgeMap with key\r\n * @param edge\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n */\nvar getEdgeFromMap = function (edge, seriesModel) {\n  var key = getKeyOfEdges(edge.node1, edge.node2, seriesModel);\n  return seriesModel.__edgeMap[key];\n};\n/**\r\n * calculate all cases total length\r\n * @param edge\r\n * @param seriesModel\r\n * @returns {number}\r\n */\nvar getTotalLengthBetweenNodes = function (edge, seriesModel) {\n  var len = getEdgeMapLengthWithKey(getKeyOfEdges(edge.node1, edge.node2, seriesModel), seriesModel);\n  var lenV = getEdgeMapLengthWithKey(getKeyOfEdges(edge.node2, edge.node1, seriesModel), seriesModel);\n  return len + lenV;\n};\n/**\r\n *\r\n * @param key\r\n */\nvar getEdgeMapLengthWithKey = function (key, seriesModel) {\n  var edgeMap = seriesModel.__edgeMap;\n  return edgeMap[key] ? edgeMap[key].length : 0;\n};\n/**\r\n * Count the number of edges between the same two points, used to obtain the curvature table and the parity of the edge\r\n * @see /graph/GraphSeries.js@getInitialData\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n */\nexport function initCurvenessList(seriesModel) {\n  if (!getAutoCurvenessParams(seriesModel)) {\n    return;\n  }\n  seriesModel.__curvenessList = [];\n  seriesModel.__edgeMap = {};\n  // calc the array of curveness List\n  createCurveness(seriesModel);\n}\n/**\r\n * set edgeMap with key\r\n * @param {number|string|module:echarts/data/Graph.Node} n1\r\n * @param {number|string|module:echarts/data/Graph.Node} n2\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @param {number} index\r\n */\nexport function createEdgeMapForCurveness(n1, n2, seriesModel, index) {\n  if (!getAutoCurvenessParams(seriesModel)) {\n    return;\n  }\n  var key = getKeyOfEdges(n1, n2, seriesModel);\n  var edgeMap = seriesModel.__edgeMap;\n  var oppositeEdges = edgeMap[getOppositeKey(key)];\n  // set direction\n  if (edgeMap[key] && !oppositeEdges) {\n    edgeMap[key].isForward = true;\n  } else if (oppositeEdges && edgeMap[key]) {\n    oppositeEdges.isForward = true;\n    edgeMap[key].isForward = false;\n  }\n  edgeMap[key] = edgeMap[key] || [];\n  edgeMap[key].push(index);\n}\n/**\r\n * get curvature for edge\r\n * @param edge\r\n * @param {module:echarts/model/SeriesModel} seriesModel\r\n * @param index\r\n */\nexport function getCurvenessForEdge(edge, seriesModel, index, needReverse) {\n  var autoCurvenessParams = getAutoCurvenessParams(seriesModel);\n  var isArrayParam = zrUtil.isArray(autoCurvenessParams);\n  if (!autoCurvenessParams) {\n    return null;\n  }\n  var edgeArray = getEdgeFromMap(edge, seriesModel);\n  if (!edgeArray) {\n    return null;\n  }\n  var edgeIndex = -1;\n  for (var i = 0; i < edgeArray.length; i++) {\n    if (edgeArray[i] === index) {\n      edgeIndex = i;\n      break;\n    }\n  }\n  // if totalLen is Longer createCurveness\n  var totalLen = getTotalLengthBetweenNodes(edge, seriesModel);\n  createCurveness(seriesModel, totalLen);\n  edge.lineStyle = edge.lineStyle || {};\n  // if is opposite edge, must set curvenss to opposite number\n  var curKey = getKeyOfEdges(edge.node1, edge.node2, seriesModel);\n  var curvenessList = seriesModel.__curvenessList;\n  // if pass array no need parity\n  var parityCorrection = isArrayParam ? 0 : totalLen % 2 ? 0 : 1;\n  if (!edgeArray.isForward) {\n    // the opposite edge show outside\n    var oppositeKey = getOppositeKey(curKey);\n    var len = getEdgeMapLengthWithKey(oppositeKey, seriesModel);\n    var resValue = curvenessList[edgeIndex + len + parityCorrection];\n    // isNeedReverse, simple, force type need reverse the curveness in the junction of the forword and the opposite\n    if (needReverse) {\n      // set as array may make the parity handle with the len of opposite\n      if (isArrayParam) {\n        if (autoCurvenessParams && autoCurvenessParams[0] === 0) {\n          return (len + parityCorrection) % 2 ? resValue : -resValue;\n        } else {\n          return ((len % 2 ? 0 : 1) + parityCorrection) % 2 ? resValue : -resValue;\n        }\n      } else {\n        return (len + parityCorrection) % 2 ? resValue : -resValue;\n      }\n    } else {\n      return curvenessList[edgeIndex + len + parityCorrection];\n    }\n  } else {\n    return curvenessList[parityCorrection + edgeIndex];\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}