"""
Tushare数据获取客户端
"""
import tushare as ts
import pandas as pd
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import time
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

# 导入Token Manager
try:
    from token_manager import get_valid_token
    TOKEN_MANAGER_AVAILABLE = True
    logger.info("✅ Token Manager已加载")
except ImportError:
    TOKEN_MANAGER_AVAILABLE = False
    logger.warning("⚠️ Token Manager不可用，将使用配置文件中的固定Token")

class TushareClient:
    """Tushare数据客户端"""
    
    def __init__(self):
        self.token = None
        self.pro = None
        self._init_client()

    def _init_client(self):
        """初始化Tushare客户端"""
        try:
            # 优先使用Token Manager获取Token
            if TOKEN_MANAGER_AVAILABLE:
                logger.info("🔄 使用Token Manager获取最新Token...")
                self.token = get_valid_token()
                if self.token:
                    logger.info("✅ Token Manager获取Token成功")
                else:
                    logger.warning("⚠️ Token Manager获取Token失败，使用配置文件Token")
                    self.token = settings.TUSHARE_TOKEN
            else:
                logger.info("📝 使用配置文件中的固定Token")
                self.token = settings.TUSHARE_TOKEN

            # 设置Token并初始化API
            ts.set_token(self.token)
            self.pro = ts.pro_api()
            logger.info("✅ Tushare客户端初始化成功")

        except Exception as e:
            logger.error(f"❌ Tushare客户端初始化失败: {e}")
            raise
    
    def get_stock_basic(self, market: str = "A股") -> pd.DataFrame:
        """获取股票基本信息"""
        try:
            if market == "A股":
                df = self.pro.stock_basic(
                    exchange='',
                    list_status='L',
                    fields='ts_code,symbol,name,area,industry,list_date'
                )
            elif market == "港股":
                df = self.pro.hk_basic(
                    list_status='L',
                    fields='ts_code,name,market,list_date'
                )
            else:
                raise ValueError(f"不支持的市场类型: {market}")
            
            logger.info(f"获取到 {len(df)} 只{market}股票基本信息")
            return df
            
        except Exception as e:
            logger.error(f"获取{market}股票基本信息失败: {e}")
            return pd.DataFrame()
    
    def get_daily_data(self, ts_code: str, start_date: str, end_date: str = None) -> pd.DataFrame:
        """获取日线数据"""
        try:
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')
            
            # 判断是A股还是港股
            if ts_code.endswith('.HK'):
                df = self.pro.hk_daily(
                    ts_code=ts_code,
                    start_date=start_date,
                    end_date=end_date
                )
            else:
                df = self.pro.daily(
                    ts_code=ts_code,
                    start_date=start_date,
                    end_date=end_date
                )
            
            if not df.empty:
                # 数据清洗
                df = self._clean_daily_data(df)
                logger.debug(f"获取 {ts_code} 日线数据 {len(df)} 条")
            
            return df
            
        except Exception as e:
            logger.error(f"获取 {ts_code} 日线数据失败: {e}")
            return pd.DataFrame()
    
    def get_realtime_data(self, ts_codes: List[str]) -> pd.DataFrame:
        """获取实时数据"""
        try:
            # Tushare实时数据接口
            df = self.pro.realtime_quote(ts_code=','.join(ts_codes))
            
            if not df.empty:
                logger.debug(f"获取 {len(ts_codes)} 只股票实时数据")
            
            return df
            
        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            return pd.DataFrame()
    
    def get_basic_daily(self, ts_code: str, start_date: str, end_date: str = None) -> pd.DataFrame:
        """获取每日基本面数据（估值指标等）"""
        try:
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')
            
            df = self.pro.daily_basic(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,trade_date,pe,pe_ttm,pb,ps,ps_ttm,total_mv,circ_mv,turnover_rate,turnover_rate_f,volume_ratio'
            )
            
            if not df.empty:
                logger.debug(f"获取 {ts_code} 基本面数据 {len(df)} 条")
            
            return df
            
        except Exception as e:
            logger.error(f"获取 {ts_code} 基本面数据失败: {e}")
            return pd.DataFrame()
    
    def get_trade_calendar(self, start_date: str, end_date: str, exchange: str = 'SSE') -> pd.DataFrame:
        """获取交易日历"""
        try:
            df = self.pro.trade_cal(
                exchange=exchange,
                start_date=start_date,
                end_date=end_date
            )
            
            logger.debug(f"获取交易日历 {len(df)} 条")
            return df
            
        except Exception as e:
            logger.error(f"获取交易日历失败: {e}")
            return pd.DataFrame()
    
    def _clean_daily_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗日线数据"""
        if df.empty:
            return df
        
        # 删除重复数据
        df = df.drop_duplicates(subset=['ts_code', 'trade_date'])
        
        # 处理缺失值
        numeric_columns = ['open', 'high', 'low', 'close', 'pre_close', 'change', 'pct_chg', 'vol', 'amount']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 删除价格为0或负数的异常数据
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in df.columns:
                df = df[df[col] > 0]
        
        # 检查价格逻辑（最高价>=最低价等）
        if all(col in df.columns for col in ['high', 'low', 'open', 'close']):
            df = df[
                (df['high'] >= df['low']) &
                (df['high'] >= df['open']) &
                (df['high'] >= df['close']) &
                (df['low'] <= df['open']) &
                (df['low'] <= df['close'])
            ]
        
        # 按日期排序
        if 'trade_date' in df.columns:
            df = df.sort_values('trade_date')
        
        return df
    
    def batch_get_daily_data(self, ts_codes: List[str], start_date: str, end_date: str = None, delay: float = 0.2) -> Dict[str, pd.DataFrame]:
        """批量获取日线数据"""
        results = {}
        
        for i, ts_code in enumerate(ts_codes):
            try:
                df = self.get_daily_data(ts_code, start_date, end_date)
                results[ts_code] = df
                
                # 控制调用频率
                if i < len(ts_codes) - 1:
                    time.sleep(delay)
                    
            except Exception as e:
                logger.error(f"批量获取 {ts_code} 数据失败: {e}")
                results[ts_code] = pd.DataFrame()
        
        logger.info(f"批量获取完成，成功获取 {len([k for k, v in results.items() if not v.empty])} / {len(ts_codes)} 只股票数据")
        return results

# 全局Tushare客户端实例
tushare_client = TushareClient()

def get_tushare_client() -> TushareClient:
    """获取Tushare客户端实例"""
    return tushare_client
