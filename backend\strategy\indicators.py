"""
技术指标计算引擎
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import ta
import logging

logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """技术指标计算类"""
    
    def __init__(self):
        self.indicators = {}
    
    def calculate_ma(self, data: pd.DataFrame, periods: List[int] = [5, 10, 20, 60]) -> pd.DataFrame:
        """计算移动平均线"""
        result = data.copy()
        
        for period in periods:
            if len(data) >= period:
                result[f'ma_{period}'] = data['close'].rolling(window=period).mean()
            else:
                result[f'ma_{period}'] = np.nan
        
        return result
    
    def calculate_macd(self, data: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.DataFrame:
        """计算MACD指标"""
        result = data.copy()

        if len(data) >= slow:
            # 手动计算MACD
            ema_fast = data['close'].ewm(span=fast).mean()
            ema_slow = data['close'].ewm(span=slow).mean()

            # MACD线 = 快线EMA - 慢线EMA
            macd_line = ema_fast - ema_slow

            # 信号线 = MACD线的EMA
            macd_signal = macd_line.ewm(span=signal).mean()

            # 柱状图 = MACD线 - 信号线
            macd_histogram = macd_line - macd_signal

            result['macd'] = macd_line
            result['macd_signal'] = macd_signal
            result['macd_histogram'] = macd_histogram

            # 生成交易信号
            result['macd_signal_flag'] = self._generate_macd_signals(macd_line, macd_signal)
        else:
            result['macd'] = np.nan
            result['macd_signal'] = np.nan
            result['macd_histogram'] = np.nan
            result['macd_signal_flag'] = 'HOLD'

        return result
    
    def calculate_rsi(self, data: pd.DataFrame, period: int = 14) -> pd.DataFrame:
        """计算RSI指标"""
        result = data.copy()
        
        if len(data) >= period:
            result['rsi'] = ta.momentum.RSIIndicator(data['close'], window=period).rsi()
            
            # 生成交易信号
            result['rsi_signal'] = self._generate_rsi_signals(result['rsi'])
        else:
            result['rsi'] = np.nan
            result['rsi_signal'] = 'HOLD'
        
        return result
    
    def calculate_bollinger_bands(self, data: pd.DataFrame, period: int = 20, std: float = 2) -> pd.DataFrame:
        """计算布林带"""
        result = data.copy()
        
        if len(data) >= period:
            bollinger = ta.volatility.BollingerBands(data['close'], window=period, window_dev=std)
            
            result['bb_upper'] = bollinger.bollinger_hband()
            result['bb_middle'] = bollinger.bollinger_mavg()
            result['bb_lower'] = bollinger.bollinger_lband()
            result['bb_width'] = (result['bb_upper'] - result['bb_lower']) / result['bb_middle']
            
            # 生成交易信号
            result['bb_signal'] = self._generate_bb_signals(data['close'], result['bb_upper'], result['bb_lower'])
        else:
            result['bb_upper'] = np.nan
            result['bb_middle'] = np.nan
            result['bb_lower'] = np.nan
            result['bb_width'] = np.nan
            result['bb_signal'] = 'HOLD'
        
        return result
    
    def calculate_kdj(self, data: pd.DataFrame, k_period: int = 9, d_period: int = 3, j_period: int = 3) -> pd.DataFrame:
        """计算KDJ指标"""
        result = data.copy()
        
        if len(data) >= k_period:
            # 计算RSV
            low_min = data['low'].rolling(window=k_period).min()
            high_max = data['high'].rolling(window=k_period).max()
            rsv = (data['close'] - low_min) / (high_max - low_min) * 100
            
            # 计算K值
            result['k'] = rsv.ewm(alpha=1/d_period).mean()
            
            # 计算D值
            result['d'] = result['k'].ewm(alpha=1/d_period).mean()
            
            # 计算J值
            result['j'] = 3 * result['k'] - 2 * result['d']
            
            # 生成交易信号
            result['kdj_signal'] = self._generate_kdj_signals(result['k'], result['d'], result['j'])
        else:
            result['k'] = np.nan
            result['d'] = np.nan
            result['j'] = np.nan
            result['kdj_signal'] = 'HOLD'
        
        return result
    
    def calculate_volume_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算成交量指标"""
        result = data.copy()
        
        if len(data) >= 5:
            # 成交量移动平均
            result['vol_ma5'] = data['vol'].rolling(window=5).mean()
            result['vol_ma10'] = data['vol'].rolling(window=10).mean()
            
            # 量比
            if len(data) >= 10:
                result['volume_ratio'] = data['vol'] / result['vol_ma5']
            else:
                result['volume_ratio'] = np.nan
            
            # OBV (On Balance Volume)
            result['obv'] = ta.volume.OnBalanceVolumeIndicator(data['close'], data['vol']).on_balance_volume()
        else:
            result['vol_ma5'] = np.nan
            result['vol_ma10'] = np.nan
            result['volume_ratio'] = np.nan
            result['obv'] = np.nan
        
        return result
    
    def calculate_all_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算所有技术指标"""
        try:
            result = data.copy()
            
            # 确保数据按日期排序
            if 'trade_date' in result.columns:
                result = result.sort_values('trade_date')
            
            # 计算各种指标
            result = self.calculate_ma(result)
            result = self.calculate_macd(result)
            result = self.calculate_rsi(result)
            result = self.calculate_bollinger_bands(result)
            result = self.calculate_kdj(result)
            result = self.calculate_volume_indicators(result)
            
            # 计算综合信号
            result['综合信号'] = self._generate_composite_signal(result)
            
            logger.info(f"计算技术指标完成，数据长度: {len(result)}")
            return result
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return data
    
    def _generate_macd_signals(self, macd: pd.Series, signal: pd.Series) -> pd.Series:
        """生成MACD交易信号"""
        signals = pd.Series('HOLD', index=macd.index)
        
        # 金叉：MACD线上穿信号线
        golden_cross = (macd > signal) & (macd.shift(1) <= signal.shift(1))
        signals[golden_cross] = 'BUY'
        
        # 死叉：MACD线下穿信号线
        death_cross = (macd < signal) & (macd.shift(1) >= signal.shift(1))
        signals[death_cross] = 'SELL'
        
        return signals
    
    def _generate_rsi_signals(self, rsi: pd.Series) -> pd.Series:
        """生成RSI交易信号"""
        signals = pd.Series('HOLD', index=rsi.index)
        
        # 超卖信号
        oversold = (rsi < 30) & (rsi.shift(1) >= 30)
        signals[oversold] = 'BUY'
        
        # 超买信号
        overbought = (rsi > 70) & (rsi.shift(1) <= 70)
        signals[overbought] = 'SELL'
        
        return signals
    
    def _generate_bb_signals(self, close: pd.Series, upper: pd.Series, lower: pd.Series) -> pd.Series:
        """生成布林带交易信号"""
        signals = pd.Series('HOLD', index=close.index)
        
        # 价格触及下轨
        touch_lower = (close <= lower) & (close.shift(1) > lower.shift(1))
        signals[touch_lower] = 'BUY'
        
        # 价格触及上轨
        touch_upper = (close >= upper) & (close.shift(1) < upper.shift(1))
        signals[touch_upper] = 'SELL'
        
        return signals
    
    def _generate_kdj_signals(self, k: pd.Series, d: pd.Series, j: pd.Series) -> pd.Series:
        """生成KDJ交易信号"""
        signals = pd.Series('HOLD', index=k.index)
        
        # K线上穿D线且在低位
        golden_cross = (k > d) & (k.shift(1) <= d.shift(1)) & (k < 20)
        signals[golden_cross] = 'BUY'
        
        # K线下穿D线且在高位
        death_cross = (k < d) & (k.shift(1) >= d.shift(1)) & (k > 80)
        signals[death_cross] = 'SELL'
        
        return signals
    
    def _generate_composite_signal(self, data: pd.DataFrame) -> pd.Series:
        """生成综合交易信号"""
        signals = pd.Series('HOLD', index=data.index)
        
        # 统计各指标的买卖信号
        buy_signals = 0
        sell_signals = 0
        
        signal_columns = ['macd_signal_flag', 'rsi_signal', 'bb_signal', 'kdj_signal']
        
        for col in signal_columns:
            if col in data.columns:
                buy_signals += (data[col] == 'BUY').astype(int)
                sell_signals += (data[col] == 'SELL').astype(int)
        
        # 多数指标发出买入信号
        signals[buy_signals >= 2] = 'BUY'
        
        # 多数指标发出卖出信号
        signals[sell_signals >= 2] = 'SELL'
        
        return signals

# 全局技术指标计算器实例
technical_indicators = TechnicalIndicators()

def get_technical_indicators() -> TechnicalIndicators:
    """获取技术指标计算器实例"""
    return technical_indicators
