{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BaseAxisPointer from './BaseAxisPointer.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as viewHelper from './viewHelper.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport AxisBuilder from '../axis/AxisBuilder.js';\nvar PolarAxisPointer = /** @class */function (_super) {\n  __extends(PolarAxisPointer, _super);\n  function PolarAxisPointer() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /**\r\n   * @override\r\n   */\n  PolarAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    var axis = axisModel.axis;\n    if (axis.dim === 'angle') {\n      this.animationThreshold = Math.PI / 18;\n    }\n    var polar = axis.polar;\n    var otherAxis = polar.getOtherAxis(axis);\n    var otherExtent = otherAxis.getExtent();\n    var coordValue = axis.dataToCoord(value);\n    var axisPointerType = axisPointerModel.get('type');\n    if (axisPointerType && axisPointerType !== 'none') {\n      var elStyle = viewHelper.buildElStyle(axisPointerModel);\n      var pointerOption = pointerShapeBuilder[axisPointerType](axis, polar, coordValue, otherExtent);\n      pointerOption.style = elStyle;\n      elOption.graphicKey = pointerOption.type;\n      elOption.pointer = pointerOption;\n    }\n    var labelMargin = axisPointerModel.get(['label', 'margin']);\n    var labelPos = getLabelPosition(value, axisModel, axisPointerModel, polar, labelMargin);\n    viewHelper.buildLabelElOption(elOption, axisModel, axisPointerModel, api, labelPos);\n  };\n  return PolarAxisPointer;\n}(BaseAxisPointer);\n;\nfunction getLabelPosition(value, axisModel, axisPointerModel, polar, labelMargin) {\n  var axis = axisModel.axis;\n  var coord = axis.dataToCoord(value);\n  var axisAngle = polar.getAngleAxis().getExtent()[0];\n  axisAngle = axisAngle / 180 * Math.PI;\n  var radiusExtent = polar.getRadiusAxis().getExtent();\n  var position;\n  var align;\n  var verticalAlign;\n  if (axis.dim === 'radius') {\n    var transform = matrix.create();\n    matrix.rotate(transform, transform, axisAngle);\n    matrix.translate(transform, transform, [polar.cx, polar.cy]);\n    position = graphic.applyTransform([coord, -labelMargin], transform);\n    var labelRotation = axisModel.getModel('axisLabel').get('rotate') || 0;\n    // @ts-ignore\n    var labelLayout = AxisBuilder.innerTextLayout(axisAngle, labelRotation * Math.PI / 180, -1);\n    align = labelLayout.textAlign;\n    verticalAlign = labelLayout.textVerticalAlign;\n  } else {\n    // angle axis\n    var r = radiusExtent[1];\n    position = polar.coordToPoint([r + labelMargin, coord]);\n    var cx = polar.cx;\n    var cy = polar.cy;\n    align = Math.abs(position[0] - cx) / r < 0.3 ? 'center' : position[0] > cx ? 'left' : 'right';\n    verticalAlign = Math.abs(position[1] - cy) / r < 0.3 ? 'middle' : position[1] > cy ? 'top' : 'bottom';\n  }\n  return {\n    position: position,\n    align: align,\n    verticalAlign: verticalAlign\n  };\n}\nvar pointerShapeBuilder = {\n  line: function (axis, polar, coordValue, otherExtent) {\n    return axis.dim === 'angle' ? {\n      type: 'Line',\n      shape: viewHelper.makeLineShape(polar.coordToPoint([otherExtent[0], coordValue]), polar.coordToPoint([otherExtent[1], coordValue]))\n    } : {\n      type: 'Circle',\n      shape: {\n        cx: polar.cx,\n        cy: polar.cy,\n        r: coordValue\n      }\n    };\n  },\n  shadow: function (axis, polar, coordValue, otherExtent) {\n    var bandWidth = Math.max(1, axis.getBandWidth());\n    var radian = Math.PI / 180;\n    return axis.dim === 'angle' ? {\n      type: 'Sector',\n      shape: viewHelper.makeSectorShape(polar.cx, polar.cy, otherExtent[0], otherExtent[1],\n      // In ECharts y is negative if angle is positive\n      (-coordValue - bandWidth / 2) * radian, (-coordValue + bandWidth / 2) * radian)\n    } : {\n      type: 'Sector',\n      shape: viewHelper.makeSectorShape(polar.cx, polar.cy, coordValue - bandWidth / 2, coordValue + bandWidth / 2, 0, Math.PI * 2)\n    };\n  }\n};\nexport default PolarAxisPointer;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}