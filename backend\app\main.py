"""
量化交易系统 - 主应用入口
"""
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import logging

from app.core.config import settings
from app.api.api_v1.api import api_router
from app.websocket.websocket_handler import get_websocket_handler
from app.tasks.scheduler import get_scheduler
from app.tasks.data_tasks import register_all_tasks

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="量化交易监控系统 - 专注于数据分析和AI辅助决策"
)

# 设置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# WebSocket处理器
websocket_handler = get_websocket_handler()

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket连接端点"""
    await websocket_handler.handle_connection(websocket, client_id)

@app.websocket("/ws")
async def websocket_endpoint_simple(websocket: WebSocket):
    """简单WebSocket连接端点"""
    await websocket_handler.handle_connection(websocket)

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("启动量化交易监控系统")

    # 注册定时任务
    register_all_tasks()

    # 启动任务调度器
    scheduler = get_scheduler()
    asyncio.create_task(scheduler.start())

    logger.info("系统启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("关闭量化交易监控系统")

    # 停止任务调度器
    scheduler = get_scheduler()
    scheduler.stop()

@app.get("/")
async def root():
    return {"message": "量化交易监控系统 API", "version": settings.VERSION}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "quant-trading-system"}

@app.get("/ws/stats")
async def websocket_stats():
    """获取WebSocket连接统计"""
    return websocket_handler.manager.get_connection_stats()

@app.get("/tasks/status")
async def task_status():
    """获取任务状态"""
    scheduler = get_scheduler()
    return {
        "tasks": scheduler.get_task_status(),
        "scheduler_running": scheduler.running
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
