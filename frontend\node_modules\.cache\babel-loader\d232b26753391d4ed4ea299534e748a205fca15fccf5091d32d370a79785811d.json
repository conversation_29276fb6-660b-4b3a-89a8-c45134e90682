{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nimport * as polyHelper from '../helper/poly.js';\nvar PolylineShape = function () {\n  function PolylineShape() {\n    this.points = null;\n    this.percent = 1;\n    this.smooth = 0;\n    this.smoothConstraint = null;\n  }\n  return PolylineShape;\n}();\nexport { PolylineShape };\nvar Polyline = function (_super) {\n  __extends(Polyline, _super);\n  function Polyline(opts) {\n    return _super.call(this, opts) || this;\n  }\n  Polyline.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  Polyline.prototype.getDefaultShape = function () {\n    return new PolylineShape();\n  };\n  Polyline.prototype.buildPath = function (ctx, shape) {\n    polyHelper.buildPath(ctx, shape, false);\n  };\n  return Polyline;\n}(Path);\nPolyline.prototype.type = 'polyline';\nexport default Polyline;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}