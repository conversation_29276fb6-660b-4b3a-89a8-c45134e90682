{"ast": null, "code": "import { __assign, __extends } from \"tslib\";\nimport React, { PureComponent } from 'react';\nimport { bind, clear } from 'size-sensor';\nimport { pick } from './helper/pick';\nimport { isFunction } from './helper/is-function';\nimport { isString } from './helper/is-string';\nimport { isEqual } from './helper/is-equal';\n/**\n * core component for echarts binding\n */\nvar EChartsReactCore = /** @class */function (_super) {\n  __extends(EChartsReactCore, _super);\n  function EChartsReactCore(props) {\n    var _this = _super.call(this, props) || this;\n    _this.echarts = props.echarts;\n    _this.ele = null;\n    _this.isInitialResize = true;\n    return _this;\n  }\n  EChartsReactCore.prototype.componentDidMount = function () {\n    this.renderNewEcharts();\n  };\n  // update\n  EChartsReactCore.prototype.componentDidUpdate = function (prevProps) {\n    /**\n     * if shouldSetOption return false, then return, not update echarts options\n     * default is true\n     */\n    var shouldSetOption = this.props.shouldSetOption;\n    if (isFunction(shouldSetOption) && !shouldSetOption(prevProps, this.props)) {\n      return;\n    }\n    // 以下属性修改的时候，需要 dispose 之后再新建\n    // 1. 切换 theme 的时候\n    // 2. 修改 opts 的时候\n    // 3. 修改 onEvents 的时候，这样可以取消所有之前绑定的事件 issue #151\n    if (!isEqual(prevProps.theme, this.props.theme) || !isEqual(prevProps.opts, this.props.opts) || !isEqual(prevProps.onEvents, this.props.onEvents)) {\n      this.dispose();\n      this.renderNewEcharts(); // 重建\n      return;\n    }\n    // when these props are not isEqual, update echarts\n    var pickKeys = ['option', 'notMerge', 'lazyUpdate', 'showLoading', 'loadingOption'];\n    if (!isEqual(pick(this.props, pickKeys), pick(prevProps, pickKeys))) {\n      this.updateEChartsOption();\n    }\n    /**\n     * when style or class name updated, change size.\n     */\n    if (!isEqual(prevProps.style, this.props.style) || !isEqual(prevProps.className, this.props.className)) {\n      this.resize();\n    }\n  };\n  EChartsReactCore.prototype.componentWillUnmount = function () {\n    this.dispose();\n  };\n  /**\n   * return the echart object\n   * 1. if exist, return the existed instance\n   * 2. or new one instance\n   */\n  EChartsReactCore.prototype.getEchartsInstance = function () {\n    return this.echarts.getInstanceByDom(this.ele) || this.echarts.init(this.ele, this.props.theme, this.props.opts);\n  };\n  /**\n   * dispose echarts and clear size-sensor\n   */\n  EChartsReactCore.prototype.dispose = function () {\n    if (this.ele) {\n      try {\n        clear(this.ele);\n      } catch (e) {\n        console.warn(e);\n      }\n      // dispose echarts instance\n      this.echarts.dispose(this.ele);\n    }\n  };\n  /**\n   * render a new echarts instance\n   */\n  EChartsReactCore.prototype.renderNewEcharts = function () {\n    var _this = this;\n    var _a = this.props,\n      onEvents = _a.onEvents,\n      onChartReady = _a.onChartReady;\n    // 1. new echarts instance\n    var echartsInstance = this.updateEChartsOption();\n    // 2. bind events\n    this.bindEvents(echartsInstance, onEvents || {});\n    // 3. on chart ready\n    if (isFunction(onChartReady)) onChartReady(echartsInstance);\n    // 4. on resize\n    if (this.ele) {\n      bind(this.ele, function () {\n        _this.resize();\n      });\n    }\n  };\n  // bind the events\n  EChartsReactCore.prototype.bindEvents = function (instance, events) {\n    function _bindEvent(eventName, func) {\n      // ignore the event config which not satisfy\n      if (isString(eventName) && isFunction(func)) {\n        // binding event\n        instance.on(eventName, function (param) {\n          func(param, instance);\n        });\n      }\n    }\n    // loop and bind\n    for (var eventName in events) {\n      if (Object.prototype.hasOwnProperty.call(events, eventName)) {\n        _bindEvent(eventName, events[eventName]);\n      }\n    }\n  };\n  /**\n   * render the echarts\n   */\n  EChartsReactCore.prototype.updateEChartsOption = function () {\n    var _a = this.props,\n      option = _a.option,\n      _b = _a.notMerge,\n      notMerge = _b === void 0 ? false : _b,\n      _c = _a.lazyUpdate,\n      lazyUpdate = _c === void 0 ? false : _c,\n      showLoading = _a.showLoading,\n      _d = _a.loadingOption,\n      loadingOption = _d === void 0 ? null : _d;\n    // 1. get or initial the echarts object\n    var echartInstance = this.getEchartsInstance();\n    // 2. set the echarts option\n    echartInstance.setOption(option, notMerge, lazyUpdate);\n    // 3. set loading mask\n    if (showLoading) echartInstance.showLoading(loadingOption);else echartInstance.hideLoading();\n    return echartInstance;\n  };\n  /**\n   * resize wrapper\n   */\n  EChartsReactCore.prototype.resize = function () {\n    // 1. get the echarts object\n    var echartsInstance = this.getEchartsInstance();\n    // 2. call echarts instance resize if not the initial resize\n    // resize should not happen on first render as it will cancel initial echarts animations\n    if (!this.isInitialResize) {\n      try {\n        echartsInstance.resize();\n      } catch (e) {\n        console.warn(e);\n      }\n    }\n    // 3. update variable for future calls\n    this.isInitialResize = false;\n  };\n  EChartsReactCore.prototype.render = function () {\n    var _this = this;\n    var _a = this.props,\n      style = _a.style,\n      _b = _a.className,\n      className = _b === void 0 ? '' : _b;\n    // default height = 300\n    var newStyle = __assign({\n      height: 300\n    }, style);\n    return React.createElement(\"div\", {\n      ref: function (e) {\n        _this.ele = e;\n      },\n      style: newStyle,\n      className: \"echarts-for-react \" + className\n    });\n  };\n  return EChartsReactCore;\n}(PureComponent);\nexport default EChartsReactCore;\n//# sourceMappingURL=core.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}