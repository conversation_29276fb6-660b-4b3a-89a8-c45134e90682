"""
智能选股模块
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass

from strategy.indicators import get_technical_indicators
from data.tushare_client import get_tushare_client

logger = logging.getLogger(__name__)

@dataclass
class StockScore:
    """股票评分"""
    ts_code: str
    name: str
    total_score: float
    technical_score: float
    fundamental_score: float
    momentum_score: float
    risk_score: float
    recommendation: str  # BUY/HOLD/SELL
    reasons: List[str]

class StockSelector:
    """智能选股器"""
    
    def __init__(self):
        self.technical_indicators = get_technical_indicators()
        self.tushare_client = get_tushare_client()
        
        # 评分权重
        self.weights = {
            'technical': 0.3,
            'fundamental': 0.25,
            'momentum': 0.25,
            'risk': 0.2
        }
        
        # 评分阈值
        self.thresholds = {
            'buy': 75,
            'hold': 50,
            'sell': 30
        }
    
    def select_stocks(self, 
                     stock_pool: List[str],
                     top_n: int = 10,
                     min_score: float = 60) -> List[StockScore]:
        """智能选股"""
        
        logger.info(f"开始智能选股，股票池大小: {len(stock_pool)}")
        
        stock_scores = []
        
        for ts_code in stock_pool:
            try:
                score = self._calculate_stock_score(ts_code)
                if score and score.total_score >= min_score:
                    stock_scores.append(score)
            except Exception as e:
                logger.error(f"评估股票 {ts_code} 失败: {e}")
        
        # 按总分排序
        stock_scores.sort(key=lambda x: x.total_score, reverse=True)
        
        # 返回前N只
        selected = stock_scores[:top_n]
        
        logger.info(f"选股完成，选出 {len(selected)} 只股票")
        return selected
    
    def _calculate_stock_score(self, ts_code: str) -> Optional[StockScore]:
        """计算股票综合评分"""
        
        try:
            # 获取股票数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=60)
            
            df = self.tushare_client.get_daily_data(
                ts_code,
                start_date.strftime('%Y%m%d'),
                end_date.strftime('%Y%m%d')
            )
            
            if df.empty:
                return None
            
            # 计算技术指标
            df_with_indicators = self.technical_indicators.calculate_all_indicators(df)
            latest = df_with_indicators.iloc[-1]
            
            # 获取股票名称
            stock_name = self._get_stock_name(ts_code)
            
            # 计算各维度评分
            technical_score = self._calculate_technical_score(df_with_indicators)
            fundamental_score = self._calculate_fundamental_score(ts_code, latest)
            momentum_score = self._calculate_momentum_score(df_with_indicators)
            risk_score = self._calculate_risk_score(df_with_indicators)
            
            # 计算总分
            total_score = (
                technical_score * self.weights['technical'] +
                fundamental_score * self.weights['fundamental'] +
                momentum_score * self.weights['momentum'] +
                risk_score * self.weights['risk']
            )
            
            # 生成推荐和理由
            recommendation, reasons = self._generate_recommendation(
                total_score, technical_score, fundamental_score, momentum_score, risk_score
            )
            
            return StockScore(
                ts_code=ts_code,
                name=stock_name,
                total_score=total_score,
                technical_score=technical_score,
                fundamental_score=fundamental_score,
                momentum_score=momentum_score,
                risk_score=risk_score,
                recommendation=recommendation,
                reasons=reasons
            )
            
        except Exception as e:
            logger.error(f"计算 {ts_code} 评分失败: {e}")
            return None
    
    def _calculate_technical_score(self, df: pd.DataFrame) -> float:
        """计算技术面评分"""
        if df.empty:
            return 0
        
        latest = df.iloc[-1]
        score = 0
        max_score = 100
        
        try:
            # MA趋势评分 (25分)
            if pd.notna(latest.get('ma_5')) and pd.notna(latest.get('ma_20')):
                if latest['ma_5'] > latest['ma_20']:
                    score += 15  # 短期均线在长期均线之上
                if latest['close'] > latest['ma_5']:
                    score += 10  # 价格在短期均线之上
            
            # MACD评分 (25分)
            if pd.notna(latest.get('macd')) and pd.notna(latest.get('macd_signal')):
                if latest['macd'] > latest['macd_signal']:
                    score += 15  # MACD金叉
                if latest['macd'] > 0:
                    score += 10  # MACD在零轴之上
            
            # RSI评分 (25分)
            if pd.notna(latest.get('rsi')):
                rsi = latest['rsi']
                if 30 < rsi < 70:
                    score += 15  # RSI在正常区间
                elif 50 < rsi < 70:
                    score += 25  # RSI在强势区间
                elif rsi > 70:
                    score += 5   # RSI超买
            
            # 布林带评分 (25分)
            if all(pd.notna(latest.get(col)) for col in ['bb_upper', 'bb_middle', 'bb_lower']):
                bb_position = (latest['close'] - latest['bb_lower']) / (latest['bb_upper'] - latest['bb_lower'])
                if 0.2 < bb_position < 0.8:
                    score += 15  # 在布林带中部
                elif bb_position > 0.8:
                    score += 5   # 接近上轨
                elif bb_position < 0.2:
                    score += 25  # 接近下轨，可能反弹
            
        except Exception as e:
            logger.error(f"计算技术面评分失败: {e}")
        
        return min(score, max_score)
    
    def _calculate_fundamental_score(self, ts_code: str, latest: pd.Series) -> float:
        """计算基本面评分"""
        score = 50  # 基础分
        
        try:
            # 获取基本面数据
            basic_data = self.tushare_client.get_basic_daily(
                ts_code,
                latest['trade_date'],
                latest['trade_date']
            )
            
            if not basic_data.empty:
                basic = basic_data.iloc[0]
                
                # PE评分 (30分)
                if pd.notna(basic.get('pe_ttm')):
                    pe = basic['pe_ttm']
                    if 10 < pe < 25:
                        score += 20  # 合理PE
                    elif 5 < pe <= 10:
                        score += 30  # 低PE
                    elif pe > 50:
                        score -= 10  # 高PE
                
                # PB评分 (20分)
                if pd.notna(basic.get('pb')):
                    pb = basic['pb']
                    if 1 < pb < 3:
                        score += 15  # 合理PB
                    elif pb <= 1:
                        score += 20  # 低PB
                    elif pb > 5:
                        score -= 5   # 高PB
            
            # 成交量评分 (20分)
            if pd.notna(latest.get('volume_ratio')):
                vol_ratio = latest['volume_ratio']
                if 1.2 < vol_ratio < 3:
                    score += 15  # 适度放量
                elif vol_ratio > 3:
                    score += 5   # 大幅放量
            
        except Exception as e:
            logger.error(f"计算基本面评分失败: {e}")
        
        return min(max(score, 0), 100)
    
    def _calculate_momentum_score(self, df: pd.DataFrame) -> float:
        """计算动量评分"""
        if len(df) < 20:
            return 50
        
        score = 0
        
        try:
            # 价格动量 (40分)
            recent_5 = df.tail(5)['close'].mean()
            recent_20 = df.tail(20)['close'].mean()
            
            if recent_5 > recent_20:
                score += 25  # 短期均价高于长期均价
            
            # 涨跌幅评分 (30分)
            latest_change = df.iloc[-1]['pct_chg']
            if 0 < latest_change < 5:
                score += 20  # 适度上涨
            elif latest_change >= 5:
                score += 10  # 大幅上涨
            elif -2 < latest_change < 0:
                score += 15  # 小幅下跌
            
            # 连续性评分 (30分)
            recent_changes = df.tail(5)['pct_chg']
            positive_days = (recent_changes > 0).sum()
            if positive_days >= 3:
                score += 25  # 多数上涨
            elif positive_days >= 2:
                score += 15  # 半数上涨
            
        except Exception as e:
            logger.error(f"计算动量评分失败: {e}")
            score = 50
        
        return min(score, 100)
    
    def _calculate_risk_score(self, df: pd.DataFrame) -> float:
        """计算风险评分（分数越高风险越低）"""
        if len(df) < 20:
            return 50
        
        score = 100  # 从满分开始扣分
        
        try:
            # 波动率风险 (40分)
            returns = df['close'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(252)  # 年化波动率
            
            if volatility > 0.4:
                score -= 30  # 高波动率
            elif volatility > 0.3:
                score -= 20  # 中等波动率
            elif volatility > 0.2:
                score -= 10  # 适中波动率
            
            # 回撤风险 (30分)
            cummax = df['close'].expanding().max()
            drawdown = (df['close'] - cummax) / cummax
            max_drawdown = drawdown.min()
            
            if max_drawdown < -0.3:
                score -= 25  # 大幅回撤
            elif max_drawdown < -0.2:
                score -= 15  # 中等回撤
            elif max_drawdown < -0.1:
                score -= 5   # 小幅回撤
            
            # 流动性风险 (30分)
            avg_volume = df.tail(20)['vol'].mean()
            if avg_volume < 100000:
                score -= 20  # 低流动性
            elif avg_volume < 500000:
                score -= 10  # 中等流动性
            
        except Exception as e:
            logger.error(f"计算风险评分失败: {e}")
            score = 50
        
        return max(score, 0)
    
    def _generate_recommendation(self, total_score: float, tech_score: float, 
                               fund_score: float, momentum_score: float, risk_score: float) -> Tuple[str, List[str]]:
        """生成推荐和理由"""
        
        reasons = []
        
        # 分析各维度表现
        if tech_score >= 70:
            reasons.append("技术面表现强劲")
        elif tech_score <= 40:
            reasons.append("技术面偏弱")
        
        if fund_score >= 70:
            reasons.append("基本面良好")
        elif fund_score <= 40:
            reasons.append("基本面一般")
        
        if momentum_score >= 70:
            reasons.append("动量强劲")
        elif momentum_score <= 40:
            reasons.append("动量不足")
        
        if risk_score >= 70:
            reasons.append("风险可控")
        elif risk_score <= 40:
            reasons.append("风险较高")
        
        # 生成推荐
        if total_score >= self.thresholds['buy']:
            recommendation = "BUY"
            if not reasons:
                reasons.append("综合评分优秀")
        elif total_score >= self.thresholds['hold']:
            recommendation = "HOLD"
            if not reasons:
                reasons.append("综合评分中等")
        else:
            recommendation = "SELL"
            if not reasons:
                reasons.append("综合评分偏低")
        
        return recommendation, reasons
    
    def _get_stock_name(self, ts_code: str) -> str:
        """获取股票名称"""
        # 这里可以从数据库或缓存中获取
        # 简化实现，返回代码
        return ts_code.split('.')[0]

# 全局选股器实例
stock_selector = StockSelector()

def get_stock_selector() -> StockSelector:
    """获取选股器实例"""
    return stock_selector
