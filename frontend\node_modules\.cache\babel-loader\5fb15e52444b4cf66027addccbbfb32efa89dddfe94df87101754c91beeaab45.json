{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as echarts from '../../../core/echarts.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { ToolboxFeature } from '../featureManager.js';\nimport { SINGLE_REFERRING } from '../../../util/model.js';\nvar INNER_STACK_KEYWORD = '__ec_magicType_stack__';\nvar ICON_TYPES = ['line', 'bar', 'stack'];\n// stack and tiled appears in pair for the title\nvar TITLE_TYPES = ['line', 'bar', 'stack', 'tiled'];\nvar radioTypes = [['line', 'bar'], ['stack']];\nvar MagicType = /** @class */function (_super) {\n  __extends(MagicType, _super);\n  function MagicType() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  MagicType.prototype.getIcons = function () {\n    var model = this.model;\n    var availableIcons = model.get('icon');\n    var icons = {};\n    zrUtil.each(model.get('type'), function (type) {\n      if (availableIcons[type]) {\n        icons[type] = availableIcons[type];\n      }\n    });\n    return icons;\n  };\n  MagicType.getDefaultOption = function (ecModel) {\n    var defaultOption = {\n      show: true,\n      type: [],\n      // Icon group\n      icon: {\n        line: 'M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4',\n        bar: 'M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7',\n        // eslint-disable-next-line\n        stack: 'M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z' // jshint ignore:line\n      },\n      // `line`, `bar`, `stack`, `tiled`\n      title: ecModel.getLocaleModel().get(['toolbox', 'magicType', 'title']),\n      option: {},\n      seriesIndex: {}\n    };\n    return defaultOption;\n  };\n  MagicType.prototype.onclick = function (ecModel, api, type) {\n    var model = this.model;\n    var seriesIndex = model.get(['seriesIndex', type]);\n    // Not supported magicType\n    if (!seriesOptGenreator[type]) {\n      return;\n    }\n    var newOption = {\n      series: []\n    };\n    var generateNewSeriesTypes = function (seriesModel) {\n      var seriesType = seriesModel.subType;\n      var seriesId = seriesModel.id;\n      var newSeriesOpt = seriesOptGenreator[type](seriesType, seriesId, seriesModel, model);\n      if (newSeriesOpt) {\n        // PENDING If merge original option?\n        zrUtil.defaults(newSeriesOpt, seriesModel.option);\n        newOption.series.push(newSeriesOpt);\n      }\n      // Modify boundaryGap\n      var coordSys = seriesModel.coordinateSystem;\n      if (coordSys && coordSys.type === 'cartesian2d' && (type === 'line' || type === 'bar')) {\n        var categoryAxis = coordSys.getAxesByScale('ordinal')[0];\n        if (categoryAxis) {\n          var axisDim = categoryAxis.dim;\n          var axisType = axisDim + 'Axis';\n          var axisModel = seriesModel.getReferringComponents(axisType, SINGLE_REFERRING).models[0];\n          var axisIndex = axisModel.componentIndex;\n          newOption[axisType] = newOption[axisType] || [];\n          for (var i = 0; i <= axisIndex; i++) {\n            newOption[axisType][axisIndex] = newOption[axisType][axisIndex] || {};\n          }\n          newOption[axisType][axisIndex].boundaryGap = type === 'bar';\n        }\n      }\n    };\n    zrUtil.each(radioTypes, function (radio) {\n      if (zrUtil.indexOf(radio, type) >= 0) {\n        zrUtil.each(radio, function (item) {\n          model.setIconStatus(item, 'normal');\n        });\n      }\n    });\n    model.setIconStatus(type, 'emphasis');\n    ecModel.eachComponent({\n      mainType: 'series',\n      query: seriesIndex == null ? null : {\n        seriesIndex: seriesIndex\n      }\n    }, generateNewSeriesTypes);\n    var newTitle;\n    var currentType = type;\n    // Change title of stack\n    if (type === 'stack') {\n      // use titles in model instead of ecModel\n      // as stack and tiled appears in pair, just flip them\n      // no need of checking stack state\n      newTitle = zrUtil.merge({\n        stack: model.option.title.tiled,\n        tiled: model.option.title.stack\n      }, model.option.title);\n      if (model.get(['iconStatus', type]) !== 'emphasis') {\n        currentType = 'tiled';\n      }\n    }\n    api.dispatchAction({\n      type: 'changeMagicType',\n      currentType: currentType,\n      newOption: newOption,\n      newTitle: newTitle,\n      featureName: 'magicType'\n    });\n  };\n  return MagicType;\n}(ToolboxFeature);\nvar seriesOptGenreator = {\n  'line': function (seriesType, seriesId, seriesModel, model) {\n    if (seriesType === 'bar') {\n      return zrUtil.merge({\n        id: seriesId,\n        type: 'line',\n        // Preserve data related option\n        data: seriesModel.get('data'),\n        stack: seriesModel.get('stack'),\n        markPoint: seriesModel.get('markPoint'),\n        markLine: seriesModel.get('markLine')\n      }, model.get(['option', 'line']) || {}, true);\n    }\n  },\n  'bar': function (seriesType, seriesId, seriesModel, model) {\n    if (seriesType === 'line') {\n      return zrUtil.merge({\n        id: seriesId,\n        type: 'bar',\n        // Preserve data related option\n        data: seriesModel.get('data'),\n        stack: seriesModel.get('stack'),\n        markPoint: seriesModel.get('markPoint'),\n        markLine: seriesModel.get('markLine')\n      }, model.get(['option', 'bar']) || {}, true);\n    }\n  },\n  'stack': function (seriesType, seriesId, seriesModel, model) {\n    var isStack = seriesModel.get('stack') === INNER_STACK_KEYWORD;\n    if (seriesType === 'line' || seriesType === 'bar') {\n      model.setIconStatus('stack', isStack ? 'normal' : 'emphasis');\n      return zrUtil.merge({\n        id: seriesId,\n        stack: isStack ? '' : INNER_STACK_KEYWORD\n      }, model.get(['option', 'stack']) || {}, true);\n    }\n  }\n};\n// TODO: SELF REGISTERED.\necharts.registerAction({\n  type: 'changeMagicType',\n  event: 'magicTypeChanged',\n  update: 'prepareAndUpdate'\n}, function (payload, ecModel) {\n  ecModel.mergeOption(payload.newOption);\n});\nexport default MagicType;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}